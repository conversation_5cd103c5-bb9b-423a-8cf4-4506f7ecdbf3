MODULE Linux arm64 CD594BC30F6EE90608499C790B2735650 libpipewire-module-adapter.so
INFO CODE_ID C34B59CD6E0F06E908499C790B27356519931186
PUBLIC 12ca4 0 pipewire__module_init
STACK CFI INIT a010 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT a040 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT a080 48 .cfa: sp 0 + .ra: x30
STACK CFI a084 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a08c x19: .cfa -16 + ^
STACK CFI a0c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a0d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT a0e0 2b0 .cfa: sp 0 + .ra: x30
STACK CFI a0e8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a0f0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a0fc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a110 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a114 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a30c x19: x19 x20: x20
STACK CFI a314 x23: x23 x24: x24
STACK CFI a328 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a330 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI a334 x19: x19 x20: x20
STACK CFI a340 x23: x23 x24: x24
STACK CFI a34c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a354 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI a35c x19: x19 x20: x20
STACK CFI a368 x23: x23 x24: x24
STACK CFI a374 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a37c .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI a388 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a38c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT a390 340 .cfa: sp 0 + .ra: x30
STACK CFI a398 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a484 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a490 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a5c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a5d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a624 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a62c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a6a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a6ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT a6d0 48 .cfa: sp 0 + .ra: x30
STACK CFI a6d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a6f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a700 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a708 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a720 d4 .cfa: sp 0 + .ra: x30
STACK CFI a728 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a738 x19: .cfa -16 + ^
STACK CFI a794 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a79c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a7ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a7f4 a0 .cfa: sp 0 + .ra: x30
STACK CFI a7fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a80c x19: .cfa -16 + ^
STACK CFI a840 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a848 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT a894 108 .cfa: sp 0 + .ra: x30
STACK CFI a89c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a8ac x19: .cfa -16 + ^
STACK CFI a938 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a940 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a994 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a9a0 cc .cfa: sp 0 + .ra: x30
STACK CFI a9a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a9b8 x19: .cfa -16 + ^
STACK CFI aa18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI aa20 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI aa64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT aa70 e0 .cfa: sp 0 + .ra: x30
STACK CFI aa78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI aa80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI aa90 x21: .cfa -16 + ^
STACK CFI aae8 x21: x21
STACK CFI aaf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI aaf8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI ab24 x21: x21
STACK CFI ab2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ab34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI ab3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ab44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT ab50 50 .cfa: sp 0 + .ra: x30
STACK CFI ab58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ab60 x19: .cfa -16 + ^
STACK CFI ab98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT aba0 114 .cfa: sp 0 + .ra: x30
STACK CFI aba8 .cfa: sp 48 +
STACK CFI abac .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI abb4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI abd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI abd8 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI ac28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ac30 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI ac94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ac9c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT acb4 98 .cfa: sp 0 + .ra: x30
STACK CFI acbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI acc4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI ad34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ad3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI ad44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT ad50 cc .cfa: sp 0 + .ra: x30
STACK CFI ad58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ad68 x19: .cfa -16 + ^
STACK CFI adbc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI adc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI ae14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ae20 13c .cfa: sp 0 + .ra: x30
STACK CFI ae28 .cfa: sp 96 +
STACK CFI ae38 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ae40 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI aee4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI aeec .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT af60 1c4 .cfa: sp 0 + .ra: x30
STACK CFI af68 .cfa: sp 64 +
STACK CFI af6c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI af74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI af88 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b02c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b034 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b124 250 .cfa: sp 0 + .ra: x30
STACK CFI b12c .cfa: sp 96 +
STACK CFI b130 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b138 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b144 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b14c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI b254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b25c .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT b374 250 .cfa: sp 0 + .ra: x30
STACK CFI b37c .cfa: sp 96 +
STACK CFI b380 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b388 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b394 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b39c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI b4a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b4ac .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT b5c4 1cc .cfa: sp 0 + .ra: x30
STACK CFI b5cc .cfa: sp 64 +
STACK CFI b5d0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b5d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b5e0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b6bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b6c4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b790 2c8c .cfa: sp 0 + .ra: x30
STACK CFI b798 .cfa: sp 480 +
STACK CFI b7ac .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI b7c4 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI b7cc x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI b7e4 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI bf90 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI bf98 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT e420 de8 .cfa: sp 0 + .ra: x30
STACK CFI e428 .cfa: sp 352 +
STACK CFI e43c .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI e444 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI e454 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI e460 x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI e468 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI e830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e838 .cfa: sp 352 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT f210 3d0 .cfa: sp 0 + .ra: x30
STACK CFI f218 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI f22c .cfa: sp 4336 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI f294 x23: .cfa -32 + ^
STACK CFI f29c x24: .cfa -24 + ^
STACK CFI f2a4 x25: .cfa -16 + ^
STACK CFI f2ac x26: .cfa -8 + ^
STACK CFI f41c x23: x23
STACK CFI f424 x24: x24
STACK CFI f428 x25: x25
STACK CFI f42c x26: x26
STACK CFI f430 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI f43c x23: x23
STACK CFI f440 x24: x24
STACK CFI f444 x25: x25
STACK CFI f448 x26: x26
STACK CFI f48c .cfa: sp 80 +
STACK CFI f49c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f4a4 .cfa: sp 4336 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI f4a8 x23: x23
STACK CFI f4ac x24: x24
STACK CFI f4b0 x25: x25
STACK CFI f4b4 x26: x26
STACK CFI f4b8 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI f4c8 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI f4e8 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI f508 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI f548 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI f5b8 x23: x23
STACK CFI f5c0 x24: x24
STACK CFI f5c4 x25: x25
STACK CFI f5c8 x26: x26
STACK CFI f5d0 x23: .cfa -32 + ^
STACK CFI f5d4 x24: .cfa -24 + ^
STACK CFI f5d8 x25: .cfa -16 + ^
STACK CFI f5dc x26: .cfa -8 + ^
STACK CFI INIT f5e0 2f8 .cfa: sp 0 + .ra: x30
STACK CFI f5e8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f5f0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI f5f8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI f60c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f610 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f614 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f7a0 x19: x19 x20: x20
STACK CFI f7b0 x25: x25 x26: x26
STACK CFI f7b4 x27: x27 x28: x28
STACK CFI f7b8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI f7c0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI f7d0 x19: x19 x20: x20
STACK CFI f7d4 x25: x25 x26: x26
STACK CFI f7d8 x27: x27 x28: x28
STACK CFI f7e4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI f7ec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI f880 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f89c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f8a0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f8a4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f8b0 x19: x19 x20: x20
STACK CFI f8b8 x25: x25 x26: x26
STACK CFI f8bc x27: x27 x28: x28
STACK CFI f8c8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI f8d0 .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT f8e0 44 .cfa: sp 0 + .ra: x30
STACK CFI f8e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f8f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI f91c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT f924 338 .cfa: sp 0 + .ra: x30
STACK CFI f92c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f938 x19: .cfa -16 + ^
STACK CFI f980 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f988 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT fc60 cf8 .cfa: sp 0 + .ra: x30
STACK CFI fc68 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI fc80 .cfa: sp 6416 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI fd00 x25: .cfa -32 + ^
STACK CFI fd08 x26: .cfa -24 + ^
STACK CFI fd10 x27: .cfa -16 + ^
STACK CFI fd58 x28: .cfa -8 + ^
STACK CFI 100b0 x25: x25
STACK CFI 100b4 x26: x26
STACK CFI 100b8 x27: x27
STACK CFI 100bc x28: x28
STACK CFI 100e0 .cfa: sp 96 +
STACK CFI 100f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 100f8 .cfa: sp 6416 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 10944 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 10948 x25: .cfa -32 + ^
STACK CFI 1094c x26: .cfa -24 + ^
STACK CFI 10950 x27: .cfa -16 + ^
STACK CFI 10954 x28: .cfa -8 + ^
STACK CFI INIT 10960 c90 .cfa: sp 0 + .ra: x30
STACK CFI 10968 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 10974 .cfa: x29 112 +
STACK CFI 10978 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 10984 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1098c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 109a0 v8: .cfa -16 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 10a74 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10a7c .cfa: x29 112 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 115f0 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 115f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1160c .cfa: sp 1184 + x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 11778 .cfa: sp 64 +
STACK CFI 11788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 11790 .cfa: sp 1184 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 117a4 7ec .cfa: sp 0 + .ra: x30
STACK CFI 117ac .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 117c0 .cfa: sp 4384 + x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 118b4 x28: .cfa -24 + ^
STACK CFI 118c0 x23: .cfa -64 + ^
STACK CFI 118d4 x24: .cfa -56 + ^
STACK CFI 118e0 x25: .cfa -48 + ^
STACK CFI 118e8 x26: .cfa -40 + ^
STACK CFI 118f0 x27: .cfa -32 + ^
STACK CFI 119a0 x23: x23
STACK CFI 119a4 x24: x24
STACK CFI 119a8 x25: x25
STACK CFI 119ac x26: x26
STACK CFI 119b0 x27: x27
STACK CFI 119b4 x28: x28
STACK CFI 119dc .cfa: sp 112 +
STACK CFI 119ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 119f4 .cfa: sp 4384 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 11b04 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 11b50 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 11bf8 v8: .cfa -16 + ^
STACK CFI 11c68 v8: v8
STACK CFI 11c6c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 11c88 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 11cb8 x23: x23
STACK CFI 11cbc x24: x24
STACK CFI 11cc0 x25: x25
STACK CFI 11cc4 x26: x26
STACK CFI 11cc8 x27: x27
STACK CFI 11ccc x28: x28
STACK CFI 11cd0 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 11da4 v8: .cfa -16 + ^
STACK CFI 11e14 v8: v8
STACK CFI 11e6c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 11edc x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 11f20 v8: .cfa -16 + ^
STACK CFI 11f68 v8: v8
STACK CFI 11f70 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 11f74 x23: .cfa -64 + ^
STACK CFI 11f78 x24: .cfa -56 + ^
STACK CFI 11f7c x25: .cfa -48 + ^
STACK CFI 11f80 x26: .cfa -40 + ^
STACK CFI 11f84 x27: .cfa -32 + ^
STACK CFI 11f88 x28: .cfa -24 + ^
STACK CFI 11f8c v8: .cfa -16 + ^
STACK CFI INIT 11f90 d14 .cfa: sp 0 + .ra: x30
STACK CFI 11f98 .cfa: sp 352 +
STACK CFI 11fa4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 11fac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 11fb4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 11fbc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 11fd0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11fd8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12530 x27: x27 x28: x28
STACK CFI 12598 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 125a0 .cfa: sp 352 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 126d4 x27: x27 x28: x28
STACK CFI 126d8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12910 x27: x27 x28: x28
STACK CFI 12914 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12920 x27: x27 x28: x28
STACK CFI 12924 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 129a4 x27: x27 x28: x28
STACK CFI 129a8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12c9c x27: x27 x28: x28
STACK CFI 12ca0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 12ca4 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 12cac .cfa: sp 80 +
STACK CFI 12cb8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12cc4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 12e1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 12e24 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
