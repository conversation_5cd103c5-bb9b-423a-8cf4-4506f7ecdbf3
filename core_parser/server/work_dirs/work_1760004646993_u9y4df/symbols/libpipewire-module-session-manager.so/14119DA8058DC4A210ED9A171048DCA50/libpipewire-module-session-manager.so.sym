MODULE Linux arm64 14119DA8058DC4A210ED9A171048DCA50 libpipewire-module-session-manager.so
INFO CODE_ID A89D11148D05A2C410ED9A171048DCA54A1E9D94
PUBLIC 9e74 0 pipewire__module_init
STACK CFI INIT 3da0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3dd0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e10 48 .cfa: sp 0 + .ra: x30
STACK CFI 3e14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e1c x19: .cfa -16 + ^
STACK CFI 3e54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3e60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3e70 48 .cfa: sp 0 + .ra: x30
STACK CFI 3e90 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ea4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3ec0 48 .cfa: sp 0 + .ra: x30
STACK CFI 3ee0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ef4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f10 48 .cfa: sp 0 + .ra: x30
STACK CFI 3f30 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f60 48 .cfa: sp 0 + .ra: x30
STACK CFI 3f80 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3fb0 48 .cfa: sp 0 + .ra: x30
STACK CFI 3fd4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3fe4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4000 48 .cfa: sp 0 + .ra: x30
STACK CFI 4024 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4034 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4050 48 .cfa: sp 0 + .ra: x30
STACK CFI 4074 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4084 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 40a0 68 .cfa: sp 0 + .ra: x30
STACK CFI 40a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40b0 x19: .cfa -16 + ^
STACK CFI 40f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 40f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4100 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4110 68 .cfa: sp 0 + .ra: x30
STACK CFI 4118 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4120 x19: .cfa -16 + ^
STACK CFI 4160 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4168 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4170 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4180 78 .cfa: sp 0 + .ra: x30
STACK CFI 4188 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4190 x19: .cfa -16 + ^
STACK CFI 41e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 41e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 41f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4200 78 .cfa: sp 0 + .ra: x30
STACK CFI 4208 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4210 x19: .cfa -16 + ^
STACK CFI 4260 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4268 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4270 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4280 48 .cfa: sp 0 + .ra: x30
STACK CFI 42a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 42b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 42d0 48 .cfa: sp 0 + .ra: x30
STACK CFI 42f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4304 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4320 48 .cfa: sp 0 + .ra: x30
STACK CFI 4344 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4354 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4370 48 .cfa: sp 0 + .ra: x30
STACK CFI 4394 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 43c0 48 .cfa: sp 0 + .ra: x30
STACK CFI 43e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4410 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 4418 .cfa: sp 80 +
STACK CFI 441c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4424 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4438 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4440 x23: .cfa -16 + ^
STACK CFI 4530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4538 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4600 3c4 .cfa: sp 0 + .ra: x30
STACK CFI 4608 .cfa: sp 160 +
STACK CFI 4614 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4620 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 462c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4650 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 46b4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4754 x21: x21 x22: x22
STACK CFI 4758 x27: x27 x28: x28
STACK CFI 4790 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4798 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 4824 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 483c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 486c x27: x27 x28: x28
STACK CFI 4880 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4884 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 48dc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 490c x21: x21 x22: x22
STACK CFI 4980 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4984 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4988 x27: x27 x28: x28
STACK CFI 499c x21: x21 x22: x22
STACK CFI INIT 49c4 38c .cfa: sp 0 + .ra: x30
STACK CFI 49cc .cfa: sp 160 +
STACK CFI 49d8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 49e4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 49f0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4a14 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4a74 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4b14 x21: x21 x22: x22
STACK CFI 4b18 x27: x27 x28: x28
STACK CFI 4b50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4b58 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 4bbc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4bd4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4c04 x27: x27 x28: x28
STACK CFI 4c18 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4c1c x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 4c5c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4c90 x21: x21 x22: x22
STACK CFI 4d00 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4d14 x21: x21 x22: x22
STACK CFI 4d1c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4d20 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4d24 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI INIT 4d50 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4d58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4d60 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4d6c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4dec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4df4 18 .cfa: sp 0 + .ra: x30
STACK CFI 4dfc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4e04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4e10 18 .cfa: sp 0 + .ra: x30
STACK CFI 4e18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4e20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4e30 18 .cfa: sp 0 + .ra: x30
STACK CFI 4e38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4e40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4e50 6c .cfa: sp 0 + .ra: x30
STACK CFI 4e58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4e60 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4e6c x21: .cfa -16 + ^
STACK CFI 4eb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4ec0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 4ec8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4ed0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4edc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4f58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4f94 c0 .cfa: sp 0 + .ra: x30
STACK CFI 4f9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4fa4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 500c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5014 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5054 6c .cfa: sp 0 + .ra: x30
STACK CFI 505c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5064 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5070 x21: .cfa -16 + ^
STACK CFI 50b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 50c0 58 .cfa: sp 0 + .ra: x30
STACK CFI 50c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 50d0 x19: .cfa -16 + ^
STACK CFI 5110 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5120 6c .cfa: sp 0 + .ra: x30
STACK CFI 5128 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5130 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 513c x21: .cfa -16 + ^
STACK CFI 5184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5190 a4 .cfa: sp 0 + .ra: x30
STACK CFI 5198 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 51a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 51ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 522c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5234 18 .cfa: sp 0 + .ra: x30
STACK CFI 523c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5244 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5250 18 .cfa: sp 0 + .ra: x30
STACK CFI 5258 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5260 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5270 d0 .cfa: sp 0 + .ra: x30
STACK CFI 5278 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5284 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 52ac x21: .cfa -16 + ^
STACK CFI 5318 x21: x21
STACK CFI 531c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5324 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5338 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5340 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5348 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5354 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 537c x21: .cfa -16 + ^
STACK CFI 53ec x21: x21
STACK CFI 53f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 53f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 540c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5414 d4 .cfa: sp 0 + .ra: x30
STACK CFI 541c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5428 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5450 x21: .cfa -16 + ^
STACK CFI 54c0 x21: x21
STACK CFI 54c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 54cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 54e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 54f0 1dc .cfa: sp 0 + .ra: x30
STACK CFI 54f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5500 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 550c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5518 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 55e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 55e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 56d0 1dc .cfa: sp 0 + .ra: x30
STACK CFI 56d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 56e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 56ec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 56f8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 57c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 57c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 58b0 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 58b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 58c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 58cc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 58d8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 59a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 59a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5a90 1dc .cfa: sp 0 + .ra: x30
STACK CFI 5a98 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5aa0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5aac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5ab8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5b80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5b88 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5c70 e4 .cfa: sp 0 + .ra: x30
STACK CFI 5c78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c88 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5d18 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5d54 6e8 .cfa: sp 0 + .ra: x30
STACK CFI 5d5c .cfa: sp 160 +
STACK CFI 5d64 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5d6c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5d74 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5d80 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5d88 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5d90 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5f20 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6440 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 6448 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6450 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6460 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6574 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 657c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6610 dc .cfa: sp 0 + .ra: x30
STACK CFI 6618 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6628 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 66a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 66b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 66f0 6d4 .cfa: sp 0 + .ra: x30
STACK CFI 66f8 .cfa: sp 160 +
STACK CFI 6700 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6708 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6710 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 671c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6724 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 672c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 68b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 68c0 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6dc4 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 6dcc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6dd4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6de4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6eec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6ef4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6f90 148 .cfa: sp 0 + .ra: x30
STACK CFI 6f98 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6fa4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6fac x23: .cfa -16 + ^
STACK CFI 70b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 70c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 70d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 70e0 150 .cfa: sp 0 + .ra: x30
STACK CFI 70e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 70f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 70fc x23: .cfa -16 + ^
STACK CFI 7210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7218 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 7230 150 .cfa: sp 0 + .ra: x30
STACK CFI 7238 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7244 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 724c x23: .cfa -16 + ^
STACK CFI 7360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7368 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 7380 60 .cfa: sp 0 + .ra: x30
STACK CFI 7388 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7390 x19: .cfa -16 + ^
STACK CFI 73d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 73e0 60 .cfa: sp 0 + .ra: x30
STACK CFI 73e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 73f0 x19: .cfa -16 + ^
STACK CFI 7438 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7440 60 .cfa: sp 0 + .ra: x30
STACK CFI 7448 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7450 x19: .cfa -16 + ^
STACK CFI 7498 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 74a0 78 .cfa: sp 0 + .ra: x30
STACK CFI 74a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 74b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 74d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 74d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7520 78 .cfa: sp 0 + .ra: x30
STACK CFI 7528 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7530 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7558 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7590 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 75a0 78 .cfa: sp 0 + .ra: x30
STACK CFI 75a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 75b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 75d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 75d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7620 400 .cfa: sp 0 + .ra: x30
STACK CFI 7628 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7634 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7640 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7648 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7650 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 786c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7a20 400 .cfa: sp 0 + .ra: x30
STACK CFI 7a28 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7a34 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7a40 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7a48 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7a50 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7c64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7c6c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7e20 68 .cfa: sp 0 + .ra: x30
STACK CFI 7e28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e30 x19: .cfa -16 + ^
STACK CFI 7e70 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7e78 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7e80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7e90 68 .cfa: sp 0 + .ra: x30
STACK CFI 7e98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ea0 x19: .cfa -16 + ^
STACK CFI 7ee0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7ee8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7ef0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7f00 68 .cfa: sp 0 + .ra: x30
STACK CFI 7f08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7f10 x19: .cfa -16 + ^
STACK CFI 7f50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7f58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7f60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7f70 68 .cfa: sp 0 + .ra: x30
STACK CFI 7f78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7f80 x19: .cfa -16 + ^
STACK CFI 7fc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7fc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7fd0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7fe0 144 .cfa: sp 0 + .ra: x30
STACK CFI 7fe8 .cfa: sp 96 +
STACK CFI 7ff8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8000 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 80a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 80ac .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8124 144 .cfa: sp 0 + .ra: x30
STACK CFI 812c .cfa: sp 96 +
STACK CFI 813c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8144 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 81e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 81f0 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8270 144 .cfa: sp 0 + .ra: x30
STACK CFI 8278 .cfa: sp 96 +
STACK CFI 8288 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8290 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8334 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 833c .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 83b4 144 .cfa: sp 0 + .ra: x30
STACK CFI 83bc .cfa: sp 96 +
STACK CFI 83cc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 83d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8480 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8500 174 .cfa: sp 0 + .ra: x30
STACK CFI 8508 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 853c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8548 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 857c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8584 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8590 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 859c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 85a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 85a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 85dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 85e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8620 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8630 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 863c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 864c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8674 c8 .cfa: sp 0 + .ra: x30
STACK CFI 867c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8688 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8694 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 86f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8700 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8740 18 .cfa: sp 0 + .ra: x30
STACK CFI 8748 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8750 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8760 18 .cfa: sp 0 + .ra: x30
STACK CFI 8768 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8770 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8780 18 .cfa: sp 0 + .ra: x30
STACK CFI 8788 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8790 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 87a0 18 .cfa: sp 0 + .ra: x30
STACK CFI 87a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 87b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 87c0 18 .cfa: sp 0 + .ra: x30
STACK CFI 87c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 87d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 87e0 18 .cfa: sp 0 + .ra: x30
STACK CFI 87e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 87f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8800 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 8808 .cfa: sp 208 +
STACK CFI 880c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8814 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8824 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 882c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8998 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 89a0 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8ab0 2ec .cfa: sp 0 + .ra: x30
STACK CFI 8ab8 .cfa: sp 224 +
STACK CFI 8abc .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8ac4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8ad4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8ae0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8c48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8c50 .cfa: sp 224 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 8d00 x25: .cfa -16 + ^
STACK CFI 8d28 x25: x25
STACK CFI 8d30 x25: .cfa -16 + ^
STACK CFI 8d34 x25: x25
STACK CFI 8d98 x25: .cfa -16 + ^
STACK CFI INIT 8da0 2c0 .cfa: sp 0 + .ra: x30
STACK CFI 8da8 .cfa: sp 208 +
STACK CFI 8dac .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8db4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8dc4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8dcc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8f58 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9060 204 .cfa: sp 0 + .ra: x30
STACK CFI 9068 .cfa: sp 128 +
STACK CFI 906c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9074 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9088 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 90b0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 90bc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 90c8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9130 x21: x21 x22: x22
STACK CFI 9134 x23: x23 x24: x24
STACK CFI 9138 x27: x27 x28: x28
STACK CFI 9178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 9180 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 924c x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 9250 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9254 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9258 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 9264 208 .cfa: sp 0 + .ra: x30
STACK CFI 926c .cfa: sp 128 +
STACK CFI 9270 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9278 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 928c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 92b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 92c0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 92cc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9338 x21: x21 x22: x22
STACK CFI 933c x23: x23 x24: x24
STACK CFI 9340 x27: x27 x28: x28
STACK CFI 9380 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 9388 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 9454 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 9458 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 945c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9460 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 9470 204 .cfa: sp 0 + .ra: x30
STACK CFI 9478 .cfa: sp 128 +
STACK CFI 947c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9484 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9498 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 94c0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 94cc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 94d8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9540 x21: x21 x22: x22
STACK CFI 9544 x23: x23 x24: x24
STACK CFI 9548 x27: x27 x28: x28
STACK CFI 9588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 9590 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 965c x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 9660 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9664 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9668 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 9674 400 .cfa: sp 0 + .ra: x30
STACK CFI 967c .cfa: sp 160 +
STACK CFI 9688 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9690 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 969c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 96a8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 96b4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 9884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 988c .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9a74 400 .cfa: sp 0 + .ra: x30
STACK CFI 9a7c .cfa: sp 160 +
STACK CFI 9a88 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9a90 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9a9c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9aa8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9ab4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 9c84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9c8c .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9e74 4ec .cfa: sp 0 + .ra: x30
STACK CFI 9e7c .cfa: sp 112 +
STACK CFI 9e88 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9e90 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9ebc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9ecc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9ed0 x25: .cfa -16 + ^
STACK CFI a2dc x19: x19 x20: x20
STACK CFI a2e4 x23: x23 x24: x24
STACK CFI a2e8 x25: x25
STACK CFI a310 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI a318 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI a348 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25
STACK CFI a354 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a358 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a35c x25: .cfa -16 + ^
STACK CFI INIT a360 e0 .cfa: sp 0 + .ra: x30
STACK CFI a368 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a370 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a37c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a384 x23: .cfa -16 + ^
STACK CFI a430 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI a438 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT a440 1624 .cfa: sp 0 + .ra: x30
STACK CFI a448 .cfa: sp 352 +
STACK CFI a458 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a464 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a484 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a4a0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a4ac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a4bc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a53c x21: x21 x22: x22
STACK CFI a540 x23: x23 x24: x24
STACK CFI a544 x25: x25 x26: x26
STACK CFI a548 x27: x27 x28: x28
STACK CFI a574 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a57c .cfa: sp 352 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI a580 x21: x21 x22: x22
STACK CFI a588 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a7d4 x21: x21 x22: x22
STACK CFI a7dc x23: x23 x24: x24
STACK CFI a7e0 x25: x25 x26: x26
STACK CFI a7e4 x27: x27 x28: x28
STACK CFI a7e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ba1c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI ba20 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ba24 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ba28 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ba2c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT ba64 4c0 .cfa: sp 0 + .ra: x30
STACK CFI ba6c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ba88 .cfa: sp 2304 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI bb48 .cfa: sp 96 +
STACK CFI bb64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI bb6c .cfa: sp 2304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT bf24 128 .cfa: sp 0 + .ra: x30
STACK CFI bf2c .cfa: sp 112 +
STACK CFI bf34 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI bf3c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI bf4c x27: .cfa -16 + ^
STACK CFI bf5c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI bf64 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI bf70 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c028 x19: x19 x20: x20
STACK CFI c02c x21: x21 x22: x22
STACK CFI c030 x25: x25 x26: x26
STACK CFI c044 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI INIT c050 4c4 .cfa: sp 0 + .ra: x30
STACK CFI c058 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c074 .cfa: sp 2304 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c138 .cfa: sp 96 +
STACK CFI c154 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c15c .cfa: sp 2304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT c514 128 .cfa: sp 0 + .ra: x30
STACK CFI c51c .cfa: sp 112 +
STACK CFI c524 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c52c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c53c x27: .cfa -16 + ^
STACK CFI c54c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c554 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c560 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c618 x19: x19 x20: x20
STACK CFI c61c x21: x21 x22: x22
STACK CFI c620 x25: x25 x26: x26
STACK CFI c634 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI INIT c640 4c4 .cfa: sp 0 + .ra: x30
STACK CFI c648 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c664 .cfa: sp 2304 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c728 .cfa: sp 96 +
STACK CFI c744 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c74c .cfa: sp 2304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT cb04 128 .cfa: sp 0 + .ra: x30
STACK CFI cb0c .cfa: sp 112 +
STACK CFI cb14 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI cb1c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI cb2c x27: .cfa -16 + ^
STACK CFI cb3c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI cb44 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI cb50 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI cc08 x19: x19 x20: x20
STACK CFI cc0c x21: x21 x22: x22
STACK CFI cc10 x25: x25 x26: x26
STACK CFI cc24 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI INIT cc30 424 .cfa: sp 0 + .ra: x30
STACK CFI cc38 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI cc50 .cfa: sp 2288 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ccbc x22: .cfa -56 + ^
STACK CFI ccd4 x21: .cfa -64 + ^
STACK CFI ce24 x21: x21
STACK CFI ce28 x22: x22
STACK CFI ce48 .cfa: sp 96 +
STACK CFI ce60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ce68 .cfa: sp 2288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI d048 x21: x21 x22: x22
STACK CFI d04c x21: .cfa -64 + ^
STACK CFI d050 x22: .cfa -56 + ^
STACK CFI INIT d054 134 .cfa: sp 0 + .ra: x30
STACK CFI d05c .cfa: sp 112 +
STACK CFI d060 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d068 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d070 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d07c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d0a0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d0ac x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d164 x25: x25 x26: x26
STACK CFI d168 x27: x27 x28: x28
STACK CFI d180 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT d190 424 .cfa: sp 0 + .ra: x30
STACK CFI d198 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d1b0 .cfa: sp 2288 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d21c x22: .cfa -56 + ^
STACK CFI d234 x21: .cfa -64 + ^
STACK CFI d384 x21: x21
STACK CFI d388 x22: x22
STACK CFI d3a8 .cfa: sp 96 +
STACK CFI d3c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI d3c8 .cfa: sp 2288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI d5a8 x21: x21 x22: x22
STACK CFI d5ac x21: .cfa -64 + ^
STACK CFI d5b0 x22: .cfa -56 + ^
STACK CFI INIT d5b4 134 .cfa: sp 0 + .ra: x30
STACK CFI d5bc .cfa: sp 112 +
STACK CFI d5c0 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d5c8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d5d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d5dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d600 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d60c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d6c4 x25: x25 x26: x26
STACK CFI d6c8 x27: x27 x28: x28
STACK CFI d6e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT d6f0 490 .cfa: sp 0 + .ra: x30
STACK CFI d6f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d714 .cfa: sp 2288 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d914 .cfa: sp 96 +
STACK CFI d930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI d938 .cfa: sp 2288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT db80 134 .cfa: sp 0 + .ra: x30
STACK CFI db88 .cfa: sp 112 +
STACK CFI db8c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI db94 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI db9c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI dba8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI dbcc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI dbd8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI dc90 x25: x25 x26: x26
STACK CFI dc94 x27: x27 x28: x28
STACK CFI dcac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT dcb4 420 .cfa: sp 0 + .ra: x30
STACK CFI dcbc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI dcd4 .cfa: sp 2288 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI dd40 x22: .cfa -56 + ^
STACK CFI dd58 x21: .cfa -64 + ^
STACK CFI dea8 x21: x21
STACK CFI deac x22: x22
STACK CFI decc .cfa: sp 96 +
STACK CFI dee4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI deec .cfa: sp 2288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI e0c8 x21: x21 x22: x22
STACK CFI e0cc x21: .cfa -64 + ^
STACK CFI e0d0 x22: .cfa -56 + ^
STACK CFI INIT e0d4 134 .cfa: sp 0 + .ra: x30
STACK CFI e0dc .cfa: sp 112 +
STACK CFI e0e0 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e0e8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI e0f0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e0fc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e120 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI e12c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e1e4 x25: x25 x26: x26
STACK CFI e1e8 x27: x27 x28: x28
STACK CFI e200 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT e210 d0 .cfa: sp 0 + .ra: x30
STACK CFI e218 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e2c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e2d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT e2e0 1c .cfa: sp 0 + .ra: x30
STACK CFI e2e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e2f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e300 3f8 .cfa: sp 0 + .ra: x30
STACK CFI e308 .cfa: sp 160 +
STACK CFI e314 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e31c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e328 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e334 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e340 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI e510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI e518 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT e700 68 .cfa: sp 0 + .ra: x30
STACK CFI e708 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e710 x19: .cfa -16 + ^
STACK CFI e750 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e758 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI e760 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e770 78 .cfa: sp 0 + .ra: x30
STACK CFI e778 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e780 x19: .cfa -16 + ^
STACK CFI e7d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e7d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI e7e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e7f0 144 .cfa: sp 0 + .ra: x30
STACK CFI e7f8 .cfa: sp 96 +
STACK CFI e808 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e810 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI e8b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e8bc .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT e934 2010 .cfa: sp 0 + .ra: x30
STACK CFI e93c .cfa: sp 480 +
STACK CFI e950 .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI e968 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI e970 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI e988 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI ede0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ede8 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 10944 268 .cfa: sp 0 + .ra: x30
STACK CFI 1094c .cfa: sp 112 +
STACK CFI 10958 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10960 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 10968 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 10970 x23: .cfa -16 + ^
STACK CFI 10b0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 10b14 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 10bb0 25c .cfa: sp 0 + .ra: x30
STACK CFI 10bb8 .cfa: sp 112 +
STACK CFI 10bbc .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 10bc4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 10bd8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 10be0 x23: .cfa -16 + ^
STACK CFI 10d68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 10d70 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 10e10 23c .cfa: sp 0 + .ra: x30
STACK CFI 10e18 .cfa: sp 96 +
STACK CFI 10e1c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10e24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10e38 x21: .cfa -16 + ^
STACK CFI 10fac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 10fb4 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 11050 244 .cfa: sp 0 + .ra: x30
STACK CFI 11058 .cfa: sp 192 +
STACK CFI 11064 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1106c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11078 x21: .cfa -16 + ^
STACK CFI 111a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 111a8 .cfa: sp 192 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 11294 268 .cfa: sp 0 + .ra: x30
STACK CFI 1129c .cfa: sp 144 +
STACK CFI 112a0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 112a8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 112bc x21: .cfa -16 + ^
STACK CFI 1145c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 11464 .cfa: sp 144 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 11500 25c .cfa: sp 0 + .ra: x30
STACK CFI 11508 .cfa: sp 208 +
STACK CFI 11514 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1151c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 11528 x21: .cfa -16 + ^
STACK CFI 11664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1166c .cfa: sp 208 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 11760 24c .cfa: sp 0 + .ra: x30
STACK CFI 11768 .cfa: sp 96 +
STACK CFI 11774 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11780 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1190c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11914 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 119b0 198 .cfa: sp 0 + .ra: x30
STACK CFI 119b8 .cfa: sp 96 +
STACK CFI 119c4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 119cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 119d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 119e0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 11b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 11b2c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 11b50 19c .cfa: sp 0 + .ra: x30
STACK CFI 11b58 .cfa: sp 128 +
STACK CFI 11b64 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 11b6c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 11b74 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 11b80 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 11b8c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 11ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 11ce8 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 11cf0 74 .cfa: sp 0 + .ra: x30
STACK CFI 11cf8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11d00 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 11d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 11d64 19c .cfa: sp 0 + .ra: x30
STACK CFI 11d6c .cfa: sp 96 +
STACK CFI 11d78 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 11d80 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 11d88 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11d94 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 11edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 11ee4 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 11f00 19c .cfa: sp 0 + .ra: x30
STACK CFI 11f08 .cfa: sp 128 +
STACK CFI 11f14 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 11f1c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 11f24 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 11f30 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 11f3c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 12090 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 12098 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 120a0 174 .cfa: sp 0 + .ra: x30
STACK CFI 120a8 .cfa: sp 96 +
STACK CFI 120b4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 120bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 120c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 120d0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 12208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 12210 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 12214 74 .cfa: sp 0 + .ra: x30
STACK CFI 1221c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12224 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 12278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 12290 24c .cfa: sp 0 + .ra: x30
STACK CFI 12298 .cfa: sp 96 +
STACK CFI 122a4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 122b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1243c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 12444 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 124e0 74 .cfa: sp 0 + .ra: x30
STACK CFI 124e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 124f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 12544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 12554 74 .cfa: sp 0 + .ra: x30
STACK CFI 1255c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12564 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 125b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 125d0 74 .cfa: sp 0 + .ra: x30
STACK CFI 125d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 125e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 12634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 12644 74 .cfa: sp 0 + .ra: x30
STACK CFI 1264c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12654 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 126a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 126c0 264 .cfa: sp 0 + .ra: x30
STACK CFI 126c8 .cfa: sp 160 +
STACK CFI 126d4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 126dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 126e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 126f0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 126fc x25: .cfa -16 + ^
STACK CFI 1282c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 12834 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 12924 18 .cfa: sp 0 + .ra: x30
STACK CFI 1292c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12934 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12940 18 .cfa: sp 0 + .ra: x30
STACK CFI 12948 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12950 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12960 18 .cfa: sp 0 + .ra: x30
STACK CFI 12968 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12970 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12980 178 .cfa: sp 0 + .ra: x30
STACK CFI 12988 .cfa: sp 96 +
STACK CFI 12994 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1299c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 129a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 129ac x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 12aec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 12af4 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 12b00 18 .cfa: sp 0 + .ra: x30
STACK CFI 12b08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12b10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12b20 18 .cfa: sp 0 + .ra: x30
STACK CFI 12b28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12b30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12b40 18 .cfa: sp 0 + .ra: x30
STACK CFI 12b48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12b50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12b60 264 .cfa: sp 0 + .ra: x30
STACK CFI 12b68 .cfa: sp 144 +
STACK CFI 12b74 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12b7c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 12b84 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12b90 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 12cd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 12cd8 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 12dc4 18 .cfa: sp 0 + .ra: x30
STACK CFI 12dcc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12dd4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12de0 18 .cfa: sp 0 + .ra: x30
STACK CFI 12de8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12df0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12e00 18 .cfa: sp 0 + .ra: x30
STACK CFI 12e08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12e10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12e20 198 .cfa: sp 0 + .ra: x30
STACK CFI 12e28 .cfa: sp 96 +
STACK CFI 12e34 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12e3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 12e44 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12e50 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 12f94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 12f9c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 12fc0 18 .cfa: sp 0 + .ra: x30
STACK CFI 12fc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12fd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12fe0 18 .cfa: sp 0 + .ra: x30
STACK CFI 12fe8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12ff0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 13000 18 .cfa: sp 0 + .ra: x30
STACK CFI 13008 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 13010 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 13020 20 .cfa: sp 0 + .ra: x30
STACK CFI 13028 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 13034 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 13040 20 .cfa: sp 0 + .ra: x30
STACK CFI 13048 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 13054 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 13060 20 .cfa: sp 0 + .ra: x30
STACK CFI 13068 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 13074 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 13080 20 .cfa: sp 0 + .ra: x30
STACK CFI 13088 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 13094 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 130a0 20 .cfa: sp 0 + .ra: x30
STACK CFI 130a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 130b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 130c0 20 .cfa: sp 0 + .ra: x30
STACK CFI 130c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 130d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 130e0 290 .cfa: sp 0 + .ra: x30
STACK CFI 130e8 .cfa: sp 112 +
STACK CFI 130f4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 130fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 13104 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 13110 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 13260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 13268 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 13370 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 13378 .cfa: sp 128 +
STACK CFI 13384 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1338c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 13394 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 133a0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 133ac x25: .cfa -16 + ^
STACK CFI 13504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1350c .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 13620 290 .cfa: sp 0 + .ra: x30
STACK CFI 13628 .cfa: sp 112 +
STACK CFI 13634 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1363c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 13644 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 13650 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 137a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 137a8 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 138b0 250 .cfa: sp 0 + .ra: x30
STACK CFI 138b8 .cfa: sp 128 +
STACK CFI 138c4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 138cc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 138d4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 138e0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 138ec x25: .cfa -16 + ^
STACK CFI 13a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 13a4c .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 13b00 74 .cfa: sp 0 + .ra: x30
STACK CFI 13b08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13b10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 13b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 13b74 250 .cfa: sp 0 + .ra: x30
STACK CFI 13b7c .cfa: sp 96 +
STACK CFI 13b88 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 13b94 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 13d24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 13d2c .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 13dc4 74 .cfa: sp 0 + .ra: x30
STACK CFI 13dcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13dd4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 13e28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 13e40 74 .cfa: sp 0 + .ra: x30
STACK CFI 13e48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13e50 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 13ea4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 13eb4 74 .cfa: sp 0 + .ra: x30
STACK CFI 13ebc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13ec4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 13f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 13f30 74 .cfa: sp 0 + .ra: x30
STACK CFI 13f38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13f40 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 13f94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 13fa4 260 .cfa: sp 0 + .ra: x30
STACK CFI 13fac .cfa: sp 160 +
STACK CFI 13fb8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 13fc0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 13fc8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 13fd4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13fe0 x25: .cfa -16 + ^
STACK CFI 14110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 14118 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 14204 18 .cfa: sp 0 + .ra: x30
STACK CFI 1420c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14214 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14220 18 .cfa: sp 0 + .ra: x30
STACK CFI 14228 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14230 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14240 18 .cfa: sp 0 + .ra: x30
STACK CFI 14248 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14250 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14260 178 .cfa: sp 0 + .ra: x30
STACK CFI 14268 .cfa: sp 96 +
STACK CFI 14274 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1427c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 14284 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1428c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 143cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 143d4 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 143e0 18 .cfa: sp 0 + .ra: x30
STACK CFI 143e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 143f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14400 18 .cfa: sp 0 + .ra: x30
STACK CFI 14408 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14410 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14420 18 .cfa: sp 0 + .ra: x30
STACK CFI 14428 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14430 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14440 264 .cfa: sp 0 + .ra: x30
STACK CFI 14448 .cfa: sp 144 +
STACK CFI 14454 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1445c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 14464 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14470 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 145b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 145b8 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 146a4 18 .cfa: sp 0 + .ra: x30
STACK CFI 146ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 146b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 146c0 18 .cfa: sp 0 + .ra: x30
STACK CFI 146c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 146d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 146e0 18 .cfa: sp 0 + .ra: x30
STACK CFI 146e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 146f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14700 198 .cfa: sp 0 + .ra: x30
STACK CFI 14708 .cfa: sp 96 +
STACK CFI 14714 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1471c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 14724 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14730 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 14874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1487c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 148a0 18 .cfa: sp 0 + .ra: x30
STACK CFI 148a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 148b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 148c0 18 .cfa: sp 0 + .ra: x30
STACK CFI 148c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 148d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 148e0 18 .cfa: sp 0 + .ra: x30
STACK CFI 148e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 148f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14900 20 .cfa: sp 0 + .ra: x30
STACK CFI 14908 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14914 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14920 20 .cfa: sp 0 + .ra: x30
STACK CFI 14928 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14934 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14940 20 .cfa: sp 0 + .ra: x30
STACK CFI 14948 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14954 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14960 20 .cfa: sp 0 + .ra: x30
STACK CFI 14968 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14974 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14980 18 .cfa: sp 0 + .ra: x30
STACK CFI 14988 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14990 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 149a0 18 .cfa: sp 0 + .ra: x30
STACK CFI 149a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 149b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 149c0 18 .cfa: sp 0 + .ra: x30
STACK CFI 149c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 149d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 149e0 18 .cfa: sp 0 + .ra: x30
STACK CFI 149e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 149f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14a00 18 .cfa: sp 0 + .ra: x30
STACK CFI 14a08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14a10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14a20 18 .cfa: sp 0 + .ra: x30
STACK CFI 14a28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14a30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14a40 18 .cfa: sp 0 + .ra: x30
STACK CFI 14a48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14a50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14a60 18 .cfa: sp 0 + .ra: x30
STACK CFI 14a68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14a70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14a80 18 .cfa: sp 0 + .ra: x30
STACK CFI 14a88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14a90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14aa0 18 .cfa: sp 0 + .ra: x30
STACK CFI 14aa8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14ab0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14ac0 18 .cfa: sp 0 + .ra: x30
STACK CFI 14ac8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14ad0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14ae0 18 .cfa: sp 0 + .ra: x30
STACK CFI 14ae8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14af0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14b00 18 .cfa: sp 0 + .ra: x30
STACK CFI 14b08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14b10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14b20 18 .cfa: sp 0 + .ra: x30
STACK CFI 14b28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14b30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14b40 18 .cfa: sp 0 + .ra: x30
STACK CFI 14b48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14b50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14b60 18 .cfa: sp 0 + .ra: x30
STACK CFI 14b68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14b70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14b80 18 .cfa: sp 0 + .ra: x30
STACK CFI 14b88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14b90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14ba0 18 .cfa: sp 0 + .ra: x30
STACK CFI 14ba8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14bb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14bc0 de8 .cfa: sp 0 + .ra: x30
STACK CFI 14bc8 .cfa: sp 352 +
STACK CFI 14bdc .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 14be4 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 14bf4 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 14c00 x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 14c08 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 14fd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 14fd8 .cfa: sp 352 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT 159b0 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 159b8 .cfa: sp 128 +
STACK CFI 159c4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 159cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 15b84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15b8c .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 15ba0 204 .cfa: sp 0 + .ra: x30
STACK CFI 15ba8 .cfa: sp 144 +
STACK CFI 15bb4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15bbc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 15d90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15d98 .cfa: sp 144 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 15da4 214 .cfa: sp 0 + .ra: x30
STACK CFI 15dac .cfa: sp 160 +
STACK CFI 15db8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15dc0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 15fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15fac .cfa: sp 160 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 15fc0 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 15fc8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 15fd4 .cfa: x29 48 +
STACK CFI 15fe0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 16150 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 16158 .cfa: x29 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 16270 598 .cfa: sp 0 + .ra: x30
STACK CFI 1627c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 16280 .cfa: x29 96 +
STACK CFI 16294 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 162ac x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 16568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 16570 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 16810 58c .cfa: sp 0 + .ra: x30
STACK CFI 1681c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 16820 .cfa: x29 96 +
STACK CFI 16834 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1684c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 16af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 16b00 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 16da0 204 .cfa: sp 0 + .ra: x30
STACK CFI 16da8 .cfa: sp 144 +
STACK CFI 16db4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16dbc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 16f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 16f98 .cfa: sp 144 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 16fa4 214 .cfa: sp 0 + .ra: x30
STACK CFI 16fac .cfa: sp 160 +
STACK CFI 16fb8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16fc0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 171a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 171ac .cfa: sp 160 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 171c0 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 171c8 .cfa: sp 128 +
STACK CFI 171d4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 171dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1739c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 173a4 .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 173b0 540 .cfa: sp 0 + .ra: x30
STACK CFI 173b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 173bc .cfa: x29 80 +
STACK CFI 173d0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 173e0 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1765c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 17664 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 178f0 5b4 .cfa: sp 0 + .ra: x30
STACK CFI 178f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 178fc .cfa: x29 96 +
STACK CFI 17910 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1792c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 17c00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 17c08 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 17ea4 45c .cfa: sp 0 + .ra: x30
STACK CFI 17eb0 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 17eb4 .cfa: x29 80 +
STACK CFI 17ec0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 17ec8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 17edc x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 180cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 180d4 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 18300 460 .cfa: sp 0 + .ra: x30
STACK CFI 1830c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 18310 .cfa: x29 80 +
STACK CFI 1831c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 18324 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 18338 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1852c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 18534 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 18760 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 18768 .cfa: sp 128 +
STACK CFI 18774 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1877c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 18934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1893c .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 18950 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 18958 .cfa: sp 128 +
STACK CFI 18964 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1896c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 18b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18b2c .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 18b40 440 .cfa: sp 0 + .ra: x30
STACK CFI 18b4c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 18b50 .cfa: x29 80 +
STACK CFI 18b5c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 18b64 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 18b78 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 18d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 18d54 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 18f80 440 .cfa: sp 0 + .ra: x30
STACK CFI 18f8c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 18f90 .cfa: x29 80 +
STACK CFI 18f9c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 18fa4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 18fb8 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1918c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 19194 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 193c0 450 .cfa: sp 0 + .ra: x30
STACK CFI 193cc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 193d0 .cfa: x29 80 +
STACK CFI 193dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 193e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 193f8 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 195dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 195e4 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 19810 450 .cfa: sp 0 + .ra: x30
STACK CFI 1981c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 19820 .cfa: x29 80 +
STACK CFI 1982c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 19834 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 19848 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 19a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 19a34 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 19c60 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 19c68 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 19c74 .cfa: x29 48 +
STACK CFI 19c80 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 19df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19df8 .cfa: x29 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 19f10 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 19f18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 19f24 .cfa: x29 48 +
STACK CFI 19f30 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1a0a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1a0a8 .cfa: x29 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1a1c0 418 .cfa: sp 0 + .ra: x30
STACK CFI 1a1c8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1a1d4 .cfa: x29 80 +
STACK CFI 1a1d8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1a1f0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 1a39c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1a3a4 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1a5e0 418 .cfa: sp 0 + .ra: x30
STACK CFI 1a5e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1a5f4 .cfa: x29 80 +
STACK CFI 1a5f8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1a610 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 1a7bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1a7c4 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1aa00 220 .cfa: sp 0 + .ra: x30
STACK CFI 1aa08 .cfa: sp 176 +
STACK CFI 1aa14 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1aa1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1ac0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1ac14 .cfa: sp 176 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1ac20 18 .cfa: sp 0 + .ra: x30
STACK CFI 1ac28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ac30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ac40 18 .cfa: sp 0 + .ra: x30
STACK CFI 1ac48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ac50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ac60 18 .cfa: sp 0 + .ra: x30
STACK CFI 1ac68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ac70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ac80 220 .cfa: sp 0 + .ra: x30
STACK CFI 1ac88 .cfa: sp 176 +
STACK CFI 1ac94 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ac9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1ae8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1ae94 .cfa: sp 176 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1aea0 18 .cfa: sp 0 + .ra: x30
STACK CFI 1aea8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1aeb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1aec0 18 .cfa: sp 0 + .ra: x30
STACK CFI 1aec8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1aed0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1aee0 18 .cfa: sp 0 + .ra: x30
STACK CFI 1aee8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1aef0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1af00 48 .cfa: sp 0 + .ra: x30
STACK CFI 1af08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1af30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1af3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1af40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1af50 48 .cfa: sp 0 + .ra: x30
STACK CFI 1af74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1af84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1afa0 204 .cfa: sp 0 + .ra: x30
STACK CFI 1afa8 .cfa: sp 144 +
STACK CFI 1afb4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1afbc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1b190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b198 .cfa: sp 144 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b1a4 21c .cfa: sp 0 + .ra: x30
STACK CFI 1b1ac .cfa: sp 176 +
STACK CFI 1b1b8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b1c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1b3ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b3b4 .cfa: sp 176 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b3c0 204 .cfa: sp 0 + .ra: x30
STACK CFI 1b3c8 .cfa: sp 144 +
STACK CFI 1b3d4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b3dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1b5b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b5b8 .cfa: sp 144 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b5c4 208 .cfa: sp 0 + .ra: x30
STACK CFI 1b5cc .cfa: sp 144 +
STACK CFI 1b5d8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b5e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1b7b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b7c0 .cfa: sp 144 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b7d0 220 .cfa: sp 0 + .ra: x30
STACK CFI 1b7d8 .cfa: sp 176 +
STACK CFI 1b7e4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b7ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1b9dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b9e4 .cfa: sp 176 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b9f0 204 .cfa: sp 0 + .ra: x30
STACK CFI 1b9f8 .cfa: sp 144 +
STACK CFI 1ba04 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ba0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1bbe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1bbe8 .cfa: sp 144 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1bbf4 48 .cfa: sp 0 + .ra: x30
STACK CFI 1bc18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1bc28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1bc40 100 .cfa: sp 0 + .ra: x30
STACK CFI 1bc48 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1bc54 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1bc64 x21: .cfa -16 + ^
STACK CFI 1bcf8 x21: x21
STACK CFI 1bd04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1bd0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1bd10 x21: x21
STACK CFI 1bd20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1bd28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1bd2c x21: x21
STACK CFI 1bd38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1bd40 100 .cfa: sp 0 + .ra: x30
STACK CFI 1bd48 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1bd54 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1bd64 x21: .cfa -16 + ^
STACK CFI 1bdf8 x21: x21
STACK CFI 1be04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1be0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1be10 x21: x21
STACK CFI 1be20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1be28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1be2c x21: x21
STACK CFI 1be38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1be40 100 .cfa: sp 0 + .ra: x30
STACK CFI 1be48 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1be54 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1be64 x21: .cfa -16 + ^
STACK CFI 1bef8 x21: x21
STACK CFI 1bf04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1bf0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1bf10 x21: x21
STACK CFI 1bf20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1bf28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1bf2c x21: x21
STACK CFI 1bf38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1bf40 100 .cfa: sp 0 + .ra: x30
STACK CFI 1bf48 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1bf54 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1bf64 x21: .cfa -16 + ^
STACK CFI 1bff8 x21: x21
STACK CFI 1c004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c00c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1c010 x21: x21
STACK CFI 1c020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c028 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1c02c x21: x21
STACK CFI 1c038 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1c040 18 .cfa: sp 0 + .ra: x30
STACK CFI 1c048 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1c050 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1c060 d4 .cfa: sp 0 + .ra: x30
STACK CFI 1c068 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1c074 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1c09c x21: .cfa -16 + ^
STACK CFI 1c10c x21: x21
STACK CFI 1c110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c118 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1c12c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1c134 68 .cfa: sp 0 + .ra: x30
STACK CFI 1c13c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c144 x19: .cfa -16 + ^
STACK CFI 1c184 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1c18c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1c194 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1c1a0 118 .cfa: sp 0 + .ra: x30
STACK CFI 1c1a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1c1b8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1c298 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1c2a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1c2b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1c2c0 78 .cfa: sp 0 + .ra: x30
STACK CFI 1c2c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c2d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1c2f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c2f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1c330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1c340 284 .cfa: sp 0 + .ra: x30
STACK CFI 1c348 .cfa: sp 208 +
STACK CFI 1c34c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1c354 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1c35c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1c364 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1c4d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1c4e0 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1c5c4 18 .cfa: sp 0 + .ra: x30
STACK CFI 1c5cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1c5d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1c5e0 200 .cfa: sp 0 + .ra: x30
STACK CFI 1c5e8 .cfa: sp 128 +
STACK CFI 1c5ec .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1c5f4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1c608 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1c630 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1c63c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1c648 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1c6b0 x21: x21 x22: x22
STACK CFI 1c6b4 x23: x23 x24: x24
STACK CFI 1c6b8 x27: x27 x28: x28
STACK CFI 1c6f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 1c700 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1c7c8 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 1c7cc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1c7d0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1c7d4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 1c7e0 68 .cfa: sp 0 + .ra: x30
STACK CFI 1c7e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c7f0 x19: .cfa -16 + ^
STACK CFI 1c830 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1c838 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1c840 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1c850 78 .cfa: sp 0 + .ra: x30
STACK CFI 1c858 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c860 x19: .cfa -16 + ^
STACK CFI 1c8b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1c8b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1c8c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1c8d0 144 .cfa: sp 0 + .ra: x30
STACK CFI 1c8d8 .cfa: sp 96 +
STACK CFI 1c8e8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c8f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1c994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c99c .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1ca14 d8 .cfa: sp 0 + .ra: x30
STACK CFI 1ca1c .cfa: sp 336 +
STACK CFI 1ca2c .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 1cae0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1cae8 .cfa: sp 336 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI INIT 1caf0 400 .cfa: sp 0 + .ra: x30
STACK CFI 1caf8 .cfa: sp 160 +
STACK CFI 1cb04 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1cb0c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1cb18 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1cb24 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1cb30 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1cd00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1cd08 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1cef0 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 1cef8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1cf14 .cfa: sp 2304 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1cfd8 .cfa: sp 96 +
STACK CFI 1cff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1cffc .cfa: sp 2304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1d3b4 128 .cfa: sp 0 + .ra: x30
STACK CFI 1d3bc .cfa: sp 112 +
STACK CFI 1d3c4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1d3cc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1d3dc x27: .cfa -16 + ^
STACK CFI 1d3ec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1d3f4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1d400 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1d4b8 x19: x19 x20: x20
STACK CFI 1d4bc x21: x21 x22: x22
STACK CFI 1d4c0 x25: x25 x26: x26
STACK CFI 1d4d4 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI INIT 1d4e0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 1d4e8 .cfa: sp 336 +
STACK CFI 1d4f8 .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 1d5ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1d5b4 .cfa: sp 336 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI INIT 1d5c0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 1d5c8 .cfa: sp 336 +
STACK CFI 1d5d8 .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 1d68c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1d694 .cfa: sp 336 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI INIT 1d6a0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 1d6a8 .cfa: sp 336 +
STACK CFI 1d6b8 .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 1d76c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1d774 .cfa: sp 336 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI INIT 1d780 1bc .cfa: sp 0 + .ra: x30
STACK CFI 1d788 .cfa: sp 64 +
STACK CFI 1d78c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1d794 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d7a8 x21: .cfa -16 + ^
STACK CFI 1d844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1d84c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1d940 dc .cfa: sp 0 + .ra: x30
STACK CFI 1d948 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1d950 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d95c x21: .cfa -16 + ^
STACK CFI 1da14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1da20 164 .cfa: sp 0 + .ra: x30
STACK CFI 1da28 .cfa: sp 64 +
STACK CFI 1da2c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1da34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1da3c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1db1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1db24 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
