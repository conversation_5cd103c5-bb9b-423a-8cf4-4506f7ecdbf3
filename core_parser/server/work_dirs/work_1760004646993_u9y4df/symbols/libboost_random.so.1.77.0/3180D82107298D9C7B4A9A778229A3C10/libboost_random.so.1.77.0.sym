MODULE Linux arm64 3180D82107298D9C7B4A9A778229A3C10 libboost_random.so.1.77.0
INFO CODE_ID 21D8803129079C8D7B4A9A778229A3C1
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 3820 24 0 init_have_lse_atomics
3820 4 45 0
3824 4 46 0
3828 4 45 0
382c 4 46 0
3830 4 47 0
3834 4 47 0
3838 4 48 0
383c 4 47 0
3840 4 48 0
PUBLIC 3368 0 _init
PUBLIC 3650 0 void boost::throw_exception<boost::system::system_error>(boost::system::system_error const&)
PUBLIC 37c4 0 boost::wrapexcept<boost::system::system_error>::rethrow() const
PUBLIC 3844 0 call_weak_fn
PUBLIC 3860 0 deregister_tm_clones
PUBLIC 3890 0 register_tm_clones
PUBLIC 38d0 0 __do_global_dtors_aux
PUBLIC 3920 0 frame_dummy
PUBLIC 3930 0 boost::random::random_device::entropy() const
PUBLIC 3940 0 boost::random::random_device::random_device()
PUBLIC 3a80 0 boost::random::random_device::~random_device()
PUBLIC 3af0 0 boost::random::random_device::operator()()
PUBLIC 3bf0 0 boost::random::random_device::random_device(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 3d60 0 boost::system::error_category::failed(int) const
PUBLIC 3d70 0 boost::system::detail::generic_error_category::name() const
PUBLIC 3d80 0 boost::system::detail::system_error_category::name() const
PUBLIC 3d90 0 boost::system::detail::system_error_category::default_error_condition(int) const
PUBLIC 3db0 0 boost::system::detail::interop_error_category::name() const
PUBLIC 3dc0 0 boost::system::error_category::equivalent(boost::system::error_code const&, int) const
PUBLIC 3ea0 0 boost::system::detail::std_category::name() const
PUBLIC 3ec0 0 boost::system::detail::std_category::message[abi:cxx11](int) const
PUBLIC 3f30 0 boost::system::detail::system_error_category::message(int, char*, unsigned long) const
PUBLIC 3f40 0 boost::system::detail::generic_error_category::message(int, char*, unsigned long) const
PUBLIC 3f50 0 boost::system::detail::std_category::~std_category()
PUBLIC 3f70 0 boost::system::detail::std_category::~std_category()
PUBLIC 3fb0 0 boost::system::error_category::equivalent(int, boost::system::error_condition const&) const
PUBLIC 4040 0 boost::system::error_category::default_error_condition(int) const
PUBLIC 40e0 0 boost::system::detail::system_error_category::message[abi:cxx11](int) const
PUBLIC 4200 0 boost::system::detail::generic_error_category::message[abi:cxx11](int) const
PUBLIC 4320 0 boost::wrapexcept<boost::system::system_error>::clone() const
PUBLIC 4640 0 boost::system::system_error::~system_error()
PUBLIC 4690 0 boost::system::system_error::~system_error()
PUBLIC 46f0 0 non-virtual thunk to boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 4780 0 non-virtual thunk to boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 4810 0 boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 48a0 0 boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 4930 0 non-virtual thunk to boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 49d0 0 non-virtual thunk to boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 4a70 0 boost::system::detail::std_category::default_error_condition(int) const
PUBLIC 4c30 0 boost::system::detail::std_category::equivalent(std::error_code const&, int) const
PUBLIC 5160 0 boost::system::detail::std_category::equivalent(int, std::error_condition const&) const
PUBLIC 5740 0 boost::system::detail::snprintf(char*, unsigned long, char const*, ...)
PUBLIC 57f0 0 boost::system::detail::interop_error_category::message(int, char*, unsigned long) const
PUBLIC 5830 0 boost::system::error_category::message(int, char*, unsigned long) const
PUBLIC 5930 0 boost::system::detail::interop_error_category::message[abi:cxx11](int) const
PUBLIC 5a90 0 boost::random::random_device::impl::error(char const*)
PUBLIC 5ca0 0 boost::wrapexcept<boost::system::system_error>::wrapexcept(boost::wrapexcept<boost::system::system_error> const&)
PUBLIC 5e60 0 boost::system::system_error::what() const
PUBLIC 6090 0 __aarch64_cas8_acq_rel
PUBLIC 60c4 0 _fini
STACK CFI INIT 3860 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3890 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 38d0 48 .cfa: sp 0 + .ra: x30
STACK CFI 38d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 38dc x19: .cfa -16 + ^
STACK CFI 3914 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3920 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d60 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d70 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d80 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d90 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3db0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3dc0 d4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ea0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ec0 64 .cfa: sp 0 + .ra: x30
STACK CFI 3ec4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3ed0 x19: .cfa -32 + ^
STACK CFI 3f1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3f20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3f30 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f40 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f50 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f70 38 .cfa: sp 0 + .ra: x30
STACK CFI 3f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f84 x19: .cfa -16 + ^
STACK CFI 3fa4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3fb0 88 .cfa: sp 0 + .ra: x30
STACK CFI 3fb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3fbc x19: .cfa -16 + ^
STACK CFI 3fe4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3fe8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 401c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4020 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4034 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4040 94 .cfa: sp 0 + .ra: x30
STACK CFI 4044 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4058 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 40b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 40b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 40d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 40e0 114 .cfa: sp 0 + .ra: x30
STACK CFI 40e4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 40f8 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4100 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4180 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4184 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 4200 114 .cfa: sp 0 + .ra: x30
STACK CFI 4204 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 4218 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4220 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 42a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 42a4 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 4320 318 .cfa: sp 0 + .ra: x30
STACK CFI 4324 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4334 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4348 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 44e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 44ec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4640 50 .cfa: sp 0 + .ra: x30
STACK CFI 4644 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 465c x19: .cfa -16 + ^
STACK CFI 468c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4690 5c .cfa: sp 0 + .ra: x30
STACK CFI 4694 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 46ac x19: .cfa -16 + ^
STACK CFI 46e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 46f0 88 .cfa: sp 0 + .ra: x30
STACK CFI 46f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4704 x19: .cfa -16 + ^
STACK CFI 4774 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4780 84 .cfa: sp 0 + .ra: x30
STACK CFI 4784 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4794 x19: .cfa -16 + ^
STACK CFI 4800 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4810 84 .cfa: sp 0 + .ra: x30
STACK CFI 4814 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4824 x19: .cfa -16 + ^
STACK CFI 4890 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 48a0 90 .cfa: sp 0 + .ra: x30
STACK CFI 48a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 48b4 x19: .cfa -16 + ^
STACK CFI 492c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4930 98 .cfa: sp 0 + .ra: x30
STACK CFI 4934 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4944 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 49c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 49d0 9c .cfa: sp 0 + .ra: x30
STACK CFI 49d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 49e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4a68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4a70 1bc .cfa: sp 0 + .ra: x30
STACK CFI 4a74 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a8c x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4af4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4af8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4b44 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4b6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4b70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4c30 530 .cfa: sp 0 + .ra: x30
STACK CFI 4c34 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4c3c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4c54 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4c74 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4da0 x25: x25 x26: x26
STACK CFI 4da8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4dfc x25: x25 x26: x26
STACK CFI 4e04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4e08 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 4e80 x25: x25 x26: x26
STACK CFI 4eb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4ebc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 4ed0 x25: x25 x26: x26
STACK CFI 4ed4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4f60 x25: x25 x26: x26
STACK CFI 500c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5020 x25: x25 x26: x26
STACK CFI 5034 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 50e8 x25: x25 x26: x26
STACK CFI 50fc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5110 x25: x25 x26: x26
STACK CFI 512c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5140 x25: x25 x26: x26
STACK CFI 5144 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 5160 5d4 .cfa: sp 0 + .ra: x30
STACK CFI 5164 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 516c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5184 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5444 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5448 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 5508 x25: .cfa -48 + ^
STACK CFI 553c x25: x25
STACK CFI 56fc x25: .cfa -48 + ^
STACK CFI 5718 x25: x25
STACK CFI INIT 5740 a4 .cfa: sp 0 + .ra: x30
STACK CFI 5744 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 57dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 57e0 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI INIT 57f0 3c .cfa: sp 0 + .ra: x30
STACK CFI 57f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 580c x19: .cfa -16 + ^
STACK CFI 5828 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5830 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5834 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5844 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5874 x21: .cfa -64 + ^
STACK CFI 58b8 x21: x21
STACK CFI 58e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58e4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI 58e8 x21: x21
STACK CFI 58f8 x21: .cfa -64 + ^
STACK CFI 5920 x21: x21
STACK CFI INIT 5930 158 .cfa: sp 0 + .ra: x30
STACK CFI 5934 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5948 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5950 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 59ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 59f0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3930 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3650 174 .cfa: sp 0 + .ra: x30
STACK CFI 3654 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3664 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3674 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 5a90 210 .cfa: sp 0 + .ra: x30
STACK CFI 5a94 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 5aa4 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 5ab0 x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 5b4c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 5b8c x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 5bec x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 5bf0 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 5c18 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 5c28 x25: x25 x26: x26
STACK CFI 5c2c x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 5c44 x23: x23 x24: x24
STACK CFI 5c50 x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: x25 x26: x26
STACK CFI 5c58 x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 5c64 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 5c6c x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 5c78 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI INIT 3940 138 .cfa: sp 0 + .ra: x30
STACK CFI 3944 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3954 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 395c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3a10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3a14 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3a80 6c .cfa: sp 0 + .ra: x30
STACK CFI 3a84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a8c x19: .cfa -16 + ^
STACK CFI 3acc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3ad0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3ad8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3adc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3af0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 3af4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3b04 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3b0c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3b7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3b80 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3bf0 168 .cfa: sp 0 + .ra: x30
STACK CFI 3bf4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3c08 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3c14 x23: .cfa -32 + ^
STACK CFI 3ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3cac .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5ca0 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 5ca4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5cb4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5cc0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5cd4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5cdc x25: .cfa -32 + ^
STACK CFI 5dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5dcc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 37c4 54 .cfa: sp 0 + .ra: x30
STACK CFI 37c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37d0 x19: .cfa -16 + ^
STACK CFI INIT 5e60 224 .cfa: sp 0 + .ra: x30
STACK CFI 5e64 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5e6c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5e7c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5ec0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5ec4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6090 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3820 24 .cfa: sp 0 + .ra: x30
STACK CFI 3824 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 383c .cfa: sp 0 + .ra: .ra x29: x29
