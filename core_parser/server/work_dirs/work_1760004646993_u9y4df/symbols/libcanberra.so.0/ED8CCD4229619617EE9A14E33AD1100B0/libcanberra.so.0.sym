MODULE Linux arm64 ED8CCD4229619617EE9A14E33AD1100B0 libcanberra.so.0
INFO CODE_ID 42CD8CED61291796EE9A14E33AD1100B7C8CC3BC
PUBLIC 2f20 0 ca_mutex_new
PUBLIC 2fa0 0 ca_mutex_free
PUBLIC 3054 0 ca_mutex_lock
PUBLIC 3100 0 ca_mutex_try_lock
PUBLIC 31c0 0 ca_mutex_unlock
PUBLIC 3264 0 ca_sound_file_get_nchannels
PUBLIC 32c0 0 ca_sound_file_get_rate
PUBLIC 3320 0 ca_sound_file_get_sample_type
PUBLIC 3380 0 ca_sound_file_frame_size
PUBLIC 3404 0 ca_vorbis_close
PUBLIC 3474 0 ca_vorbis_get_nchannels
PUBLIC 3520 0 ca_vorbis_get_rate
PUBLIC 35d0 0 ca_vorbis_get_channel_map
PUBLIC 3724 0 ca_wav_close
PUBLIC 37a0 0 ca_sound_file_close
PUBLIC 3830 0 ca_wav_get_nchannels
PUBLIC 3890 0 ca_wav_get_rate
PUBLIC 38f0 0 ca_wav_get_channel_map
PUBLIC 3a10 0 ca_sound_file_get_channel_map
PUBLIC 3a80 0 ca_wav_get_sample_type
PUBLIC 3af0 0 ca_get_data_dirs
PUBLIC 3b40 0 ca_theme_data_free
PUBLIC 3c60 0 ca_debug
PUBLIC 3ca0 0 ca_strerror
PUBLIC 3df0 0 ca_parse_cache_control
PUBLIC 3f30 0 ca_proplist_create
PUBLIC 41b4 0 ca_proplist_setf
PUBLIC 4520 0 ca_proplist_set
PUBLIC 4790 0 ca_proplist_sets
PUBLIC 4900 0 ca_proplist_get_unlocked
PUBLIC 4a44 0 ca_proplist_gets_unlocked
PUBLIC 4b30 0 ca_proplist_destroy
PUBLIC 4d24 0 ca_proplist_merge
PUBLIC 4f00 0 ca_proplist_contains
PUBLIC 5000 0 ca_proplist_merge_ap
PUBLIC 5140 0 ca_proplist_from_ap
PUBLIC 5294 0 ca_vorbis_open
PUBLIC 5490 0 ca_vorbis_read_s16ne
PUBLIC 57d0 0 ca_vorbis_get_size
PUBLIC 5a24 0 ca_wav_open
PUBLIC 5d80 0 ca_sound_file_open
PUBLIC 5f80 0 ca_wav_read_s16le
PUBLIC 6220 0 ca_sound_file_read_int16
PUBLIC 6460 0 ca_wav_read_u8
PUBLIC 66d4 0 ca_sound_file_read_uint8
PUBLIC 6900 0 ca_sound_file_read_arbitrary
PUBLIC 6b60 0 ca_wav_get_size
PUBLIC 6bd4 0 ca_sound_file_get_size
PUBLIC 6c60 0 ca_get_data_home
PUBLIC 7ac4 0 ca_memdup
PUBLIC 7b60 0 ca_sprintf_malloc
PUBLIC 93c0 0 ca_detect_fork
PUBLIC 9410 0 ca_context_destroy
PUBLIC 9580 0 ca_context_set_driver
PUBLIC 9700 0 ca_context_change_device
PUBLIC 9900 0 ca_context_create
PUBLIC 9f24 0 ca_context_open
PUBLIC a080 0 ca_context_change_props_full
PUBLIC a390 0 ca_context_change_props
PUBLIC a574 0 ca_context_play_full
PUBLIC aa10 0 ca_context_play
PUBLIC ac10 0 ca_context_cancel
PUBLIC ae20 0 ca_context_cache_full
PUBLIC b100 0 ca_context_cache
PUBLIC b2e4 0 ca_cache_lookup_sound
PUBLIC ba34 0 ca_cache_store_sound
PUBLIC bdb4 0 ca_lookup_sound_with_callback
PUBLIC c364 0 ca_lookup_sound
PUBLIC c3a0 0 driver_playing
PUBLIC c514 0 ca_context_playing
STACK CFI INIT 2c50 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c80 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cc0 48 .cfa: sp 0 + .ra: x30
STACK CFI 2cc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ccc x19: .cfa -16 + ^
STACK CFI 2d04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2d10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d20 124 .cfa: sp 0 + .ra: x30
STACK CFI 2d28 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2d34 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2d88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2d90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2e44 d8 .cfa: sp 0 + .ra: x30
STACK CFI 2e4c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2e58 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2e60 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2e6c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2f14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 2f20 54 .cfa: sp 0 + .ra: x30
STACK CFI 2f28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2f5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2f64 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2f74 24 .cfa: sp 0 + .ra: x30
STACK CFI 2f7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2fa0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2fa8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2fb0 x19: .cfa -16 + ^
STACK CFI 2fcc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2fd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3054 a4 .cfa: sp 0 + .ra: x30
STACK CFI 305c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3070 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3078 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3100 b8 .cfa: sp 0 + .ra: x30
STACK CFI 3108 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3124 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 312c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 31c0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 31c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 31dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 31e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3264 5c .cfa: sp 0 + .ra: x30
STACK CFI 3284 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 32c0 5c .cfa: sp 0 + .ra: x30
STACK CFI 32e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3320 5c .cfa: sp 0 + .ra: x30
STACK CFI 3340 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3380 84 .cfa: sp 0 + .ra: x30
STACK CFI 3388 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3390 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 33bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 33c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3404 70 .cfa: sp 0 + .ra: x30
STACK CFI 340c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3414 x19: .cfa -16 + ^
STACK CFI 342c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3434 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3474 ac .cfa: sp 0 + .ra: x30
STACK CFI 347c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3494 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 34a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3520 ac .cfa: sp 0 + .ra: x30
STACK CFI 3528 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3540 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 354c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 35d0 154 .cfa: sp 0 + .ra: x30
STACK CFI 35d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35e0 x19: .cfa -16 + ^
STACK CFI 3630 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3638 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3688 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3690 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 36b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 36c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 36d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 36d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3704 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 370c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3724 74 .cfa: sp 0 + .ra: x30
STACK CFI 372c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3734 x19: .cfa -16 + ^
STACK CFI 3750 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3758 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 37a0 8c .cfa: sp 0 + .ra: x30
STACK CFI 37a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37b0 x19: .cfa -16 + ^
STACK CFI 37e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 37ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3830 5c .cfa: sp 0 + .ra: x30
STACK CFI 3850 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3890 5c .cfa: sp 0 + .ra: x30
STACK CFI 38b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 38f0 118 .cfa: sp 0 + .ra: x30
STACK CFI 38f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3970 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3978 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 397c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3988 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3a10 6c .cfa: sp 0 + .ra: x30
STACK CFI 3a40 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3a80 68 .cfa: sp 0 + .ra: x30
STACK CFI 3aac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3af0 50 .cfa: sp 0 + .ra: x30
STACK CFI 3af8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3b2c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3b40 120 .cfa: sp 0 + .ra: x30
STACK CFI 3b48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b50 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3bd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3be0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3c60 3c .cfa: sp 0 + .ra: x30
STACK CFI 3c68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3ca0 148 .cfa: sp 0 + .ra: x30
STACK CFI 3ca8 .cfa: sp 176 +
STACK CFI 3cb8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3d44 .cfa: sp 176 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3df0 13c .cfa: sp 0 + .ra: x30
STACK CFI 3df8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e04 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3e44 x19: x19 x20: x20
STACK CFI 3e4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3e54 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3e7c x19: x19 x20: x20
STACK CFI 3e8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3e98 x19: x19 x20: x20
STACK CFI 3ee4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3f24 x19: x19 x20: x20
STACK CFI INIT 3f30 b8 .cfa: sp 0 + .ra: x30
STACK CFI 3f38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f48 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3f74 x19: x19 x20: x20
STACK CFI 3f78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3f80 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3fd4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3fe0 x19: x19 x20: x20
STACK CFI INIT 3ff0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 3ff8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4004 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4010 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4074 x23: .cfa -16 + ^
STACK CFI 40d8 x23: x23
STACK CFI 40e0 x19: x19 x20: x20
STACK CFI 40e4 x21: x21 x22: x22
STACK CFI 40e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 40f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4104 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 4114 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4120 x19: x19 x20: x20
STACK CFI 416c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 41ac x19: x19 x20: x20
STACK CFI INIT 41b4 364 .cfa: sp 0 + .ra: x30
STACK CFI 41bc .cfa: sp 400 +
STACK CFI 41cc .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 4210 x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 4218 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 4224 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 424c x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 4258 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 4380 x21: x21 x22: x22
STACK CFI 4384 x23: x23 x24: x24
STACK CFI 4388 x25: x25 x26: x26
STACK CFI 438c x27: x27 x28: x28
STACK CFI 43b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 43c0 .cfa: sp 400 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^ x29: .cfa -272 + ^
STACK CFI 43d4 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 43e4 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 43f0 x25: x25 x26: x26
STACK CFI 43f8 x21: .cfa -240 + ^ x22: .cfa -232 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 4404 x21: x21 x22: x22
STACK CFI 4408 x25: x25 x26: x26
STACK CFI 4450 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 4494 x25: x25 x26: x26
STACK CFI 4498 x21: .cfa -240 + ^ x22: .cfa -232 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 44dc x21: x21 x22: x22
STACK CFI 44e0 x25: x25 x26: x26
STACK CFI 44e4 x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 44f0 x23: x23 x24: x24
STACK CFI 44f4 x27: x27 x28: x28
STACK CFI 44fc x21: x21 x22: x22
STACK CFI 4500 x25: x25 x26: x26
STACK CFI 4508 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 450c x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 4510 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 4514 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI INIT 4520 268 .cfa: sp 0 + .ra: x30
STACK CFI 4528 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4530 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4538 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4544 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4630 x19: x19 x20: x20
STACK CFI 4634 x23: x23 x24: x24
STACK CFI 4640 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4648 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 465c x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 466c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4678 x19: x19 x20: x20
STACK CFI 4680 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 468c x19: x19 x20: x20
STACK CFI 4694 x23: x23 x24: x24
STACK CFI 46dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4720 x19: x19 x20: x20
STACK CFI 4724 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4768 x19: x19 x20: x20
STACK CFI 476c x23: x23 x24: x24
STACK CFI 4770 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 477c x19: x19 x20: x20
STACK CFI 4784 x23: x23 x24: x24
STACK CFI INIT 4790 170 .cfa: sp 0 + .ra: x30
STACK CFI 4798 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 47a4 x21: .cfa -16 + ^
STACK CFI 47b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 47d4 x21: x21
STACK CFI 47dc x19: x19 x20: x20
STACK CFI 47e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 47ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 47f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4804 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4810 x21: x21
STACK CFI 4818 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4820 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 482c x19: x19 x20: x20
STACK CFI 4830 x21: x21
STACK CFI 4834 x21: .cfa -16 + ^
STACK CFI 4874 x21: x21
STACK CFI 48b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 48f8 x19: x19 x20: x20
STACK CFI 48fc x21: x21
STACK CFI INIT 4900 144 .cfa: sp 0 + .ra: x30
STACK CFI 4908 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4910 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 499c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 49a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4a44 ec .cfa: sp 0 + .ra: x30
STACK CFI 4a4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a54 x19: .cfa -16 + ^
STACK CFI 4a88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4a90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4b30 c0 .cfa: sp 0 + .ra: x30
STACK CFI 4b38 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4b44 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4b48 x21: .cfa -16 + ^
STACK CFI 4b88 x19: x19 x20: x20
STACK CFI 4b90 x21: x21
STACK CFI 4b94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4b9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4bf0 134 .cfa: sp 0 + .ra: x30
STACK CFI 4bf8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4c08 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4c58 x21: x21 x22: x22
STACK CFI 4c64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4c6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4c7c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4c88 x21: x21 x22: x22
STACK CFI 4cd4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4d20 x21: x21 x22: x22
STACK CFI INIT 4d24 1dc .cfa: sp 0 + .ra: x30
STACK CFI 4d2c .cfa: sp 64 +
STACK CFI 4d38 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4d4c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4d5c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4db0 x21: x21 x22: x22
STACK CFI 4ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4de4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4de8 x21: x21 x22: x22
STACK CFI 4dec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4df8 x21: x21 x22: x22
STACK CFI 4e58 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4e64 x21: x21 x22: x22
STACK CFI 4eb0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4ef4 x21: x21 x22: x22
STACK CFI 4efc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 4f00 f8 .cfa: sp 0 + .ra: x30
STACK CFI 4f08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5000 138 .cfa: sp 0 + .ra: x30
STACK CFI 5008 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5010 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5018 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5064 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 506c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 509c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 50a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5140 154 .cfa: sp 0 + .ra: x30
STACK CFI 5148 .cfa: sp 96 +
STACK CFI 5154 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 515c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5178 x21: .cfa -16 + ^
STACK CFI 51b4 x21: x21
STACK CFI 51e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 51e8 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 51f4 x21: x21
STACK CFI 524c x21: .cfa -16 + ^
STACK CFI 528c x21: x21
STACK CFI 5290 x21: .cfa -16 + ^
STACK CFI INIT 5294 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 529c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 52a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 52ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5328 x19: x19 x20: x20
STACK CFI 5334 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 533c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5374 x19: x19 x20: x20
STACK CFI 5380 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5388 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 53b8 x19: x19 x20: x20
STACK CFI 53cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 53d8 x19: x19 x20: x20
STACK CFI 53e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 53f0 x19: x19 x20: x20
STACK CFI 5434 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5478 x19: x19 x20: x20
STACK CFI 547c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5480 x19: x19 x20: x20
STACK CFI INIT 5490 33c .cfa: sp 0 + .ra: x30
STACK CFI 5498 .cfa: sp 80 +
STACK CFI 54a4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 54bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 54c8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 54d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5564 x19: x19 x20: x20
STACK CFI 556c x21: x21 x22: x22
STACK CFI 5570 x23: x23 x24: x24
STACK CFI 5574 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5598 x19: x19 x20: x20
STACK CFI 559c x21: x21 x22: x22
STACK CFI 55a0 x23: x23 x24: x24
STACK CFI 55c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 55d0 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 55dc x19: x19 x20: x20
STACK CFI 55e0 x21: x21 x22: x22
STACK CFI 55e4 x23: x23 x24: x24
STACK CFI 55f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5604 x19: x19 x20: x20
STACK CFI 560c x21: x21 x22: x22
STACK CFI 5610 x23: x23 x24: x24
STACK CFI 5614 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5620 x19: x19 x20: x20
STACK CFI 5628 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5634 x19: x19 x20: x20
STACK CFI 563c x23: x23 x24: x24
STACK CFI 5640 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 564c x19: x19 x20: x20
STACK CFI 5654 x21: x21 x22: x22
STACK CFI 565c x23: x23 x24: x24
STACK CFI 56a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 56e4 x19: x19 x20: x20
STACK CFI 56ec x21: x21 x22: x22
STACK CFI 56f0 x23: x23 x24: x24
STACK CFI 56f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5734 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5774 x19: x19 x20: x20
STACK CFI 577c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 57bc x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 57c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 57c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 57c8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 57d0 74 .cfa: sp 0 + .ra: x30
STACK CFI 57e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 57f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5804 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5844 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 584c .cfa: sp 80 +
STACK CFI 5858 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5870 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 587c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5888 x23: .cfa -16 + ^
STACK CFI 5900 x19: x19 x20: x20
STACK CFI 5904 x21: x21 x22: x22
STACK CFI 5908 x23: x23
STACK CFI 592c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5934 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5938 x19: x19 x20: x20
STACK CFI 5940 x21: x21 x22: x22
STACK CFI 5944 x23: x23
STACK CFI 5948 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 5954 x19: x19 x20: x20
STACK CFI 5958 x21: x21 x22: x22
STACK CFI 595c x23: x23
STACK CFI 5960 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 5964 x19: x19 x20: x20
STACK CFI 596c x21: x21 x22: x22
STACK CFI 5970 x23: x23
STACK CFI 59c8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 59cc x19: x19 x20: x20
STACK CFI 59d4 x21: x21 x22: x22
STACK CFI 59d8 x23: x23
STACK CFI 59e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 59e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 59e8 x23: .cfa -16 + ^
STACK CFI INIT 5a24 35c .cfa: sp 0 + .ra: x30
STACK CFI 5a2c .cfa: sp 128 +
STACK CFI 5a38 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5a40 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5a58 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ac8 x19: x19 x20: x20
STACK CFI 5af4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5afc .cfa: sp 128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5bb0 x19: x19 x20: x20
STACK CFI 5bc0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5bcc x19: x19 x20: x20
STACK CFI 5c18 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5cbc x19: x19 x20: x20
STACK CFI 5cc0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5d04 x19: x19 x20: x20
STACK CFI 5d08 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5d6c x19: x19 x20: x20
STACK CFI 5d70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5d78 x19: x19 x20: x20
STACK CFI INIT 5d80 200 .cfa: sp 0 + .ra: x30
STACK CFI 5d88 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5d90 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5da4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5e10 x21: x21 x22: x22
STACK CFI 5e1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5e24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5e58 x21: x21 x22: x22
STACK CFI 5e60 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5eac x21: x21 x22: x22
STACK CFI 5f08 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5f10 x21: x21 x22: x22
STACK CFI 5f54 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5f58 x21: x21 x22: x22
STACK CFI 5f60 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 5f80 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 5f88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5f94 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5fdc x19: x19 x20: x20
STACK CFI 5fe4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5fec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6034 x19: x19 x20: x20
STACK CFI 6044 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6050 x19: x19 x20: x20
STACK CFI 6058 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 60a0 x19: x19 x20: x20
STACK CFI 60a8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 60f0 x19: x19 x20: x20
STACK CFI 60f8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6140 x19: x19 x20: x20
STACK CFI 618c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 61cc x19: x19 x20: x20
STACK CFI 61d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6218 x19: x19 x20: x20
STACK CFI INIT 6220 238 .cfa: sp 0 + .ra: x30
STACK CFI 6228 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 625c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6264 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6278 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6280 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6370 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6378 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6460 274 .cfa: sp 0 + .ra: x30
STACK CFI 6468 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6470 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 64c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 64c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 66d4 224 .cfa: sp 0 + .ra: x30
STACK CFI 66dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6718 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6720 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6730 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6738 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6900 258 .cfa: sp 0 + .ra: x30
STACK CFI 6908 .cfa: sp 48 +
STACK CFI 6914 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6930 x19: .cfa -16 + ^
STACK CFI 6968 x19: x19
STACK CFI 698c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6994 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 69b8 x19: x19
STACK CFI 69bc x19: .cfa -16 + ^
STACK CFI 69c8 x19: x19
STACK CFI 6a28 x19: .cfa -16 + ^
STACK CFI 6a34 x19: x19
STACK CFI 6a3c x19: .cfa -16 + ^
STACK CFI 6a84 x19: x19
STACK CFI 6ad0 x19: .cfa -16 + ^
STACK CFI 6b10 x19: x19
STACK CFI 6b1c x19: .cfa -16 + ^
STACK CFI INIT 6b60 74 .cfa: sp 0 + .ra: x30
STACK CFI 6b78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6b88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6b94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6bd4 84 .cfa: sp 0 + .ra: x30
STACK CFI 6bfc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6c0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6c18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6c60 138 .cfa: sp 0 + .ra: x30
STACK CFI 6c68 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6c74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6c84 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6d10 x19: x19 x20: x20
STACK CFI 6d18 x21: x21 x22: x22
STACK CFI 6d1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6d24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6d34 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 6d88 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6d8c x19: x19 x20: x20
STACK CFI 6d94 x21: x21 x22: x22
STACK CFI INIT 6da0 27c .cfa: sp 0 + .ra: x30
STACK CFI 6da8 .cfa: sp 96 +
STACK CFI 6db4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6dbc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6dd0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6e40 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6e44 x25: .cfa -16 + ^
STACK CFI 6ebc x19: x19 x20: x20
STACK CFI 6ec0 x25: x25
STACK CFI 6ef0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6ef8 .cfa: sp 96 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 6f04 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^
STACK CFI 6f08 x19: x19 x20: x20
STACK CFI 6f10 x25: x25
STACK CFI 700c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7010 x25: .cfa -16 + ^
STACK CFI INIT 7020 6e4 .cfa: sp 0 + .ra: x30
STACK CFI 7028 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7034 .cfa: sp 1184 + x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7054 x19: .cfa -80 + ^
STACK CFI 705c x20: .cfa -72 + ^
STACK CFI 7064 x23: .cfa -48 + ^
STACK CFI 7068 x24: .cfa -40 + ^
STACK CFI 7074 x25: .cfa -32 + ^
STACK CFI 7078 x26: .cfa -24 + ^
STACK CFI 7114 x27: .cfa -16 + ^
STACK CFI 711c x28: .cfa -8 + ^
STACK CFI 7204 x19: x19
STACK CFI 7208 x20: x20
STACK CFI 720c x23: x23
STACK CFI 7210 x24: x24
STACK CFI 7214 x25: x25
STACK CFI 7218 x26: x26
STACK CFI 721c x27: x27
STACK CFI 7220 x28: x28
STACK CFI 7240 .cfa: sp 96 +
STACK CFI 724c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7254 .cfa: sp 1184 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 7444 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7454 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7460 x19: x19
STACK CFI 7468 x20: x20
STACK CFI 746c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7478 x19: x19
STACK CFI 7480 x20: x20
STACK CFI 7484 x23: x23
STACK CFI 7488 x24: x24
STACK CFI 748c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7494 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 74d8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 75b4 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 75f8 x19: x19
STACK CFI 75fc x20: x20
STACK CFI 7600 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7644 x19: x19
STACK CFI 7648 x20: x20
STACK CFI 764c x23: x23
STACK CFI 7650 x24: x24
STACK CFI 7654 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 769c x27: x27 x28: x28
STACK CFI 76b8 x19: x19
STACK CFI 76bc x20: x20
STACK CFI 76c0 x23: x23
STACK CFI 76c4 x24: x24
STACK CFI 76c8 x25: x25
STACK CFI 76cc x26: x26
STACK CFI 76d0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 76d8 x27: x27 x28: x28
STACK CFI 76e0 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 76e4 x19: .cfa -80 + ^
STACK CFI 76e8 x20: .cfa -72 + ^
STACK CFI 76ec x23: .cfa -48 + ^
STACK CFI 76f0 x24: .cfa -40 + ^
STACK CFI 76f4 x25: .cfa -32 + ^
STACK CFI 76f8 x26: .cfa -24 + ^
STACK CFI 76fc x27: .cfa -16 + ^
STACK CFI 7700 x28: .cfa -8 + ^
STACK CFI INIT 7704 138 .cfa: sp 0 + .ra: x30
STACK CFI 770c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7714 x21: .cfa -16 + ^
STACK CFI 771c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 778c x19: x19 x20: x20
STACK CFI 779c .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 77a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 77b4 x19: x19 x20: x20
STACK CFI 77c0 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 77c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 77dc x19: x19 x20: x20
STACK CFI 7830 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7834 x19: x19 x20: x20
STACK CFI INIT 7840 164 .cfa: sp 0 + .ra: x30
STACK CFI 7848 .cfa: sp 320 +
STACK CFI 7854 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 786c x21: .cfa -16 + ^
STACK CFI 788c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 78fc x19: x19 x20: x20
STACK CFI 7900 x21: x21
STACK CFI 7924 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 792c .cfa: sp 320 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 793c x19: x19 x20: x20
STACK CFI 7940 x21: x21
STACK CFI 799c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 79a0 x21: .cfa -16 + ^
STACK CFI INIT 79a4 120 .cfa: sp 0 + .ra: x30
STACK CFI 79ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 79b4 x21: .cfa -16 + ^
STACK CFI 79bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7a00 x19: x19 x20: x20
STACK CFI 7a0c .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 7a14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7a1c x19: x19 x20: x20
STACK CFI 7a2c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7a38 x19: x19 x20: x20
STACK CFI 7a84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 7ac4 94 .cfa: sp 0 + .ra: x30
STACK CFI 7acc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7ad8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 7b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7b18 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7b60 194 .cfa: sp 0 + .ra: x30
STACK CFI 7b68 .cfa: sp 384 +
STACK CFI 7b74 .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 7b80 x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 7b8c x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 7ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7ca8 .cfa: sp 384 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x29: .cfa -272 + ^
STACK CFI INIT 7cf4 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 7cfc .cfa: sp 80 +
STACK CFI 7d00 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7d0c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7d3c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7d48 x23: .cfa -16 + ^
STACK CFI 7db8 x21: x21 x22: x22
STACK CFI 7dbc x23: x23
STACK CFI 7dcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7dd4 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7e48 x21: x21 x22: x22
STACK CFI 7e4c x23: x23
STACK CFI 7e50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e58 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 7fdc x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 7fe0 x21: x21 x22: x22
STACK CFI 7fe8 x23: x23
STACK CFI INIT 7ff0 354 .cfa: sp 0 + .ra: x30
STACK CFI 7ff8 .cfa: sp 112 +
STACK CFI 7ffc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8004 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 800c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8044 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8050 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 805c x27: .cfa -16 + ^
STACK CFI 80cc x21: x21 x22: x22
STACK CFI 80d0 x23: x23 x24: x24
STACK CFI 80d4 x25: x25 x26: x26
STACK CFI 80d8 x27: x27
STACK CFI 80e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 80f0 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 8194 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 81a0 x21: x21 x22: x22
STACK CFI 81a8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 81ec x21: x21 x22: x22
STACK CFI 823c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8288 x21: x21 x22: x22
STACK CFI 828c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 82d8 x21: x21 x22: x22
STACK CFI 82dc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8328 x21: x21 x22: x22
STACK CFI 832c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8330 x21: x21 x22: x22
STACK CFI 8338 x23: x23 x24: x24
STACK CFI 833c x25: x25 x26: x26
STACK CFI 8340 x27: x27
STACK CFI INIT 8344 340 .cfa: sp 0 + .ra: x30
STACK CFI 834c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8354 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 835c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8378 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 838c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8398 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 83b0 x21: x21 x22: x22
STACK CFI 83b4 x23: x23 x24: x24
STACK CFI 83b8 x25: x25 x26: x26
STACK CFI 83bc x27: x27 x28: x28
STACK CFI 83c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 83d0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 84a8 x23: x23 x24: x24
STACK CFI 84b8 x27: x27 x28: x28
STACK CFI 84c0 x25: x25 x26: x26
STACK CFI 84d0 x21: x21 x22: x22
STACK CFI 84d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 84dc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 84e8 x21: x21 x22: x22
STACK CFI 84f0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8530 x21: x21 x22: x22
STACK CFI 8580 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 85cc x21: x21 x22: x22
STACK CFI 85d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8608 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8614 x21: x21 x22: x22
STACK CFI 861c x23: x23 x24: x24
STACK CFI 8620 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8664 x21: x21 x22: x22
STACK CFI 8668 x23: x23 x24: x24
STACK CFI 866c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8670 x21: x21 x22: x22
STACK CFI 8678 x23: x23 x24: x24
STACK CFI 867c x25: x25 x26: x26
STACK CFI 8680 x27: x27 x28: x28
STACK CFI INIT 8684 24c .cfa: sp 0 + .ra: x30
STACK CFI 868c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8698 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 86a0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 86bc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 86c4 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 86d0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 877c x19: x19 x20: x20
STACK CFI 8780 x21: x21 x22: x22
STACK CFI 8784 x23: x23 x24: x24
STACK CFI 8788 x25: x25 x26: x26
STACK CFI 8794 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 879c .cfa: sp 112 + .ra: .cfa -104 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 87a8 x21: x21 x22: x22
STACK CFI 87b0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 87f4 x21: x21 x22: x22
STACK CFI 8844 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 8890 x21: x21 x22: x22
STACK CFI 8894 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 88a4 x19: x19 x20: x20
STACK CFI 88ac x21: x21 x22: x22
STACK CFI 88b0 x23: x23 x24: x24
STACK CFI 88b4 x25: x25 x26: x26
STACK CFI 88b8 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 88bc x19: x19 x20: x20
STACK CFI 88c4 x21: x21 x22: x22
STACK CFI 88c8 x23: x23 x24: x24
STACK CFI 88cc x25: x25 x26: x26
STACK CFI INIT 88d0 2dc .cfa: sp 0 + .ra: x30
STACK CFI 88d8 .cfa: sp 128 +
STACK CFI 88e4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8900 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8908 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8924 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8930 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8980 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8a18 x19: x19 x20: x20
STACK CFI 8a1c x21: x21 x22: x22
STACK CFI 8a20 x25: x25 x26: x26
STACK CFI 8a24 x27: x27 x28: x28
STACK CFI 8a28 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8a2c x21: x21 x22: x22
STACK CFI 8a30 x25: x25 x26: x26
STACK CFI 8a34 x27: x27 x28: x28
STACK CFI 8a60 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 8a68 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8a6c x19: x19 x20: x20
STACK CFI 8a74 x21: x21 x22: x22
STACK CFI 8a78 x25: x25 x26: x26
STACK CFI 8a7c x27: x27 x28: x28
STACK CFI 8a90 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8a9c x21: x21 x22: x22
STACK CFI 8aa4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8af0 x21: x21 x22: x22
STACK CFI 8b38 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8b7c x21: x21 x22: x22
STACK CFI 8b84 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8b88 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8b8c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8b90 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8b98 x19: x19 x20: x20
STACK CFI 8ba0 x21: x21 x22: x22
STACK CFI 8ba4 x25: x25 x26: x26
STACK CFI 8ba8 x27: x27 x28: x28
STACK CFI INIT 8bb0 220 .cfa: sp 0 + .ra: x30
STACK CFI 8bb8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8bc8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8bd4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8be0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8bec x25: .cfa -16 + ^
STACK CFI 8c44 x19: x19 x20: x20
STACK CFI 8c48 x21: x21 x22: x22
STACK CFI 8c4c x23: x23 x24: x24
STACK CFI 8c50 x25: x25
STACK CFI 8c54 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 8c58 x19: x19 x20: x20
STACK CFI 8c60 x21: x21 x22: x22
STACK CFI 8c64 x23: x23 x24: x24
STACK CFI 8c68 x25: x25
STACK CFI 8c6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8c74 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8cd0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8cdc x21: x21 x22: x22
STACK CFI 8ce4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8cf0 x21: x21 x22: x22
STACK CFI 8cf8 x23: x23 x24: x24
STACK CFI 8d40 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8d80 x21: x21 x22: x22
STACK CFI 8d84 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8dc4 x21: x21 x22: x22
STACK CFI 8dcc x23: x23 x24: x24
STACK CFI INIT 8dd0 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 8dd8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8de4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8df8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8e04 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8e14 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 8e28 x19: x19 x20: x20
STACK CFI 8e2c x21: x21 x22: x22
STACK CFI 8e30 x23: x23 x24: x24
STACK CFI 8e34 x25: x25 x26: x26
STACK CFI 8e38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8e40 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 8e5c x25: x25 x26: x26
STACK CFI 8e78 x21: x21 x22: x22
STACK CFI 8e80 x19: x19 x20: x20
STACK CFI 8e88 x23: x23 x24: x24
STACK CFI 8e8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8e94 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 8ec0 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 8ed0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8edc x21: x21 x22: x22
STACK CFI 8ee4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8f2c x21: x21 x22: x22
STACK CFI 8f78 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8fb8 x21: x21 x22: x22
STACK CFI INIT 8fc0 2fc .cfa: sp 0 + .ra: x30
STACK CFI 8fc8 .cfa: sp 112 +
STACK CFI 8fd8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8fe4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8fec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9058 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9060 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 90b4 x23: .cfa -16 + ^
STACK CFI 91d8 x23: x23
STACK CFI 91f0 x23: .cfa -16 + ^
STACK CFI 928c x23: x23
STACK CFI 92a4 x23: .cfa -16 + ^
STACK CFI 92b4 x23: x23
STACK CFI 92b8 x23: .cfa -16 + ^
STACK CFI INIT 92c0 fc .cfa: sp 0 + .ra: x30
STACK CFI 92c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 92d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 92ec x21: .cfa -16 + ^
STACK CFI 931c x21: x21
STACK CFI 9324 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 932c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 939c x21: x21
STACK CFI 93a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 93a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 93ac x21: x21
STACK CFI INIT 93c0 4c .cfa: sp 0 + .ra: x30
STACK CFI 93c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 93d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9404 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9410 168 .cfa: sp 0 + .ra: x30
STACK CFI 9418 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9420 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9480 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9580 180 .cfa: sp 0 + .ra: x30
STACK CFI 9588 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9590 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 959c x21: .cfa -16 + ^
STACK CFI 95f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 95f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9700 1fc .cfa: sp 0 + .ra: x30
STACK CFI 9708 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9710 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9718 x21: .cfa -16 + ^
STACK CFI 9794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 979c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9900 198 .cfa: sp 0 + .ra: x30
STACK CFI 9908 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9914 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 99b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 99bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 99d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 99e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9aa0 484 .cfa: sp 0 + .ra: x30
STACK CFI 9aa8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9ab0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9ab8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9ae8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 9b0c x23: .cfa -16 + ^
STACK CFI 9c70 x23: x23
STACK CFI 9c74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9c7c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 9cd8 x23: x23
STACK CFI 9d00 x23: .cfa -16 + ^
STACK CFI 9d60 x23: x23
STACK CFI 9e44 x23: .cfa -16 + ^
STACK CFI 9e64 x23: x23
STACK CFI 9e68 x23: .cfa -16 + ^
STACK CFI 9ec0 x23: x23
STACK CFI 9ec4 x23: .cfa -16 + ^
STACK CFI 9edc x23: x23
STACK CFI 9ee0 x23: .cfa -16 + ^
STACK CFI 9ef8 x23: x23
STACK CFI 9efc x23: .cfa -16 + ^
STACK CFI 9f0c x23: x23
STACK CFI 9f10 x23: .cfa -16 + ^
STACK CFI 9f20 x23: x23
STACK CFI INIT 9f24 15c .cfa: sp 0 + .ra: x30
STACK CFI 9f2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9f34 x19: .cfa -16 + ^
STACK CFI 9f78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9f80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT a080 30c .cfa: sp 0 + .ra: x30
STACK CFI a088 .cfa: sp 64 +
STACK CFI a094 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a09c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a0a4 x21: .cfa -16 + ^
STACK CFI a164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a16c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT a390 1e4 .cfa: sp 0 + .ra: x30
STACK CFI a398 .cfa: sp 304 +
STACK CFI a3a4 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI a3ac x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI a480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a488 .cfa: sp 304 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x29: .cfa -224 + ^
STACK CFI INIT a574 498 .cfa: sp 0 + .ra: x30
STACK CFI a57c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a584 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a590 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a598 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a5e0 x25: .cfa -16 + ^
STACK CFI a6b4 x25: x25
STACK CFI a6c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a6d0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI a700 x25: x25
STACK CFI a7b8 x25: .cfa -16 + ^
STACK CFI a7d0 x25: x25
STACK CFI a8ac x25: .cfa -16 + ^
STACK CFI a9cc x25: x25
STACK CFI INIT aa10 1f8 .cfa: sp 0 + .ra: x30
STACK CFI aa18 .cfa: sp 304 +
STACK CFI aa24 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI aa2c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI aa34 x21: .cfa -192 + ^
STACK CFI ab14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ab1c .cfa: sp 304 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x29: .cfa -224 + ^
STACK CFI INIT ac10 210 .cfa: sp 0 + .ra: x30
STACK CFI ac18 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ac20 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI ac78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ac80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT ae20 2e0 .cfa: sp 0 + .ra: x30
STACK CFI ae28 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ae30 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ae38 x21: .cfa -16 + ^
STACK CFI aebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI aec4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT b100 1e4 .cfa: sp 0 + .ra: x30
STACK CFI b108 .cfa: sp 304 +
STACK CFI b114 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI b11c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI b1f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b1f8 .cfa: sp 304 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x29: .cfa -224 + ^
STACK CFI INIT 2bd0 60 .cfa: sp 0 + .ra: x30
STACK CFI 2bd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bf0 x19: .cfa -16 + ^
STACK CFI 2c1c x19: x19
STACK CFI 2c20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c28 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2c2c x19: x19
STACK CFI INIT b2e4 750 .cfa: sp 0 + .ra: x30
STACK CFI b2ec .cfa: sp 320 +
STACK CFI b2f8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b300 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b314 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b344 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b36c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b374 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b4f4 x23: x23 x24: x24
STACK CFI b4f8 x25: x25 x26: x26
STACK CFI b4fc x27: x27 x28: x28
STACK CFI b52c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b534 .cfa: sp 320 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI b590 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b5a4 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI b6d4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b818 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI b85c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ba00 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI ba04 x23: x23 x24: x24
STACK CFI ba10 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ba14 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ba18 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT ba34 380 .cfa: sp 0 + .ra: x30
STACK CFI ba3c .cfa: sp 96 +
STACK CFI ba48 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ba5c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ba80 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI ba90 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI bb4c x21: x21 x22: x22
STACK CFI bb50 x23: x23 x24: x24
STACK CFI bb7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bb84 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI bcbc x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI bd90 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI bd94 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI bd98 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI bda8 x23: x23 x24: x24
STACK CFI bdb0 x21: x21 x22: x22
STACK CFI INIT bdb4 5b0 .cfa: sp 0 + .ra: x30
STACK CFI bdbc .cfa: sp 128 +
STACK CFI bdc8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI bddc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI bde4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI bdf0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI bdfc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI be10 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI bee8 x19: x19 x20: x20
STACK CFI beec x21: x21 x22: x22
STACK CFI bef0 x23: x23 x24: x24
STACK CFI bef4 x25: x25 x26: x26
STACK CFI bf20 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI bf28 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI bff8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI c008 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c014 x23: x23 x24: x24
STACK CFI c01c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c028 x21: x21 x22: x22
STACK CFI c02c x23: x23 x24: x24
STACK CFI c030 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c03c x19: x19 x20: x20
STACK CFI c040 x21: x21 x22: x22
STACK CFI c044 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c084 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c168 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI c1ac x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c1f0 x23: x23 x24: x24
STACK CFI c1f4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c238 x21: x21 x22: x22
STACK CFI c23c x23: x23 x24: x24
STACK CFI c240 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c284 x19: x19 x20: x20
STACK CFI c288 x21: x21 x22: x22
STACK CFI c28c x23: x23 x24: x24
STACK CFI c290 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c350 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI c354 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c358 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c35c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c360 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT c364 3c .cfa: sp 0 + .ra: x30
STACK CFI c36c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c378 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c3a0 174 .cfa: sp 0 + .ra: x30
STACK CFI c3a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c3c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c3d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c3e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c3ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT c514 1c4 .cfa: sp 0 + .ra: x30
STACK CFI c51c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c524 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c530 x21: .cfa -16 + ^
STACK CFI c584 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c58c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
