MODULE Linux arm64 50BF11A0696CE5656D27FE61C502DFBE0 libresolv.so.2
INFO CODE_ID A011BF506C6965E56D27FE61C502DFBECA3415D2
PUBLIC 3450 0 __b64_ntop
PUBLIC 35d0 0 __b64_pton
PUBLIC 37e0 0 _sethtent
PUBLIC 3840 0 _gethtent
PUBLIC 3a80 0 _gethtbyname2
PUBLIC 3b70 0 _gethtbyname
PUBLIC 3b84 0 _gethtbyaddr
PUBLIC 4914 0 res_gethostbyaddr
PUBLIC 4c80 0 res_gethostbyname2
PUBLIC 4cf0 0 res_gethostbyname
PUBLIC 4d60 0 res_send_setqhook
PUBLIC 4d84 0 res_send_setrhook
PUBLIC 4db0 0 inet_net_ntop
PUBLIC 4f90 0 inet_net_pton
PUBLIC 53c0 0 inet_neta
PUBLIC 54d0 0 ns_datetosecs
PUBLIC 58a0 0 ns_name_ntol
PUBLIC 59b0 0 ns_name_rollback
PUBLIC 5a00 0 ns_get16
PUBLIC 5a20 0 ns_get32
PUBLIC 5a40 0 ns_put16
PUBLIC 5a60 0 ns_put32
PUBLIC 5a80 0 ns_msg_getflag
PUBLIC 5ab4 0 ns_skiprr
PUBLIC 5b84 0 ns_initparse
PUBLIC 5cc0 0 ns_parserr
PUBLIC 6470 0 ns_sprintrrf
PUBLIC 7894 0 ns_sprintrr
PUBLIC 7910 0 ns_samedomain
PUBLIC 7a90 0 ns_subdomain
PUBLIC 7bb0 0 ns_format_ttl
PUBLIC 7d90 0 ns_parse_ttl
PUBLIC 7f00 0 __putlong
PUBLIC 7f14 0 __putshort
PUBLIC 7f30 0 _getlong
PUBLIC 7f44 0 _getshort
PUBLIC 7f60 0 __res_close
PUBLIC 8830 0 __fp_nquery
PUBLIC 8f00 0 __fp_query
PUBLIC 8f20 0 __p_query
PUBLIC 8f40 0 __p_cdnname
PUBLIC 9000 0 __p_cdname
PUBLIC 9020 0 __p_fqnname
PUBLIC 90c0 0 __p_fqname
PUBLIC 9150 0 __sym_ston
PUBLIC 91d0 0 __sym_ntos
PUBLIC 9260 0 __sym_ntop
PUBLIC 92f0 0 __p_type
PUBLIC 9360 0 __p_class
PUBLIC 93d4 0 __p_option
PUBLIC 95a4 0 __fp_resstat
PUBLIC 9650 0 __p_time
PUBLIC 96d0 0 __p_rcode
PUBLIC 9744 0 __loc_aton
PUBLIC 9b40 0 __loc_ntoa
PUBLIC 9fd0 0 __dn_count_labels
PUBLIC a060 0 __p_secstodate
PUBLIC a160 0 __res_hostalias
PUBLIC a1f4 0 __hostalias
PUBLIC a280 0 __res_isourserver
PUBLIC a3f0 0 ns_makecanon
PUBLIC a400 0 ns_samename
PUBLIC a410 0 __res_nameinquery
PUBLIC a420 0 __res_queriesmatch
STACK CFI INIT 3380 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 33b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 33f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 33f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33fc x19: .cfa -16 + ^
STACK CFI 3434 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3440 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3450 178 .cfa: sp 0 + .ra: x30
STACK CFI 3454 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3524 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3528 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 35ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 35d0 210 .cfa: sp 0 + .ra: x30
STACK CFI 35d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 35dc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 35e4 x25: .cfa -32 + ^
STACK CFI 3600 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3608 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 36e0 x19: x19 x20: x20
STACK CFI 36e8 x23: x23 x24: x24
STACK CFI 36f4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI 36f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI 3774 x19: x19 x20: x20
STACK CFI 377c x23: x23 x24: x24
STACK CFI 3780 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 37e0 60 .cfa: sp 0 + .ra: x30
STACK CFI 37e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3814 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 383c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3840 238 .cfa: sp 0 + .ra: x30
STACK CFI 3844 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 384c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3854 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3860 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3880 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 392c x23: x23 x24: x24
STACK CFI 3934 x25: x25 x26: x26
STACK CFI 3954 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3958 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3a2c x23: x23 x24: x24
STACK CFI 3a34 x25: x25 x26: x26
STACK CFI 3a3c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3a50 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI INIT 3a80 ec .cfa: sp 0 + .ra: x30
STACK CFI 3a84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3a8c x23: .cfa -16 + ^
STACK CFI 3a94 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3aa4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3b2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3b30 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3b4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3b50 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3b70 14 .cfa: sp 0 + .ra: x30
STACK CFI 3b74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3b84 d4 .cfa: sp 0 + .ra: x30
STACK CFI 3b88 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3b90 x23: .cfa -16 + ^
STACK CFI 3b98 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3ba4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3c1c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3c3c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3c60 16c .cfa: sp 0 + .ra: x30
STACK CFI 3c64 .cfa: sp 96 +
STACK CFI 3c70 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3dc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3dc8 .cfa: sp 96 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3dd0 868 .cfa: sp 0 + .ra: x30
STACK CFI 3dd4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3de8 .cfa: sp 1232 + x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3e54 .cfa: sp 96 +
STACK CFI 3e64 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 3e68 .cfa: sp 1232 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 3e74 x19: .cfa -80 + ^
STACK CFI 3e78 x20: .cfa -72 + ^
STACK CFI 3e7c x25: .cfa -32 + ^
STACK CFI 3e80 x26: .cfa -24 + ^
STACK CFI 424c x20: x20
STACK CFI 4254 x25: x25
STACK CFI 425c x19: x19
STACK CFI 4260 x26: x26
STACK CFI 4264 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4274 x19: x19
STACK CFI 427c x20: x20
STACK CFI 4284 x25: x25
STACK CFI 4288 x26: x26
STACK CFI 4298 x19: .cfa -80 + ^
STACK CFI 429c x20: .cfa -72 + ^
STACK CFI 42a0 x25: .cfa -32 + ^
STACK CFI 42a4 x26: .cfa -24 + ^
STACK CFI 4514 x20: x20
STACK CFI 4518 x25: x25
STACK CFI 4520 x19: x19
STACK CFI 4524 x26: x26
STACK CFI 4528 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 45e0 x19: x19 x20: x20 x25: x25 x26: x26
STACK CFI 45e4 x19: .cfa -80 + ^
STACK CFI 45e8 x20: .cfa -72 + ^
STACK CFI 45ec x25: .cfa -32 + ^
STACK CFI 45f0 x26: .cfa -24 + ^
STACK CFI INIT 4640 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 4644 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4648 .cfa: x29 80 +
STACK CFI 4668 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 4794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4798 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4914 84 .cfa: sp 0 + .ra: x30
STACK CFI 4918 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4920 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4928 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 496c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4970 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 49a0 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 49a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 49a8 .cfa: x29 64 +
STACK CFI 49c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4b2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4b30 .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4c80 68 .cfa: sp 0 + .ra: x30
STACK CFI 4c84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4cc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4ce4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4cf0 68 .cfa: sp 0 + .ra: x30
STACK CFI 4cf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4cfc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4d30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4d34 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4d60 24 .cfa: sp 0 + .ra: x30
STACK CFI 4d70 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4d80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4d84 24 .cfa: sp 0 + .ra: x30
STACK CFI 4d94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4da4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4db0 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 4db4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4dbc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4de8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4dec .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 4dfc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4e08 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4e10 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4e14 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4eec x23: x23 x24: x24
STACK CFI 4ef4 x25: x25 x26: x26
STACK CFI 4ef8 x27: x27 x28: x28
STACK CFI 4f38 x19: x19 x20: x20
STACK CFI 4f40 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4f44 x23: x23 x24: x24
STACK CFI 4f48 x25: x25 x26: x26
STACK CFI 4f4c x27: x27 x28: x28
STACK CFI 4f54 x19: x19 x20: x20
STACK CFI 4f60 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4f64 x23: x23 x24: x24
STACK CFI 4f6c x25: x25 x26: x26
STACK CFI 4f70 x27: x27 x28: x28
STACK CFI 4f74 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 4f90 42c .cfa: sp 0 + .ra: x30
STACK CFI 4f94 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4fa4 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 4fac x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 4fb8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4fc4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 505c x21: x21 x22: x22
STACK CFI 5060 x23: x23 x24: x24
STACK CFI 5064 x27: x27 x28: x28
STACK CFI 5088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 508c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 51b4 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 51bc x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 51c0 x21: x21 x22: x22
STACK CFI 51c8 x23: x23 x24: x24
STACK CFI 51cc x27: x27 x28: x28
STACK CFI 51d0 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5270 x21: x21 x22: x22
STACK CFI 5274 x23: x23 x24: x24
STACK CFI 5278 x27: x27 x28: x28
STACK CFI 527c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 53c0 10c .cfa: sp 0 + .ra: x30
STACK CFI 53c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 53cc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 53d4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 53e8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5438 x19: x19 x20: x20
STACK CFI 5448 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 544c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5468 x19: x19 x20: x20
STACK CFI 5498 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 549c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 54a0 x19: x19 x20: x20
STACK CFI 54c8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 54d0 3c4 .cfa: sp 0 + .ra: x30
STACK CFI 54d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 54dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5508 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 58a0 108 .cfa: sp 0 + .ra: x30
STACK CFI 58a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 58ac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 58bc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 58c8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 58cc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5960 x19: x19 x20: x20
STACK CFI 596c x23: x23 x24: x24
STACK CFI 5970 x25: x25 x26: x26
STACK CFI 5974 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5978 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 597c x19: x19 x20: x20
STACK CFI 5980 x23: x23 x24: x24
STACK CFI 5984 x25: x25 x26: x26
STACK CFI 59a4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 59b0 44 .cfa: sp 0 + .ra: x30
STACK CFI 59b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 59e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 59e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 59ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5a00 1c .cfa: sp 0 + .ra: x30
STACK CFI 5a04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5a10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5a20 18 .cfa: sp 0 + .ra: x30
STACK CFI 5a24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5a30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5a40 18 .cfa: sp 0 + .ra: x30
STACK CFI 5a44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5a50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5a60 18 .cfa: sp 0 + .ra: x30
STACK CFI 5a64 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5a70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5a80 34 .cfa: sp 0 + .ra: x30
STACK CFI 5a90 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5aac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5ab4 d0 .cfa: sp 0 + .ra: x30
STACK CFI 5ab8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5ac0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5ac8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5ae0 x23: .cfa -16 + ^
STACK CFI 5b34 x23: x23
STACK CFI 5b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b5c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5b60 x23: x23
STACK CFI 5b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5b7c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5b84 13c .cfa: sp 0 + .ra: x30
STACK CFI 5b8c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5b94 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5ba0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5bf0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5c28 x23: x23 x24: x24
STACK CFI 5c44 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5c84 x23: x23 x24: x24
STACK CFI 5c90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5c94 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5cb8 x23: x23 x24: x24
STACK CFI INIT 5cc0 25c .cfa: sp 0 + .ra: x30
STACK CFI 5ccc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5cd4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5ce4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5d14 x23: .cfa -16 + ^
STACK CFI 5e04 x23: x23
STACK CFI 5e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5e18 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5e30 x23: x23
STACK CFI 5e38 x23: .cfa -16 + ^
STACK CFI 5eb8 x23: x23
STACK CFI 5ed4 x23: .cfa -16 + ^
STACK CFI 5ee4 x23: x23
STACK CFI 5ef4 x23: .cfa -16 + ^
STACK CFI 5ef8 x23: x23
STACK CFI 5f00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5f20 c8 .cfa: sp 0 + .ra: x30
STACK CFI 5f24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5f2c x21: .cfa -16 + ^
STACK CFI 5f3c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5f9c x19: x19 x20: x20
STACK CFI 5fa4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5fbc x19: x19 x20: x20
STACK CFI 5fc4 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 5fc8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5fd4 x19: x19 x20: x20
STACK CFI 5fdc .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 5fe0 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5ff0 28 .cfa: sp 0 + .ra: x30
STACK CFI 5ff4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6020 90 .cfa: sp 0 + .ra: x30
STACK CFI 6024 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 602c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6044 x21: .cfa -16 + ^
STACK CFI 6084 x21: x21
STACK CFI 608c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6090 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 60ac x21: .cfa -16 + ^
STACK CFI INIT 60b0 15c .cfa: sp 0 + .ra: x30
STACK CFI 60b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 60bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 60cc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 60d4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 60dc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 60e8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 61c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 61c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6200 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6204 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6210 160 .cfa: sp 0 + .ra: x30
STACK CFI 6214 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6220 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6228 x25: .cfa -16 + ^
STACK CFI 6230 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 623c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 62e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 62e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6370 fc .cfa: sp 0 + .ra: x30
STACK CFI 6374 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6400 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6404 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6424 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6428 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6470 1424 .cfa: sp 0 + .ra: x30
STACK CFI 6474 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6494 .cfa: sp 8544 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 68e0 .cfa: sp 96 +
STACK CFI 68f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 68fc .cfa: sp 8544 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7894 74 .cfa: sp 0 + .ra: x30
STACK CFI 7898 .cfa: sp 48 +
STACK CFI 78b0 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7904 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7910 17c .cfa: sp 0 + .ra: x30
STACK CFI 7914 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 791c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7924 x21: .cfa -16 + ^
STACK CFI 79e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 79ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7a90 50 .cfa: sp 0 + .ra: x30
STACK CFI 7a94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7a9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7acc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7ad0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7adc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7ae0 cc .cfa: sp 0 + .ra: x30
STACK CFI 7ae4 .cfa: sp 112 +
STACK CFI 7af8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7b04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7b10 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7b9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7ba0 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7bb0 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 7bb4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7bc4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7bd4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7bec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7c04 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7cec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7cf0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7d90 164 .cfa: sp 0 + .ra: x30
STACK CFI 7d94 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7d9c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7da8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7dc4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7e64 x25: x25 x26: x26
STACK CFI 7e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7e80 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 7ec4 x25: x25 x26: x26
STACK CFI 7ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 7f00 14 .cfa: sp 0 + .ra: x30
STACK CFI 7f04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7f10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7f14 14 .cfa: sp 0 + .ra: x30
STACK CFI 7f18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7f24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7f30 14 .cfa: sp 0 + .ra: x30
STACK CFI 7f34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7f40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7f44 14 .cfa: sp 0 + .ra: x30
STACK CFI 7f48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7f54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7f60 34 .cfa: sp 0 + .ra: x30
STACK CFI 7f70 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7f84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7f88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7f8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7fa0 110 .cfa: sp 0 + .ra: x30
STACK CFI 7fa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7fac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8050 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8058 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 80b0 2fc .cfa: sp 0 + .ra: x30
STACK CFI 80b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 80bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 80c8 x21: .cfa -16 + ^
STACK CFI 82a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 82a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 83b0 47c .cfa: sp 0 + .ra: x30
STACK CFI 83b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 83c0 .cfa: sp 1232 + x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 840c .cfa: sp 96 +
STACK CFI 8414 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 8418 .cfa: sp 1232 + .ra: .cfa -88 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8420 x19: .cfa -80 + ^
STACK CFI 8428 x20: .cfa -72 + ^
STACK CFI 8430 x23: .cfa -48 + ^
STACK CFI 8438 x24: .cfa -40 + ^
STACK CFI 843c x25: .cfa -32 + ^
STACK CFI 8444 x26: .cfa -24 + ^
STACK CFI 8488 x22: .cfa -56 + ^
STACK CFI 84c8 x21: .cfa -64 + ^
STACK CFI 8628 x19: x19
STACK CFI 8630 x20: x20
STACK CFI 8634 x21: x21
STACK CFI 8638 x22: x22
STACK CFI 863c x23: x23
STACK CFI 8640 x24: x24
STACK CFI 8644 x25: x25
STACK CFI 8648 x26: x26
STACK CFI 864c .cfa: sp 96 +
STACK CFI 8654 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 8658 .cfa: sp 1232 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8728 x21: x21
STACK CFI 872c x22: x22
STACK CFI 8748 x19: x19
STACK CFI 874c x20: x20
STACK CFI 8750 x23: x23
STACK CFI 8754 x24: x24
STACK CFI 8758 x25: x25
STACK CFI 875c x26: x26
STACK CFI 8760 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8808 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 880c x19: .cfa -80 + ^
STACK CFI 8810 x20: .cfa -72 + ^
STACK CFI 8814 x21: .cfa -64 + ^
STACK CFI 8818 x22: .cfa -56 + ^
STACK CFI 881c x23: .cfa -48 + ^
STACK CFI 8820 x24: .cfa -40 + ^
STACK CFI 8824 x25: .cfa -32 + ^
STACK CFI 8828 x26: .cfa -24 + ^
STACK CFI INIT 8830 6d0 .cfa: sp 0 + .ra: x30
STACK CFI 8834 .cfa: sp 224 +
STACK CFI 8848 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8850 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 885c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8884 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8894 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 88a0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8ae8 x23: x23 x24: x24
STACK CFI 8aec x25: x25 x26: x26
STACK CFI 8af0 x27: x27 x28: x28
STACK CFI 8b1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8b20 .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8d30 x23: x23 x24: x24
STACK CFI 8d34 x25: x25 x26: x26
STACK CFI 8d38 x27: x27 x28: x28
STACK CFI 8d3c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8eb8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8ee4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8ef0 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8ef4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8ef8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8efc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 8f00 18 .cfa: sp 0 + .ra: x30
STACK CFI 8f04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8f14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8f20 20 .cfa: sp 0 + .ra: x30
STACK CFI 8f24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8f38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8f40 b8 .cfa: sp 0 + .ra: x30
STACK CFI 8f44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8f54 .cfa: sp 1088 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8fd0 .cfa: sp 48 +
STACK CFI 8fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8fe0 .cfa: sp 1088 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9000 18 .cfa: sp 0 + .ra: x30
STACK CFI 9004 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9014 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9020 98 .cfa: sp 0 + .ra: x30
STACK CFI 9024 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 902c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9044 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 908c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9090 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 90b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 90c0 90 .cfa: sp 0 + .ra: x30
STACK CFI 90c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 90d4 .cfa: sp 1088 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 9138 .cfa: sp 48 +
STACK CFI 9148 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 914c .cfa: sp 1088 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9150 7c .cfa: sp 0 + .ra: x30
STACK CFI 9154 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 915c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 916c x21: .cfa -16 + ^
STACK CFI 91ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 91b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 91c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 91d0 88 .cfa: sp 0 + .ra: x30
STACK CFI 91d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 91e8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9224 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9260 90 .cfa: sp 0 + .ra: x30
STACK CFI 9264 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9274 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 92b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 92b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 92ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 92f0 70 .cfa: sp 0 + .ra: x30
STACK CFI 9328 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9338 x19: .cfa -16 + ^
STACK CFI 935c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9360 74 .cfa: sp 0 + .ra: x30
STACK CFI 939c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 93ac x19: .cfa -16 + ^
STACK CFI 93d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 93d4 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 9450 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9460 x19: .cfa -16 + ^
STACK CFI 9488 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 95a4 a4 .cfa: sp 0 + .ra: x30
STACK CFI 95a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 95b8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 95c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 95dc x23: .cfa -16 + ^
STACK CFI 9644 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 9650 80 .cfa: sp 0 + .ra: x30
STACK CFI 9654 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9664 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9674 x21: .cfa -16 + ^
STACK CFI 9698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 969c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 96cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 96d0 74 .cfa: sp 0 + .ra: x30
STACK CFI 970c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 971c x19: .cfa -16 + ^
STACK CFI 9740 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9744 3fc .cfa: sp 0 + .ra: x30
STACK CFI 9748 .cfa: sp 128 +
STACK CFI 9754 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 975c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 976c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9784 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 97f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 97f8 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 9810 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9974 x27: x27 x28: x28
STACK CFI 9990 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9b38 x27: x27 x28: x28
STACK CFI 9b3c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 9b40 48c .cfa: sp 0 + .ra: x30
STACK CFI 9b44 .cfa: sp 240 +
STACK CFI 9b4c .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9b54 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9b74 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 9b80 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9b88 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 9b8c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 9f40 x21: x21 x22: x22
STACK CFI 9f44 x23: x23 x24: x24
STACK CFI 9f48 x25: x25 x26: x26
STACK CFI 9f4c x27: x27 x28: x28
STACK CFI 9f54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9f58 .cfa: sp 240 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 9f78 x21: x21 x22: x22
STACK CFI 9f7c x23: x23 x24: x24
STACK CFI 9f80 x25: x25 x26: x26
STACK CFI 9f84 x27: x27 x28: x28
STACK CFI 9f8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9f90 .cfa: sp 240 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 9fb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9fb4 .cfa: sp 240 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 9fd0 8c .cfa: sp 0 + .ra: x30
STACK CFI 9fd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9fdc x19: .cfa -16 + ^
STACK CFI a040 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a04c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a058 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a060 100 .cfa: sp 0 + .ra: x30
STACK CFI a064 .cfa: sp 128 +
STACK CFI a074 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a07c x19: .cfa -16 + ^
STACK CFI a158 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a15c .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT a160 94 .cfa: sp 0 + .ra: x30
STACK CFI a164 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a16c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a174 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a1b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a1bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI a1f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT a1f4 84 .cfa: sp 0 + .ra: x30
STACK CFI a1f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a200 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a23c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a240 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a280 168 .cfa: sp 0 + .ra: x30
STACK CFI a284 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a294 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a2a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a2c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a2cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI a364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a368 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT a3f0 10 .cfa: sp 0 + .ra: x30
STACK CFI a3f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a3fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a400 10 .cfa: sp 0 + .ra: x30
STACK CFI a404 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a40c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a410 10 .cfa: sp 0 + .ra: x30
STACK CFI a414 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a41c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a420 10 .cfa: sp 0 + .ra: x30
STACK CFI a424 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a42c .cfa: sp 0 + .ra: .ra x29: x29
