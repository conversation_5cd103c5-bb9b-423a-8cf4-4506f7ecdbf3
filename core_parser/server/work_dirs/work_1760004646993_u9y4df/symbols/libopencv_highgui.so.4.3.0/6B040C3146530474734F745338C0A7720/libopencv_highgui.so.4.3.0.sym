MODULE Linux arm64 6B040C3146530474734F745338C0A7720 libopencv_highgui.so.4.3
INFO CODE_ID 310C046B53467404734F745338C0A77223AC7F1E
PUBLIC 2f40 0 _init
PUBLIC 32a0 0 _GLOBAL__sub_I_window.cpp
PUBLIC 32d0 0 call_weak_fn
PUBLIC 32e8 0 deregister_tm_clones
PUBLIC 3320 0 register_tm_clones
PUBLIC 3360 0 __do_global_dtors_aux
PUBLIC 33a8 0 frame_dummy
PUBLIC 33e0 0 cv::Mat::~Mat()
PUBLIC 3470 0 cvSetWindowProperty
PUBLIC 3478 0 cvGetWindowProperty
PUBLIC 3480 0 cvGetWindowImageRect
PUBLIC 3490 0 cv::getWindowImageRect(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 3500 0 cv::setWindowProperty(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int, double)
PUBLIC 3570 0 cv::getWindowProperty(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int)
PUBLIC 35e0 0 cv::getMouseWheelDelta(int)
PUBLIC 3628 0 cv::imshow(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::ogl::Texture2D const&)
PUBLIC 3708 0 cvSetOpenGlDrawCallback
PUBLIC 37b8 0 cv::setOpenGlDrawCallback(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, void (*)(void*), void*)
PUBLIC 3830 0 cvSetOpenGlContext
PUBLIC 38e0 0 cv::setOpenGlContext(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 3940 0 cvUpdateWindow
PUBLIC 39f0 0 cv::updateWindow(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 3a50 0 cv::fontQt(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int, cv::Scalar_<double>, int, int, int)
PUBLIC 3b00 0 cv::addText(cv::Mat const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::Point_<int>, cv::QtFont const&)
PUBLIC 3bb0 0 cv::addText(cv::Mat const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::Point_<int>, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int, cv::Scalar_<double>, int, int, int)
PUBLIC 3c60 0 cv::displayStatusBar(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int)
PUBLIC 3d10 0 cv::displayOverlay(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int)
PUBLIC 3dc0 0 cv::startLoop(int (*)(int, char**), int, char**)
PUBLIC 3e70 0 cv::stopLoop()
PUBLIC 3f20 0 cv::saveWindowParameters(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 3fd0 0 cv::loadWindowParameters(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 4080 0 cv::createButton(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, void (*)(int, void*), void*, int, bool)
PUBLIC 4130 0 cv::setWindowTitle(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 41c8 0 cvNamedWindow
PUBLIC 4260 0 cv::namedWindow(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int)
PUBLIC 42c8 0 cvDestroyWindow
PUBLIC 4360 0 cv::destroyWindow(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 43c0 0 cvDestroyAllWindows
PUBLIC 4458 0 cv::destroyAllWindows()
PUBLIC 44b0 0 cvShowImage
PUBLIC 4548 0 cv::imshow(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::_InputArray const&)
PUBLIC 4818 0 cvResizeWindow
PUBLIC 48b0 0 cv::resizeWindow(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int, int)
PUBLIC 4928 0 cv::resizeWindow(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::Size_<int> const&)
PUBLIC 4990 0 cvMoveWindow
PUBLIC 4a28 0 cv::moveWindow(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int, int)
PUBLIC 4aa0 0 cvCreateTrackbar
PUBLIC 4b38 0 cvCreateTrackbar2
PUBLIC 4bd0 0 cv::createTrackbar(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int*, int, void (*)(int, void*), void*)
PUBLIC 4c70 0 cvSetMouseCallback
PUBLIC 4d08 0 cv::setMouseCallback(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, void (*)(int, int, int, int, void*), void*)
PUBLIC 4d80 0 cvGetTrackbarPos
PUBLIC 4e18 0 cv::getTrackbarPos(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 4e88 0 cvSetTrackbarPos
PUBLIC 4f20 0 cv::setTrackbarPos(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int)
PUBLIC 4f98 0 cvSetTrackbarMax
PUBLIC 5030 0 cv::setTrackbarMax(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int)
PUBLIC 50a8 0 cvSetTrackbarMin
PUBLIC 5140 0 cv::setTrackbarMin(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int)
PUBLIC 51b8 0 cvGetWindowHandle
PUBLIC 5250 0 cvGetWindowName
PUBLIC 52e8 0 cvWaitKey
PUBLIC 5380 0 cv::waitKeyEx(int)
PUBLIC 53e8 0 cv::waitKey(int)
PUBLIC 5490 0 cvInitSystem
PUBLIC 5528 0 cvStartWindowThread
PUBLIC 55c0 0 cv::startWindowThread()
PUBLIC 5620 0 cvAddText
PUBLIC 56b8 0 cvDisplayStatusBar
PUBLIC 5750 0 cvDisplayOverlay
PUBLIC 57e8 0 cvStartLoop
PUBLIC 5880 0 cvStopLoop
PUBLIC 5918 0 cvSaveWindowParameters
PUBLIC 59b0 0 cvCreateButton
PUBLIC 5a48 0 (anonymous namespace)::ROISelector::emptyMouseHandler(int, int, int, int, void*)
PUBLIC 5a50 0 (anonymous namespace)::ROISelector::mouseHandler(int, int, int, int, void*)
PUBLIC 5be0 0 cv::selectROI(cv::_InputArray const&, bool, bool)
PUBLIC 6450 0 cv::selectROI(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::_InputArray const&, bool, bool)
PUBLIC 6c60 0 void std::vector<cv::Rect_<int>, std::allocator<cv::Rect_<int> > >::_M_emplace_back_aux<cv::Rect_<int> const&>(cv::Rect_<int> const&)
PUBLIC 6d70 0 cv::selectROIs(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, cv::_InputArray const&, std::vector<cv::Rect_<int>, std::allocator<cv::Rect_<int> > >&, bool, bool)
PUBLIC 7740 0 _fini
STACK CFI INIT 33e0 90 .cfa: sp 0 + .ra: x30
STACK CFI 33e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 3458 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 3460 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 346c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 3470 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3478 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3480 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3490 6c .cfa: sp 0 + .ra: x30
STACK CFI 3494 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 34a4 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 34dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 34e0 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 3500 70 .cfa: sp 0 + .ra: x30
STACK CFI 3504 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3510 .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 3550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 3554 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT 3570 70 .cfa: sp 0 + .ra: x30
STACK CFI 3574 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3580 .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 35c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 35c4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT 35e0 44 .cfa: sp 0 + .ra: x30
STACK CFI 35e4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 35f8 .ra: .cfa -32 + ^
STACK CFI 3620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 3628 e0 .cfa: sp 0 + .ra: x30
STACK CFI 362c .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 363c .ra: .cfa -72 + ^ x21: .cfa -80 + ^
STACK CFI INIT 3708 ac .cfa: sp 0 + .ra: x30
STACK CFI 370c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 371c .ra: .cfa -64 + ^
STACK CFI INIT 37b8 78 .cfa: sp 0 + .ra: x30
STACK CFI 37bc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 37cc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 37dc .ra: .cfa -32 + ^
STACK CFI 3810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 3814 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 3830 ac .cfa: sp 0 + .ra: x30
STACK CFI 3834 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3844 .ra: .cfa -64 + ^
STACK CFI INIT 38e0 60 .cfa: sp 0 + .ra: x30
STACK CFI 38e4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 38f8 .ra: .cfa -32 + ^
STACK CFI 3920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3924 .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 3940 ac .cfa: sp 0 + .ra: x30
STACK CFI 3944 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3954 .ra: .cfa -64 + ^
STACK CFI INIT 39f0 60 .cfa: sp 0 + .ra: x30
STACK CFI 39f4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3a08 .ra: .cfa -32 + ^
STACK CFI 3a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3a34 .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 3a50 ac .cfa: sp 0 + .ra: x30
STACK CFI 3a54 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3a64 .ra: .cfa -64 + ^
STACK CFI INIT 3b00 ac .cfa: sp 0 + .ra: x30
STACK CFI 3b04 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3b14 .ra: .cfa -64 + ^
STACK CFI INIT 3bb0 ac .cfa: sp 0 + .ra: x30
STACK CFI 3bb4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3bc4 .ra: .cfa -64 + ^
STACK CFI INIT 3c60 ac .cfa: sp 0 + .ra: x30
STACK CFI 3c64 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3c74 .ra: .cfa -64 + ^
STACK CFI INIT 3d10 ac .cfa: sp 0 + .ra: x30
STACK CFI 3d14 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3d24 .ra: .cfa -64 + ^
STACK CFI INIT 3dc0 ac .cfa: sp 0 + .ra: x30
STACK CFI 3dc4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3dd4 .ra: .cfa -64 + ^
STACK CFI INIT 3e70 ac .cfa: sp 0 + .ra: x30
STACK CFI 3e74 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3e84 .ra: .cfa -64 + ^
STACK CFI INIT 3f20 ac .cfa: sp 0 + .ra: x30
STACK CFI 3f24 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3f34 .ra: .cfa -64 + ^
STACK CFI INIT 3fd0 ac .cfa: sp 0 + .ra: x30
STACK CFI 3fd4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3fe4 .ra: .cfa -64 + ^
STACK CFI INIT 4080 ac .cfa: sp 0 + .ra: x30
STACK CFI 4084 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4094 .ra: .cfa -64 + ^
STACK CFI INIT 4130 94 .cfa: sp 0 + .ra: x30
STACK CFI 4134 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4144 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 41c8 94 .cfa: sp 0 + .ra: x30
STACK CFI 41cc .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 41dc .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 4260 68 .cfa: sp 0 + .ra: x30
STACK CFI 4264 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4270 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 42a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 42ac .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 42c8 94 .cfa: sp 0 + .ra: x30
STACK CFI 42cc .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 42dc .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 4360 60 .cfa: sp 0 + .ra: x30
STACK CFI 4364 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4378 .ra: .cfa -32 + ^
STACK CFI 43a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 43a4 .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 43c0 94 .cfa: sp 0 + .ra: x30
STACK CFI 43c4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 43d4 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 4458 58 .cfa: sp 0 + .ra: x30
STACK CFI 445c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 446c .ra: .cfa -32 + ^
STACK CFI 4490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 4494 .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 44b0 94 .cfa: sp 0 + .ra: x30
STACK CFI 44b4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 44c4 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 4548 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 454c .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 4558 .ra: .cfa -176 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 46a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 46a8 .cfa: sp 208 + .ra: .cfa -176 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI INIT 4818 94 .cfa: sp 0 + .ra: x30
STACK CFI 481c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 482c .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 48b0 78 .cfa: sp 0 + .ra: x30
STACK CFI 48b4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 48c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 48d4 .ra: .cfa -32 + ^
STACK CFI 4908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 490c .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 4928 68 .cfa: sp 0 + .ra: x30
STACK CFI 492c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 493c .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 4970 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 4974 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 4990 94 .cfa: sp 0 + .ra: x30
STACK CFI 4994 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 49a4 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 4a28 78 .cfa: sp 0 + .ra: x30
STACK CFI 4a2c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4a3c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4a4c .ra: .cfa -32 + ^
STACK CFI 4a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 4a84 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 4aa0 94 .cfa: sp 0 + .ra: x30
STACK CFI 4aa4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4ab4 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 4b38 94 .cfa: sp 0 + .ra: x30
STACK CFI 4b3c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4b4c .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 4bd0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 4bd4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4bdc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4bec .ra: .cfa -24 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 4c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 4c54 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI INIT 4c70 94 .cfa: sp 0 + .ra: x30
STACK CFI 4c74 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4c84 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 4d08 78 .cfa: sp 0 + .ra: x30
STACK CFI 4d0c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4d1c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4d2c .ra: .cfa -32 + ^
STACK CFI 4d60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 4d64 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 4d80 94 .cfa: sp 0 + .ra: x30
STACK CFI 4d84 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4d94 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 4e18 70 .cfa: sp 0 + .ra: x30
STACK CFI 4e1c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4e24 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 4e68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 4e6c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 4e88 94 .cfa: sp 0 + .ra: x30
STACK CFI 4e8c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4e9c .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 4f20 78 .cfa: sp 0 + .ra: x30
STACK CFI 4f24 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4f2c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4f44 .ra: .cfa -32 + ^
STACK CFI 4f78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 4f7c .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 4f98 94 .cfa: sp 0 + .ra: x30
STACK CFI 4f9c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4fac .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 5030 78 .cfa: sp 0 + .ra: x30
STACK CFI 5034 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 503c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5054 .ra: .cfa -32 + ^
STACK CFI 5088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 508c .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 50a8 94 .cfa: sp 0 + .ra: x30
STACK CFI 50ac .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 50bc .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 5140 78 .cfa: sp 0 + .ra: x30
STACK CFI 5144 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 514c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5164 .ra: .cfa -32 + ^
STACK CFI 5198 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 519c .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 51b8 94 .cfa: sp 0 + .ra: x30
STACK CFI 51bc .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 51cc .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 5250 94 .cfa: sp 0 + .ra: x30
STACK CFI 5254 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5264 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 52e8 94 .cfa: sp 0 + .ra: x30
STACK CFI 52ec .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 52fc .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 5380 68 .cfa: sp 0 + .ra: x30
STACK CFI 5384 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5398 .ra: .cfa -32 + ^
STACK CFI 53c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 53cc .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 53e8 a8 .cfa: sp 0 + .ra: x30
STACK CFI 53ec .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5400 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 5440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 5448 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 5490 94 .cfa: sp 0 + .ra: x30
STACK CFI 5494 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 54a4 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 5528 94 .cfa: sp 0 + .ra: x30
STACK CFI 552c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 553c .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 55c0 60 .cfa: sp 0 + .ra: x30
STACK CFI 55c4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 55d4 .ra: .cfa -32 + ^
STACK CFI 5600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 5604 .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 5620 94 .cfa: sp 0 + .ra: x30
STACK CFI 5624 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5634 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 56b8 94 .cfa: sp 0 + .ra: x30
STACK CFI 56bc .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 56cc .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 5750 94 .cfa: sp 0 + .ra: x30
STACK CFI 5754 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5764 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 57e8 94 .cfa: sp 0 + .ra: x30
STACK CFI 57ec .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 57fc .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 5880 94 .cfa: sp 0 + .ra: x30
STACK CFI 5884 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5894 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 5918 94 .cfa: sp 0 + .ra: x30
STACK CFI 591c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 592c .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 59b0 94 .cfa: sp 0 + .ra: x30
STACK CFI 59b4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 59c4 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI INIT 32a0 30 .cfa: sp 0 + .ra: x30
STACK CFI 32a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 32c0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 5a48 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5a50 18c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5be0 848 .cfa: sp 0 + .ra: x30
STACK CFI 5be4 .cfa: sp 912 +
STACK CFI 5c04 x19: .cfa -912 + ^ x20: .cfa -904 + ^
STACK CFI 5c14 x21: .cfa -896 + ^ x22: .cfa -888 + ^ x23: .cfa -880 + ^ x24: .cfa -872 + ^
STACK CFI 5c2c .ra: .cfa -832 + ^ x25: .cfa -864 + ^ x26: .cfa -856 + ^ x27: .cfa -848 + ^ x28: .cfa -840 + ^
STACK CFI 62c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 62c8 .cfa: sp 912 + .ra: .cfa -832 + ^ x19: .cfa -912 + ^ x20: .cfa -904 + ^ x21: .cfa -896 + ^ x22: .cfa -888 + ^ x23: .cfa -880 + ^ x24: .cfa -872 + ^ x25: .cfa -864 + ^ x26: .cfa -856 + ^ x27: .cfa -848 + ^ x28: .cfa -840 + ^
STACK CFI INIT 6450 7e8 .cfa: sp 0 + .ra: x30
STACK CFI 6454 .cfa: sp 880 +
STACK CFI 646c x19: .cfa -880 + ^ x20: .cfa -872 + ^
STACK CFI 648c x21: .cfa -864 + ^ x22: .cfa -856 + ^ x23: .cfa -848 + ^ x24: .cfa -840 + ^ x25: .cfa -832 + ^ x26: .cfa -824 + ^ x27: .cfa -816 + ^ x28: .cfa -808 + ^
STACK CFI 6494 .ra: .cfa -800 + ^
STACK CFI 6ae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6af0 .cfa: sp 880 + .ra: .cfa -800 + ^ x19: .cfa -880 + ^ x20: .cfa -872 + ^ x21: .cfa -864 + ^ x22: .cfa -856 + ^ x23: .cfa -848 + ^ x24: .cfa -840 + ^ x25: .cfa -832 + ^ x26: .cfa -824 + ^ x27: .cfa -816 + ^ x28: .cfa -808 + ^
STACK CFI INIT 6c60 110 .cfa: sp 0 + .ra: x30
STACK CFI 6c64 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6c6c .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 6c74 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 6d40 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 6d70 9ac .cfa: sp 0 + .ra: x30
STACK CFI 6d78 .cfa: sp 1040 +
STACK CFI 6d80 x19: .cfa -1040 + ^ x20: .cfa -1032 + ^
STACK CFI 6da0 x21: .cfa -1024 + ^ x22: .cfa -1016 + ^ x23: .cfa -1008 + ^ x24: .cfa -1000 + ^ x25: .cfa -992 + ^ x26: .cfa -984 + ^ x27: .cfa -976 + ^ x28: .cfa -968 + ^
STACK CFI 6da8 .ra: .cfa -960 + ^
STACK CFI 7610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7614 .cfa: sp 1040 + .ra: .cfa -960 + ^ x19: .cfa -1040 + ^ x20: .cfa -1032 + ^ x21: .cfa -1024 + ^ x22: .cfa -1016 + ^ x23: .cfa -1008 + ^ x24: .cfa -1000 + ^ x25: .cfa -992 + ^ x26: .cfa -984 + ^ x27: .cfa -976 + ^ x28: .cfa -968 + ^
