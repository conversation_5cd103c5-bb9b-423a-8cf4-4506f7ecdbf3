MODULE Linux arm64 34853729D47F8A6EC7EBB9822659EFE30 libpulse-simple.so.0
INFO CODE_ID 293785347FD46E8AC7EBB9822659EFE369755AA3
PUBLIC 1ae0 0 pa_simple_free
PUBLIC 1b90 0 pa_simple_new
PUBLIC 1e10 0 pa_simple_write
PUBLIC 2014 0 pa_simple_read
PUBLIC 22a4 0 pa_simple_drain
PUBLIC 24d0 0 pa_simple_flush
PUBLIC 26e0 0 pa_simple_get_latency
STACK CFI INIT 16a0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16d0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1710 48 .cfa: sp 0 + .ra: x30
STACK CFI 1714 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 171c x19: .cfa -16 + ^
STACK CFI 1754 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1760 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1770 e0 .cfa: sp 0 + .ra: x30
STACK CFI 1778 .cfa: sp 48 +
STACK CFI 177c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1784 x19: .cfa -16 + ^
STACK CFI 17ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 17b4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 17c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 17d0 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1850 68 .cfa: sp 0 + .ra: x30
STACK CFI 186c .cfa: sp 32 +
STACK CFI 1884 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 18c0 68 .cfa: sp 0 + .ra: x30
STACK CFI 18dc .cfa: sp 32 +
STACK CFI 18f4 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1930 bc .cfa: sp 0 + .ra: x30
STACK CFI 1938 .cfa: sp 32 +
STACK CFI 193c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1958 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1964 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 19f0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 19f8 .cfa: sp 48 +
STACK CFI 19fc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a04 x19: .cfa -16 + ^
STACK CFI 1a2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a34 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1a48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a50 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1ae0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 1ae8 .cfa: sp 48 +
STACK CFI 1aec .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1af4 x19: .cfa -16 + ^
STACK CFI 1b44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1b4c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b90 278 .cfa: sp 0 + .ra: x30
STACK CFI 1b98 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1ba0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1ba8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1bb4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1bc0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1bc8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1da4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1dac .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1e10 204 .cfa: sp 0 + .ra: x30
STACK CFI 1e18 .cfa: sp 80 +
STACK CFI 1e1c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1e24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1e34 x23: .cfa -16 + ^
STACK CFI 1e44 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1f0c x21: x21 x22: x22
STACK CFI 1f14 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1f54 x21: x21 x22: x22
STACK CFI 1f60 x19: x19 x20: x20
STACK CFI 1f64 x23: x23
STACK CFI 1f68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1f70 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1f8c x21: x21 x22: x22
STACK CFI 1f90 x23: x23
STACK CFI 1fd0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1fd4 x23: .cfa -16 + ^
STACK CFI 1fe4 x21: x21 x22: x22
STACK CFI 2000 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2010 x21: x21 x22: x22
STACK CFI INIT 2014 290 .cfa: sp 0 + .ra: x30
STACK CFI 201c .cfa: sp 96 +
STACK CFI 2020 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2028 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2038 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2050 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 20c8 x25: .cfa -16 + ^
STACK CFI 2104 x25: x25
STACK CFI 2180 x21: x21 x22: x22
STACK CFI 218c x19: x19 x20: x20
STACK CFI 2190 x23: x23 x24: x24
STACK CFI 2194 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 219c .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 21c4 x21: x21 x22: x22
STACK CFI 21c8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI 21ec x25: x25
STACK CFI 21f4 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2234 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2238 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 223c x25: .cfa -16 + ^
STACK CFI 2250 x21: x21 x22: x22
STACK CFI 2258 x25: x25
STACK CFI 225c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2260 x21: x21 x22: x22
STACK CFI 2270 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2278 x25: .cfa -16 + ^
STACK CFI 2280 x21: x21 x22: x22 x25: x25
STACK CFI 2290 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 229c x25: .cfa -16 + ^
STACK CFI 22a0 x25: x25
STACK CFI INIT 22a4 22c .cfa: sp 0 + .ra: x30
STACK CFI 22ac .cfa: sp 64 +
STACK CFI 22b0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 22bc x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 23a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 23a8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2410 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 24d0 210 .cfa: sp 0 + .ra: x30
STACK CFI 24d8 .cfa: sp 64 +
STACK CFI 24dc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 24e8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 25c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 25c8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2630 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 26e0 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 26e8 .cfa: sp 96 +
STACK CFI 26f4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 26fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2704 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 27fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2804 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
