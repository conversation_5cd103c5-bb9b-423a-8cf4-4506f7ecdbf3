MODULE Linux arm64 AD69AD3239B16171CEC2366A6DDBA5CA0 libavahi-common.so.3
INFO CODE_ID 32AD69ADB1397161CEC2366A6DDBA5CAA6BD44A3
PUBLIC 2fa0 0 avahi_malloc
PUBLIC 3020 0 avahi_malloc0
PUBLIC 30f0 0 avahi_free
PUBLIC 3150 0 avahi_realloc
PUBLIC 31d0 0 avahi_strdup
PUBLIC 3260 0 avahi_strndup
PUBLIC 3330 0 avahi_set_allocator
PUBLIC 3350 0 avahi_strdup_vprintf
PUBLIC 3480 0 avahi_strdup_printf
PUBLIC 3520 0 avahi_memdup
PUBLIC 35a0 0 avahi_address_cmp
PUBLIC 3640 0 avahi_reverse_lookup_name
PUBLIC 38e0 0 avahi_proto_to_af
PUBLIC 3944 0 avahi_address_snprint
PUBLIC 3a10 0 avahi_address_parse
PUBLIC 3b10 0 avahi_af_to_proto
PUBLIC 3b74 0 avahi_proto_to_string
PUBLIC 3c60 0 avahi_alternative_host_name
PUBLIC 3ec4 0 avahi_alternative_service_name
PUBLIC 4090 0 avahi_strerror
PUBLIC 4120 0 avahi_string_list_add_anonymous
PUBLIC 4160 0 avahi_string_list_add_arbitrary
PUBLIC 4210 0 avahi_string_list_add
PUBLIC 4274 0 avahi_string_list_free
PUBLIC 42b4 0 avahi_string_list_parse
PUBLIC 43c0 0 avahi_string_list_reverse
PUBLIC 4400 0 avahi_string_list_to_string
PUBLIC 4620 0 avahi_string_list_serialize
PUBLIC 4790 0 avahi_string_list_equal
PUBLIC 4850 0 avahi_string_list_add_many_va
PUBLIC 48e0 0 avahi_string_list_add_many
PUBLIC 4960 0 avahi_string_list_new
PUBLIC 4a00 0 avahi_string_list_new_va
PUBLIC 4a34 0 avahi_string_list_copy
PUBLIC 4ab0 0 avahi_string_list_new_from_array
PUBLIC 4b50 0 avahi_string_list_length
PUBLIC 4b90 0 avahi_string_list_add_vprintf
PUBLIC 4ce4 0 avahi_string_list_add_printf
PUBLIC 4d80 0 avahi_string_list_find
PUBLIC 4e30 0 avahi_string_list_add_pair
PUBLIC 4e84 0 avahi_string_list_add_pair_arbitrary
PUBLIC 4f70 0 avahi_string_list_get_pair
PUBLIC 5090 0 avahi_string_list_get_next
PUBLIC 50d0 0 avahi_string_list_get_text
PUBLIC 5110 0 avahi_string_list_get_size
PUBLIC 5150 0 avahi_string_list_get_service_cookie
PUBLIC 51e0 0 avahi_unescape_label
PUBLIC 53e0 0 avahi_escape_label
PUBLIC 5674 0 avahi_normalize_name
PUBLIC 5840 0 avahi_normalize_name_strdup
PUBLIC 58e0 0 avahi_domain_equal
PUBLIC 5a80 0 avahi_is_valid_service_type_generic
PUBLIC 5b74 0 avahi_is_valid_service_type_strict
PUBLIC 5cb0 0 avahi_get_type_from_subtype
PUBLIC 5e70 0 avahi_is_valid_service_subtype
PUBLIC 5ec0 0 avahi_is_valid_domain_name
PUBLIC 5fe0 0 avahi_is_valid_service_name
PUBLIC 6050 0 avahi_is_valid_host_name
PUBLIC 6124 0 avahi_domain_hash
PUBLIC 6224 0 avahi_service_name_join
PUBLIC 63e0 0 avahi_service_name_split
PUBLIC 6720 0 avahi_is_valid_fqdn
PUBLIC 6870 0 avahi_timeval_compare
PUBLIC 6904 0 avahi_timeval_diff
PUBLIC 69d0 0 avahi_timeval_add
PUBLIC 6a60 0 avahi_age
PUBLIC 6ad0 0 avahi_elapse_time
PUBLIC 6fe0 0 avahi_simple_poll_wakeup
PUBLIC 7590 0 avahi_simple_poll_free
PUBLIC 76d4 0 avahi_simple_poll_prepare
PUBLIC 7a30 0 avahi_simple_poll_run
PUBLIC 7b20 0 avahi_simple_poll_dispatch
PUBLIC 7cb0 0 avahi_simple_poll_iterate
PUBLIC 7d00 0 avahi_simple_poll_quit
PUBLIC 7d44 0 avahi_simple_poll_get
PUBLIC 7d80 0 avahi_simple_poll_set_func
PUBLIC 7de0 0 avahi_simple_poll_new
PUBLIC 7ed0 0 avahi_simple_poll_loop
PUBLIC 8020 0 avahi_threaded_poll_new
PUBLIC 80b0 0 avahi_threaded_poll_get
PUBLIC 80f0 0 avahi_threaded_poll_start
PUBLIC 81a0 0 avahi_threaded_poll_stop
PUBLIC 8260 0 avahi_threaded_poll_free
PUBLIC 8310 0 avahi_threaded_poll_quit
PUBLIC 8394 0 avahi_threaded_poll_lock
PUBLIC 8420 0 avahi_threaded_poll_unlock
PUBLIC 84b0 0 avahi_rlist_prepend
PUBLIC 8500 0 avahi_rlist_remove
PUBLIC 85a0 0 avahi_rlist_remove_by_link
PUBLIC 8670 0 avahi_utf8_valid
PUBLIC 8790 0 avahi_init_i18n
STACK CFI INIT 2e80 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2eb0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ef0 48 .cfa: sp 0 + .ra: x30
STACK CFI 2ef4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2efc x19: .cfa -16 + ^
STACK CFI 2f34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2f40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f50 4c .cfa: sp 0 + .ra: x30
STACK CFI 2f58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f64 x19: .cfa -16 + ^
STACK CFI INIT 2fa0 7c .cfa: sp 0 + .ra: x30
STACK CFI 2fb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2fcc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2fe0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2fec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2ff4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3020 d0 .cfa: sp 0 + .ra: x30
STACK CFI 3028 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3030 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3058 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3068 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 309c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 30ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 30b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 30f0 5c .cfa: sp 0 + .ra: x30
STACK CFI 3124 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3150 78 .cfa: sp 0 + .ra: x30
STACK CFI 3158 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3178 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3184 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3190 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3198 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 31d0 88 .cfa: sp 0 + .ra: x30
STACK CFI 31d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 31e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31ec x21: .cfa -16 + ^
STACK CFI 3214 x21: x21
STACK CFI 3220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3228 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3240 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3244 x21: x21
STACK CFI 3250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3260 c8 .cfa: sp 0 + .ra: x30
STACK CFI 3268 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3270 x21: .cfa -16 + ^
STACK CFI 3278 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 32d0 x19: x19 x20: x20
STACK CFI 32dc .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 32e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 32e8 x19: x19 x20: x20
STACK CFI 32f4 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 32fc .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3304 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 3330 20 .cfa: sp 0 + .ra: x30
STACK CFI 3338 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3348 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3350 128 .cfa: sp 0 + .ra: x30
STACK CFI 3358 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 3360 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 3364 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 3368 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 3394 x25: .cfa -112 + ^
STACK CFI 3410 x25: x25
STACK CFI 3418 x21: x21 x22: x22
STACK CFI 3420 x19: x19 x20: x20
STACK CFI 3424 x23: x23 x24: x24
STACK CFI 3428 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3430 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x29: .cfa -176 + ^
STACK CFI 3434 x21: x21 x22: x22
STACK CFI 343c x19: x19 x20: x20
STACK CFI 3440 x23: x23 x24: x24
STACK CFI 3444 x25: x25
STACK CFI 3448 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3450 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI 3474 x25: .cfa -112 + ^
STACK CFI INIT 3480 9c .cfa: sp 0 + .ra: x30
STACK CFI 3488 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 34f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 34f8 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI INIT 3520 78 .cfa: sp 0 + .ra: x30
STACK CFI 3528 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3534 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 356c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3574 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 35a0 9c .cfa: sp 0 + .ra: x30
STACK CFI 35a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 35ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 35fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3640 29c .cfa: sp 0 + .ra: x30
STACK CFI 3648 .cfa: sp 304 +
STACK CFI 364c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3654 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3674 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3678 x23: .cfa -16 + ^
STACK CFI 37dc x21: x21 x22: x22
STACK CFI 37e0 x23: x23
STACK CFI 37e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 37ec .cfa: sp 304 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 382c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3834 .cfa: sp 304 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 3858 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 385c x23: .cfa -16 + ^
STACK CFI 3860 x21: x21 x22: x22 x23: x23
STACK CFI 3884 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3888 x23: .cfa -16 + ^
STACK CFI 388c x21: x21 x22: x22 x23: x23
STACK CFI 38b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 38b4 x23: .cfa -16 + ^
STACK CFI INIT 38e0 64 .cfa: sp 0 + .ra: x30
STACK CFI 391c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3944 cc .cfa: sp 0 + .ra: x30
STACK CFI 394c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3958 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 399c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 39a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3a10 100 .cfa: sp 0 + .ra: x30
STACK CFI 3a18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3a24 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 3a70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3a78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3b10 64 .cfa: sp 0 + .ra: x30
STACK CFI 3b4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3b74 6c .cfa: sp 0 + .ra: x30
STACK CFI 3bb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3be0 78 .cfa: sp 0 + .ra: x30
STACK CFI 3be8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3bf0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3c30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c38 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3c60 264 .cfa: sp 0 + .ra: x30
STACK CFI 3c68 .cfa: sp 416 +
STACK CFI 3c74 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c9c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3d7c x21: x21 x22: x22
STACK CFI 3da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3db0 .cfa: sp 416 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3e54 x21: x21 x22: x22
STACK CFI 3e5c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3e6c x21: x21 x22: x22
STACK CFI 3e74 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3e78 x21: x21 x22: x22
STACK CFI 3e9c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 3ec4 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 3ecc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3ed8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3f94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3f9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4090 84 .cfa: sp 0 + .ra: x30
STACK CFI 4098 .cfa: sp 464 + .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI 40ac x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 40e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 40f0 .cfa: sp 464 + .ra: .cfa -456 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x29: .cfa -464 + ^
STACK CFI 40fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4120 40 .cfa: sp 0 + .ra: x30
STACK CFI 4128 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4130 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4160 a8 .cfa: sp 0 + .ra: x30
STACK CFI 4168 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4178 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 41b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 41c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 41e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 41e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4210 64 .cfa: sp 0 + .ra: x30
STACK CFI 4218 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4220 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4250 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4274 40 .cfa: sp 0 + .ra: x30
STACK CFI 4284 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 428c x19: .cfa -16 + ^
STACK CFI 42a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 42b4 108 .cfa: sp 0 + .ra: x30
STACK CFI 42bc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 42cc x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 4348 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4350 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 436c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4374 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 43c0 38 .cfa: sp 0 + .ra: x30
STACK CFI 43c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4400 218 .cfa: sp 0 + .ra: x30
STACK CFI 4408 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4414 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 45e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4620 168 .cfa: sp 0 + .ra: x30
STACK CFI 4628 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4630 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4638 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 463c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4648 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 46ec x19: x19 x20: x20
STACK CFI 46f0 x23: x23 x24: x24
STACK CFI 46f4 x25: x25 x26: x26
STACK CFI 4700 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4708 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 4754 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 475c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 4760 x19: x19 x20: x20
STACK CFI 476c x23: x23 x24: x24
STACK CFI 4770 x25: x25 x26: x26
STACK CFI 4774 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 477c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4790 bc .cfa: sp 0 + .ra: x30
STACK CFI 4798 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4838 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4850 88 .cfa: sp 0 + .ra: x30
STACK CFI 4858 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4864 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 48c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 48c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 48e0 78 .cfa: sp 0 + .ra: x30
STACK CFI 48e8 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 4950 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4960 98 .cfa: sp 0 + .ra: x30
STACK CFI 4968 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 49e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 49e8 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 49f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a00 34 .cfa: sp 0 + .ra: x30
STACK CFI 4a10 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a34 78 .cfa: sp 0 + .ra: x30
STACK CFI 4a44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a50 x19: .cfa -16 + ^
STACK CFI 4a88 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4a90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4a98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4ab0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 4ab8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4ac4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4b00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4b08 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4b2c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4b50 40 .cfa: sp 0 + .ra: x30
STACK CFI 4b58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4b78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4b80 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4b84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4b90 154 .cfa: sp 0 + .ra: x30
STACK CFI 4b98 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 4ba0 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4ba4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4bb0 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4bb8 x27: .cfa -112 + ^
STACK CFI 4bd8 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 4c5c x23: x23 x24: x24
STACK CFI 4c64 x21: x21 x22: x22
STACK CFI 4c6c x19: x19 x20: x20
STACK CFI 4c70 x25: x25 x26: x26
STACK CFI 4c74 x27: x27
STACK CFI 4c78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4c80 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x29: .cfa -192 + ^
STACK CFI 4c98 x19: x19 x20: x20
STACK CFI 4c9c x21: x21 x22: x22
STACK CFI 4ca0 x23: x23 x24: x24
STACK CFI 4ca4 x25: x25 x26: x26
STACK CFI 4ca8 x27: x27
STACK CFI 4cac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4cb4 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI 4cd8 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 4cdc x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 4ce0 x27: .cfa -112 + ^
STACK CFI INIT 4ce4 98 .cfa: sp 0 + .ra: x30
STACK CFI 4cec .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 4d50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4d58 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI INIT 4d80 b0 .cfa: sp 0 + .ra: x30
STACK CFI 4d88 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4d94 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4e04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4e0c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4e30 54 .cfa: sp 0 + .ra: x30
STACK CFI 4e5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4e84 e8 .cfa: sp 0 + .ra: x30
STACK CFI 4e8c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4e98 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4eac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4eb4 x23: .cfa -16 + ^
STACK CFI 4f14 x19: x19 x20: x20
STACK CFI 4f1c x21: x21 x22: x22
STACK CFI 4f20 x23: x23
STACK CFI 4f24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4f2c .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4f30 x21: x21 x22: x22
STACK CFI 4f34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4f3c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4f60 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4f64 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4f68 x23: .cfa -16 + ^
STACK CFI INIT 4f70 118 .cfa: sp 0 + .ra: x30
STACK CFI 4f78 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4f8c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 5020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5028 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5090 40 .cfa: sp 0 + .ra: x30
STACK CFI 50a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 50d0 40 .cfa: sp 0 + .ra: x30
STACK CFI 50e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5110 40 .cfa: sp 0 + .ra: x30
STACK CFI 5128 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5150 90 .cfa: sp 0 + .ra: x30
STACK CFI 5158 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5168 x19: .cfa -32 + ^
STACK CFI 51cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 51d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 51e0 1fc .cfa: sp 0 + .ra: x30
STACK CFI 51e8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5200 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 52c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 52c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 5374 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 537c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 53e0 294 .cfa: sp 0 + .ra: x30
STACK CFI 53e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 54d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 54d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 55a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 55b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5674 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 567c .cfa: sp 176 +
STACK CFI 5688 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 569c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 56a4 x25: .cfa -16 + ^
STACK CFI 5798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 57a0 .cfa: sp 176 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5840 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5848 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 585c .cfa: sp 1056 + x19: .cfa -16 + ^
STACK CFI 58a8 .cfa: sp 32 +
STACK CFI 58b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 58b8 .cfa: sp 1056 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 58e0 19c .cfa: sp 0 + .ra: x30
STACK CFI 58e8 .cfa: sp 208 +
STACK CFI 58f4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 591c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5928 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5984 x19: x19 x20: x20
STACK CFI 598c x21: x21 x22: x22
STACK CFI 5990 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5994 x19: x19 x20: x20
STACK CFI 599c x21: x21 x22: x22
STACK CFI 59c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 59c8 .cfa: sp 208 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 59d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5a18 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 5a1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5a20 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5a24 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 5a48 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5a4c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5a50 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 5a74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5a78 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 5a80 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5a88 .cfa: sp 128 +
STACK CFI 5a94 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5aa0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5b3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5b44 .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5b74 138 .cfa: sp 0 + .ra: x30
STACK CFI 5b7c .cfa: sp 128 +
STACK CFI 5b88 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5b94 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5bf0 .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5cb0 1bc .cfa: sp 0 + .ra: x30
STACK CFI 5cb8 .cfa: sp 144 +
STACK CFI 5cc4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5cdc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5d00 x21: .cfa -16 + ^
STACK CFI 5dfc x21: x21
STACK CFI 5e00 x21: .cfa -16 + ^
STACK CFI 5e04 x21: x21
STACK CFI 5e30 x19: x19 x20: x20
STACK CFI 5e34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5e3c .cfa: sp 144 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5e60 x21: .cfa -16 + ^
STACK CFI 5e64 x21: x21
STACK CFI 5e68 x21: .cfa -16 + ^
STACK CFI INIT 5e70 4c .cfa: sp 0 + .ra: x30
STACK CFI 5e78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5e8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5e98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5ec0 11c .cfa: sp 0 + .ra: x30
STACK CFI 5ec8 .cfa: sp 144 +
STACK CFI 5ed4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5eec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5f08 x21: .cfa -16 + ^
STACK CFI 5f38 x21: x21
STACK CFI 5f70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f78 .cfa: sp 144 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5f90 x21: x21
STACK CFI 5f98 x21: .cfa -16 + ^
STACK CFI 5f9c x21: x21
STACK CFI 5fc8 x21: .cfa -16 + ^
STACK CFI 5fcc x21: x21
STACK CFI 5fd0 x21: .cfa -16 + ^
STACK CFI 5fd8 x21: x21
STACK CFI INIT 5fe0 6c .cfa: sp 0 + .ra: x30
STACK CFI 5fe8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ff0 x19: .cfa -16 + ^
STACK CFI 6020 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6028 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6050 d4 .cfa: sp 0 + .ra: x30
STACK CFI 6058 .cfa: sp 128 +
STACK CFI 6064 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6070 x19: .cfa -16 + ^
STACK CFI 60c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 60cc .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6124 100 .cfa: sp 0 + .ra: x30
STACK CFI 612c .cfa: sp 144 +
STACK CFI 6138 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6154 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 615c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 61bc x21: x21 x22: x22
STACK CFI 61e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 61f0 .cfa: sp 144 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 61f8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 621c x21: x21 x22: x22
STACK CFI 6220 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 6224 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 622c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6240 .cfa: sp 2400 + x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6350 .cfa: sp 64 +
STACK CFI 6360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6368 .cfa: sp 2400 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 63e0 340 .cfa: sp 0 + .ra: x30
STACK CFI 63e8 .cfa: sp 224 +
STACK CFI 63f4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6408 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 641c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6434 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6470 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 647c x27: .cfa -16 + ^
STACK CFI 6530 x21: x21 x22: x22
STACK CFI 6534 x27: x27
STACK CFI 655c x19: x19 x20: x20
STACK CFI 6564 x25: x25 x26: x26
STACK CFI 6568 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 6570 .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 65d4 x21: x21 x22: x22
STACK CFI 65dc x27: x27
STACK CFI 65e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 65ec x21: x21 x22: x22
STACK CFI 65f4 x27: x27
STACK CFI 65fc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6600 x27: .cfa -16 + ^
STACK CFI 6604 x21: x21 x22: x22 x27: x27
STACK CFI 6628 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 662c x27: .cfa -16 + ^
STACK CFI 6630 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27
STACK CFI 6654 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6658 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 665c x27: .cfa -16 + ^
STACK CFI 6660 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27
STACK CFI 6684 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6688 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 668c x27: .cfa -16 + ^
STACK CFI 6690 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27
STACK CFI 66b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 66b8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 66bc x27: .cfa -16 + ^
STACK CFI 66c0 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27
STACK CFI 66e4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 66e8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 66ec x27: .cfa -16 + ^
STACK CFI 66f0 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27
STACK CFI 6714 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6718 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 671c x27: .cfa -16 + ^
STACK CFI INIT 6720 150 .cfa: sp 0 + .ra: x30
STACK CFI 6728 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6730 .cfa: sp 1168 +
STACK CFI 6750 x19: .cfa -32 + ^
STACK CFI 6754 x20: .cfa -24 + ^
STACK CFI 678c x19: x19
STACK CFI 6790 x20: x20
STACK CFI 6794 .cfa: sp 48 +
STACK CFI 6798 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 67a0 .cfa: sp 1168 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 67b4 x21: .cfa -16 + ^
STACK CFI 6830 x21: x21
STACK CFI 6838 x21: .cfa -16 + ^
STACK CFI 683c x21: x21
STACK CFI 6864 x21: .cfa -16 + ^
STACK CFI 6868 x21: x21
STACK CFI 686c x21: .cfa -16 + ^
STACK CFI INIT 6870 94 .cfa: sp 0 + .ra: x30
STACK CFI 6878 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 68bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 68c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6904 cc .cfa: sp 0 + .ra: x30
STACK CFI 690c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6918 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 6980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6988 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 69d0 8c .cfa: sp 0 + .ra: x30
STACK CFI 6a34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6a60 68 .cfa: sp 0 + .ra: x30
STACK CFI 6a68 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6a70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6a9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6aa4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6ad0 128 .cfa: sp 0 + .ra: x30
STACK CFI 6ad8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6ae0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6ae4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6b08 x21: x21 x22: x22
STACK CFI 6b10 x19: x19 x20: x20
STACK CFI 6b14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6b1c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6b34 x23: .cfa -16 + ^
STACK CFI 6ba4 x21: x21 x22: x22
STACK CFI 6bac x19: x19 x20: x20
STACK CFI 6bb0 x23: x23
STACK CFI 6bb4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6bbc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6bd0 x23: x23
STACK CFI 6bf4 x23: .cfa -16 + ^
STACK CFI INIT 6c00 9c .cfa: sp 0 + .ra: x30
STACK CFI 6c08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6c2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6c3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6ca0 1c .cfa: sp 0 + .ra: x30
STACK CFI 6ca8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6cb4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6cc0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 6cc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6cfc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6d04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6d0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6d1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6d64 30 .cfa: sp 0 + .ra: x30
STACK CFI 6d6c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6d94 d4 .cfa: sp 0 + .ra: x30
STACK CFI 6d9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6e00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6e08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6e70 30 .cfa: sp 0 + .ra: x30
STACK CFI 6e78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6ea0 30 .cfa: sp 0 + .ra: x30
STACK CFI 6ea8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6ed0 94 .cfa: sp 0 + .ra: x30
STACK CFI 6ed8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6ee0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6f38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6f40 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6f64 78 .cfa: sp 0 + .ra: x30
STACK CFI 6f6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f74 x19: .cfa -16 + ^
STACK CFI 6f94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6f9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6fb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6fb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6fe0 6c .cfa: sp 0 + .ra: x30
STACK CFI 6fe8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6ff4 x19: .cfa -32 + ^
STACK CFI 7020 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7028 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7050 9c .cfa: sp 0 + .ra: x30
STACK CFI 7058 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7060 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 709c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 70a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 70f0 90 .cfa: sp 0 + .ra: x30
STACK CFI 70f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7100 x19: .cfa -16 + ^
STACK CFI 7130 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7138 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7180 10c .cfa: sp 0 + .ra: x30
STACK CFI 7188 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7198 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 7218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7220 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7290 e8 .cfa: sp 0 + .ra: x30
STACK CFI 7298 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 72a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 72e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 72f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 730c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7380 b8 .cfa: sp 0 + .ra: x30
STACK CFI 7388 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7390 x19: .cfa -16 + ^
STACK CFI 73e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 73f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7440 148 .cfa: sp 0 + .ra: x30
STACK CFI 7448 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7458 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 74f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 74f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7590 144 .cfa: sp 0 + .ra: x30
STACK CFI 7598 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 75a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7660 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7668 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 76d4 358 .cfa: sp 0 + .ra: x30
STACK CFI 76dc .cfa: sp 112 +
STACK CFI 76e8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 76f0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 770c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7814 x23: .cfa -16 + ^
STACK CFI 7850 x23: x23
STACK CFI 78a0 x21: x21 x22: x22
STACK CFI 78a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 78ac .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 794c x23: .cfa -16 + ^
STACK CFI 7950 x23: x23
STACK CFI 7998 x23: .cfa -16 + ^
STACK CFI 799c x23: x23
STACK CFI 79a8 x21: x21 x22: x22
STACK CFI 79cc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 79d0 x23: .cfa -16 + ^
STACK CFI 79d4 x23: x23
STACK CFI 79d8 x23: .cfa -16 + ^
STACK CFI 79dc x23: x23
STACK CFI 7a00 x23: .cfa -16 + ^
STACK CFI 7a04 x23: x23
STACK CFI 7a28 x23: .cfa -16 + ^
STACK CFI INIT 7a30 ec .cfa: sp 0 + .ra: x30
STACK CFI 7a38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7a40 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7ab0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7ab8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7acc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7ad4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7b20 18c .cfa: sp 0 + .ra: x30
STACK CFI 7b28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7b30 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7bc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7bd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7bec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7bf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7c1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7cb0 4c .cfa: sp 0 + .ra: x30
STACK CFI 7cb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7cc0 x19: .cfa -16 + ^
STACK CFI 7cd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7cdc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7cf4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7d00 44 .cfa: sp 0 + .ra: x30
STACK CFI 7d1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7d44 3c .cfa: sp 0 + .ra: x30
STACK CFI 7d58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7d80 58 .cfa: sp 0 + .ra: x30
STACK CFI 7db0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7de0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 7de8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7df4 x19: .cfa -16 + ^
STACK CFI 7ea0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7ea8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7ec0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7ed0 7c .cfa: sp 0 + .ra: x30
STACK CFI 7ed8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ee0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7f20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7f28 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7f50 64 .cfa: sp 0 + .ra: x30
STACK CFI 7f58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7f60 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7f70 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7fac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7fb4 64 .cfa: sp 0 + .ra: x30
STACK CFI 7fbc .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 7fc4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 8010 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8020 88 .cfa: sp 0 + .ra: x30
STACK CFI 8028 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8034 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8088 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 80a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 80b0 3c .cfa: sp 0 + .ra: x30
STACK CFI 80c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 80f0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 80f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8100 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8140 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8148 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 81a0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 81a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 81b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8210 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8260 ac .cfa: sp 0 + .ra: x30
STACK CFI 8268 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8270 x19: .cfa -16 + ^
STACK CFI 82a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 82a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8310 84 .cfa: sp 0 + .ra: x30
STACK CFI 8318 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8320 x19: .cfa -16 + ^
STACK CFI 8344 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 834c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8394 8c .cfa: sp 0 + .ra: x30
STACK CFI 839c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 83a4 x19: .cfa -16 + ^
STACK CFI 83d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 83d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8420 8c .cfa: sp 0 + .ra: x30
STACK CFI 8428 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8430 x19: .cfa -16 + ^
STACK CFI 845c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8464 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 84b0 48 .cfa: sp 0 + .ra: x30
STACK CFI 84b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 84c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 84f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8500 a0 .cfa: sp 0 + .ra: x30
STACK CFI 8508 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8510 x19: .cfa -16 + ^
STACK CFI 8568 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8570 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 85a0 cc .cfa: sp 0 + .ra: x30
STACK CFI 85a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 85b0 x19: .cfa -16 + ^
STACK CFI 85f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 85f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 861c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8624 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8670 120 .cfa: sp 0 + .ra: x30
STACK CFI 8678 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8768 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8770 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8790 6c .cfa: sp 0 + .ra: x30
STACK CFI 8798 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 87a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 87b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 87bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 87f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
