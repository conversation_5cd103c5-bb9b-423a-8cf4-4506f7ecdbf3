MODULE Linux arm64 1FA0102EB4DDAF56B056B2060F9CB4BC0 libpipewire-module-rtp-session.so
INFO CODE_ID 2E10A01FDDB456AFB056B2060F9CB4BC9B435064
PUBLIC f770 0 pipewire__module_init
STACK CFI INIT 3830 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3860 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 38a0 48 .cfa: sp 0 + .ra: x30
STACK CFI 38a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 38ac x19: .cfa -16 + ^
STACK CFI 38e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 38f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3900 340 .cfa: sp 0 + .ra: x30
STACK CFI 3908 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 39f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3a00 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3b44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3b9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3c1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3c40 50 .cfa: sp 0 + .ra: x30
STACK CFI 3c48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3c68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3c7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3c90 50 .cfa: sp 0 + .ra: x30
STACK CFI 3c98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ca0 x19: .cfa -16 + ^
STACK CFI 3cd8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3ce0 128 .cfa: sp 0 + .ra: x30
STACK CFI 3ce8 .cfa: sp 112 +
STACK CFI 3cfc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d10 x21: .cfa -16 + ^
STACK CFI 3dfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3e04 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3e10 114 .cfa: sp 0 + .ra: x30
STACK CFI 3e18 .cfa: sp 96 +
STACK CFI 3e28 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f20 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3f24 1c .cfa: sp 0 + .ra: x30
STACK CFI 3f2c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f40 20 .cfa: sp 0 + .ra: x30
STACK CFI 3f48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f60 18 .cfa: sp 0 + .ra: x30
STACK CFI 3f68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3f80 18 .cfa: sp 0 + .ra: x30
STACK CFI 3f88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3f90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3fa0 2c .cfa: sp 0 + .ra: x30
STACK CFI 3fa8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3fc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3fd0 58 .cfa: sp 0 + .ra: x30
STACK CFI 3fd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4000 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 401c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4020 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4030 b8 .cfa: sp 0 + .ra: x30
STACK CFI 4038 .cfa: sp 48 +
STACK CFI 4044 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 40dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 40e4 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 40f0 128 .cfa: sp 0 + .ra: x30
STACK CFI 40f8 .cfa: sp 80 +
STACK CFI 40fc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4104 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 41fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4204 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4220 4fc .cfa: sp 0 + .ra: x30
STACK CFI 4228 .cfa: sp 160 +
STACK CFI 422c .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 4234 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 4248 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 4250 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 4254 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4258 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 431c v8: .cfa -48 + ^
STACK CFI 43cc v8: v8
STACK CFI 4440 x21: x21 x22: x22
STACK CFI 4444 x23: x23 x24: x24
STACK CFI 4448 x25: x25 x26: x26
STACK CFI 444c x27: x27 x28: x28
STACK CFI 4458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4460 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 4550 v8: .cfa -48 + ^
STACK CFI 45a0 v8: v8
STACK CFI 45d0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4604 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 460c .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 4624 v8: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 4628 v8: v8
STACK CFI 4658 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 466c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 468c .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 4720 510 .cfa: sp 0 + .ra: x30
STACK CFI 4728 .cfa: sp 160 +
STACK CFI 472c .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 4734 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 4748 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 4750 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 4754 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4758 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 481c v8: .cfa -48 + ^
STACK CFI 48cc v8: v8
STACK CFI 4940 x21: x21 x22: x22
STACK CFI 4944 x23: x23 x24: x24
STACK CFI 4948 x25: x25 x26: x26
STACK CFI 494c x27: x27 x28: x28
STACK CFI 4958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4960 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 4a54 v8: .cfa -48 + ^
STACK CFI 4aa8 v8: v8
STACK CFI 4ad8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4b0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4b14 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 4b2c v8: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 4b30 v8: v8
STACK CFI 4b60 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4b98 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 4c30 6f0 .cfa: sp 0 + .ra: x30
STACK CFI 4c38 .cfa: sp 160 +
STACK CFI 4c3c .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4c44 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 4c54 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4c5c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4c64 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4ca4 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 4d3c v8: .cfa -32 + ^
STACK CFI 4e04 v8: v8
STACK CFI 4e44 x19: x19 x20: x20
STACK CFI 4e4c x21: x21 x22: x22
STACK CFI 4e50 x23: x23 x24: x24
STACK CFI 4e54 x27: x27 x28: x28
STACK CFI 4e60 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 4e68 .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 4eb4 v8: .cfa -32 + ^
STACK CFI 4ecc v8: v8
STACK CFI 502c x27: x27 x28: x28
STACK CFI 5058 x19: x19 x20: x20
STACK CFI 505c x21: x21 x22: x22
STACK CFI 5060 x23: x23 x24: x24
STACK CFI 5068 v8: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 50cc v8: v8
STACK CFI 5154 x27: x27 x28: x28
STACK CFI 518c x19: x19 x20: x20
STACK CFI 5194 x21: x21 x22: x22
STACK CFI 5198 x23: x23 x24: x24
STACK CFI 519c x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 51b4 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 520c x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5260 x19: x19 x20: x20
STACK CFI 5268 x21: x21 x22: x22
STACK CFI 526c x23: x23 x24: x24
STACK CFI 5288 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5310 x19: x19 x20: x20
STACK CFI 5318 x21: x21 x22: x22
STACK CFI 531c x23: x23 x24: x24
STACK CFI INIT 5320 62c .cfa: sp 0 + .ra: x30
STACK CFI 5328 .cfa: sp 288 +
STACK CFI 5334 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 533c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 535c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5368 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 536c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5370 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5668 x21: x21 x22: x22
STACK CFI 566c x23: x23 x24: x24
STACK CFI 5670 x25: x25 x26: x26
STACK CFI 5674 x27: x27 x28: x28
STACK CFI 569c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 56a4 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5714 x21: x21 x22: x22
STACK CFI 5718 x23: x23 x24: x24
STACK CFI 571c x25: x25 x26: x26
STACK CFI 5720 x27: x27 x28: x28
STACK CFI 5724 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5854 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 58ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58d0 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 58e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5938 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 593c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5940 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5944 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5948 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 5950 76c .cfa: sp 0 + .ra: x30
STACK CFI 5958 .cfa: sp 272 +
STACK CFI 5964 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 596c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 598c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5994 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 599c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5a44 x21: x21 x22: x22
STACK CFI 5a48 x23: x23 x24: x24
STACK CFI 5a4c x25: x25 x26: x26
STACK CFI 5a58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a60 .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 5a64 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5d1c x27: x27 x28: x28
STACK CFI 5d20 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5d24 x27: x27 x28: x28
STACK CFI 5d28 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 5d78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5d80 .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 5d98 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5da4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5e24 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5e54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5e78 .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 60a8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 60ac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 60b0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 60b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 60b8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 60c0 74c .cfa: sp 0 + .ra: x30
STACK CFI 60c8 .cfa: sp 176 +
STACK CFI 60cc .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 60d8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 60e8 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 60f0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 60f8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 60fc x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 61c4 v8: .cfa -48 + ^
STACK CFI 6294 v8: v8
STACK CFI 62dc x19: x19 x20: x20
STACK CFI 62e4 x21: x21 x22: x22
STACK CFI 62e8 x23: x23 x24: x24
STACK CFI 62ec x25: x25 x26: x26
STACK CFI 62f8 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 6300 .cfa: sp 176 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 634c v8: .cfa -48 + ^
STACK CFI 6364 v8: v8
STACK CFI 6570 x19: x19 x20: x20
STACK CFI 6574 x21: x21 x22: x22
STACK CFI 6578 x23: x23 x24: x24
STACK CFI 657c x25: x25 x26: x26
STACK CFI 6584 v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 65f4 v8: v8
STACK CFI 666c x19: x19 x20: x20
STACK CFI 6674 x21: x21 x22: x22
STACK CFI 6678 x23: x23 x24: x24
STACK CFI 667c x25: x25 x26: x26
STACK CFI 6680 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 6698 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 66f0 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 6744 x19: x19 x20: x20
STACK CFI 674c x21: x21 x22: x22
STACK CFI 6750 x23: x23 x24: x24
STACK CFI 6754 x25: x25 x26: x26
STACK CFI 6770 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 67f8 x19: x19 x20: x20
STACK CFI 6800 x21: x21 x22: x22
STACK CFI 6804 x23: x23 x24: x24
STACK CFI 6808 x25: x25 x26: x26
STACK CFI INIT 6810 360 .cfa: sp 0 + .ra: x30
STACK CFI 6818 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 682c .cfa: sp 1568 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6894 .cfa: sp 96 +
STACK CFI 68a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 68ac .cfa: sp 1568 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 68b0 x25: .cfa -32 + ^
STACK CFI 68b8 x26: .cfa -24 + ^
STACK CFI 68c0 x27: .cfa -16 + ^
STACK CFI 68c8 x28: .cfa -8 + ^
STACK CFI 6ab0 x25: x25
STACK CFI 6ab4 x26: x26
STACK CFI 6ab8 x27: x27
STACK CFI 6abc x28: x28
STACK CFI 6ac0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6b5c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6b60 x25: .cfa -32 + ^
STACK CFI 6b64 x26: .cfa -24 + ^
STACK CFI 6b68 x27: .cfa -16 + ^
STACK CFI 6b6c x28: .cfa -8 + ^
STACK CFI INIT 6b70 3c0 .cfa: sp 0 + .ra: x30
STACK CFI 6b78 .cfa: sp 128 +
STACK CFI 6b7c .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6b84 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 6b98 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 6ba4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 6bac x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6bb0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6cd0 x21: x21 x22: x22
STACK CFI 6cd4 x23: x23 x24: x24
STACK CFI 6cd8 x25: x25 x26: x26
STACK CFI 6cdc x27: x27 x28: x28
STACK CFI 6ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6ce8 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 6e00 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6e34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6e3c .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 6e54 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 6ef8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6f30 30c .cfa: sp 0 + .ra: x30
STACK CFI 6f38 .cfa: sp 96 +
STACK CFI 6f44 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6fcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6ff0 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7038 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7040 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7240 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 7248 .cfa: sp 448 +
STACK CFI 7258 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 726c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7274 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7284 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 73f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 73f8 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7704 20c .cfa: sp 0 + .ra: x30
STACK CFI 770c .cfa: sp 96 +
STACK CFI 7718 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7724 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 772c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7738 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 78a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 78a8 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 78f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 78fc .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7910 b0 .cfa: sp 0 + .ra: x30
STACK CFI 7918 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7920 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7928 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7934 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 79b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 79b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 79c0 124 .cfa: sp 0 + .ra: x30
STACK CFI 79c8 .cfa: sp 96 +
STACK CFI 79d4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 79dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 79e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 79f4 x23: .cfa -16 + ^
STACK CFI 7ad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7ad8 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7ae4 74 .cfa: sp 0 + .ra: x30
STACK CFI 7aec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7af8 x19: .cfa -16 + ^
STACK CFI 7b3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7b44 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7b50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7b60 6c .cfa: sp 0 + .ra: x30
STACK CFI 7b68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7b70 x19: .cfa -16 + ^
STACK CFI 7bb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7bb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7bc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7bd0 50 .cfa: sp 0 + .ra: x30
STACK CFI 7bd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7be0 x19: .cfa -16 + ^
STACK CFI 7c18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7c20 170 .cfa: sp 0 + .ra: x30
STACK CFI 7c28 .cfa: sp 112 +
STACK CFI 7c30 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7c38 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7c44 x21: .cfa -16 + ^
STACK CFI 7d84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7d8c .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7d90 98 .cfa: sp 0 + .ra: x30
STACK CFI 7d98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7da0 x19: .cfa -16 + ^
STACK CFI 7e20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7e30 64 .cfa: sp 0 + .ra: x30
STACK CFI 7e38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e40 x19: .cfa -16 + ^
STACK CFI 7e8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7e94 6e0 .cfa: sp 0 + .ra: x30
STACK CFI 7e9c .cfa: sp 320 +
STACK CFI 7ea8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7eb0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7eb8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7ec8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7ed4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7f1c x27: .cfa -16 + ^
STACK CFI 802c x27: x27
STACK CFI 8034 x27: .cfa -16 + ^
STACK CFI 806c x27: x27
STACK CFI 80d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 80dc .cfa: sp 320 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 80f4 x27: .cfa -16 + ^
STACK CFI 826c x27: x27
STACK CFI 829c x27: .cfa -16 + ^
STACK CFI 856c x27: x27
STACK CFI 8570 x27: .cfa -16 + ^
STACK CFI INIT 8574 54 .cfa: sp 0 + .ra: x30
STACK CFI 857c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8584 x19: .cfa -16 + ^
STACK CFI 85c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 85d0 104 .cfa: sp 0 + .ra: x30
STACK CFI 85d8 .cfa: sp 96 +
STACK CFI 85dc .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 85e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 85f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 862c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8634 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 86cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 86d4 118 .cfa: sp 0 + .ra: x30
STACK CFI 86dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 86e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 87dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 87e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 87f0 50 .cfa: sp 0 + .ra: x30
STACK CFI 87f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8800 x19: .cfa -16 + ^
STACK CFI 8838 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8840 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 8848 .cfa: sp 192 +
STACK CFI 8854 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8868 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8898 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 88a0 .cfa: sp 192 + .ra: .cfa -88 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 88a4 x27: .cfa -16 + ^
STACK CFI 88ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 88b8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 88c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 899c x19: x19 x20: x20
STACK CFI 89a0 x21: x21 x22: x22
STACK CFI 89a8 x25: x25 x26: x26
STACK CFI 89ac x27: x27
STACK CFI 89b0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 89b8 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 89c0 x19: x19 x20: x20
STACK CFI 89c4 x21: x21 x22: x22
STACK CFI 89c8 x25: x25 x26: x26
STACK CFI 89cc x27: x27
STACK CFI 89d0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8a84 x19: x19 x20: x20
STACK CFI 8a8c x21: x21 x22: x22
STACK CFI 8a9c x25: x25 x26: x26
STACK CFI 8aa4 x27: x27
STACK CFI 8aac .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 8ac4 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 8adc x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27
STACK CFI 8ae0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8ae4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8ae8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8aec x27: .cfa -16 + ^
STACK CFI INIT 8af0 19c .cfa: sp 0 + .ra: x30
STACK CFI 8af8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b18 x19: .cfa -16 + ^
STACK CFI 8b38 x19: x19
STACK CFI 8b3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8b44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b48 x19: .cfa -16 + ^
STACK CFI 8b84 x19: x19
STACK CFI 8b8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8bb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8bd0 x19: x19
STACK CFI 8bd8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8bfc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8c00 x19: .cfa -16 + ^
STACK CFI 8c2c x19: x19
STACK CFI 8c34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8c58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8c90 42c .cfa: sp 0 + .ra: x30
STACK CFI 8cb0 .cfa: sp 64 +
STACK CFI 8cb4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8cbc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8cc4 x21: .cfa -16 + ^
STACK CFI 8d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8d78 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 8f28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8f30 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 90c0 378 .cfa: sp 0 + .ra: x30
STACK CFI 90c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 90d8 .cfa: sp 2672 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9128 x21: .cfa -64 + ^
STACK CFI 9130 x22: .cfa -56 + ^
STACK CFI 91a4 x21: x21
STACK CFI 91a8 x22: x22
STACK CFI 91c8 .cfa: sp 96 +
STACK CFI 91d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 91dc .cfa: sp 2672 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 9220 x25: .cfa -32 + ^
STACK CFI 9228 x26: .cfa -24 + ^
STACK CFI 9234 x27: .cfa -16 + ^
STACK CFI 92a8 x21: x21
STACK CFI 92ac x22: x22
STACK CFI 92b0 x25: x25
STACK CFI 92b4 x26: x26
STACK CFI 92b8 x27: x27
STACK CFI 9324 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 93b8 x21: x21
STACK CFI 93bc x22: x22
STACK CFI 93c0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 93f4 x21: x21
STACK CFI 93f8 x22: x22
STACK CFI 93fc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9418 x21: x21
STACK CFI 941c x22: x22
STACK CFI 9424 x21: .cfa -64 + ^
STACK CFI 9428 x22: .cfa -56 + ^
STACK CFI 942c x25: .cfa -32 + ^
STACK CFI 9430 x26: .cfa -24 + ^
STACK CFI 9434 x27: .cfa -16 + ^
STACK CFI INIT 9440 28 .cfa: sp 0 + .ra: x30
STACK CFI 9448 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9454 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 945c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9460 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9470 3ac .cfa: sp 0 + .ra: x30
STACK CFI 9478 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9488 .cfa: sp 2800 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9504 x23: .cfa -48 + ^
STACK CFI 950c x24: .cfa -40 + ^
STACK CFI 9514 x25: .cfa -32 + ^
STACK CFI 9520 x26: .cfa -24 + ^
STACK CFI 952c x27: .cfa -16 + ^
STACK CFI 95b4 x23: x23
STACK CFI 95b8 x24: x24
STACK CFI 95bc x25: x25
STACK CFI 95c0 x26: x26
STACK CFI 95c4 x27: x27
STACK CFI 95e4 .cfa: sp 96 +
STACK CFI 95f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 95f8 .cfa: sp 2800 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 9620 x23: .cfa -48 + ^
STACK CFI 9640 x24: .cfa -40 + ^
STACK CFI 964c x25: .cfa -32 + ^
STACK CFI 9654 x26: .cfa -24 + ^
STACK CFI 9660 x27: .cfa -16 + ^
STACK CFI 9734 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 9808 x23: .cfa -48 + ^
STACK CFI 980c x24: .cfa -40 + ^
STACK CFI 9810 x25: .cfa -32 + ^
STACK CFI 9814 x26: .cfa -24 + ^
STACK CFI 9818 x27: .cfa -16 + ^
STACK CFI INIT 9820 28 .cfa: sp 0 + .ra: x30
STACK CFI 9828 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9834 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 983c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9840 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9850 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 9858 .cfa: sp 64 +
STACK CFI 985c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9864 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9878 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 991c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9924 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9a14 758 .cfa: sp 0 + .ra: x30
STACK CFI 9a1c .cfa: sp 304 +
STACK CFI 9a28 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9a30 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9a50 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9a5c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9a60 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9a64 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9b34 x21: x21 x22: x22
STACK CFI 9b38 x23: x23 x24: x24
STACK CFI 9b3c x25: x25 x26: x26
STACK CFI 9b40 x27: x27 x28: x28
STACK CFI 9b68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9b70 .cfa: sp 304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 9b94 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9bec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9c10 .cfa: sp 304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 9c28 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a158 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a15c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a160 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a164 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a168 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT a170 cec .cfa: sp 0 + .ra: x30
STACK CFI a178 .cfa: sp 320 +
STACK CFI a184 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI a18c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI a1a8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI a1b0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI a1cc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI a1f8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI a530 x21: x21 x22: x22
STACK CFI a534 x23: x23 x24: x24
STACK CFI a538 x25: x25 x26: x26
STACK CFI a53c x27: x27 x28: x28
STACK CFI a564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a56c .cfa: sp 320 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI a72c v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI a730 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI a738 v12: .cfa -16 + ^ v13: .cfa -8 + ^
STACK CFI a85c v10: v10 v11: v11
STACK CFI a864 v8: v8 v9: v9
STACK CFI a868 v12: v12 v13: v13
STACK CFI a870 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI a914 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9
STACK CFI a9d4 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI aa0c v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI aa38 x21: x21 x22: x22
STACK CFI aa3c x23: x23 x24: x24
STACK CFI aa40 x25: x25 x26: x26
STACK CFI aa48 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI aa88 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9
STACK CFI ab0c x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI ab44 x23: x23 x24: x24
STACK CFI ab4c x25: x25 x26: x26
STACK CFI ab50 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI abb0 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI abc8 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI ac38 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI ac60 x21: x21 x22: x22
STACK CFI ac68 x23: x23 x24: x24
STACK CFI ac6c x25: x25 x26: x26
STACK CFI ac70 x27: x27 x28: x28
STACK CFI ac74 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI aca0 x21: x21 x22: x22
STACK CFI aca8 x23: x23 x24: x24
STACK CFI acac x25: x25 x26: x26
STACK CFI acb0 x27: x27 x28: x28
STACK CFI acb4 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI accc x27: x27 x28: x28
STACK CFI ace4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI ad2c x27: x27 x28: x28
STACK CFI ad80 x21: x21 x22: x22
STACK CFI ad88 x23: x23 x24: x24
STACK CFI ad8c x25: x25 x26: x26
STACK CFI ad90 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI ada8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI adac x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI adb0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI adb4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI adb8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI adbc v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI adc0 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI adc4 v12: .cfa -16 + ^ v13: .cfa -8 + ^
STACK CFI adc8 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI adf4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI ae20 x21: x21 x22: x22
STACK CFI ae28 x23: x23 x24: x24
STACK CFI ae2c x25: x25 x26: x26
STACK CFI ae30 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT ae60 2b2c .cfa: sp 0 + .ra: x30
STACK CFI ae68 .cfa: sp 480 +
STACK CFI ae7c .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI ae94 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI ae9c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI aeb4 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI b610 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b618 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT d990 1178 .cfa: sp 0 + .ra: x30
STACK CFI d998 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI d9ac .cfa: sp 1360 + x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI d9f0 x21: .cfa -96 + ^
STACK CFI d9f8 x22: .cfa -88 + ^
STACK CFI d9fc x25: .cfa -64 + ^
STACK CFI da04 x26: .cfa -56 + ^
STACK CFI db24 v8: .cfa -32 + ^
STACK CFI db2c v9: .cfa -24 + ^
STACK CFI db30 v10: .cfa -16 + ^
STACK CFI db34 v11: .cfa -8 + ^
STACK CFI e12c x21: x21
STACK CFI e130 x22: x22
STACK CFI e134 x25: x25
STACK CFI e138 x26: x26
STACK CFI e13c v8: v8
STACK CFI e140 v9: v9
STACK CFI e144 v10: v10
STACK CFI e148 v11: v11
STACK CFI e168 .cfa: sp 128 +
STACK CFI e17c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI e184 .cfa: sp 1360 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI e198 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI e20c v10: v10 v11: v11 v8: v8 v9: v9
STACK CFI e2bc v8: .cfa -32 + ^
STACK CFI e2c4 v9: .cfa -24 + ^
STACK CFI e2c8 v10: .cfa -16 + ^
STACK CFI e2cc v11: .cfa -8 + ^
STACK CFI e2e4 v10: v10 v11: v11 v8: v8 v9: v9
STACK CFI e35c v8: .cfa -32 + ^
STACK CFI e364 v9: .cfa -24 + ^
STACK CFI e36c v10: .cfa -16 + ^
STACK CFI e374 v11: .cfa -8 + ^
STACK CFI e3d4 x21: x21
STACK CFI e3d8 x22: x22
STACK CFI e3dc x25: x25
STACK CFI e3e0 x26: x26
STACK CFI e3e4 v8: v8
STACK CFI e3e8 v9: v9
STACK CFI e3ec v10: v10
STACK CFI e3f0 v11: v11
STACK CFI e408 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI e8e8 v10: v10 v11: v11 v8: v8 v9: v9
STACK CFI e8f0 x21: x21
STACK CFI e8f8 x22: x22
STACK CFI e8fc x25: x25
STACK CFI e900 x26: x26
STACK CFI e904 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI e954 v10: v10 v11: v11 v8: v8 v9: v9
STACK CFI ea68 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI ea90 v10: v10 v11: v11 v8: v8 v9: v9
STACK CFI ead8 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI eae8 x21: .cfa -96 + ^
STACK CFI eaec x22: .cfa -88 + ^
STACK CFI eaf0 x25: .cfa -64 + ^
STACK CFI eaf4 x26: .cfa -56 + ^
STACK CFI eaf8 v8: .cfa -32 + ^
STACK CFI eafc v9: .cfa -24 + ^
STACK CFI eb00 v10: .cfa -16 + ^
STACK CFI eb04 v11: .cfa -8 + ^
STACK CFI INIT eb10 c60 .cfa: sp 0 + .ra: x30
STACK CFI eb18 .cfa: sp 368 +
STACK CFI eb24 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI eb34 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ebbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI ebc4 .cfa: sp 368 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI ebe0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ebec x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f0e4 x25: x25 x26: x26
STACK CFI f0e8 x27: x27 x28: x28
STACK CFI f0ec x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f248 x25: x25 x26: x26
STACK CFI f24c x27: x27 x28: x28
STACK CFI f250 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f2c8 x25: x25 x26: x26
STACK CFI f2cc x27: x27 x28: x28
STACK CFI f2d0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f324 x25: x25 x26: x26
STACK CFI f328 x27: x27 x28: x28
STACK CFI f32c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f340 x25: x25 x26: x26
STACK CFI f344 x27: x27 x28: x28
STACK CFI f348 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f3f0 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f434 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f74c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f750 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f754 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT f770 f94 .cfa: sp 0 + .ra: x30
STACK CFI f778 .cfa: sp 240 +
STACK CFI f784 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f78c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f798 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI f82c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f834 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI fff8 x25: x25 x26: x26
STACK CFI 10000 x27: x27 x28: x28
STACK CFI 100ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 100f4 .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 10124 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10210 x25: x25 x26: x26
STACK CFI 10214 x27: x27 x28: x28
STACK CFI 10218 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 102e0 x25: x25 x26: x26
STACK CFI 102e4 x27: x27 x28: x28
STACK CFI 102e8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10368 x25: x25 x26: x26
STACK CFI 1036c x27: x27 x28: x28
STACK CFI 10370 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10400 x25: x25 x26: x26
STACK CFI 10408 x27: x27 x28: x28
STACK CFI 1040c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10574 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 105a4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10608 x25: x25 x26: x26
STACK CFI 1060c x27: x27 x28: x28
STACK CFI 10610 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10670 x25: x25 x26: x26
STACK CFI 10674 x27: x27 x28: x28
STACK CFI 10678 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 106cc x25: x25 x26: x26
STACK CFI 106d0 x27: x27 x28: x28
STACK CFI 106d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 106e8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 106fc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 10700 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 10704 80 .cfa: sp 0 + .ra: x30
STACK CFI 10718 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10734 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1073c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10754 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10784 184 .cfa: sp 0 + .ra: x30
STACK CFI 1078c .cfa: sp 224 +
STACK CFI 10794 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1079c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 107a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 107b0 x23: .cfa -16 + ^
STACK CFI 108c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 108d0 .cfa: sp 224 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 10910 ec .cfa: sp 0 + .ra: x30
STACK CFI 10918 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10920 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10970 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 109dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 109e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 109f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10a00 dc .cfa: sp 0 + .ra: x30
STACK CFI 10a08 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10a1c x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 10a90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10a98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 10ae0 14c .cfa: sp 0 + .ra: x30
STACK CFI 10ae8 .cfa: sp 160 +
STACK CFI 10af0 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10afc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10bd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10be0 .cfa: sp 160 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10c30 18c .cfa: sp 0 + .ra: x30
STACK CFI 10c38 .cfa: sp 64 +
STACK CFI 10c48 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10c50 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10ce4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10cec .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 10d78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10d80 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 10db0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10db8 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10dc0 130 .cfa: sp 0 + .ra: x30
STACK CFI 10dc8 .cfa: sp 144 +
STACK CFI 10dd0 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10e90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 10e98 .cfa: sp 144 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 10ef0 10c .cfa: sp 0 + .ra: x30
STACK CFI 10ef8 .cfa: sp 48 +
STACK CFI 10f04 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10f0c x19: .cfa -16 + ^
STACK CFI 10f44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10f4c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 10ff4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 11000 60 .cfa: sp 0 + .ra: x30
STACK CFI 11008 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11028 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11030 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1104c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11054 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11058 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 11060 60 .cfa: sp 0 + .ra: x30
STACK CFI 11068 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11088 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11090 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 110ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 110b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 110b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 110c0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 110c8 .cfa: sp 80 +
STACK CFI 110d8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1111c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11124 .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 11180 e4 .cfa: sp 0 + .ra: x30
STACK CFI 11188 .cfa: sp 112 +
STACK CFI 11194 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 111d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 111e0 .cfa: sp 112 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 11270 fe8 .cfa: sp 0 + .ra: x30
STACK CFI 11278 .cfa: sp 128 +
STACK CFI 11284 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1128c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 112f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 112f4 x23: .cfa -16 + ^
STACK CFI 11404 x21: x21 x22: x22 x23: x23
STACK CFI 1142c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11430 x23: .cfa -16 + ^
STACK CFI 11528 x21: x21 x22: x22
STACK CFI 1152c x23: x23
STACK CFI 11534 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 115fc x21: x21 x22: x22
STACK CFI 11600 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11604 x23: .cfa -16 + ^
STACK CFI 116f4 x21: x21 x22: x22 x23: x23
STACK CFI 11710 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 117d4 x21: x21 x22: x22
STACK CFI 117f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 117f4 x23: .cfa -16 + ^
STACK CFI 118f8 x21: x21 x22: x22 x23: x23
STACK CFI 118fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 119c4 x21: x21 x22: x22
STACK CFI 119c8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11a8c x21: x21 x22: x22
STACK CFI 11a90 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11a94 x23: .cfa -16 + ^
STACK CFI 11ba8 x21: x21 x22: x22 x23: x23
STACK CFI 11bac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11c74 x21: x21 x22: x22
STACK CFI 11cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11cb8 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 11cbc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11cc0 x23: .cfa -16 + ^
STACK CFI 11dbc x21: x21 x22: x22 x23: x23
STACK CFI 11dc0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11dc4 x23: .cfa -16 + ^
STACK CFI 11f0c x21: x21 x22: x22 x23: x23
STACK CFI 11f10 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12004 x21: x21 x22: x22
STACK CFI 12008 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12048 x23: .cfa -16 + ^
STACK CFI 1224c x21: x21 x22: x22 x23: x23
STACK CFI 12250 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12254 x23: .cfa -16 + ^
STACK CFI INIT 12260 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 12268 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1227c .cfa: sp 624 + x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 12298 x19: .cfa -80 + ^
STACK CFI 122a0 x20: .cfa -72 + ^
STACK CFI 122a8 x23: .cfa -48 + ^
STACK CFI 122b0 x24: .cfa -40 + ^
STACK CFI 122bc x25: .cfa -32 + ^
STACK CFI 122c4 x26: .cfa -24 + ^
STACK CFI 122cc x27: .cfa -16 + ^
STACK CFI 122d8 x28: .cfa -8 + ^
STACK CFI 12358 x19: x19
STACK CFI 1235c x20: x20
STACK CFI 12360 x23: x23
STACK CFI 12364 x24: x24
STACK CFI 12368 x25: x25
STACK CFI 1236c x26: x26
STACK CFI 12370 x27: x27
STACK CFI 12374 x28: x28
STACK CFI 12394 .cfa: sp 96 +
STACK CFI 1239c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 123a4 .cfa: sp 624 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 123f4 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 123f8 x19: .cfa -80 + ^
STACK CFI 123fc x20: .cfa -72 + ^
STACK CFI 12400 x23: .cfa -48 + ^
STACK CFI 12404 x24: .cfa -40 + ^
STACK CFI 12408 x25: .cfa -32 + ^
STACK CFI 1240c x26: .cfa -24 + ^
STACK CFI 12410 x27: .cfa -16 + ^
STACK CFI 12414 x28: .cfa -8 + ^
STACK CFI INIT 12420 12e4 .cfa: sp 0 + .ra: x30
STACK CFI 12428 .cfa: sp 416 +
STACK CFI 12434 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1243c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 12448 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1247c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 124ac x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 124cc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1253c x21: x21 x22: x22
STACK CFI 12540 x23: x23 x24: x24
STACK CFI 12544 x27: x27 x28: x28
STACK CFI 12570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 12578 .cfa: sp 416 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 12594 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 125f8 x27: x27 x28: x28
STACK CFI 125fc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 12600 x21: x21 x22: x22
STACK CFI 12608 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 12610 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1262c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 126c8 x21: x21 x22: x22
STACK CFI 126cc x23: x23 x24: x24
STACK CFI 126d4 x27: x27 x28: x28
STACK CFI 126d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 126e0 .cfa: sp 416 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 126e4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 126e8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 128a8 x21: x21 x22: x22
STACK CFI 128ac x23: x23 x24: x24
STACK CFI 128b0 x27: x27 x28: x28
STACK CFI 128b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 128b8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12900 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 12a08 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 12a0c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12a2c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 12a98 x23: x23 x24: x24
STACK CFI 12a9c x27: x27 x28: x28
STACK CFI 12aa0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12aa4 x23: x23 x24: x24
STACK CFI 12aec x21: x21 x22: x22
STACK CFI 12b10 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 12b14 x23: x23 x24: x24
STACK CFI 12b38 x21: x21 x22: x22
STACK CFI 12b3c x27: x27 x28: x28
STACK CFI 12b40 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 12b44 x23: x23 x24: x24
STACK CFI 12bac x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 12bc4 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 12c34 x27: x27 x28: x28
STACK CFI 12c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 12c40 .cfa: sp 416 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 12c84 x21: x21 x22: x22
STACK CFI 12cdc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 12d34 x21: x21 x22: x22
STACK CFI 12d40 x23: x23 x24: x24
STACK CFI 12d48 x27: x27 x28: x28
STACK CFI 12d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 12d54 .cfa: sp 416 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 12ea4 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 12ebc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 12eec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 13154 x21: x21 x22: x22
STACK CFI 1316c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1319c x21: x21 x22: x22
STACK CFI 131b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 13258 x23: x23 x24: x24
STACK CFI 132a0 x21: x21 x22: x22
STACK CFI 13344 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1335c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1343c x23: x23 x24: x24
STACK CFI 13464 x21: x21 x22: x22
STACK CFI 13488 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 13534 x21: x21 x22: x22
STACK CFI 1356c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 135f4 x21: x21 x22: x22
STACK CFI 1362c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 136d8 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 136dc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 136e0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 136e4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 136e8 x21: x21 x22: x22
STACK CFI 136ec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 136f0 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 136f4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 136f8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 136fc x23: x23 x24: x24
STACK CFI 13700 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 13704 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1370c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1371c x19: .cfa -16 + ^
STACK CFI 13778 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 13780 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 13794 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1379c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 137b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
