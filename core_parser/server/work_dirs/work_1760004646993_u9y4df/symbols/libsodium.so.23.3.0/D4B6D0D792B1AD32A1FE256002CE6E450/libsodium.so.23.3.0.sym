MODULE Linux arm64 D4B6D0D792B1AD32A1FE256002CE6E450 libsodium.so.23
INFO CODE_ID D7D0B6D4B19232ADA1FE256002CE6E45
PUBLIC eac0 0 _init
PUBLIC fb60 0 call_weak_fn
PUBLIC fb80 0 deregister_tm_clones
PUBLIC fbb0 0 register_tm_clones
PUBLIC fbf0 0 __do_global_dtors_aux
PUBLIC fc40 0 frame_dummy
PUBLIC fc50 0 crypto_aead_chacha20poly1305_encrypt_detached
PUBLIC fda0 0 crypto_aead_chacha20poly1305_encrypt
PUBLIC fe10 0 crypto_aead_chacha20poly1305_ietf_encrypt_detached
PUBLIC ff90 0 crypto_aead_chacha20poly1305_ietf_encrypt
PUBLIC 10010 0 crypto_aead_chacha20poly1305_decrypt_detached
PUBLIC 101a0 0 crypto_aead_chacha20poly1305_decrypt
PUBLIC 10210 0 crypto_aead_chacha20poly1305_ietf_decrypt_detached
PUBLIC 103d0 0 crypto_aead_chacha20poly1305_ietf_decrypt
PUBLIC 10440 0 crypto_aead_chacha20poly1305_ietf_keybytes
PUBLIC 10450 0 crypto_aead_chacha20poly1305_ietf_npubbytes
PUBLIC 10460 0 crypto_aead_chacha20poly1305_ietf_nsecbytes
PUBLIC 10470 0 crypto_aead_chacha20poly1305_ietf_abytes
PUBLIC 10480 0 crypto_aead_chacha20poly1305_ietf_messagebytes_max
PUBLIC 10490 0 crypto_aead_chacha20poly1305_ietf_keygen
PUBLIC 104a0 0 crypto_aead_chacha20poly1305_keybytes
PUBLIC 104b0 0 crypto_aead_chacha20poly1305_npubbytes
PUBLIC 104c0 0 crypto_aead_chacha20poly1305_nsecbytes
PUBLIC 104d0 0 crypto_aead_chacha20poly1305_abytes
PUBLIC 104e0 0 crypto_aead_chacha20poly1305_messagebytes_max
PUBLIC 104f0 0 crypto_aead_chacha20poly1305_keygen
PUBLIC 10500 0 crypto_aead_xchacha20poly1305_ietf_encrypt_detached
PUBLIC 106c0 0 crypto_aead_xchacha20poly1305_ietf_encrypt
PUBLIC 10730 0 crypto_aead_xchacha20poly1305_ietf_decrypt_detached
PUBLIC 10920 0 crypto_aead_xchacha20poly1305_ietf_decrypt
PUBLIC 10990 0 crypto_aead_xchacha20poly1305_ietf_keybytes
PUBLIC 109a0 0 crypto_aead_xchacha20poly1305_ietf_npubbytes
PUBLIC 109b0 0 crypto_aead_xchacha20poly1305_ietf_nsecbytes
PUBLIC 109c0 0 crypto_aead_xchacha20poly1305_ietf_abytes
PUBLIC 109d0 0 crypto_aead_xchacha20poly1305_ietf_messagebytes_max
PUBLIC 109e0 0 crypto_aead_xchacha20poly1305_ietf_keygen
PUBLIC 109f0 0 crypto_auth_bytes
PUBLIC 10a00 0 crypto_auth_keybytes
PUBLIC 10a10 0 crypto_auth_primitive
PUBLIC 10a20 0 crypto_auth
PUBLIC 10a30 0 crypto_auth_verify
PUBLIC 10a40 0 crypto_auth_keygen
PUBLIC 10a50 0 crypto_auth_hmacsha256_bytes
PUBLIC 10a60 0 crypto_auth_hmacsha256_keybytes
PUBLIC 10a70 0 crypto_auth_hmacsha256_statebytes
PUBLIC 10a80 0 crypto_auth_hmacsha256_keygen
PUBLIC 10a90 0 crypto_auth_hmacsha256_init
PUBLIC 10ed0 0 crypto_auth_hmacsha256_update
PUBLIC 10ef0 0 crypto_auth_hmacsha256_final
PUBLIC 10f90 0 crypto_auth_hmacsha256
PUBLIC 11030 0 crypto_auth_hmacsha256_verify
PUBLIC 110c0 0 crypto_auth_hmacsha512_bytes
PUBLIC 110d0 0 crypto_auth_hmacsha512_keybytes
PUBLIC 110e0 0 crypto_auth_hmacsha512_statebytes
PUBLIC 110f0 0 crypto_auth_hmacsha512_keygen
PUBLIC 11100 0 crypto_auth_hmacsha512_init
PUBLIC 11610 0 crypto_auth_hmacsha512_update
PUBLIC 11630 0 crypto_auth_hmacsha512_final
PUBLIC 116d0 0 crypto_auth_hmacsha512
PUBLIC 11770 0 crypto_auth_hmacsha512_verify
PUBLIC 11800 0 crypto_auth_hmacsha512256_bytes
PUBLIC 11810 0 crypto_auth_hmacsha512256_keybytes
PUBLIC 11820 0 crypto_auth_hmacsha512256_statebytes
PUBLIC 11830 0 crypto_auth_hmacsha512256_keygen
PUBLIC 11840 0 crypto_auth_hmacsha512256_init
PUBLIC 11850 0 crypto_auth_hmacsha512256_update
PUBLIC 11860 0 crypto_auth_hmacsha512256_final
PUBLIC 118d0 0 crypto_auth_hmacsha512256
PUBLIC 11970 0 crypto_auth_hmacsha512256_verify
PUBLIC 11a00 0 crypto_box_seedbytes
PUBLIC 11a10 0 crypto_box_publickeybytes
PUBLIC 11a20 0 crypto_box_secretkeybytes
PUBLIC 11a30 0 crypto_box_beforenmbytes
PUBLIC 11a40 0 crypto_box_noncebytes
PUBLIC 11a50 0 crypto_box_zerobytes
PUBLIC 11a60 0 crypto_box_boxzerobytes
PUBLIC 11a70 0 crypto_box_macbytes
PUBLIC 11a80 0 crypto_box_messagebytes_max
PUBLIC 11a90 0 crypto_box_primitive
PUBLIC 11aa0 0 crypto_box_seed_keypair
PUBLIC 11ab0 0 crypto_box_keypair
PUBLIC 11ac0 0 crypto_box_beforenm
PUBLIC 11ad0 0 crypto_box_afternm
PUBLIC 11ae0 0 crypto_box_open_afternm
PUBLIC 11af0 0 crypto_box
PUBLIC 11b00 0 crypto_box_open
PUBLIC 11b10 0 crypto_box_detached_afternm
PUBLIC 11b20 0 crypto_box_detached
PUBLIC 11be0 0 crypto_box_easy_afternm
PUBLIC 11c20 0 crypto_box_easy
PUBLIC 11c60 0 crypto_box_open_detached_afternm
PUBLIC 11c70 0 crypto_box_open_detached
PUBLIC 11d30 0 crypto_box_open_easy_afternm
PUBLIC 11d60 0 crypto_box_open_easy
PUBLIC 11d90 0 _crypto_box_seal_nonce.isra.0
PUBLIC 11e40 0 crypto_box_seal
PUBLIC 11f80 0 crypto_box_seal_open
PUBLIC 12030 0 crypto_box_sealbytes
PUBLIC 12040 0 crypto_box_curve25519xsalsa20poly1305_seed_keypair
PUBLIC 120d0 0 crypto_box_curve25519xsalsa20poly1305_keypair
PUBLIC 12110 0 crypto_box_curve25519xsalsa20poly1305_beforenm
PUBLIC 121a0 0 crypto_box_curve25519xsalsa20poly1305_afternm
PUBLIC 121b0 0 crypto_box_curve25519xsalsa20poly1305_open_afternm
PUBLIC 121c0 0 crypto_box_curve25519xsalsa20poly1305
PUBLIC 12280 0 crypto_box_curve25519xsalsa20poly1305_open
PUBLIC 12340 0 crypto_box_curve25519xsalsa20poly1305_seedbytes
PUBLIC 12350 0 crypto_box_curve25519xsalsa20poly1305_publickeybytes
PUBLIC 12360 0 crypto_box_curve25519xsalsa20poly1305_secretkeybytes
PUBLIC 12370 0 crypto_box_curve25519xsalsa20poly1305_beforenmbytes
PUBLIC 12380 0 crypto_box_curve25519xsalsa20poly1305_noncebytes
PUBLIC 12390 0 crypto_box_curve25519xsalsa20poly1305_zerobytes
PUBLIC 123a0 0 crypto_box_curve25519xsalsa20poly1305_boxzerobytes
PUBLIC 123b0 0 crypto_box_curve25519xsalsa20poly1305_macbytes
PUBLIC 123c0 0 crypto_box_curve25519xsalsa20poly1305_messagebytes_max
PUBLIC 123d0 0 fe25519_sub
PUBLIC 12460 0 fe25519_mul
PUBLIC 126a0 0 fe25519_sq
PUBLIC 12830 0 fe25519_sq2
PUBLIC 129e0 0 slide_vartime
PUBLIC 12af0 0 ge25519_madd
PUBLIC 12c60 0 ge25519_p2_dbl
PUBLIC 12d80 0 fe25519_pow22523
PUBLIC 13140 0 ge25519_cmov8
PUBLIC 13800 0 ge25519_cmov8_cached
PUBLIC 14080 0 fe25519_frombytes
PUBLIC 140c0 0 fe25519_tobytes
PUBLIC 14270 0 ristretto255_sqrt_ratio_m1
PUBLIC 144e0 0 ristretto255_elligator
PUBLIC 148b0 0 fe25519_invert
PUBLIC 14c80 0 ge25519_add
PUBLIC 14df0 0 ge25519_frombytes
PUBLIC 15090 0 ge25519_frombytes_negate_vartime
PUBLIC 152c0 0 ge25519_p1p1_to_p2
PUBLIC 15530 0 ge25519_p1p1_to_p3
PUBLIC 157b0 0 ge25519_p3_to_cached
PUBLIC 15890 0 ge25519_p3_tobytes
PUBLIC 15950 0 ge25519_elligator2
PUBLIC 16090 0 ge25519_sub
PUBLIC 16210 0 ge25519_tobytes
PUBLIC 162d0 0 ge25519_double_scalarmult_vartime
PUBLIC 167c0 0 ge25519_scalarmult
PUBLIC 16bd0 0 ge25519_scalarmult_base
PUBLIC 16e20 0 ge25519_is_on_curve
PUBLIC 16f60 0 ge25519_is_on_main_subgroup
PUBLIC 172d0 0 ge25519_is_canonical
PUBLIC 17380 0 ge25519_has_small_order
PUBLIC 17680 0 sc25519_mul
PUBLIC 18480 0 sc25519_muladd
PUBLIC 19230 0 sc25519_invert
PUBLIC 19860 0 sc25519_reduce
PUBLIC 19fc0 0 sc25519_is_canonical
PUBLIC 1a010 0 ge25519_from_uniform
PUBLIC 1a0a0 0 ge25519_from_hash
PUBLIC 1a380 0 ristretto255_frombytes
PUBLIC 1a6f0 0 ristretto255_p3_tobytes
PUBLIC 1aaa0 0 ristretto255_from_hash
PUBLIC 1ab80 0 crypto_core_hchacha20
PUBLIC 1ad80 0 crypto_core_hchacha20_outputbytes
PUBLIC 1ad90 0 crypto_core_hchacha20_inputbytes
PUBLIC 1ada0 0 crypto_core_hchacha20_keybytes
PUBLIC 1adb0 0 crypto_core_hchacha20_constbytes
PUBLIC 1adc0 0 crypto_core_hsalsa20
PUBLIC 1af60 0 crypto_core_hsalsa20_outputbytes
PUBLIC 1af70 0 crypto_core_hsalsa20_inputbytes
PUBLIC 1af80 0 crypto_core_hsalsa20_keybytes
PUBLIC 1af90 0 crypto_core_hsalsa20_constbytes
PUBLIC 1afa0 0 crypto_core_salsa20
PUBLIC 1b210 0 crypto_core_salsa20_outputbytes
PUBLIC 1b220 0 crypto_core_salsa20_inputbytes
PUBLIC 1b230 0 crypto_core_salsa20_keybytes
PUBLIC 1b240 0 crypto_core_salsa20_constbytes
PUBLIC 1b250 0 crypto_core_salsa2012
PUBLIC 1b4c0 0 crypto_core_salsa2012_outputbytes
PUBLIC 1b4d0 0 crypto_core_salsa2012_inputbytes
PUBLIC 1b4e0 0 crypto_core_salsa2012_keybytes
PUBLIC 1b4f0 0 crypto_core_salsa2012_constbytes
PUBLIC 1b500 0 crypto_core_salsa208
PUBLIC 1b770 0 crypto_core_salsa208_outputbytes
PUBLIC 1b780 0 crypto_core_salsa208_inputbytes
PUBLIC 1b790 0 crypto_core_salsa208_keybytes
PUBLIC 1b7a0 0 crypto_core_salsa208_constbytes
PUBLIC 1b7b0 0 crypto_generichash_bytes_min
PUBLIC 1b7c0 0 crypto_generichash_bytes_max
PUBLIC 1b7d0 0 crypto_generichash_bytes
PUBLIC 1b7e0 0 crypto_generichash_keybytes_min
PUBLIC 1b7f0 0 crypto_generichash_keybytes_max
PUBLIC 1b800 0 crypto_generichash_keybytes
PUBLIC 1b810 0 crypto_generichash_primitive
PUBLIC 1b820 0 crypto_generichash_statebytes
PUBLIC 1b830 0 crypto_generichash
PUBLIC 1b840 0 crypto_generichash_init
PUBLIC 1b850 0 crypto_generichash_update
PUBLIC 1b860 0 crypto_generichash_final
PUBLIC 1b870 0 crypto_generichash_keygen
PUBLIC 1b880 0 crypto_generichash_blake2b_bytes_min
PUBLIC 1b890 0 crypto_generichash_blake2b_bytes_max
PUBLIC 1b8a0 0 crypto_generichash_blake2b_bytes
PUBLIC 1b8b0 0 crypto_generichash_blake2b_keybytes_min
PUBLIC 1b8c0 0 crypto_generichash_blake2b_keybytes_max
PUBLIC 1b8d0 0 crypto_generichash_blake2b_keybytes
PUBLIC 1b8e0 0 crypto_generichash_blake2b_saltbytes
PUBLIC 1b8f0 0 crypto_generichash_blake2b_personalbytes
PUBLIC 1b900 0 crypto_generichash_blake2b_statebytes
PUBLIC 1b910 0 crypto_generichash_blake2b_keygen
PUBLIC 1b920 0 blake2b_compress_ref
PUBLIC 1d0c0 0 crypto_generichash_blake2b__init_param
PUBLIC 1d210 0 crypto_generichash_blake2b__init
PUBLIC 1d2a0 0 crypto_generichash_blake2b__init_salt_personal
PUBLIC 1d350 0 crypto_generichash_blake2b__update
PUBLIC 1d460 0 crypto_generichash_blake2b__init_key
PUBLIC 1d570 0 crypto_generichash_blake2b__init_key_salt_personal
PUBLIC 1d6a0 0 crypto_generichash_blake2b__final
PUBLIC 1d820 0 crypto_generichash_blake2b__blake2b
PUBLIC 1d910 0 crypto_generichash_blake2b__blake2b_salt_personal
PUBLIC 1da20 0 crypto_generichash_blake2b__pick_best_implementation
PUBLIC 1da40 0 crypto_generichash_blake2b
PUBLIC 1da80 0 crypto_generichash_blake2b_salt_personal
PUBLIC 1dac0 0 crypto_generichash_blake2b_init
PUBLIC 1db30 0 crypto_generichash_blake2b_init_salt_personal
PUBLIC 1dbb0 0 crypto_generichash_blake2b_update
PUBLIC 1dbc0 0 crypto_generichash_blake2b_final
PUBLIC 1dbd0 0 _crypto_generichash_blake2b_pick_best_implementation
PUBLIC 1dbe0 0 crypto_hash_bytes
PUBLIC 1dbf0 0 crypto_hash
PUBLIC 1dc00 0 crypto_hash_primitive
PUBLIC 1dc10 0 crypto_hash_sha256_bytes
PUBLIC 1dc20 0 crypto_hash_sha256_statebytes
PUBLIC 1dc30 0 SHA256_Transform
PUBLIC 1e6d0 0 crypto_hash_sha256_update.part.0
PUBLIC 1ec20 0 crypto_hash_sha256_init
PUBLIC 1ec40 0 crypto_hash_sha256_update
PUBLIC 1ec70 0 crypto_hash_sha256_final
PUBLIC 1edf0 0 crypto_hash_sha256
PUBLIC 1ee80 0 crypto_hash_sha512_bytes
PUBLIC 1ee90 0 crypto_hash_sha512_statebytes
PUBLIC 1eea0 0 SHA512_Transform
PUBLIC 1f980 0 crypto_hash_sha512_update.part.0
PUBLIC 1ffe0 0 crypto_hash_sha512_init
PUBLIC 20010 0 crypto_hash_sha512_update
PUBLIC 20040 0 crypto_hash_sha512_final
PUBLIC 20230 0 crypto_hash_sha512
PUBLIC 202c0 0 crypto_kdf_blake2b_bytes_min
PUBLIC 202d0 0 crypto_kdf_blake2b_bytes_max
PUBLIC 202e0 0 crypto_kdf_blake2b_contextbytes
PUBLIC 202f0 0 crypto_kdf_blake2b_keybytes
PUBLIC 20300 0 crypto_kdf_blake2b_derive_from_key
PUBLIC 20390 0 crypto_kdf_primitive
PUBLIC 203a0 0 crypto_kdf_bytes_min
PUBLIC 203b0 0 crypto_kdf_bytes_max
PUBLIC 203c0 0 crypto_kdf_contextbytes
PUBLIC 203d0 0 crypto_kdf_keybytes
PUBLIC 203e0 0 crypto_kdf_derive_from_key
PUBLIC 203f0 0 crypto_kdf_keygen
PUBLIC 20400 0 crypto_kx_seed_keypair
PUBLIC 20440 0 crypto_kx_keypair
PUBLIC 20480 0 crypto_kx_client_session_keys
PUBLIC 20620 0 crypto_kx_server_session_keys
PUBLIC 207c0 0 crypto_kx_publickeybytes
PUBLIC 207d0 0 crypto_kx_secretkeybytes
PUBLIC 207e0 0 crypto_kx_seedbytes
PUBLIC 207f0 0 crypto_kx_sessionkeybytes
PUBLIC 20800 0 crypto_kx_primitive
PUBLIC 20810 0 crypto_onetimeauth_statebytes
PUBLIC 20820 0 crypto_onetimeauth_bytes
PUBLIC 20830 0 crypto_onetimeauth_keybytes
PUBLIC 20840 0 crypto_onetimeauth
PUBLIC 20850 0 crypto_onetimeauth_verify
PUBLIC 20860 0 crypto_onetimeauth_init
PUBLIC 20870 0 crypto_onetimeauth_update
PUBLIC 20880 0 crypto_onetimeauth_final
PUBLIC 20890 0 crypto_onetimeauth_primitive
PUBLIC 208a0 0 crypto_onetimeauth_keygen
PUBLIC 208b0 0 crypto_onetimeauth_poly1305
PUBLIC 208d0 0 crypto_onetimeauth_poly1305_verify
PUBLIC 208f0 0 crypto_onetimeauth_poly1305_init
PUBLIC 20910 0 crypto_onetimeauth_poly1305_update
PUBLIC 20930 0 crypto_onetimeauth_poly1305_final
PUBLIC 20950 0 crypto_onetimeauth_poly1305_bytes
PUBLIC 20960 0 crypto_onetimeauth_poly1305_keybytes
PUBLIC 20970 0 crypto_onetimeauth_poly1305_statebytes
PUBLIC 20980 0 crypto_onetimeauth_poly1305_keygen
PUBLIC 20990 0 _crypto_onetimeauth_poly1305_pick_best_implementation
PUBLIC 209b0 0 poly1305_blocks
PUBLIC 20ad0 0 crypto_onetimeauth_poly1305_donna_init
PUBLIC 20b30 0 poly1305_blocks.constprop.0
PUBLIC 20c40 0 poly1305_finish
PUBLIC 20d90 0 crypto_onetimeauth_poly1305_donna_final
PUBLIC 20db0 0 crypto_onetimeauth_poly1305_donna
PUBLIC 20ec0 0 crypto_onetimeauth_poly1305_donna_update
PUBLIC 213d0 0 crypto_onetimeauth_poly1305_donna_verify
PUBLIC 214f0 0 initial_hash.part.0
PUBLIC 21700 0 free_instance
PUBLIC 21780 0 finalize
PUBLIC 21960 0 fill_memory_blocks
PUBLIC 219f0 0 validate_inputs
PUBLIC 21b10 0 fill_first_blocks
PUBLIC 21cb0 0 initial_hash
PUBLIC 21cd0 0 initialize
PUBLIC 21e20 0 argon2_pick_best_implementation
PUBLIC 21e40 0 _crypto_pwhash_argon2_pick_best_implementation
PUBLIC 21e50 0 decode_string
PUBLIC 22300 0 encode_string
PUBLIC 22a80 0 fill_block_with_xor
PUBLIC 23160 0 generate_addresses
PUBLIC 232e0 0 fill_block
PUBLIC 239a0 0 fill_segment_ref
PUBLIC 23d80 0 argon2_ctx
PUBLIC 23e90 0 argon2_hash
PUBLIC 24040 0 argon2i_hash_encoded
PUBLIC 24080 0 argon2i_hash_raw
PUBLIC 240b0 0 argon2id_hash_encoded
PUBLIC 240f0 0 argon2id_hash_raw
PUBLIC 24120 0 argon2_verify
PUBLIC 24310 0 argon2i_verify
PUBLIC 24320 0 argon2id_verify
PUBLIC 24330 0 blake2b_long
PUBLIC 24570 0 crypto_pwhash_argon2i_alg_argon2i13
PUBLIC 24580 0 crypto_pwhash_argon2i_bytes_min
PUBLIC 24590 0 crypto_pwhash_argon2i_bytes_max
PUBLIC 245a0 0 crypto_pwhash_argon2i_passwd_min
PUBLIC 245b0 0 crypto_pwhash_argon2i_passwd_max
PUBLIC 245c0 0 crypto_pwhash_argon2i_saltbytes
PUBLIC 245d0 0 crypto_pwhash_argon2i_strbytes
PUBLIC 245e0 0 crypto_pwhash_argon2i_strprefix
PUBLIC 245f0 0 crypto_pwhash_argon2i_opslimit_min
PUBLIC 24600 0 crypto_pwhash_argon2i_opslimit_max
PUBLIC 24610 0 crypto_pwhash_argon2i_memlimit_min
PUBLIC 24620 0 crypto_pwhash_argon2i_memlimit_max
PUBLIC 24630 0 crypto_pwhash_argon2i_opslimit_interactive
PUBLIC 24640 0 crypto_pwhash_argon2i_memlimit_interactive
PUBLIC 24650 0 crypto_pwhash_argon2i_opslimit_moderate
PUBLIC 24660 0 crypto_pwhash_argon2i_memlimit_moderate
PUBLIC 24670 0 crypto_pwhash_argon2i_opslimit_sensitive
PUBLIC 24680 0 crypto_pwhash_argon2i_memlimit_sensitive
PUBLIC 24690 0 crypto_pwhash_argon2i
PUBLIC 24790 0 crypto_pwhash_argon2i_str
PUBLIC 248c0 0 crypto_pwhash_argon2i_str_verify
PUBLIC 24920 0 crypto_pwhash_argon2i_str_needs_rehash
PUBLIC 24a60 0 crypto_pwhash_argon2id_str_needs_rehash
PUBLIC 24ba0 0 crypto_pwhash_argon2id_alg_argon2id13
PUBLIC 24bb0 0 crypto_pwhash_argon2id_bytes_min
PUBLIC 24bc0 0 crypto_pwhash_argon2id_bytes_max
PUBLIC 24bd0 0 crypto_pwhash_argon2id_passwd_min
PUBLIC 24be0 0 crypto_pwhash_argon2id_passwd_max
PUBLIC 24bf0 0 crypto_pwhash_argon2id_saltbytes
PUBLIC 24c00 0 crypto_pwhash_argon2id_strbytes
PUBLIC 24c10 0 crypto_pwhash_argon2id_strprefix
PUBLIC 24c20 0 crypto_pwhash_argon2id_opslimit_min
PUBLIC 24c30 0 crypto_pwhash_argon2id_opslimit_max
PUBLIC 24c40 0 crypto_pwhash_argon2id_memlimit_min
PUBLIC 24c50 0 crypto_pwhash_argon2id_memlimit_max
PUBLIC 24c60 0 crypto_pwhash_argon2id_opslimit_interactive
PUBLIC 24c70 0 crypto_pwhash_argon2id_memlimit_interactive
PUBLIC 24c80 0 crypto_pwhash_argon2id_opslimit_moderate
PUBLIC 24c90 0 crypto_pwhash_argon2id_memlimit_moderate
PUBLIC 24ca0 0 crypto_pwhash_argon2id_opslimit_sensitive
PUBLIC 24cb0 0 crypto_pwhash_argon2id_memlimit_sensitive
PUBLIC 24cc0 0 crypto_pwhash_argon2id
PUBLIC 24dc0 0 crypto_pwhash_argon2id_str
PUBLIC 24ef0 0 crypto_pwhash_argon2id_str_verify
PUBLIC 24f50 0 crypto_pwhash_alg_argon2i13
PUBLIC 24f60 0 crypto_pwhash_alg_argon2id13
PUBLIC 24f70 0 crypto_pwhash_alg_default
PUBLIC 24f80 0 crypto_pwhash_bytes_min
PUBLIC 24f90 0 crypto_pwhash_bytes_max
PUBLIC 24fa0 0 crypto_pwhash_passwd_min
PUBLIC 24fb0 0 crypto_pwhash_passwd_max
PUBLIC 24fc0 0 crypto_pwhash_saltbytes
PUBLIC 24fd0 0 crypto_pwhash_strbytes
PUBLIC 24fe0 0 crypto_pwhash_strprefix
PUBLIC 24ff0 0 crypto_pwhash_opslimit_min
PUBLIC 25000 0 crypto_pwhash_opslimit_max
PUBLIC 25010 0 crypto_pwhash_memlimit_min
PUBLIC 25020 0 crypto_pwhash_memlimit_max
PUBLIC 25030 0 crypto_pwhash_opslimit_interactive
PUBLIC 25040 0 crypto_pwhash_memlimit_interactive
PUBLIC 25050 0 crypto_pwhash_opslimit_moderate
PUBLIC 25060 0 crypto_pwhash_memlimit_moderate
PUBLIC 25070 0 crypto_pwhash_opslimit_sensitive
PUBLIC 25080 0 crypto_pwhash_memlimit_sensitive
PUBLIC 25090 0 crypto_pwhash
PUBLIC 250d0 0 crypto_pwhash_str
PUBLIC 250e0 0 crypto_pwhash_str_alg
PUBLIC 25110 0 crypto_pwhash_str_verify
PUBLIC 251c0 0 crypto_pwhash_str_needs_rehash
PUBLIC 25270 0 crypto_pwhash_primitive
PUBLIC 25280 0 crypto_scalarmult_primitive
PUBLIC 25290 0 crypto_scalarmult_base
PUBLIC 252a0 0 crypto_scalarmult
PUBLIC 252b0 0 crypto_scalarmult_bytes
PUBLIC 252c0 0 crypto_scalarmult_scalarbytes
PUBLIC 252d0 0 fe25519_sub
PUBLIC 25360 0 fe25519_mul
PUBLIC 255a0 0 fe25519_sq
PUBLIC 25730 0 crypto_scalarmult_curve25519_ref10_base
PUBLIC 25850 0 crypto_scalarmult_curve25519_ref10.part.0
PUBLIC 25d80 0 crypto_scalarmult_curve25519_ref10
PUBLIC 260c0 0 crypto_scalarmult_curve25519
PUBLIC 26140 0 crypto_scalarmult_curve25519_base
PUBLIC 26160 0 crypto_scalarmult_curve25519_bytes
PUBLIC 26170 0 crypto_scalarmult_curve25519_scalarbytes
PUBLIC 26180 0 _crypto_scalarmult_curve25519_pick_best_implementation
PUBLIC 261a0 0 crypto_secretbox_keybytes
PUBLIC 261b0 0 crypto_secretbox_noncebytes
PUBLIC 261c0 0 crypto_secretbox_zerobytes
PUBLIC 261d0 0 crypto_secretbox_boxzerobytes
PUBLIC 261e0 0 crypto_secretbox_macbytes
PUBLIC 261f0 0 crypto_secretbox_messagebytes_max
PUBLIC 26200 0 crypto_secretbox_primitive
PUBLIC 26210 0 crypto_secretbox
PUBLIC 26220 0 crypto_secretbox_open
PUBLIC 26230 0 crypto_secretbox_keygen
PUBLIC 26240 0 crypto_secretbox_detached
PUBLIC 26450 0 crypto_secretbox_easy
PUBLIC 26490 0 crypto_secretbox_open_detached
PUBLIC 26680 0 crypto_secretbox_open_easy
PUBLIC 266b0 0 crypto_secretbox_xsalsa20poly1305
PUBLIC 26700 0 crypto_secretbox_xsalsa20poly1305_open
PUBLIC 267e0 0 crypto_secretbox_xsalsa20poly1305_keybytes
PUBLIC 267f0 0 crypto_secretbox_xsalsa20poly1305_noncebytes
PUBLIC 26800 0 crypto_secretbox_xsalsa20poly1305_zerobytes
PUBLIC 26810 0 crypto_secretbox_xsalsa20poly1305_boxzerobytes
PUBLIC 26820 0 crypto_secretbox_xsalsa20poly1305_macbytes
PUBLIC 26830 0 crypto_secretbox_xsalsa20poly1305_messagebytes_max
PUBLIC 26840 0 crypto_secretbox_xsalsa20poly1305_keygen
PUBLIC 26850 0 crypto_secretstream_xchacha20poly1305_keygen
PUBLIC 26860 0 crypto_secretstream_xchacha20poly1305_init_push
PUBLIC 268d0 0 crypto_secretstream_xchacha20poly1305_init_pull
PUBLIC 26920 0 crypto_secretstream_xchacha20poly1305_rekey
PUBLIC 269c0 0 crypto_secretstream_xchacha20poly1305_push
PUBLIC 26ca0 0 crypto_secretstream_xchacha20poly1305_pull
PUBLIC 26f30 0 crypto_secretstream_xchacha20poly1305_statebytes
PUBLIC 26f40 0 crypto_secretstream_xchacha20poly1305_abytes
PUBLIC 26f50 0 crypto_secretstream_xchacha20poly1305_headerbytes
PUBLIC 26f60 0 crypto_secretstream_xchacha20poly1305_keybytes
PUBLIC 26f70 0 crypto_secretstream_xchacha20poly1305_messagebytes_max
PUBLIC 26f80 0 crypto_secretstream_xchacha20poly1305_tag_message
PUBLIC 26f90 0 crypto_secretstream_xchacha20poly1305_tag_push
PUBLIC 26fa0 0 crypto_secretstream_xchacha20poly1305_tag_rekey
PUBLIC 26fb0 0 crypto_secretstream_xchacha20poly1305_tag_final
PUBLIC 26fc0 0 crypto_shorthash_bytes
PUBLIC 26fd0 0 crypto_shorthash_keybytes
PUBLIC 26fe0 0 crypto_shorthash_primitive
PUBLIC 26ff0 0 crypto_shorthash
PUBLIC 27000 0 crypto_shorthash_keygen
PUBLIC 27010 0 crypto_shorthash_siphash24_bytes
PUBLIC 27020 0 crypto_shorthash_siphash24_keybytes
PUBLIC 27030 0 crypto_shorthash_siphash24
PUBLIC 27280 0 crypto_sign_statebytes
PUBLIC 27290 0 crypto_sign_bytes
PUBLIC 272a0 0 crypto_sign_seedbytes
PUBLIC 272b0 0 crypto_sign_publickeybytes
PUBLIC 272c0 0 crypto_sign_secretkeybytes
PUBLIC 272d0 0 crypto_sign_messagebytes_max
PUBLIC 272e0 0 crypto_sign_primitive
PUBLIC 272f0 0 crypto_sign_seed_keypair
PUBLIC 27300 0 crypto_sign_keypair
PUBLIC 27310 0 crypto_sign
PUBLIC 27320 0 crypto_sign_open
PUBLIC 27330 0 crypto_sign_detached
PUBLIC 27340 0 crypto_sign_verify_detached
PUBLIC 27350 0 crypto_sign_init
PUBLIC 27360 0 crypto_sign_update
PUBLIC 27370 0 crypto_sign_final_create
PUBLIC 27380 0 crypto_sign_final_verify
PUBLIC 27390 0 crypto_sign_ed25519ph_statebytes
PUBLIC 273a0 0 crypto_sign_ed25519_bytes
PUBLIC 273b0 0 crypto_sign_ed25519_seedbytes
PUBLIC 273c0 0 crypto_sign_ed25519_publickeybytes
PUBLIC 273d0 0 crypto_sign_ed25519_secretkeybytes
PUBLIC 273e0 0 crypto_sign_ed25519_messagebytes_max
PUBLIC 273f0 0 crypto_sign_ed25519_sk_to_seed
PUBLIC 27410 0 crypto_sign_ed25519_sk_to_pk
PUBLIC 27430 0 crypto_sign_ed25519ph_init
PUBLIC 27450 0 crypto_sign_ed25519ph_update
PUBLIC 27460 0 crypto_sign_ed25519ph_final_create
PUBLIC 274f0 0 crypto_sign_ed25519ph_final_verify
PUBLIC 27570 0 crypto_sign_ed25519_seed_keypair
PUBLIC 27640 0 crypto_sign_ed25519_keypair
PUBLIC 276e0 0 crypto_sign_ed25519_pk_to_curve25519
PUBLIC 27a30 0 crypto_sign_ed25519_sk_to_curve25519
PUBLIC 27ad0 0 _crypto_sign_ed25519_verify_detached
PUBLIC 27c50 0 crypto_sign_ed25519_verify_detached
PUBLIC 27c60 0 crypto_sign_ed25519_open
PUBLIC 27d30 0 _crypto_sign_ed25519_ref10_hinit
PUBLIC 27d80 0 _crypto_sign_ed25519_detached
PUBLIC 27f40 0 crypto_sign_ed25519_detached
PUBLIC 27f50 0 crypto_sign_ed25519
PUBLIC 28020 0 crypto_stream_chacha20_keybytes
PUBLIC 28030 0 crypto_stream_chacha20_noncebytes
PUBLIC 28040 0 crypto_stream_chacha20_messagebytes_max
PUBLIC 28050 0 crypto_stream_chacha20_ietf_keybytes
PUBLIC 28060 0 crypto_stream_chacha20_ietf_noncebytes
PUBLIC 28070 0 crypto_stream_chacha20_ietf_messagebytes_max
PUBLIC 28080 0 crypto_stream_chacha20
PUBLIC 280a0 0 crypto_stream_chacha20_xor_ic
PUBLIC 280c0 0 crypto_stream_chacha20_xor
PUBLIC 280e0 0 crypto_stream_chacha20_ietf_ext
PUBLIC 28100 0 crypto_stream_chacha20_ietf_ext_xor_ic
PUBLIC 28120 0 crypto_stream_chacha20_ietf
PUBLIC 28140 0 crypto_stream_chacha20_ietf_xor_ic
PUBLIC 28170 0 crypto_stream_chacha20_ietf_xor
PUBLIC 281b0 0 crypto_stream_chacha20_ietf_keygen
PUBLIC 281c0 0 crypto_stream_chacha20_keygen
PUBLIC 281d0 0 _crypto_stream_chacha20_pick_best_implementation
PUBLIC 281f0 0 chacha20_encrypt_bytes.part.0
PUBLIC 288d0 0 stream_ietf_ext_ref_xor_ic
PUBLIC 28980 0 stream_ref_xor_ic
PUBLIC 28a20 0 stream_ietf_ext_ref
PUBLIC 28ae0 0 stream_ref
PUBLIC 28ba0 0 crypto_stream_keybytes
PUBLIC 28bb0 0 crypto_stream_noncebytes
PUBLIC 28bc0 0 crypto_stream_messagebytes_max
PUBLIC 28bd0 0 crypto_stream_primitive
PUBLIC 28be0 0 crypto_stream
PUBLIC 28bf0 0 crypto_stream_xor
PUBLIC 28c00 0 crypto_stream_keygen
PUBLIC 28c10 0 crypto_stream_salsa20_keybytes
PUBLIC 28c20 0 crypto_stream_salsa20_noncebytes
PUBLIC 28c30 0 crypto_stream_salsa20_messagebytes_max
PUBLIC 28c40 0 crypto_stream_salsa20
PUBLIC 28c60 0 crypto_stream_salsa20_xor_ic
PUBLIC 28c80 0 crypto_stream_salsa20_xor
PUBLIC 28ca0 0 crypto_stream_salsa20_keygen
PUBLIC 28cb0 0 _crypto_stream_salsa20_pick_best_implementation
PUBLIC 28cd0 0 crypto_stream_xsalsa20
PUBLIC 28d80 0 crypto_stream_xsalsa20_xor_ic
PUBLIC 28e40 0 crypto_stream_xsalsa20_xor
PUBLIC 28e50 0 crypto_stream_xsalsa20_keybytes
PUBLIC 28e60 0 crypto_stream_xsalsa20_noncebytes
PUBLIC 28e70 0 crypto_stream_xsalsa20_messagebytes_max
PUBLIC 28e80 0 crypto_stream_xsalsa20_keygen
PUBLIC 28e90 0 crypto_verify_16_bytes
PUBLIC 28ea0 0 crypto_verify_32_bytes
PUBLIC 28eb0 0 crypto_verify_64_bytes
PUBLIC 28ec0 0 crypto_verify_16
PUBLIC 29130 0 crypto_verify_32
PUBLIC 29190 0 crypto_verify_64
PUBLIC 291f0 0 randombytes_set_implementation
PUBLIC 29210 0 randombytes_stir
PUBLIC 29270 0 randombytes_implementation_name
PUBLIC 292d0 0 randombytes_random
PUBLIC 29330 0 randombytes_uniform
PUBLIC 293c0 0 randombytes_buf
PUBLIC 29420 0 randombytes_buf_deterministic
PUBLIC 29450 0 randombytes_seedbytes
PUBLIC 29460 0 randombytes_close
PUBLIC 29490 0 randombytes
PUBLIC 294a0 0 sodium_bin2hex
PUBLIC 298d0 0 sodium_hex2bin
PUBLIC 29ab0 0 sodium_base64_encoded_len
PUBLIC 29b20 0 sodium_bin2base64
PUBLIC 29f00 0 sodium_base642bin
PUBLIC 2a490 0 sodium_crit_enter
PUBLIC 2a4d0 0 sodium_crit_leave
PUBLIC 2a4f0 0 sodium_init
PUBLIC 2a580 0 sodium_misuse
PUBLIC 2a5b0 0 sodium_set_misuse_handler
PUBLIC 2a5f0 0 _sodium_runtime_get_cpu_features
PUBLIC 2a610 0 sodium_runtime_has_neon
PUBLIC 2a620 0 sodium_runtime_has_sse2
PUBLIC 2a630 0 sodium_runtime_has_sse3
PUBLIC 2a640 0 sodium_runtime_has_ssse3
PUBLIC 2a650 0 sodium_runtime_has_sse41
PUBLIC 2a660 0 sodium_runtime_has_avx
PUBLIC 2a670 0 sodium_runtime_has_avx2
PUBLIC 2a680 0 sodium_runtime_has_avx512f
PUBLIC 2a690 0 sodium_runtime_has_pclmul
PUBLIC 2a6a0 0 sodium_runtime_has_aesni
PUBLIC 2a6b0 0 sodium_runtime_has_rdrand
PUBLIC 2a6c0 0 _sodium_dummy_symbol_to_prevent_memzero_lto
PUBLIC 2a6d0 0 sodium_memzero
PUBLIC 2a6e0 0 sodium_stackzero
PUBLIC 2a740 0 _sodium_dummy_symbol_to_prevent_memcmp_lto
PUBLIC 2a750 0 sodium_memcmp
PUBLIC 2a7d0 0 _sodium_dummy_symbol_to_prevent_compare_lto
PUBLIC 2a7e0 0 sodium_compare
PUBLIC 2a890 0 sodium_is_zero
PUBLIC 2a8e0 0 sodium_increment
PUBLIC 2a910 0 sodium_add
PUBLIC 2a950 0 sodium_sub
PUBLIC 2a990 0 _sodium_alloc_init
PUBLIC 2a9e0 0 sodium_mlock
PUBLIC 2aa10 0 sodium_munlock
PUBLIC 2aa50 0 sodium_malloc
PUBLIC 2aba0 0 sodium_allocarray
PUBLIC 2abe0 0 sodium_free
PUBLIC 2ac90 0 sodium_mprotect_noaccess
PUBLIC 2acd0 0 sodium_mprotect_readonly
PUBLIC 2ad10 0 sodium_mprotect_readwrite
PUBLIC 2ad50 0 sodium_pad
PUBLIC 2ae20 0 sodium_unpad
PUBLIC 2aed0 0 sodium_version_string
PUBLIC 2aee0 0 sodium_library_version_major
PUBLIC 2aef0 0 sodium_library_version_minor
PUBLIC 2af00 0 sodium_library_minimal
PUBLIC 2af10 0 stream_ref_xor_ic
PUBLIC 2b2d0 0 stream_ref
PUBLIC 2b460 0 crypto_box_curve25519xchacha20poly1305_seed_keypair
PUBLIC 2b4f0 0 crypto_box_curve25519xchacha20poly1305_keypair
PUBLIC 2b530 0 crypto_box_curve25519xchacha20poly1305_beforenm
PUBLIC 2b5c0 0 crypto_box_curve25519xchacha20poly1305_detached_afternm
PUBLIC 2b5d0 0 crypto_box_curve25519xchacha20poly1305_detached
PUBLIC 2b690 0 crypto_box_curve25519xchacha20poly1305_easy_afternm
PUBLIC 2b6d0 0 crypto_box_curve25519xchacha20poly1305_easy
PUBLIC 2b710 0 crypto_box_curve25519xchacha20poly1305_open_detached_afternm
PUBLIC 2b720 0 crypto_box_curve25519xchacha20poly1305_open_detached
PUBLIC 2b7e0 0 crypto_box_curve25519xchacha20poly1305_open_easy_afternm
PUBLIC 2b810 0 crypto_box_curve25519xchacha20poly1305_open_easy
PUBLIC 2b840 0 crypto_box_curve25519xchacha20poly1305_seedbytes
PUBLIC 2b850 0 crypto_box_curve25519xchacha20poly1305_publickeybytes
PUBLIC 2b860 0 crypto_box_curve25519xchacha20poly1305_secretkeybytes
PUBLIC 2b870 0 crypto_box_curve25519xchacha20poly1305_beforenmbytes
PUBLIC 2b880 0 crypto_box_curve25519xchacha20poly1305_noncebytes
PUBLIC 2b890 0 crypto_box_curve25519xchacha20poly1305_macbytes
PUBLIC 2b8a0 0 crypto_box_curve25519xchacha20poly1305_messagebytes_max
PUBLIC 2b8b0 0 _crypto_box_curve25519xchacha20poly1305_seal_nonce.isra.0
PUBLIC 2b960 0 crypto_box_curve25519xchacha20poly1305_seal
PUBLIC 2baa0 0 crypto_box_curve25519xchacha20poly1305_seal_open
PUBLIC 2bb50 0 crypto_box_curve25519xchacha20poly1305_sealbytes
PUBLIC 2bb60 0 crypto_core_ed25519_is_valid_point
PUBLIC 2bc00 0 crypto_core_ed25519_add
PUBLIC 2bd00 0 crypto_core_ed25519_sub
PUBLIC 2be00 0 crypto_core_ed25519_from_uniform
PUBLIC 2be20 0 crypto_core_ed25519_from_hash
PUBLIC 2be40 0 crypto_core_ed25519_random
PUBLIC 2beb0 0 crypto_core_ed25519_scalar_random
PUBLIC 2bf00 0 crypto_core_ed25519_scalar_invert
PUBLIC 2bf30 0 crypto_core_ed25519_scalar_negate
PUBLIC 2bfe0 0 crypto_core_ed25519_scalar_complement
PUBLIC 2c0a0 0 crypto_core_ed25519_scalar_mul
PUBLIC 2c0b0 0 crypto_core_ed25519_scalar_reduce
PUBLIC 2c130 0 crypto_core_ed25519_scalar_add
PUBLIC 2c1c0 0 crypto_core_ed25519_scalar_sub
PUBLIC 2c240 0 crypto_core_ed25519_bytes
PUBLIC 2c250 0 crypto_core_ed25519_nonreducedscalarbytes
PUBLIC 2c260 0 crypto_core_ed25519_uniformbytes
PUBLIC 2c270 0 crypto_core_ed25519_hashbytes
PUBLIC 2c280 0 crypto_core_ed25519_scalarbytes
PUBLIC 2c290 0 crypto_core_ristretto255_is_valid_point
PUBLIC 2c2f0 0 crypto_core_ristretto255_add
PUBLIC 2c3d0 0 crypto_core_ristretto255_sub
PUBLIC 2c4b0 0 crypto_core_ristretto255_from_hash
PUBLIC 2c4d0 0 crypto_core_ristretto255_random
PUBLIC 2c540 0 crypto_core_ristretto255_scalar_random
PUBLIC 2c550 0 crypto_core_ristretto255_scalar_invert
PUBLIC 2c560 0 crypto_core_ristretto255_scalar_negate
PUBLIC 2c570 0 crypto_core_ristretto255_scalar_complement
PUBLIC 2c580 0 crypto_core_ristretto255_scalar_add
PUBLIC 2c590 0 crypto_core_ristretto255_scalar_sub
PUBLIC 2c5a0 0 crypto_core_ristretto255_scalar_mul
PUBLIC 2c5b0 0 crypto_core_ristretto255_scalar_reduce
PUBLIC 2c5c0 0 crypto_core_ristretto255_bytes
PUBLIC 2c5d0 0 crypto_core_ristretto255_nonreducedscalarbytes
PUBLIC 2c5e0 0 crypto_core_ristretto255_hashbytes
PUBLIC 2c5f0 0 crypto_core_ristretto255_scalarbytes
PUBLIC 2c600 0 escrypt_parse_setting
PUBLIC 2c7d0 0 escrypt_r
PUBLIC 2c9e0 0 escrypt_gensalt_r
PUBLIC 2cbb0 0 crypto_pwhash_scryptsalsa208sha256_ll
PUBLIC 2cca0 0 alloc_region
PUBLIC 2cd10 0 free_region
PUBLIC 2cd60 0 escrypt_init_local
PUBLIC 2cd80 0 escrypt_free_local
PUBLIC 2cd90 0 PBKDF2_SHA256
PUBLIC 2cfb0 0 crypto_pwhash_scryptsalsa208sha256_bytes_min
PUBLIC 2cfc0 0 crypto_pwhash_scryptsalsa208sha256_bytes_max
PUBLIC 2cfd0 0 crypto_pwhash_scryptsalsa208sha256_passwd_min
PUBLIC 2cfe0 0 crypto_pwhash_scryptsalsa208sha256_passwd_max
PUBLIC 2cff0 0 crypto_pwhash_scryptsalsa208sha256_saltbytes
PUBLIC 2d000 0 crypto_pwhash_scryptsalsa208sha256_strbytes
PUBLIC 2d010 0 crypto_pwhash_scryptsalsa208sha256_strprefix
PUBLIC 2d020 0 crypto_pwhash_scryptsalsa208sha256_opslimit_min
PUBLIC 2d030 0 crypto_pwhash_scryptsalsa208sha256_opslimit_max
PUBLIC 2d040 0 crypto_pwhash_scryptsalsa208sha256_memlimit_min
PUBLIC 2d050 0 crypto_pwhash_scryptsalsa208sha256_memlimit_max
PUBLIC 2d060 0 crypto_pwhash_scryptsalsa208sha256_opslimit_interactive
PUBLIC 2d070 0 crypto_pwhash_scryptsalsa208sha256_memlimit_interactive
PUBLIC 2d080 0 crypto_pwhash_scryptsalsa208sha256_opslimit_sensitive
PUBLIC 2d090 0 crypto_pwhash_scryptsalsa208sha256_memlimit_sensitive
PUBLIC 2d0a0 0 crypto_pwhash_scryptsalsa208sha256
PUBLIC 2d220 0 crypto_pwhash_scryptsalsa208sha256_str
PUBLIC 2d400 0 crypto_pwhash_scryptsalsa208sha256_str_verify
PUBLIC 2d520 0 crypto_pwhash_scryptsalsa208sha256_str_needs_rehash
PUBLIC 2d690 0 salsa20_8
PUBLIC 2d880 0 blockmix_salsa8
PUBLIC 2d9b0 0 escrypt_kdf_nosse
PUBLIC 2dee0 0 _crypto_scalarmult_ed25519
PUBLIC 2e0b0 0 _crypto_scalarmult_ed25519_base
PUBLIC 2e230 0 crypto_scalarmult_ed25519
PUBLIC 2e240 0 crypto_scalarmult_ed25519_noclamp
PUBLIC 2e250 0 crypto_scalarmult_ed25519_base
PUBLIC 2e260 0 crypto_scalarmult_ed25519_base_noclamp
PUBLIC 2e270 0 crypto_scalarmult_ed25519_bytes
PUBLIC 2e280 0 crypto_scalarmult_ed25519_scalarbytes
PUBLIC 2e290 0 crypto_scalarmult_ristretto255
PUBLIC 2e390 0 crypto_scalarmult_ristretto255_base
PUBLIC 2e460 0 crypto_scalarmult_ristretto255_bytes
PUBLIC 2e470 0 crypto_scalarmult_ristretto255_scalarbytes
PUBLIC 2e480 0 crypto_secretbox_xchacha20poly1305_detached
PUBLIC 2e690 0 crypto_secretbox_xchacha20poly1305_easy
PUBLIC 2e6d0 0 crypto_secretbox_xchacha20poly1305_open_detached
PUBLIC 2e8c0 0 crypto_secretbox_xchacha20poly1305_open_easy
PUBLIC 2e8f0 0 crypto_secretbox_xchacha20poly1305_keybytes
PUBLIC 2e900 0 crypto_secretbox_xchacha20poly1305_noncebytes
PUBLIC 2e910 0 crypto_secretbox_xchacha20poly1305_macbytes
PUBLIC 2e920 0 crypto_secretbox_xchacha20poly1305_messagebytes_max
PUBLIC 2e930 0 crypto_shorthash_siphashx24_bytes
PUBLIC 2e940 0 crypto_shorthash_siphashx24_keybytes
PUBLIC 2e950 0 crypto_shorthash_siphashx24
PUBLIC 2ec60 0 crypto_sign_edwards25519sha512batch_keypair
PUBLIC 2ed20 0 crypto_sign_edwards25519sha512batch
PUBLIC 2eed0 0 crypto_sign_edwards25519sha512batch_open
PUBLIC 2f0b0 0 crypto_stream_salsa2012
PUBLIC 2f240 0 crypto_stream_salsa2012_xor
PUBLIC 2f600 0 crypto_stream_salsa2012_keybytes
PUBLIC 2f610 0 crypto_stream_salsa2012_noncebytes
PUBLIC 2f620 0 crypto_stream_salsa2012_messagebytes_max
PUBLIC 2f630 0 crypto_stream_salsa2012_keygen
PUBLIC 2f640 0 crypto_stream_salsa208
PUBLIC 2f7d0 0 crypto_stream_salsa208_xor
PUBLIC 2fb90 0 crypto_stream_salsa208_keybytes
PUBLIC 2fba0 0 crypto_stream_salsa208_noncebytes
PUBLIC 2fbb0 0 crypto_stream_salsa208_messagebytes_max
PUBLIC 2fbc0 0 crypto_stream_salsa208_keygen
PUBLIC 2fbd0 0 crypto_stream_xchacha20_keybytes
PUBLIC 2fbe0 0 crypto_stream_xchacha20_noncebytes
PUBLIC 2fbf0 0 crypto_stream_xchacha20_messagebytes_max
PUBLIC 2fc00 0 crypto_stream_xchacha20
PUBLIC 2fc90 0 crypto_stream_xchacha20_xor_ic
PUBLIC 2fd40 0 crypto_stream_xchacha20_xor
PUBLIC 2fd50 0 crypto_stream_xchacha20_keygen
PUBLIC 2fd60 0 randombytes_sysrandom_implementation_name
PUBLIC 2fd70 0 randombytes_sysrandom_close
PUBLIC 2fde0 0 randombytes_sysrandom_random_dev_open
PUBLIC 2ff60 0 randombytes_sysrandom_buf
PUBLIC 30130 0 randombytes_sysrandom
PUBLIC 30190 0 randombytes_sysrandom_stir
PUBLIC 30280 0 crypto_aead_aes256gcm_encrypt_detached
PUBLIC 302b0 0 crypto_aead_aes256gcm_encrypt
PUBLIC 302e0 0 crypto_aead_aes256gcm_decrypt_detached
PUBLIC 30310 0 crypto_aead_aes256gcm_decrypt
PUBLIC 30340 0 crypto_aead_aes256gcm_beforenm
PUBLIC 30370 0 crypto_aead_aes256gcm_encrypt_detached_afternm
PUBLIC 303a0 0 crypto_aead_aes256gcm_encrypt_afternm
PUBLIC 303d0 0 crypto_aead_aes256gcm_decrypt_detached_afternm
PUBLIC 30400 0 crypto_aead_aes256gcm_decrypt_afternm
PUBLIC 30430 0 crypto_aead_aes256gcm_is_available
PUBLIC 30440 0 crypto_aead_aes256gcm_keybytes
PUBLIC 30450 0 crypto_aead_aes256gcm_nsecbytes
PUBLIC 30460 0 crypto_aead_aes256gcm_npubbytes
PUBLIC 30470 0 crypto_aead_aes256gcm_abytes
PUBLIC 30480 0 crypto_aead_aes256gcm_statebytes
PUBLIC 30490 0 crypto_aead_aes256gcm_messagebytes_max
PUBLIC 304a0 0 crypto_aead_aes256gcm_keygen
PUBLIC 304b0 0 randombytes_internal_implementation_name
PUBLIC 304c0 0 randombytes_internal_random_close
PUBLIC 30510 0 randombytes_internal_random_stir
PUBLIC 307b0 0 randombytes_internal_random
PUBLIC 308a0 0 randombytes_internal_random_buf
PUBLIC 30964 0 _fini
STACK CFI INIT fb80 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT fbb0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT fbf0 48 .cfa: sp 0 + .ra: x30
STACK CFI fbf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI fbfc x19: .cfa -16 + ^
STACK CFI fc34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT fc40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fc50 150 .cfa: sp 0 + .ra: x30
STACK CFI fc54 .cfa: sp 448 + .ra: .cfa -440 + ^ x29: .cfa -448 + ^
STACK CFI fc5c x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI fc64 x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI fc70 x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI fc80 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI fc88 x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI fd98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI fd9c .cfa: sp 448 + .ra: .cfa -440 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^ x29: .cfa -448 + ^
STACK CFI INIT fda0 70 .cfa: sp 0 + .ra: x30
STACK CFI fda4 .cfa: sp 48 +
STACK CFI fdac .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI fdb4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI fe08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fe0c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT fe10 180 .cfa: sp 0 + .ra: x30
STACK CFI fe14 .cfa: sp 448 + .ra: .cfa -440 + ^ x29: .cfa -448 + ^
STACK CFI fe1c x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI fe24 x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI fe30 x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI fe40 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI fe48 x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI ff88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ff8c .cfa: sp 448 + .ra: .cfa -440 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^ x29: .cfa -448 + ^
STACK CFI INIT ff90 74 .cfa: sp 0 + .ra: x30
STACK CFI ff94 .cfa: sp 48 +
STACK CFI ff98 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ffa0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI fffc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10000 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10010 184 .cfa: sp 0 + .ra: x30
STACK CFI 10014 .cfa: sp 448 + .ra: .cfa -440 + ^ x29: .cfa -448 + ^
STACK CFI 1001c x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 10028 x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 10038 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 10044 x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 1004c x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 10174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10178 .cfa: sp 448 + .ra: .cfa -440 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^ x29: .cfa -448 + ^
STACK CFI INIT 101a0 6c .cfa: sp 0 + .ra: x30
STACK CFI 101a4 .cfa: sp 48 +
STACK CFI 101ac .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 101b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 101f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 101fc .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10210 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 10214 .cfa: sp 448 + .ra: .cfa -440 + ^ x29: .cfa -448 + ^
STACK CFI 1021c x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 10228 x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 10240 x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 1024c x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 103a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 103a8 .cfa: sp 448 + .ra: .cfa -440 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^ x29: .cfa -448 + ^
STACK CFI INIT 103d0 6c .cfa: sp 0 + .ra: x30
STACK CFI 103d4 .cfa: sp 48 +
STACK CFI 103dc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 103e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1042c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10440 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10450 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10460 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10470 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10480 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10490 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 104a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 104b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 104c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 104d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 104e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 104f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10500 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 10504 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI 10510 x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI 1051c x21: .cfa -464 + ^ x22: .cfa -456 + ^
STACK CFI 1052c x23: .cfa -448 + ^ x24: .cfa -440 + ^
STACK CFI 10538 x25: .cfa -432 + ^ x26: .cfa -424 + ^
STACK CFI 10540 x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI 106b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 106bc .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^ x29: .cfa -496 + ^
STACK CFI INIT 106c0 70 .cfa: sp 0 + .ra: x30
STACK CFI 106c4 .cfa: sp 48 +
STACK CFI 106cc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 106d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10728 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1072c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10730 1ec .cfa: sp 0 + .ra: x30
STACK CFI 10734 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI 1073c x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI 10750 x23: .cfa -448 + ^ x24: .cfa -440 + ^
STACK CFI 10764 x21: .cfa -464 + ^ x22: .cfa -456 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^
STACK CFI 10770 x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI 108fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10900 .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^ x29: .cfa -496 + ^
STACK CFI INIT 10920 6c .cfa: sp 0 + .ra: x30
STACK CFI 10924 .cfa: sp 48 +
STACK CFI 1092c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10934 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10978 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1097c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10990 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 109a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 109b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 109c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 109d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 109e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 109f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a10 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10a90 434 .cfa: sp 0 + .ra: x30
STACK CFI 10a94 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 10aac x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 10ab8 x23: .cfa -128 + ^
STACK CFI 10d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 10d20 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x29: .cfa -176 + ^
STACK CFI INIT 10ed0 18 .cfa: sp 0 + .ra: x30
STACK CFI 10ed4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10ee4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10ef0 94 .cfa: sp 0 + .ra: x30
STACK CFI 10ef4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 10f04 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 10f10 x21: .cfa -64 + ^
STACK CFI 10f7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 10f80 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 10f90 94 .cfa: sp 0 + .ra: x30
STACK CFI 10f94 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 10fa4 x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 10fb0 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 1101c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11020 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x29: .cfa -272 + ^
STACK CFI INIT 11030 84 .cfa: sp 0 + .ra: x30
STACK CFI 11034 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 11044 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 110ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 110b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 110c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 110d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 110e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 110f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11100 508 .cfa: sp 0 + .ra: x30
STACK CFI 11104 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 1111c x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 11128 x23: .cfa -224 + ^
STACK CFI 11450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 11454 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x29: .cfa -272 + ^
STACK CFI INIT 11610 18 .cfa: sp 0 + .ra: x30
STACK CFI 11614 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11624 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 11630 94 .cfa: sp 0 + .ra: x30
STACK CFI 11634 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 11644 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 11650 x21: .cfa -96 + ^
STACK CFI 116bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 116c0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 116d0 94 .cfa: sp 0 + .ra: x30
STACK CFI 116d4 .cfa: sp 480 + .ra: .cfa -472 + ^ x29: .cfa -480 + ^
STACK CFI 116e4 x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 116f0 x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI 1175c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11760 .cfa: sp 480 + .ra: .cfa -472 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x29: .cfa -480 + ^
STACK CFI INIT 11770 84 .cfa: sp 0 + .ra: x30
STACK CFI 11774 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 11784 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 117ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 117f0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 11800 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11810 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11820 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11830 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11840 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11850 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11860 68 .cfa: sp 0 + .ra: x30
STACK CFI 11864 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 11874 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 118c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 118c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 118d0 94 .cfa: sp 0 + .ra: x30
STACK CFI 118d4 .cfa: sp 480 + .ra: .cfa -472 + ^ x29: .cfa -480 + ^
STACK CFI 118e4 x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 118f0 x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI 1195c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11960 .cfa: sp 480 + .ra: .cfa -472 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x29: .cfa -480 + ^
STACK CFI INIT 11970 84 .cfa: sp 0 + .ra: x30
STACK CFI 11974 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 11984 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 119ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 119f0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 11a00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11a10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11a20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11a30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11a40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11a50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11a60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11a70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11a80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11a90 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 11aa0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11ab0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11ac0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11ad0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11ae0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11af0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11b00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11b10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11b20 c0 .cfa: sp 0 + .ra: x30
STACK CFI 11b24 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 11b34 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 11b44 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 11b4c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 11bd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 11bd4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 11be0 34 .cfa: sp 0 + .ra: x30
STACK CFI 11c0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 11c20 38 .cfa: sp 0 + .ra: x30
STACK CFI 11c50 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 11c60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11c70 c0 .cfa: sp 0 + .ra: x30
STACK CFI 11c74 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 11c84 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 11c94 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 11c9c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 11d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 11d24 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 11d30 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 11d60 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11d90 a8 .cfa: sp 0 + .ra: x30
STACK CFI 11d94 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI 11da4 x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI 11db0 x21: .cfa -464 + ^ x22: .cfa -456 + ^
STACK CFI 11e30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11e34 .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x29: .cfa -496 + ^
STACK CFI INIT 11e40 13c .cfa: sp 0 + .ra: x30
STACK CFI 11e44 .cfa: sp 608 +
STACK CFI 11e50 .ra: .cfa -600 + ^ x29: .cfa -608 + ^
STACK CFI 11e58 x19: .cfa -592 + ^ x20: .cfa -584 + ^
STACK CFI 11e60 x21: .cfa -576 + ^ x22: .cfa -568 + ^
STACK CFI 11e6c x23: .cfa -560 + ^ x24: .cfa -552 + ^
STACK CFI 11e78 x25: .cfa -544 + ^
STACK CFI 11f6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 11f70 .cfa: sp 608 + .ra: .cfa -600 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x29: .cfa -608 + ^
STACK CFI INIT 11f80 b0 .cfa: sp 0 + .ra: x30
STACK CFI 11f84 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 11f94 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 11fb8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 11fc8 x23: .cfa -48 + ^
STACK CFI 11ff0 x21: x21 x22: x22
STACK CFI 11ff4 x23: x23
STACK CFI 12018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1201c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 12028 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1202c x23: .cfa -48 + ^
STACK CFI INIT 12030 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12040 90 .cfa: sp 0 + .ra: x30
STACK CFI 12044 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 12054 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 12060 x21: .cfa -96 + ^
STACK CFI 120c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 120cc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 120d0 34 .cfa: sp 0 + .ra: x30
STACK CFI 120d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 120dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 12100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 12110 8c .cfa: sp 0 + .ra: x30
STACK CFI 12114 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1212c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1218c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12190 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 121a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 121b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 121c0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 121c4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 121d4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 121e4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 121ec x23: .cfa -64 + ^
STACK CFI 12268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1226c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 12280 b8 .cfa: sp 0 + .ra: x30
STACK CFI 12284 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 12294 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 122a4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 122ac x23: .cfa -64 + ^
STACK CFI 12328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1232c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x29: .cfa -112 + ^
STACK CFI INIT 12340 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12350 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12360 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12370 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12380 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12390 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 123a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 123b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 123c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 123d0 88 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12460 238 .cfa: sp 0 + .ra: x30
STACK CFI 12464 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12470 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 12478 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12480 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 12688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 126a0 188 .cfa: sp 0 + .ra: x30
STACK CFI 126a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 126c0 x19: .cfa -16 + ^
STACK CFI 12804 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12830 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 12834 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 12988 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 129e0 110 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12af0 164 .cfa: sp 0 + .ra: x30
STACK CFI 12af4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 12afc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 12b10 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 12b18 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 12c4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 12c50 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 12c60 11c .cfa: sp 0 + .ra: x30
STACK CFI 12c64 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 12c74 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 12c80 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 12c8c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 12d74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 12d78 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 12d80 3b4 .cfa: sp 0 + .ra: x30
STACK CFI 12d84 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 12d94 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 12da0 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 12dac x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 12db4 x25: .cfa -144 + ^
STACK CFI 1312c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 13130 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x29: .cfa -208 + ^
STACK CFI INIT 13140 6bc .cfa: sp 0 + .ra: x30
STACK CFI 13144 .cfa: sp 320 + .ra: .cfa -312 + ^ x29: .cfa -320 + ^
STACK CFI 13190 x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 137f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 137f8 .cfa: sp 320 + .ra: .cfa -312 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^ x29: .cfa -320 + ^
STACK CFI INIT 13800 874 .cfa: sp 0 + .ra: x30
STACK CFI 13804 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 1384c x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^
STACK CFI 1406c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 14070 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x25: .cfa -288 + ^ x26: .cfa -280 + ^ x27: .cfa -272 + ^ x28: .cfa -264 + ^ x29: .cfa -352 + ^
STACK CFI INIT 14080 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 140c0 1b0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14270 270 .cfa: sp 0 + .ra: x30
STACK CFI 14274 .cfa: sp 464 + .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI 14284 x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 14290 x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI 142a0 x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 142a8 x25: .cfa -400 + ^
STACK CFI 144d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 144dc .cfa: sp 464 + .ra: .cfa -456 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x29: .cfa -464 + ^
STACK CFI INIT 144e0 3d0 .cfa: sp 0 + .ra: x30
STACK CFI 144e4 .cfa: sp 800 +
STACK CFI 144f4 .ra: .cfa -792 + ^ x29: .cfa -800 + ^
STACK CFI 144fc x19: .cfa -784 + ^ x20: .cfa -776 + ^
STACK CFI 14508 x21: .cfa -768 + ^ x22: .cfa -760 + ^
STACK CFI 14518 x23: .cfa -752 + ^ x24: .cfa -744 + ^
STACK CFI 14520 x25: .cfa -736 + ^ x26: .cfa -728 + ^
STACK CFI 1452c x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI 148a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 148ac .cfa: sp 800 + .ra: .cfa -792 + ^ x19: .cfa -784 + ^ x20: .cfa -776 + ^ x21: .cfa -768 + ^ x22: .cfa -760 + ^ x23: .cfa -752 + ^ x24: .cfa -744 + ^ x25: .cfa -736 + ^ x26: .cfa -728 + ^ x27: .cfa -720 + ^ x28: .cfa -712 + ^ x29: .cfa -800 + ^
STACK CFI INIT 148b0 3cc .cfa: sp 0 + .ra: x30
STACK CFI 148b4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 148c4 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 148d0 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 148dc x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 148e4 x25: .cfa -192 + ^
STACK CFI 14c74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 14c78 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x29: .cfa -256 + ^
STACK CFI INIT 14c80 170 .cfa: sp 0 + .ra: x30
STACK CFI 14c84 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 14c8c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 14ca0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 14ca8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 14de8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 14dec .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 14df0 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 14df4 .cfa: sp 512 +
STACK CFI 14e00 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI 14e08 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 14e14 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 14e20 x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 14e2c x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI 15088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1508c .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x29: .cfa -512 + ^
STACK CFI INIT 15090 22c .cfa: sp 0 + .ra: x30
STACK CFI 15094 .cfa: sp 416 + .ra: .cfa -408 + ^ x29: .cfa -416 + ^
STACK CFI 150a4 x19: .cfa -400 + ^ x20: .cfa -392 + ^
STACK CFI 150b0 x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI 150bc x23: .cfa -368 + ^ x24: .cfa -360 + ^
STACK CFI 150c8 x25: .cfa -352 + ^
STACK CFI 1528c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 15290 .cfa: sp 416 + .ra: .cfa -408 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^ x29: .cfa -416 + ^
STACK CFI INIT 152c0 268 .cfa: sp 0 + .ra: x30
STACK CFI 152c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 152d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 152dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 152f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 15334 x25: .cfa -16 + ^
STACK CFI 15524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 15530 27c .cfa: sp 0 + .ra: x30
STACK CFI 15534 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 15544 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 15550 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 155a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 157a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 157b0 dc .cfa: sp 0 + .ra: x30
STACK CFI INIT 15890 b8 .cfa: sp 0 + .ra: x30
STACK CFI 15894 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 158a4 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 158b0 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 15940 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 15944 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x29: .cfa -208 + ^
STACK CFI INIT 15950 738 .cfa: sp 0 + .ra: x30
STACK CFI 15954 .cfa: sp 960 +
STACK CFI 15960 .ra: .cfa -952 + ^ x29: .cfa -960 + ^
STACK CFI 15968 x19: .cfa -944 + ^ x20: .cfa -936 + ^
STACK CFI 15978 x21: .cfa -928 + ^ x22: .cfa -920 + ^
STACK CFI 1598c x23: .cfa -912 + ^ x24: .cfa -904 + ^ x25: .cfa -896 + ^ x26: .cfa -888 + ^
STACK CFI 15998 x27: .cfa -880 + ^ x28: .cfa -872 + ^
STACK CFI 1607c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 16080 .cfa: sp 960 + .ra: .cfa -952 + ^ x19: .cfa -944 + ^ x20: .cfa -936 + ^ x21: .cfa -928 + ^ x22: .cfa -920 + ^ x23: .cfa -912 + ^ x24: .cfa -904 + ^ x25: .cfa -896 + ^ x26: .cfa -888 + ^ x27: .cfa -880 + ^ x28: .cfa -872 + ^ x29: .cfa -960 + ^
STACK CFI INIT 16090 174 .cfa: sp 0 + .ra: x30
STACK CFI 16094 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1609c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 160b0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 160b8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 161fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 16200 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 16210 b8 .cfa: sp 0 + .ra: x30
STACK CFI 16214 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 16224 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 16230 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 162c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 162c4 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x29: .cfa -208 + ^
STACK CFI INIT 162d0 4e8 .cfa: sp 0 + .ra: x30
STACK CFI 162d4 .cfa: sp 2464 +
STACK CFI 162e4 .ra: .cfa -2456 + ^ x29: .cfa -2464 + ^
STACK CFI 162ec x19: .cfa -2448 + ^ x20: .cfa -2440 + ^
STACK CFI 162f8 x21: .cfa -2432 + ^ x22: .cfa -2424 + ^
STACK CFI 16304 x23: .cfa -2416 + ^ x24: .cfa -2408 + ^
STACK CFI 16310 x25: .cfa -2400 + ^ x26: .cfa -2392 + ^
STACK CFI 16528 x27: .cfa -2384 + ^ x28: .cfa -2376 + ^
STACK CFI 165f4 x27: x27 x28: x28
STACK CFI 16628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1662c .cfa: sp 2464 + .ra: .cfa -2456 + ^ x19: .cfa -2448 + ^ x20: .cfa -2440 + ^ x21: .cfa -2432 + ^ x22: .cfa -2424 + ^ x23: .cfa -2416 + ^ x24: .cfa -2408 + ^ x25: .cfa -2400 + ^ x26: .cfa -2392 + ^ x27: .cfa -2384 + ^ x28: .cfa -2376 + ^ x29: .cfa -2464 + ^
STACK CFI 167b0 x27: x27 x28: x28
STACK CFI 167b4 x27: .cfa -2384 + ^ x28: .cfa -2376 + ^
STACK CFI INIT 167c0 410 .cfa: sp 0 + .ra: x30
STACK CFI 167c8 .cfa: sp 4128 +
STACK CFI 167d4 .ra: .cfa -4120 + ^ x29: .cfa -4128 + ^
STACK CFI 167dc x19: .cfa -4112 + ^ x20: .cfa -4104 + ^
STACK CFI 167e8 x21: .cfa -4096 + ^ x22: .cfa -4088 + ^
STACK CFI 167f4 x23: .cfa -4080 + ^ x24: .cfa -4072 + ^
STACK CFI 16800 x25: .cfa -4064 + ^
STACK CFI 16bc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 16bcc .cfa: sp 4128 + .ra: .cfa -4120 + ^ x19: .cfa -4112 + ^ x20: .cfa -4104 + ^ x21: .cfa -4096 + ^ x22: .cfa -4088 + ^ x23: .cfa -4080 + ^ x24: .cfa -4072 + ^ x25: .cfa -4064 + ^ x29: .cfa -4128 + ^
STACK CFI INIT 16bd0 24c .cfa: sp 0 + .ra: x30
STACK CFI 16bd8 .cfa: sp 576 +
STACK CFI 16be8 .ra: .cfa -568 + ^ x29: .cfa -576 + ^
STACK CFI 16bf0 x19: .cfa -560 + ^ x20: .cfa -552 + ^
STACK CFI 16c00 x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^
STACK CFI 16c10 x25: .cfa -512 + ^ x26: .cfa -504 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^
STACK CFI 16e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 16e18 .cfa: sp 576 + .ra: .cfa -568 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^ x25: .cfa -512 + ^ x26: .cfa -504 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^ x29: .cfa -576 + ^
STACK CFI INIT 16e20 134 .cfa: sp 0 + .ra: x30
STACK CFI 16e24 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 16e38 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 16e40 x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 16e4c x23: .cfa -304 + ^
STACK CFI 16f4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 16f50 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x29: .cfa -352 + ^
STACK CFI INIT 16f60 368 .cfa: sp 0 + .ra: x30
STACK CFI 16f64 .cfa: sp 2192 +
STACK CFI 16f74 .ra: .cfa -2184 + ^ x29: .cfa -2192 + ^
STACK CFI 16f7c x19: .cfa -2176 + ^ x20: .cfa -2168 + ^
STACK CFI 16f88 x21: .cfa -2160 + ^ x22: .cfa -2152 + ^
STACK CFI 16f94 x23: .cfa -2144 + ^ x24: .cfa -2136 + ^
STACK CFI 16f9c x25: .cfa -2128 + ^ x26: .cfa -2120 + ^
STACK CFI 16fa8 x27: .cfa -2112 + ^ x28: .cfa -2104 + ^
STACK CFI 17298 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1729c .cfa: sp 2192 + .ra: .cfa -2184 + ^ x19: .cfa -2176 + ^ x20: .cfa -2168 + ^ x21: .cfa -2160 + ^ x22: .cfa -2152 + ^ x23: .cfa -2144 + ^ x24: .cfa -2136 + ^ x25: .cfa -2128 + ^ x26: .cfa -2120 + ^ x27: .cfa -2112 + ^ x28: .cfa -2104 + ^ x29: .cfa -2192 + ^
STACK CFI INIT 172d0 ac .cfa: sp 0 + .ra: x30
STACK CFI INIT 17380 2f8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17680 df8 .cfa: sp 0 + .ra: x30
STACK CFI 17684 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 1769c x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 18474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 18480 db0 .cfa: sp 0 + .ra: x30
STACK CFI 18484 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 1849c x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 1922c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 19230 62c .cfa: sp 0 + .ra: x30
STACK CFI 19234 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 19248 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 19254 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 19260 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 1926c x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 19278 x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 19854 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 19858 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^ x29: .cfa -368 + ^
STACK CFI INIT 19860 75c .cfa: sp 0 + .ra: x30
STACK CFI 19864 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 19890 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 1992c x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 19934 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 19fb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 19fc0 50 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a010 90 .cfa: sp 0 + .ra: x30
STACK CFI 1a014 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1a020 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1a02c x21: .cfa -64 + ^
STACK CFI 1a098 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1a09c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1a0a0 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 1a0a4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 1a0ac x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 1a0bc x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 1a378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1a37c .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI INIT 1a380 368 .cfa: sp 0 + .ra: x30
STACK CFI 1a384 .cfa: sp 624 +
STACK CFI 1a38c .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI 1a39c x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 1a450 x27: .cfa -544 + ^
STACK CFI 1a45c x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 1a464 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 1a470 x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI 1a68c x21: x21 x22: x22
STACK CFI 1a694 x23: x23 x24: x24
STACK CFI 1a69c x25: x25 x26: x26
STACK CFI 1a6a0 x27: x27
STACK CFI 1a6c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a6cc .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x29: .cfa -624 + ^
STACK CFI 1a6d8 x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 1a6dc x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 1a6e0 x25: .cfa -560 + ^ x26: .cfa -552 + ^
STACK CFI 1a6e4 x27: .cfa -544 + ^
STACK CFI INIT 1a6f0 3a8 .cfa: sp 0 + .ra: x30
STACK CFI 1a6f4 .cfa: sp 960 +
STACK CFI 1a6fc .ra: .cfa -952 + ^ x29: .cfa -960 + ^
STACK CFI 1a704 x19: .cfa -944 + ^ x20: .cfa -936 + ^
STACK CFI 1a718 x21: .cfa -928 + ^ x22: .cfa -920 + ^
STACK CFI 1a720 x23: .cfa -912 + ^ x24: .cfa -904 + ^
STACK CFI 1a730 x25: .cfa -896 + ^ x26: .cfa -888 + ^
STACK CFI 1a738 x27: .cfa -880 + ^ x28: .cfa -872 + ^
STACK CFI 1aa90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1aa94 .cfa: sp 960 + .ra: .cfa -952 + ^ x19: .cfa -944 + ^ x20: .cfa -936 + ^ x21: .cfa -928 + ^ x22: .cfa -920 + ^ x23: .cfa -912 + ^ x24: .cfa -904 + ^ x25: .cfa -896 + ^ x26: .cfa -888 + ^ x27: .cfa -880 + ^ x28: .cfa -872 + ^ x29: .cfa -960 + ^
STACK CFI INIT 1aaa0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 1aaa4 .cfa: sp 944 +
STACK CFI 1aab0 .ra: .cfa -936 + ^ x29: .cfa -944 + ^
STACK CFI 1aab8 x19: .cfa -928 + ^ x20: .cfa -920 + ^
STACK CFI 1aac4 x21: .cfa -912 + ^ x22: .cfa -904 + ^
STACK CFI 1ab78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1ab7c .cfa: sp 944 + .ra: .cfa -936 + ^ x19: .cfa -928 + ^ x20: .cfa -920 + ^ x21: .cfa -912 + ^ x22: .cfa -904 + ^ x29: .cfa -944 + ^
STACK CFI INIT 1ab80 1f4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ad80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ad90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ada0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1adb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1adc0 194 .cfa: sp 0 + .ra: x30
STACK CFI 1adc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1add4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1af2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1af30 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1af60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1af70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1af80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1af90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1afa0 264 .cfa: sp 0 + .ra: x30
STACK CFI 1afa4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1afb4 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 1afc0 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 1b1d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1b1dc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1b210 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b220 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b230 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b240 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b250 264 .cfa: sp 0 + .ra: x30
STACK CFI 1b254 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1b264 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 1b270 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 1b488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1b48c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1b4c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b4d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b4e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b4f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b500 264 .cfa: sp 0 + .ra: x30
STACK CFI 1b504 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1b514 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 1b520 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 1b738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1b73c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 1b770 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b780 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b790 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b7a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b7b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b7c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b7d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b7e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b7f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b800 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b810 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b820 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b830 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b840 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b850 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b860 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b870 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b880 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b890 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b8a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b8b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b8c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b8d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b8e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b8f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b900 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b910 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b920 1798 .cfa: sp 0 + .ra: x30
STACK CFI 1b924 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 1b940 x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 1b964 x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 1d0b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1d0b4 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^ x29: .cfa -368 + ^
STACK CFI INIT 1d0c0 14c .cfa: sp 0 + .ra: x30
STACK CFI 1d0c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d0cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1d178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1d17c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1d208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1d210 90 .cfa: sp 0 + .ra: x30
STACK CFI 1d21c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1d294 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1d298 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1d2a0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 1d2ac .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1d334 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1d338 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1d350 108 .cfa: sp 0 + .ra: x30
STACK CFI 1d358 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1d360 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1d36c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1d378 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1d384 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1d38c x27: .cfa -16 + ^
STACK CFI 1d44c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI INIT 1d460 108 .cfa: sp 0 + .ra: x30
STACK CFI 1d464 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 1d488 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 1d55c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d560 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x29: .cfa -256 + ^
STACK CFI INIT 1d570 130 .cfa: sp 0 + .ra: x30
STACK CFI 1d574 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 1d598 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 1d680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d684 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x29: .cfa -256 + ^
STACK CFI INIT 1d6a0 180 .cfa: sp 0 + .ra: x30
STACK CFI 1d6a4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1d6b4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 1d6cc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 1d6f0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 1d790 x23: x23 x24: x24
STACK CFI 1d7b4 x19: x19 x20: x20
STACK CFI 1d7bc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1d7c0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI 1d808 x23: x23 x24: x24
STACK CFI 1d814 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 1d818 x23: x23 x24: x24
STACK CFI 1d81c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI INIT 1d820 f0 .cfa: sp 0 + .ra: x30
STACK CFI 1d824 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI 1d83c x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI 1d848 x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^
STACK CFI 1d8fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1d900 .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x29: .cfa -496 + ^
STACK CFI INIT 1d910 108 .cfa: sp 0 + .ra: x30
STACK CFI 1d914 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI 1d92c x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI 1d938 x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^
STACK CFI 1d9f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1d9f8 .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x29: .cfa -496 + ^
STACK CFI INIT 1da20 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1da40 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1da80 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dac0 68 .cfa: sp 0 + .ra: x30
STACK CFI 1dae4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1db08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1db0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1db1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1db30 74 .cfa: sp 0 + .ra: x30
STACK CFI 1db50 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1db7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1db80 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1db98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1dbb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dbc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dbd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dbe0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dbf0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dc00 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dc10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dc20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dc30 aa0 .cfa: sp 0 + .ra: x30
STACK CFI 1dc34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1dc50 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1e578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1e584 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1e6a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1e6d0 544 .cfa: sp 0 + .ra: x30
STACK CFI 1e6d4 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 1e6e4 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 1e6f0 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 1e6f8 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 1e82c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1e830 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x29: .cfa -368 + ^
STACK CFI INIT 1ec20 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ec40 24 .cfa: sp 0 + .ra: x30
STACK CFI 1ec48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ec58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ec70 174 .cfa: sp 0 + .ra: x30
STACK CFI 1ec74 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 1ec7c x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 1ec98 x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^
STACK CFI 1edc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1edc4 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x29: .cfa -368 + ^
STACK CFI INIT 1edf0 8c .cfa: sp 0 + .ra: x30
STACK CFI 1edf4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1ee04 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 1ee10 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 1ee74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1ee78 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x29: .cfa -160 + ^
STACK CFI INIT 1ee80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ee90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1eea0 ad8 .cfa: sp 0 + .ra: x30
STACK CFI 1eea4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1eec0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 1f868 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1f86c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 1f974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1f980 65c .cfa: sp 0 + .ra: x30
STACK CFI 1f984 .cfa: sp 800 +
STACK CFI 1f988 .ra: .cfa -792 + ^ x29: .cfa -800 + ^
STACK CFI 1f990 x19: .cfa -784 + ^ x20: .cfa -776 + ^
STACK CFI 1f99c x21: .cfa -768 + ^ x22: .cfa -760 + ^
STACK CFI 1f9d4 x25: .cfa -736 + ^
STACK CFI 1f9f8 x23: .cfa -752 + ^ x24: .cfa -744 + ^
STACK CFI 1faec x23: x23 x24: x24
STACK CFI 1fb1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI 1fb20 .cfa: sp 800 + .ra: .cfa -792 + ^ x19: .cfa -784 + ^ x20: .cfa -776 + ^ x21: .cfa -768 + ^ x22: .cfa -760 + ^ x25: .cfa -736 + ^ x29: .cfa -800 + ^
STACK CFI 1fb80 x23: .cfa -752 + ^ x24: .cfa -744 + ^
STACK CFI 1fe54 x23: x23 x24: x24
STACK CFI 1ffa8 x23: .cfa -752 + ^ x24: .cfa -744 + ^
STACK CFI 1ffc8 x23: x23 x24: x24
STACK CFI 1ffd8 x23: .cfa -752 + ^ x24: .cfa -744 + ^
STACK CFI INIT 1ffe0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 20010 24 .cfa: sp 0 + .ra: x30
STACK CFI 20018 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 20028 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 20040 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 20044 .cfa: sp 768 +
STACK CFI 20048 .ra: .cfa -760 + ^ x29: .cfa -768 + ^
STACK CFI 20050 x19: .cfa -752 + ^ x20: .cfa -744 + ^
STACK CFI 20064 x21: .cfa -736 + ^ x22: .cfa -728 + ^
STACK CFI 20204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 20208 .cfa: sp 768 + .ra: .cfa -760 + ^ x19: .cfa -752 + ^ x20: .cfa -744 + ^ x21: .cfa -736 + ^ x22: .cfa -728 + ^ x29: .cfa -768 + ^
STACK CFI INIT 20230 8c .cfa: sp 0 + .ra: x30
STACK CFI 20234 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 20244 x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 20250 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 202b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 202b8 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x29: .cfa -272 + ^
STACK CFI INIT 202c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 202d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 202e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 202f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20300 8c .cfa: sp 0 + .ra: x30
STACK CFI 2030c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2036c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 20370 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI INIT 20390 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 203a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 203b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 203c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 203d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 203e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 203f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20400 40 .cfa: sp 0 + .ra: x30
STACK CFI 20404 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20418 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2043c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 20440 34 .cfa: sp 0 + .ra: x30
STACK CFI 20444 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2044c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 20470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 20480 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 20484 .cfa: sp 624 +
STACK CFI 20490 .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI 20498 x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 204a4 x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 204b4 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 204bc x25: .cfa -560 + ^
STACK CFI 205d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 205d4 .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x29: .cfa -624 + ^
STACK CFI INIT 20620 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 20624 .cfa: sp 624 +
STACK CFI 20630 .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI 20638 x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 20644 x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 20654 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 2065c x25: .cfa -560 + ^
STACK CFI 20770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 20774 .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x29: .cfa -624 + ^
STACK CFI INIT 207c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 207d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 207e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 207f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20800 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 20810 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20820 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20830 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20840 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20850 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20860 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20870 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20880 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20890 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 208a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 208b0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 208d0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 208f0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20910 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20930 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20950 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20960 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20970 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20980 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20990 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 209b0 120 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20ad0 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20b30 104 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20c40 144 .cfa: sp 0 + .ra: x30
STACK CFI 20c44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20c4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 20d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 20d90 18 .cfa: sp 0 + .ra: x30
STACK CFI 20d94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 20da4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 20db0 10c .cfa: sp 0 + .ra: x30
STACK CFI 20db4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 20dcc x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 20df0 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 20e70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 20e74 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI 20e90 x23: .cfa -176 + ^
STACK CFI 20eac x23: x23
STACK CFI 20eb8 x23: .cfa -176 + ^
STACK CFI INIT 20ec0 504 .cfa: sp 0 + .ra: x30
STACK CFI 20ec4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 20ed0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2101c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 21020 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 21158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2115c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 21160 x21: .cfa -16 + ^
STACK CFI 2117c x21: x21
STACK CFI INIT 213d0 118 .cfa: sp 0 + .ra: x30
STACK CFI 213d4 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 213ec x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 21410 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 2149c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 214a0 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x29: .cfa -240 + ^
STACK CFI 214bc x23: .cfa -192 + ^
STACK CFI 214d8 x23: x23
STACK CFI 214e4 x23: .cfa -192 + ^
STACK CFI INIT 214f0 210 .cfa: sp 0 + .ra: x30
STACK CFI 214f4 .cfa: sp 512 +
STACK CFI 21500 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI 21508 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 21518 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 21520 x23: .cfa -464 + ^
STACK CFI 216d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 216d4 .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x29: .cfa -512 + ^
STACK CFI INIT 21700 80 .cfa: sp 0 + .ra: x30
STACK CFI 21704 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2170c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2177c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 21780 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 21784 .cfa: sp 2128 +
STACK CFI 21790 .ra: .cfa -2120 + ^ x29: .cfa -2128 + ^
STACK CFI 21798 x21: .cfa -2096 + ^ x22: .cfa -2088 + ^
STACK CFI 217dc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 217e0 .cfa: sp 2128 + .ra: .cfa -2120 + ^ x21: .cfa -2096 + ^ x22: .cfa -2088 + ^ x29: .cfa -2128 + ^
STACK CFI 217e4 x19: .cfa -2112 + ^ x20: .cfa -2104 + ^
STACK CFI 217f0 x23: .cfa -2080 + ^
STACK CFI 21900 x19: x19 x20: x20
STACK CFI 21904 x23: x23
STACK CFI 21908 x19: .cfa -2112 + ^ x20: .cfa -2104 + ^ x23: .cfa -2080 + ^
STACK CFI 21954 x19: x19 x20: x20 x23: x23
STACK CFI 21958 x19: .cfa -2112 + ^ x20: .cfa -2104 + ^
STACK CFI 2195c x23: .cfa -2080 + ^
STACK CFI INIT 21960 90 .cfa: sp 0 + .ra: x30
STACK CFI 21968 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 21970 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 21984 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2198c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 219dc x19: x19 x20: x20
STACK CFI 219e0 x23: x23 x24: x24
STACK CFI 219e8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 219f0 118 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21b10 19c .cfa: sp 0 + .ra: x30
STACK CFI 21b14 .cfa: sp 1120 +
STACK CFI 21b18 .ra: .cfa -1112 + ^ x29: .cfa -1120 + ^
STACK CFI 21b20 x23: .cfa -1072 + ^ x24: .cfa -1064 + ^
STACK CFI 21b2c x25: .cfa -1056 + ^
STACK CFI 21b3c x21: .cfa -1088 + ^ x22: .cfa -1080 + ^
STACK CFI 21b5c x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI 21c14 x19: x19 x20: x20
STACK CFI 21c50 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 21c54 .cfa: sp 1120 + .ra: .cfa -1112 + ^ x19: .cfa -1104 + ^ x20: .cfa -1096 + ^ x21: .cfa -1088 + ^ x22: .cfa -1080 + ^ x23: .cfa -1072 + ^ x24: .cfa -1064 + ^ x25: .cfa -1056 + ^ x29: .cfa -1120 + ^
STACK CFI 21ca4 x19: x19 x20: x20
STACK CFI 21ca8 x19: .cfa -1104 + ^ x20: .cfa -1096 + ^
STACK CFI INIT 21cb0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21cd0 148 .cfa: sp 0 + .ra: x30
STACK CFI 21cd4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 21ce4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 21d08 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 21db8 x21: x21 x22: x22
STACK CFI 21de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 21de4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 21e00 x21: x21 x22: x22
STACK CFI 21e14 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI INIT 21e20 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21e40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21e50 4b0 .cfa: sp 0 + .ra: x30
STACK CFI 21e54 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 21e6c x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 22100 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 22104 x25: .cfa -48 + ^
STACK CFI 22260 x23: x23 x24: x24
STACK CFI 22264 x25: x25
STACK CFI 2228c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 22290 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x29: .cfa -112 + ^
STACK CFI 222a4 x23: x23 x24: x24
STACK CFI 222ac x25: x25
STACK CFI 222f8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 222fc x25: .cfa -48 + ^
STACK CFI INIT 22300 778 .cfa: sp 0 + .ra: x30
STACK CFI 22304 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 22318 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 22330 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 223a8 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 22928 x25: x25 x26: x26
STACK CFI 22958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2295c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI 22988 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2298c x25: x25 x26: x26
STACK CFI 22998 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 22a70 x25: x25 x26: x26
STACK CFI 22a74 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI INIT 22a80 6d4 .cfa: sp 0 + .ra: x30
STACK CFI 22a84 .cfa: sp 2144 +
STACK CFI 22a90 .ra: .cfa -2136 + ^ x29: .cfa -2144 + ^
STACK CFI 22a98 x19: .cfa -2128 + ^ x20: .cfa -2120 + ^
STACK CFI 22aa4 x21: .cfa -2112 + ^ x22: .cfa -2104 + ^
STACK CFI 22ab8 x23: .cfa -2096 + ^ x24: .cfa -2088 + ^ x25: .cfa -2080 + ^
STACK CFI 2314c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 23150 .cfa: sp 2144 + .ra: .cfa -2136 + ^ x19: .cfa -2128 + ^ x20: .cfa -2120 + ^ x21: .cfa -2112 + ^ x22: .cfa -2104 + ^ x23: .cfa -2096 + ^ x24: .cfa -2088 + ^ x25: .cfa -2080 + ^ x29: .cfa -2144 + ^
STACK CFI INIT 23160 17c .cfa: sp 0 + .ra: x30
STACK CFI 23168 .cfa: sp 4192 +
STACK CFI 23174 .ra: .cfa -4184 + ^ x29: .cfa -4192 + ^
STACK CFI 2317c x19: .cfa -4176 + ^ x20: .cfa -4168 + ^
STACK CFI 23188 x21: .cfa -4160 + ^ x22: .cfa -4152 + ^
STACK CFI 23194 x23: .cfa -4144 + ^ x24: .cfa -4136 + ^
STACK CFI 23204 x25: .cfa -4128 + ^
STACK CFI 23234 x25: x25
STACK CFI 23268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2326c .cfa: sp 4192 + .ra: .cfa -4184 + ^ x19: .cfa -4176 + ^ x20: .cfa -4168 + ^ x21: .cfa -4160 + ^ x22: .cfa -4152 + ^ x23: .cfa -4144 + ^ x24: .cfa -4136 + ^ x25: .cfa -4128 + ^ x29: .cfa -4192 + ^
STACK CFI 232d0 x25: x25
STACK CFI 232d8 x25: .cfa -4128 + ^
STACK CFI INIT 232e0 6b4 .cfa: sp 0 + .ra: x30
STACK CFI 232e4 .cfa: sp 2144 +
STACK CFI 232f0 .ra: .cfa -2136 + ^ x29: .cfa -2144 + ^
STACK CFI 232f8 x19: .cfa -2128 + ^ x20: .cfa -2120 + ^
STACK CFI 23304 x21: .cfa -2112 + ^ x22: .cfa -2104 + ^
STACK CFI 23318 x23: .cfa -2096 + ^ x24: .cfa -2088 + ^ x25: .cfa -2080 + ^
STACK CFI 2398c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 23990 .cfa: sp 2144 + .ra: .cfa -2136 + ^ x19: .cfa -2128 + ^ x20: .cfa -2120 + ^ x21: .cfa -2112 + ^ x22: .cfa -2104 + ^ x23: .cfa -2096 + ^ x24: .cfa -2088 + ^ x25: .cfa -2080 + ^ x29: .cfa -2144 + ^
STACK CFI INIT 239a0 3d4 .cfa: sp 0 + .ra: x30
STACK CFI 239a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 239b8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 239c0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 239cc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 239d4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 239dc x27: .cfa -32 + ^
STACK CFI 23b18 x19: x19 x20: x20
STACK CFI 23b1c x21: x21 x22: x22
STACK CFI 23b20 x23: x23 x24: x24
STACK CFI 23b24 x25: x25 x26: x26
STACK CFI 23b28 x27: x27
STACK CFI 23b2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 23b30 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 23d60 x19: x19 x20: x20
STACK CFI 23d64 x21: x21 x22: x22
STACK CFI 23d68 x23: x23 x24: x24
STACK CFI 23d6c x25: x25 x26: x26
STACK CFI 23d70 x27: x27
STACK CFI INIT 23d80 10c .cfa: sp 0 + .ra: x30
STACK CFI 23d84 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 23d94 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 23d9c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 23e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 23e80 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 23e90 1ac .cfa: sp 0 + .ra: x30
STACK CFI 23e94 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 23ea8 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 23eb0 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 23ed0 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 23efc x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 23f94 x27: x27 x28: x28
STACK CFI 23fc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 23fcc .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 2400c x27: x27 x28: x28
STACK CFI 2402c x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 24034 x27: x27 x28: x28
STACK CFI INIT 24040 34 .cfa: sp 0 + .ra: x30
STACK CFI 24044 .cfa: sp 48 +
STACK CFI 2404c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 24070 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 24080 30 .cfa: sp 0 + .ra: x30
STACK CFI 24084 .cfa: sp 48 +
STACK CFI 2408c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 240ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 240b0 34 .cfa: sp 0 + .ra: x30
STACK CFI 240b4 .cfa: sp 48 +
STACK CFI 240bc .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 240e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 240f0 30 .cfa: sp 0 + .ra: x30
STACK CFI 240f4 .cfa: sp 48 +
STACK CFI 240fc .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2411c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 24120 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 24124 .cfa: sp 240 +
STACK CFI 24134 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2413c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 24144 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 24150 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 24158 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 2418c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2425c x21: x21 x22: x22
STACK CFI 24294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 24298 .cfa: sp 240 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 242d8 x21: x21 x22: x22
STACK CFI 242e4 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 24304 x21: x21 x22: x22
STACK CFI 2430c x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI INIT 24310 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24320 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24330 23c .cfa: sp 0 + .ra: x30
STACK CFI 24334 .cfa: sp 640 +
STACK CFI 24340 .ra: .cfa -632 + ^ x29: .cfa -640 + ^
STACK CFI 24348 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI 24354 x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI 24378 x23: .cfa -592 + ^ x24: .cfa -584 + ^
STACK CFI 243ec x23: x23 x24: x24
STACK CFI 243f4 x23: .cfa -592 + ^ x24: .cfa -584 + ^
STACK CFI 244cc x23: x23 x24: x24
STACK CFI 24508 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2450c .cfa: sp 640 + .ra: .cfa -632 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x29: .cfa -640 + ^
STACK CFI 24554 x23: x23 x24: x24
STACK CFI 24568 x23: .cfa -592 + ^ x24: .cfa -584 + ^
STACK CFI INIT 24570 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24580 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24590 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 245a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 245b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 245c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 245d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 245e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 245f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24600 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24610 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24620 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24630 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24640 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24650 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24660 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24670 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24680 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24690 fc .cfa: sp 0 + .ra: x30
STACK CFI 24694 .cfa: sp 96 +
STACK CFI 24698 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 246a0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 246ac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 246b8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 246cc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 24760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 24764 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 24790 124 .cfa: sp 0 + .ra: x30
STACK CFI 24794 .cfa: sp 112 +
STACK CFI 247a4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 247ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 247b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 24808 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2484c x23: x23 x24: x24
STACK CFI 24880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 24884 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 248b0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 248c0 5c .cfa: sp 0 + .ra: x30
STACK CFI 248c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 248e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 248e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 24920 134 .cfa: sp 0 + .ra: x30
STACK CFI 24924 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 24934 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 2493c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 24944 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 24a38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 24a3c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI INIT 24a60 134 .cfa: sp 0 + .ra: x30
STACK CFI 24a64 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 24a74 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 24a7c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 24a84 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 24b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 24b7c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x29: .cfa -176 + ^
STACK CFI INIT 24ba0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24bb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24bc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24bd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24be0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24bf0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24c00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24c10 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 24c20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24c30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24c40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24c50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24c60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24c70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24c80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24c90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24ca0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24cb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24cc0 fc .cfa: sp 0 + .ra: x30
STACK CFI 24cc4 .cfa: sp 96 +
STACK CFI 24cc8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 24cd0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 24cdc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 24ce8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 24cfc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 24d90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 24d94 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 24dc0 124 .cfa: sp 0 + .ra: x30
STACK CFI 24dc4 .cfa: sp 112 +
STACK CFI 24dd4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 24ddc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 24de4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 24e38 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 24e7c x23: x23 x24: x24
STACK CFI 24eb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 24eb4 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 24ee0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 24ef0 5c .cfa: sp 0 + .ra: x30
STACK CFI 24ef4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 24f10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 24f14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 24f50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24f60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24f70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24f80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24f90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24fa0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24fb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24fc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24fd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24fe0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 24ff0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25000 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25010 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25020 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25030 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25040 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25050 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25060 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25070 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25080 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25090 3c .cfa: sp 0 + .ra: x30
STACK CFI 250a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 250c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 250d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 250e0 24 .cfa: sp 0 + .ra: x30
STACK CFI 250fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 25110 a4 .cfa: sp 0 + .ra: x30
STACK CFI 25114 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2511c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 25130 x21: .cfa -16 + ^
STACK CFI 25170 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 25174 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2518c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 25190 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 251b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 251c0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 251c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 251cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 251e0 x21: .cfa -16 + ^
STACK CFI 25220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 25224 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2523c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 25240 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 25260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 25270 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 25280 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 25290 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 252a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 252b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 252c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 252d0 88 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25360 238 .cfa: sp 0 + .ra: x30
STACK CFI 25364 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 25370 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 25378 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 25380 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 25588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 255a0 188 .cfa: sp 0 + .ra: x30
STACK CFI 255a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 255c0 x19: .cfa -16 + ^
STACK CFI 25704 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 25730 11c .cfa: sp 0 + .ra: x30
STACK CFI 25734 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 25744 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 25824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 25828 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x29: .cfa -336 + ^
STACK CFI INIT 25850 52c .cfa: sp 0 + .ra: x30
STACK CFI 25854 .cfa: sp 560 +
STACK CFI 25864 .ra: .cfa -552 + ^ x29: .cfa -560 + ^
STACK CFI 2587c x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 25d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 25d5c .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^ x29: .cfa -560 + ^
STACK CFI INIT 25d80 334 .cfa: sp 0 + .ra: x30
STACK CFI 25d84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 25e64 x21: .cfa -16 + ^
STACK CFI 25f44 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2609c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 260a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 260b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 260c0 74 .cfa: sp 0 + .ra: x30
STACK CFI 260c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 260dc x19: .cfa -32 + ^
STACK CFI 26128 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2612c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 26140 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26160 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26170 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26180 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 261a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 261b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 261c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 261d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 261e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 261f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26200 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 26210 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26220 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26230 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26240 204 .cfa: sp 0 + .ra: x30
STACK CFI 26244 .cfa: sp 464 + .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI 26254 x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 26264 x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI 2626c x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 2627c x25: .cfa -400 + ^ x26: .cfa -392 + ^
STACK CFI 26284 x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI 263d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 263d4 .cfa: sp 464 + .ra: .cfa -456 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^ x29: .cfa -464 + ^
STACK CFI INIT 26450 34 .cfa: sp 0 + .ra: x30
STACK CFI 2647c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 26490 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 26494 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 264a4 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 264b4 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 264bc x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 264c8 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 26554 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 265ac x27: x27 x28: x28
STACK CFI 265ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 265f0 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x29: .cfa -208 + ^
STACK CFI 26610 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 26638 x27: x27 x28: x28
STACK CFI 26674 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT 26680 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 266b0 50 .cfa: sp 0 + .ra: x30
STACK CFI 266bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 266c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 266f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 26700 e0 .cfa: sp 0 + .ra: x30
STACK CFI 26704 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 26714 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2672c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 26738 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2679c x21: x21 x22: x22
STACK CFI 267c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 267cc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 267d0 x21: x21 x22: x22
STACK CFI 267dc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI INIT 267e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 267f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26800 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26810 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26820 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26830 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26840 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26850 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26860 6c .cfa: sp 0 + .ra: x30
STACK CFI 26864 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2686c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26880 x21: .cfa -16 + ^
STACK CFI 268c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 268d0 48 .cfa: sp 0 + .ra: x30
STACK CFI 268d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 268e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 26914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 26920 9c .cfa: sp 0 + .ra: x30
STACK CFI 26924 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2692c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 269b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 269b8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 269c0 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 269c4 .cfa: sp 448 + .ra: .cfa -440 + ^ x29: .cfa -448 + ^
STACK CFI 269d4 x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 269dc x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 269e4 x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 269f0 x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 269f8 x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 26bf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 26bf8 .cfa: sp 448 + .ra: .cfa -440 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^ x29: .cfa -448 + ^
STACK CFI INIT 26ca0 284 .cfa: sp 0 + .ra: x30
STACK CFI 26ca4 .cfa: sp 464 + .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI 26cb4 x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 26cbc x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI 26cc4 x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 26ccc x25: .cfa -400 + ^ x26: .cfa -392 + ^
STACK CFI 26cd8 x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI 26ee8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 26eec .cfa: sp 464 + .ra: .cfa -456 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^ x29: .cfa -464 + ^
STACK CFI INIT 26f30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26f40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26f50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26f60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26f70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26f80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26f90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26fa0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26fb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26fc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26fd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26fe0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 26ff0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27000 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27010 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27020 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27030 250 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27280 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27290 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 272a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 272b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 272c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 272d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 272e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 272f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27300 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27310 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27320 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27330 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27340 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27350 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27360 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27370 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27380 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27390 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 273a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 273b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 273c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 273d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 273e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 273f0 1c .cfa: sp 0 + .ra: x30
STACK CFI 273f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 27408 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 27410 20 .cfa: sp 0 + .ra: x30
STACK CFI 27414 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2742c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 27430 18 .cfa: sp 0 + .ra: x30
STACK CFI 27434 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 27444 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 27450 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27460 88 .cfa: sp 0 + .ra: x30
STACK CFI 27464 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 27474 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 27480 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 274e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 274e4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT 274f0 80 .cfa: sp 0 + .ra: x30
STACK CFI 274f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 27504 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 27510 x21: .cfa -96 + ^
STACK CFI 27568 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2756c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 27570 cc .cfa: sp 0 + .ra: x30
STACK CFI 27574 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 27584 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 27590 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 27634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 27638 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI INIT 27640 94 .cfa: sp 0 + .ra: x30
STACK CFI 27644 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 27654 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 27660 x21: .cfa -64 + ^
STACK CFI 276cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 276d0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 276e0 348 .cfa: sp 0 + .ra: x30
STACK CFI 276e4 .cfa: sp 336 + .ra: .cfa -328 + ^ x29: .cfa -336 + ^
STACK CFI 276f4 x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI 276fc x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI 277a4 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 277ac x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI 279e0 x23: x23 x24: x24
STACK CFI 279e4 x25: x25 x26: x26
STACK CFI 27a10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 27a14 .cfa: sp 336 + .ra: .cfa -328 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^ x29: .cfa -336 + ^
STACK CFI 27a20 x23: .cfa -288 + ^ x24: .cfa -280 + ^
STACK CFI 27a24 x25: .cfa -272 + ^ x26: .cfa -264 + ^
STACK CFI INIT 27a30 94 .cfa: sp 0 + .ra: x30
STACK CFI 27a34 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 27a44 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 27abc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 27ac0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 27ad0 178 .cfa: sp 0 + .ra: x30
STACK CFI 27ad4 .cfa: sp 672 +
STACK CFI 27ae0 .ra: .cfa -664 + ^ x29: .cfa -672 + ^
STACK CFI 27ae8 x19: .cfa -656 + ^ x20: .cfa -648 + ^
STACK CFI 27af0 x21: .cfa -640 + ^ x22: .cfa -632 + ^
STACK CFI 27afc x23: .cfa -624 + ^ x24: .cfa -616 + ^
STACK CFI 27b08 x25: .cfa -608 + ^ x26: .cfa -600 + ^
STACK CFI 27c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 27c3c .cfa: sp 672 + .ra: .cfa -664 + ^ x19: .cfa -656 + ^ x20: .cfa -648 + ^ x21: .cfa -640 + ^ x22: .cfa -632 + ^ x23: .cfa -624 + ^ x24: .cfa -616 + ^ x25: .cfa -608 + ^ x26: .cfa -600 + ^ x29: .cfa -672 + ^
STACK CFI INIT 27c50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27c60 c8 .cfa: sp 0 + .ra: x30
STACK CFI 27c64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 27c74 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 27c8c x23: .cfa -16 + ^
STACK CFI 27cc8 x23: x23
STACK CFI 27cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 27cdc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 27cec x23: x23
STACK CFI 27cf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 27cf4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 27d0c x23: x23
STACK CFI 27d20 x23: .cfa -16 + ^
STACK CFI 27d24 x23: x23
STACK CFI INIT 27d30 44 .cfa: sp 0 + .ra: x30
STACK CFI 27d34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27d3c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 27d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 27d58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 27d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 27d80 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 27d84 .cfa: sp 672 +
STACK CFI 27d90 .ra: .cfa -664 + ^ x29: .cfa -672 + ^
STACK CFI 27d98 x19: .cfa -656 + ^ x20: .cfa -648 + ^
STACK CFI 27da4 x21: .cfa -640 + ^ x22: .cfa -632 + ^
STACK CFI 27db0 x23: .cfa -624 + ^ x24: .cfa -616 + ^
STACK CFI 27dbc x25: .cfa -608 + ^ x26: .cfa -600 + ^
STACK CFI 27dc8 x27: .cfa -592 + ^ x28: .cfa -584 + ^
STACK CFI 27f2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 27f30 .cfa: sp 672 + .ra: .cfa -664 + ^ x19: .cfa -656 + ^ x20: .cfa -648 + ^ x21: .cfa -640 + ^ x22: .cfa -632 + ^ x23: .cfa -624 + ^ x24: .cfa -616 + ^ x25: .cfa -608 + ^ x26: .cfa -600 + ^ x27: .cfa -592 + ^ x28: .cfa -584 + ^ x29: .cfa -672 + ^
STACK CFI INIT 27f40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 27f50 cc .cfa: sp 0 + .ra: x30
STACK CFI 27f54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 27f68 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 27f74 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 27ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 27ff8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 28020 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28030 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28040 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28050 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28060 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28070 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28080 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 280a0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 280c0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 280e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28100 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28120 1c .cfa: sp 0 + .ra: x30
STACK CFI 28134 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 28140 24 .cfa: sp 0 + .ra: x30
STACK CFI 2815c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 28170 34 .cfa: sp 0 + .ra: x30
STACK CFI 2819c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 281b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 281c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 281d0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 281f0 6dc .cfa: sp 0 + .ra: x30
STACK CFI 281f4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 2821c x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 28230 x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 287b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 287b8 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT 288d0 ac .cfa: sp 0 + .ra: x30
STACK CFI 288dc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 288f8 x19: .cfa -96 + ^
STACK CFI 2894c x19: x19
STACK CFI 28970 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 28974 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 28978 x19: .cfa -96 + ^
STACK CFI INIT 28980 9c .cfa: sp 0 + .ra: x30
STACK CFI 2898c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 289a8 x19: .cfa -96 + ^
STACK CFI 289ec x19: x19
STACK CFI 28a10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 28a14 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 28a18 x19: .cfa -96 + ^
STACK CFI INIT 28a20 b8 .cfa: sp 0 + .ra: x30
STACK CFI 28a2c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 28a48 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 28aa8 x19: x19 x20: x20
STACK CFI 28acc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 28ad0 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 28ad4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI INIT 28ae0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 28aec .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 28b04 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 28b64 x19: x19 x20: x20
STACK CFI 28b88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 28b8c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 28b90 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI INIT 28ba0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28bb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28bc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28bd0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 28be0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28bf0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28c00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28c10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28c20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28c30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28c40 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28c60 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28c80 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 28ca0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28cb0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28cd0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 28cd4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 28ce4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 28cf0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 28d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 28d70 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 28d80 bc .cfa: sp 0 + .ra: x30
STACK CFI 28d84 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 28d94 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 28da0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 28dac x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 28e34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 28e38 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 28e40 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 28e50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28e60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28e70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28e80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28e90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28ea0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28eb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28ec0 268 .cfa: sp 0 + .ra: x30
STACK CFI 28ec4 .cfa: sp 32 +
STACK CFI 29118 .cfa: sp 0 +
STACK CFI INIT 29130 60 .cfa: sp 0 + .ra: x30
STACK CFI 29134 .cfa: sp 32 +
STACK CFI 29180 .cfa: sp 0 +
STACK CFI INIT 29190 60 .cfa: sp 0 + .ra: x30
STACK CFI 29194 .cfa: sp 32 +
STACK CFI 291e0 .cfa: sp 0 +
STACK CFI INIT 291f0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29210 54 .cfa: sp 0 + .ra: x30
STACK CFI 29214 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2921c x19: .cfa -16 + ^
STACK CFI 2923c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29240 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 29248 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2924c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 29270 54 .cfa: sp 0 + .ra: x30
STACK CFI 29274 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2927c x19: .cfa -16 + ^
STACK CFI 29290 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2929c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 292b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 292d0 54 .cfa: sp 0 + .ra: x30
STACK CFI 292d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 292dc x19: .cfa -16 + ^
STACK CFI 292f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 292fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 29318 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 29330 88 .cfa: sp 0 + .ra: x30
STACK CFI 29334 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2933c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 29364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 29368 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2939c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 293a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 293c0 5c .cfa: sp 0 + .ra: x30
STACK CFI 293c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 293cc x19: .cfa -32 + ^
STACK CFI 293ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 293f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 293fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29400 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 29420 28 .cfa: sp 0 + .ra: x30
STACK CFI 29440 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 29450 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29460 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29490 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 294a0 430 .cfa: sp 0 + .ra: x30
STACK CFI 298c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 298d0 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 298d4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 298dc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 298e4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 298f4 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 29908 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 29914 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 299e4 x25: x25 x26: x26
STACK CFI 299ec x23: x23 x24: x24
STACK CFI 29a08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 29a0c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 29a30 x23: x23 x24: x24
STACK CFI 29a34 x25: x25 x26: x26
STACK CFI 29a38 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 29a58 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 29a70 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 29a84 x23: x23 x24: x24
STACK CFI 29a8c x25: x25 x26: x26
STACK CFI 29a98 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI INIT 29ab0 64 .cfa: sp 0 + .ra: x30
STACK CFI 29b0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 29b20 3d8 .cfa: sp 0 + .ra: x30
STACK CFI 29b24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 29b38 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 29d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 29d74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 29f00 584 .cfa: sp 0 + .ra: x30
STACK CFI 29f04 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 29f14 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 29f18 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 29f1c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 29f2c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 29f44 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2a1c0 x25: x25 x26: x26
STACK CFI 2a1dc x19: x19 x20: x20
STACK CFI 2a1e0 x21: x21 x22: x22
STACK CFI 2a1e4 x23: x23 x24: x24
STACK CFI 2a1e8 x27: x27 x28: x28
STACK CFI 2a1ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2a1f0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 2a224 x25: x25 x26: x26
STACK CFI 2a228 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2a308 x25: x25 x26: x26
STACK CFI 2a32c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2a438 x25: x25 x26: x26
STACK CFI 2a458 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2a478 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2a47c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2a480 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 2a490 34 .cfa: sp 0 + .ra: x30
STACK CFI 2a494 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a49c x19: .cfa -16 + ^
STACK CFI 2a4c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2a4d0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a4f0 90 .cfa: sp 0 + .ra: x30
STACK CFI 2a4f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a504 x19: .cfa -16 + ^
STACK CFI 2a520 x19: x19
STACK CFI 2a528 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2a52c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2a564 x19: x19
STACK CFI 2a56c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2a570 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2a574 x19: x19
STACK CFI 2a57c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2a580 28 .cfa: sp 0 + .ra: x30
STACK CFI 2a584 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2a5b0 40 .cfa: sp 0 + .ra: x30
STACK CFI 2a5b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a5bc x19: .cfa -16 + ^
STACK CFI 2a5e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2a5e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2a5f0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a610 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a620 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a630 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a640 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a650 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a660 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a670 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a680 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a690 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a6a0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a6b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a6c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a6d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a6e0 60 .cfa: sp 0 + .ra: x30
STACK CFI 2a6e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a6f8 .cfa: x29 32 +
STACK CFI 2a738 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2a73c .cfa: x29 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2a740 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a750 7c .cfa: sp 0 + .ra: x30
STACK CFI 2a754 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2a75c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2a768 x21: .cfa -32 + ^
STACK CFI 2a7c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2a7d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a7e0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2a7e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2a7f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2a800 x21: .cfa -32 + ^
STACK CFI 2a880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2a890 44 .cfa: sp 0 + .ra: x30
STACK CFI 2a894 .cfa: sp 16 +
STACK CFI 2a8c4 .cfa: sp 0 +
STACK CFI INIT 2a8e0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a910 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a950 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a990 50 .cfa: sp 0 + .ra: x30
STACK CFI 2a994 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2a9d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2a9d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2a9e0 30 .cfa: sp 0 + .ra: x30
STACK CFI 2a9e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a9f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2aa0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2aa10 3c .cfa: sp 0 + .ra: x30
STACK CFI 2aa14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2aa1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2aa48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2aa50 14c .cfa: sp 0 + .ra: x30
STACK CFI 2aa54 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2aa60 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2aa7c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2aa84 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2ab4c x21: x21 x22: x22
STACK CFI 2ab5c x25: x25 x26: x26
STACK CFI 2ab60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 2ab64 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 2ab68 x21: x21 x22: x22
STACK CFI 2ab70 x25: x25 x26: x26
STACK CFI 2ab80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 2ab84 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 2ab98 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 2aba0 3c .cfa: sp 0 + .ra: x30
STACK CFI 2abc0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2abd8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2abe0 ac .cfa: sp 0 + .ra: x30
STACK CFI 2abe8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2abf4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2ac00 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ac08 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2ac74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2ac7c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2ac90 3c .cfa: sp 0 + .ra: x30
STACK CFI 2acc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2acd0 3c .cfa: sp 0 + .ra: x30
STACK CFI 2ad04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2ad10 3c .cfa: sp 0 + .ra: x30
STACK CFI 2ad44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2ad50 cc .cfa: sp 0 + .ra: x30
STACK CFI 2ad58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2adfc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2ae00 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ae10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2ae18 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2ae20 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2ae24 .cfa: sp 16 +
STACK CFI 2aeb8 .cfa: sp 0 +
STACK CFI 2aebc .cfa: sp 16 +
STACK CFI INIT 2aed0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2aee0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2aef0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2af00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2af10 3c0 .cfa: sp 0 + .ra: x30
STACK CFI 2af1c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 2af34 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2af40 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 2af4c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 2af58 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 2af7c x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 2b0b0 x25: x25 x26: x26
STACK CFI 2b0cc x19: x19 x20: x20
STACK CFI 2b0d0 x21: x21 x22: x22
STACK CFI 2b0d4 x23: x23 x24: x24
STACK CFI 2b0d8 x27: x27 x28: x28
STACK CFI 2b0fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b100 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 2b108 x25: x25 x26: x26
STACK CFI 2b2b8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 2b2bc x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2b2c0 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 2b2c4 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 2b2c8 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 2b2cc x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 2b2d0 188 .cfa: sp 0 + .ra: x30
STACK CFI 2b2dc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2b2f4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2b300 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2b308 x23: .cfa -144 + ^
STACK CFI 2b3e4 x19: x19 x20: x20
STACK CFI 2b3e8 x21: x21 x22: x22
STACK CFI 2b3ec x23: x23
STACK CFI 2b410 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b414 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x29: .cfa -192 + ^
STACK CFI 2b448 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2b44c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2b450 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2b454 x23: .cfa -144 + ^
STACK CFI INIT 2b460 90 .cfa: sp 0 + .ra: x30
STACK CFI 2b464 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2b474 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2b480 x21: .cfa -96 + ^
STACK CFI 2b4e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2b4ec .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2b4f0 34 .cfa: sp 0 + .ra: x30
STACK CFI 2b4f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b4fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2b520 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2b530 8c .cfa: sp 0 + .ra: x30
STACK CFI 2b534 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2b54c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2b5ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b5b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2b5c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b5d0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2b5d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2b5e4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2b5f4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2b5fc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2b680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2b684 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2b690 34 .cfa: sp 0 + .ra: x30
STACK CFI 2b6bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2b6d0 38 .cfa: sp 0 + .ra: x30
STACK CFI 2b700 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2b710 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b720 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2b724 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2b734 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2b744 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2b74c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2b7d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2b7d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2b7e0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b810 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b840 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b850 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b860 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b870 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b880 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b890 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b8a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b8b0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 2b8b4 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI 2b8c4 x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI 2b8d0 x21: .cfa -464 + ^ x22: .cfa -456 + ^
STACK CFI 2b950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2b954 .cfa: sp 496 + .ra: .cfa -488 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x29: .cfa -496 + ^
STACK CFI INIT 2b960 13c .cfa: sp 0 + .ra: x30
STACK CFI 2b964 .cfa: sp 608 +
STACK CFI 2b970 .ra: .cfa -600 + ^ x29: .cfa -608 + ^
STACK CFI 2b978 x19: .cfa -592 + ^ x20: .cfa -584 + ^
STACK CFI 2b980 x21: .cfa -576 + ^ x22: .cfa -568 + ^
STACK CFI 2b98c x23: .cfa -560 + ^ x24: .cfa -552 + ^
STACK CFI 2b998 x25: .cfa -544 + ^
STACK CFI 2ba8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2ba90 .cfa: sp 608 + .ra: .cfa -600 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x29: .cfa -608 + ^
STACK CFI INIT 2baa0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 2baa4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2bab4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2bad8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2bae8 x23: .cfa -48 + ^
STACK CFI 2bb10 x21: x21 x22: x22
STACK CFI 2bb14 x23: x23
STACK CFI 2bb38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2bb3c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 2bb48 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2bb4c x23: .cfa -48 + ^
STACK CFI INIT 2bb50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bb60 9c .cfa: sp 0 + .ra: x30
STACK CFI 2bb64 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2bb74 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2bbb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2bbb8 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x29: .cfa -208 + ^
STACK CFI INIT 2bc00 f8 .cfa: sp 0 + .ra: x30
STACK CFI 2bc04 .cfa: sp 880 +
STACK CFI 2bc10 .ra: .cfa -872 + ^ x29: .cfa -880 + ^
STACK CFI 2bc18 x19: .cfa -864 + ^ x20: .cfa -856 + ^
STACK CFI 2bc20 x21: .cfa -848 + ^ x22: .cfa -840 + ^
STACK CFI 2bc28 x23: .cfa -832 + ^
STACK CFI 2bce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2bcec .cfa: sp 880 + .ra: .cfa -872 + ^ x19: .cfa -864 + ^ x20: .cfa -856 + ^ x21: .cfa -848 + ^ x22: .cfa -840 + ^ x23: .cfa -832 + ^ x29: .cfa -880 + ^
STACK CFI INIT 2bd00 f8 .cfa: sp 0 + .ra: x30
STACK CFI 2bd04 .cfa: sp 880 +
STACK CFI 2bd10 .ra: .cfa -872 + ^ x29: .cfa -880 + ^
STACK CFI 2bd18 x19: .cfa -864 + ^ x20: .cfa -856 + ^
STACK CFI 2bd20 x21: .cfa -848 + ^ x22: .cfa -840 + ^
STACK CFI 2bd28 x23: .cfa -832 + ^
STACK CFI 2bde8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2bdec .cfa: sp 880 + .ra: .cfa -872 + ^ x19: .cfa -864 + ^ x20: .cfa -856 + ^ x21: .cfa -848 + ^ x22: .cfa -840 + ^ x23: .cfa -832 + ^ x29: .cfa -880 + ^
STACK CFI INIT 2be00 18 .cfa: sp 0 + .ra: x30
STACK CFI 2be04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2be14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2be20 18 .cfa: sp 0 + .ra: x30
STACK CFI 2be24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2be34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2be40 6c .cfa: sp 0 + .ra: x30
STACK CFI 2be44 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2be54 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2bea4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2bea8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2beb0 50 .cfa: sp 0 + .ra: x30
STACK CFI 2beb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bebc x19: .cfa -16 + ^
STACK CFI 2befc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2bf00 30 .cfa: sp 0 + .ra: x30
STACK CFI 2bf04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bf0c x19: .cfa -16 + ^
STACK CFI 2bf2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2bf30 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2bf34 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2bf44 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 2bfcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2bfd0 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x29: .cfa -176 + ^
STACK CFI INIT 2bfe0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2bfec .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2bffc x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 2c08c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c090 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x29: .cfa -176 + ^
STACK CFI INIT 2c0a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c0b0 80 .cfa: sp 0 + .ra: x30
STACK CFI 2c0b4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2c0bc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2c128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c12c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2c130 90 .cfa: sp 0 + .ra: x30
STACK CFI 2c134 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 2c13c x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 2c1b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c1bc .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x29: .cfa -176 + ^
STACK CFI INIT 2c1c0 7c .cfa: sp 0 + .ra: x30
STACK CFI 2c1c4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2c1d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2c1e0 x21: .cfa -64 + ^
STACK CFI 2c234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2c238 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2c240 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c250 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c260 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c270 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c280 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c290 58 .cfa: sp 0 + .ra: x30
STACK CFI 2c29c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2c2e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c2e4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI INIT 2c2f0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 2c2f4 .cfa: sp 880 +
STACK CFI 2c300 .ra: .cfa -872 + ^ x29: .cfa -880 + ^
STACK CFI 2c308 x19: .cfa -864 + ^ x20: .cfa -856 + ^
STACK CFI 2c310 x21: .cfa -848 + ^ x22: .cfa -840 + ^
STACK CFI 2c318 x23: .cfa -832 + ^
STACK CFI 2c3c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c3c4 .cfa: sp 880 + .ra: .cfa -872 + ^ x19: .cfa -864 + ^ x20: .cfa -856 + ^ x21: .cfa -848 + ^ x22: .cfa -840 + ^ x23: .cfa -832 + ^ x29: .cfa -880 + ^
STACK CFI INIT 2c3d0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 2c3d4 .cfa: sp 880 +
STACK CFI 2c3e0 .ra: .cfa -872 + ^ x29: .cfa -880 + ^
STACK CFI 2c3e8 x19: .cfa -864 + ^ x20: .cfa -856 + ^
STACK CFI 2c3f0 x21: .cfa -848 + ^ x22: .cfa -840 + ^
STACK CFI 2c3f8 x23: .cfa -832 + ^
STACK CFI 2c4a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c4a4 .cfa: sp 880 + .ra: .cfa -872 + ^ x19: .cfa -864 + ^ x20: .cfa -856 + ^ x21: .cfa -848 + ^ x22: .cfa -840 + ^ x23: .cfa -832 + ^ x29: .cfa -880 + ^
STACK CFI INIT 2c4b0 18 .cfa: sp 0 + .ra: x30
STACK CFI 2c4b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c4c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2c4d0 6c .cfa: sp 0 + .ra: x30
STACK CFI 2c4d4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2c4e4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2c534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c538 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2c540 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c550 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c560 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c570 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c580 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c590 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c5a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c5b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c5c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c5d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c5e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c5f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c600 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 2c604 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2c60c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2c638 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2c644 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2c67c x25: .cfa -16 + ^
STACK CFI 2c768 x23: x23 x24: x24
STACK CFI 2c770 x25: x25
STACK CFI 2c778 x21: x21 x22: x22
STACK CFI 2c780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c784 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2c788 x25: x25
STACK CFI 2c78c x21: x21 x22: x22
STACK CFI 2c794 x23: x23 x24: x24
STACK CFI 2c79c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 2c7a0 x23: x23 x24: x24
STACK CFI 2c7a8 x25: x25
STACK CFI 2c7b0 x21: x21 x22: x22
STACK CFI 2c7b4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2c7b8 x23: x23 x24: x24
STACK CFI 2c7c4 x21: x21 x22: x22
STACK CFI INIT 2c7d0 204 .cfa: sp 0 + .ra: x30
STACK CFI 2c7d4 .cfa: sp 176 +
STACK CFI 2c7e0 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2c7e8 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 2c7f4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 2c7fc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 2c80c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2c834 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2c948 x27: x27 x28: x28
STACK CFI 2c980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2c984 .cfa: sp 176 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 2c994 x27: x27 x28: x28
STACK CFI 2c998 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2c9b8 x27: x27 x28: x28
STACK CFI 2c9bc x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2c9cc x27: x27 x28: x28
STACK CFI 2c9d0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 2c9e0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cbb0 ec .cfa: sp 0 + .ra: x30
STACK CFI 2cbb4 .cfa: sp 144 +
STACK CFI 2cbc0 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2cbc8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2cbd4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2cbe0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2cbec x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 2cbf8 x27: .cfa -48 + ^
STACK CFI 2cc8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2cc90 .cfa: sp 144 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2cca0 6c .cfa: sp 0 + .ra: x30
STACK CFI 2cca4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ccbc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2ccec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ccf0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2cd08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2cd10 44 .cfa: sp 0 + .ra: x30
STACK CFI 2cd14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2cd1c x19: .cfa -16 + ^
STACK CFI 2cd48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2cd4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2cd60 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cd80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cd90 218 .cfa: sp 0 + .ra: x30
STACK CFI 2cd94 .cfa: sp 640 +
STACK CFI 2cda0 .ra: .cfa -632 + ^ x29: .cfa -640 + ^
STACK CFI 2cda8 x23: .cfa -592 + ^ x24: .cfa -584 + ^
STACK CFI 2cdc8 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI 2cdcc x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI 2cdd0 x25: .cfa -576 + ^ x26: .cfa -568 + ^
STACK CFI 2ce24 x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI 2cf54 x27: x27 x28: x28
STACK CFI 2cf84 x19: x19 x20: x20
STACK CFI 2cf88 x21: x21 x22: x22
STACK CFI 2cf90 x25: x25 x26: x26
STACK CFI 2cf94 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 2cf98 .cfa: sp 640 + .ra: .cfa -632 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x29: .cfa -640 + ^
STACK CFI 2cf9c x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI 2cfa0 x27: x27 x28: x28
STACK CFI 2cfa4 x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI INIT 2cfb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cfc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cfd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cfe0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2cff0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d000 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d010 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d020 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d030 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d040 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d050 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d060 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d070 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d080 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d090 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d0a0 17c .cfa: sp 0 + .ra: x30
STACK CFI 2d0a4 .cfa: sp 96 +
STACK CFI 2d0a8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2d0b0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2d0c0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2d0cc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2d0d8 x25: .cfa -16 + ^
STACK CFI 2d174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2d178 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2d220 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 2d224 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2d234 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2d24c x19: .cfa -192 + ^ x20: .cfa -184 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 2d254 x25: .cfa -144 + ^
STACK CFI 2d3a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2d3a4 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x29: .cfa -208 + ^
STACK CFI INIT 2d400 114 .cfa: sp 0 + .ra: x30
STACK CFI 2d404 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2d418 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2d420 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2d428 x23: .cfa -160 + ^
STACK CFI 2d4f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2d4fc .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x29: .cfa -208 + ^
STACK CFI INIT 2d520 16c .cfa: sp 0 + .ra: x30
STACK CFI 2d524 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2d538 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2d668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d66c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2d690 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 2d694 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2d6a8 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2d870 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2d874 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x29: .cfa -144 + ^
STACK CFI INIT 2d880 12c .cfa: sp 0 + .ra: x30
STACK CFI 2d884 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2d88c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2d8b0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2d8e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2d984 x21: x21 x22: x22
STACK CFI 2d990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 2d994 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2d9b0 52c .cfa: sp 0 + .ra: x30
STACK CFI 2d9b4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 2d9bc x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 2d9e0 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 2da00 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 2da60 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 2da90 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 2dde8 x21: x21 x22: x22
STACK CFI 2ddf0 x23: x23 x24: x24
STACK CFI 2ddf4 x25: x25 x26: x26
STACK CFI 2ddf8 x27: x27 x28: x28
STACK CFI 2de00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2de04 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI 2de28 x21: x21 x22: x22
STACK CFI 2de30 x23: x23 x24: x24
STACK CFI 2de34 x25: x25 x26: x26
STACK CFI 2de38 x27: x27 x28: x28
STACK CFI 2de3c x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 2de44 x21: x21 x22: x22
STACK CFI 2de4c x23: x23 x24: x24
STACK CFI 2de50 x25: x25 x26: x26
STACK CFI 2de5c x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 2de64 x23: x23 x24: x24
STACK CFI 2de6c x25: x25 x26: x26
STACK CFI 2de78 x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 2de80 x23: x23 x24: x24
STACK CFI 2de88 x25: x25 x26: x26
STACK CFI 2de94 x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 2de9c x23: x23 x24: x24
STACK CFI 2dea4 x25: x25 x26: x26
STACK CFI 2deb0 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 2deb8 x25: x25 x26: x26
STACK CFI INIT 2dee0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 2dee4 .cfa: sp 400 + .ra: .cfa -392 + ^ x29: .cfa -400 + ^
STACK CFI 2def4 x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 2df04 x21: .cfa -368 + ^ x22: .cfa -360 + ^
STACK CFI 2df0c x23: .cfa -352 + ^
STACK CFI 2e064 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2e068 .cfa: sp 400 + .ra: .cfa -392 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x29: .cfa -400 + ^
STACK CFI INIT 2e0b0 174 .cfa: sp 0 + .ra: x30
STACK CFI 2e0b4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 2e0c4 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2e0d0 x21: .cfa -192 + ^
STACK CFI 2e1f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2e1fc .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x29: .cfa -224 + ^
STACK CFI INIT 2e230 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e240 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e250 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e260 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e270 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e280 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e290 f4 .cfa: sp 0 + .ra: x30
STACK CFI 2e294 .cfa: sp 384 + .ra: .cfa -376 + ^ x29: .cfa -384 + ^
STACK CFI 2e2a4 x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI 2e2b0 x21: .cfa -352 + ^
STACK CFI 2e358 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2e35c .cfa: sp 384 + .ra: .cfa -376 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x29: .cfa -384 + ^
STACK CFI INIT 2e390 cc .cfa: sp 0 + .ra: x30
STACK CFI 2e394 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2e3a4 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2e434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e438 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x29: .cfa -208 + ^
STACK CFI INIT 2e460 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e470 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e480 204 .cfa: sp 0 + .ra: x30
STACK CFI 2e484 .cfa: sp 464 + .ra: .cfa -456 + ^ x29: .cfa -464 + ^
STACK CFI 2e494 x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI 2e4a4 x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI 2e4ac x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI 2e4bc x25: .cfa -400 + ^ x26: .cfa -392 + ^
STACK CFI 2e4c4 x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI 2e610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2e614 .cfa: sp 464 + .ra: .cfa -456 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^ x29: .cfa -464 + ^
STACK CFI INIT 2e690 34 .cfa: sp 0 + .ra: x30
STACK CFI 2e6bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2e6d0 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 2e6d4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 2e6e4 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2e6f4 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2e6fc x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 2e708 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 2e794 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 2e7ec x27: x27 x28: x28
STACK CFI 2e82c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2e830 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x29: .cfa -208 + ^
STACK CFI 2e850 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 2e878 x27: x27 x28: x28
STACK CFI 2e8b4 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT 2e8c0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e8f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e900 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e910 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e920 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e930 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e940 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e950 304 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ec60 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2ec64 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 2ec74 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2ec80 x21: .cfa -192 + ^
STACK CFI 2ed0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2ed10 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x29: .cfa -224 + ^
STACK CFI INIT 2ed20 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 2ed24 .cfa: sp 832 +
STACK CFI 2ed30 .ra: .cfa -824 + ^ x29: .cfa -832 + ^
STACK CFI 2ed38 x19: .cfa -816 + ^ x20: .cfa -808 + ^
STACK CFI 2ed44 x21: .cfa -800 + ^ x22: .cfa -792 + ^
STACK CFI 2ed50 x23: .cfa -784 + ^ x24: .cfa -776 + ^
STACK CFI 2ed5c x25: .cfa -768 + ^ x26: .cfa -760 + ^
STACK CFI 2ed68 x27: .cfa -752 + ^ x28: .cfa -744 + ^
STACK CFI 2eec0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2eec4 .cfa: sp 832 + .ra: .cfa -824 + ^ x19: .cfa -816 + ^ x20: .cfa -808 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^ x28: .cfa -744 + ^ x29: .cfa -832 + ^
STACK CFI INIT 2eed0 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 2eed4 .cfa: sp 1152 +
STACK CFI 2eee0 .ra: .cfa -1144 + ^ x29: .cfa -1152 + ^
STACK CFI 2eeec x19: .cfa -1136 + ^ x20: .cfa -1128 + ^ x21: .cfa -1120 + ^ x22: .cfa -1112 + ^
STACK CFI 2eef4 x23: .cfa -1104 + ^ x24: .cfa -1096 + ^
STACK CFI 2ef40 x25: .cfa -1088 + ^ x26: .cfa -1080 + ^
STACK CFI 2ef78 x27: .cfa -1072 + ^
STACK CFI 2f044 x25: x25 x26: x26
STACK CFI 2f048 x27: x27
STACK CFI 2f07c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2f080 .cfa: sp 1152 + .ra: .cfa -1144 + ^ x19: .cfa -1136 + ^ x20: .cfa -1128 + ^ x21: .cfa -1120 + ^ x22: .cfa -1112 + ^ x23: .cfa -1104 + ^ x24: .cfa -1096 + ^ x25: .cfa -1088 + ^ x26: .cfa -1080 + ^ x29: .cfa -1152 + ^
STACK CFI 2f084 x25: x25 x26: x26
STACK CFI 2f08c x25: .cfa -1088 + ^ x26: .cfa -1080 + ^ x27: .cfa -1072 + ^
STACK CFI 2f090 x25: x25 x26: x26
STACK CFI 2f098 x27: x27
STACK CFI 2f0a0 x25: .cfa -1088 + ^ x26: .cfa -1080 + ^
STACK CFI 2f0a4 x27: .cfa -1072 + ^
STACK CFI INIT 2f0b0 188 .cfa: sp 0 + .ra: x30
STACK CFI 2f0bc .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2f0d4 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2f0e0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2f0e8 x23: .cfa -144 + ^
STACK CFI 2f1c4 x19: x19 x20: x20
STACK CFI 2f1c8 x21: x21 x22: x22
STACK CFI 2f1cc x23: x23
STACK CFI 2f1f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2f1f4 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x29: .cfa -192 + ^
STACK CFI 2f228 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2f22c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2f230 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2f234 x23: .cfa -144 + ^
STACK CFI INIT 2f240 3b8 .cfa: sp 0 + .ra: x30
STACK CFI 2f24c .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 2f264 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2f270 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 2f27c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 2f288 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 2f2a4 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 2f3d8 x25: x25 x26: x26
STACK CFI 2f3f4 x19: x19 x20: x20
STACK CFI 2f3f8 x21: x21 x22: x22
STACK CFI 2f3fc x23: x23 x24: x24
STACK CFI 2f400 x27: x27 x28: x28
STACK CFI 2f424 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2f428 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 2f430 x25: x25 x26: x26
STACK CFI 2f5e0 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 2f5e4 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2f5e8 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 2f5ec x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 2f5f0 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 2f5f4 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 2f600 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f610 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f620 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f630 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f640 188 .cfa: sp 0 + .ra: x30
STACK CFI 2f64c .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2f664 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2f670 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2f678 x23: .cfa -144 + ^
STACK CFI 2f754 x19: x19 x20: x20
STACK CFI 2f758 x21: x21 x22: x22
STACK CFI 2f75c x23: x23
STACK CFI 2f780 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2f784 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x29: .cfa -192 + ^
STACK CFI 2f7b8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2f7bc x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2f7c0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 2f7c4 x23: .cfa -144 + ^
STACK CFI INIT 2f7d0 3b8 .cfa: sp 0 + .ra: x30
STACK CFI 2f7dc .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 2f7f4 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2f800 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 2f80c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 2f818 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 2f834 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 2f968 x25: x25 x26: x26
STACK CFI 2f984 x19: x19 x20: x20
STACK CFI 2f988 x21: x21 x22: x22
STACK CFI 2f98c x23: x23 x24: x24
STACK CFI 2f990 x27: x27 x28: x28
STACK CFI 2f9b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2f9b8 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI 2f9c0 x25: x25 x26: x26
STACK CFI 2fb70 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 2fb74 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2fb78 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 2fb7c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 2fb80 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 2fb84 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 2fb90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fba0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fbb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fbc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fbd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fbe0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fbf0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fc00 8c .cfa: sp 0 + .ra: x30
STACK CFI 2fc04 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2fc14 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2fc20 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2fc84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2fc88 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2fc90 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2fc94 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2fca4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2fcb0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2fcbc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2fd2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2fd30 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2fd40 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fd50 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fd60 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fd70 68 .cfa: sp 0 + .ra: x30
STACK CFI 2fd74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2fd7c x19: .cfa -16 + ^
STACK CFI 2fda8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2fdac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2fdd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2fde0 17c .cfa: sp 0 + .ra: x30
STACK CFI 2fde4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 2fe0c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 2fe78 x21: .cfa -160 + ^
STACK CFI 2fecc x21: x21
STACK CFI 2ff00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ff04 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI 2ff38 x21: x21
STACK CFI 2ff58 x21: .cfa -160 + ^
STACK CFI INIT 2ff60 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 2ff64 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2ff6c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2ff88 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 30024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 30028 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 30130 54 .cfa: sp 0 + .ra: x30
STACK CFI 3013c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3017c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 30180 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 30190 ec .cfa: sp 0 + .ra: x30
STACK CFI 30194 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3019c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 301e4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 301e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 301ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 30248 x19: x19 x20: x20
STACK CFI 30250 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 30270 x19: x19 x20: x20
STACK CFI 30274 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 30280 24 .cfa: sp 0 + .ra: x30
STACK CFI 30284 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 302a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 302b0 24 .cfa: sp 0 + .ra: x30
STACK CFI 302b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 302d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 302e0 24 .cfa: sp 0 + .ra: x30
STACK CFI 302e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30300 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30310 24 .cfa: sp 0 + .ra: x30
STACK CFI 30314 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30330 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30340 24 .cfa: sp 0 + .ra: x30
STACK CFI 30344 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30360 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30370 24 .cfa: sp 0 + .ra: x30
STACK CFI 30374 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30390 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 303a0 24 .cfa: sp 0 + .ra: x30
STACK CFI 303a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 303c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 303d0 24 .cfa: sp 0 + .ra: x30
STACK CFI 303d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 303f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30400 24 .cfa: sp 0 + .ra: x30
STACK CFI 30404 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30420 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30430 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30440 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30450 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30460 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30470 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30480 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 30490 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 304a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 304b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 304c0 4c .cfa: sp 0 + .ra: x30
STACK CFI 304c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 304dc x19: .cfa -16 + ^
STACK CFI 30508 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 30510 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 30514 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 30530 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 305f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 305f8 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x29: .cfa -256 + ^
STACK CFI 30664 x25: .cfa -192 + ^
STACK CFI 30668 x25: x25
STACK CFI 30678 x25: .cfa -192 + ^
STACK CFI 30774 x25: x25
STACK CFI 3078c x25: .cfa -192 + ^
STACK CFI INIT 307b0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 307b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 307bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 30884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 30888 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 308a0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 308a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 308ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 308d0 v8: .cfa -16 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 30948 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3094c .cfa: sp 64 + .ra: .cfa -56 + ^ v8: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
