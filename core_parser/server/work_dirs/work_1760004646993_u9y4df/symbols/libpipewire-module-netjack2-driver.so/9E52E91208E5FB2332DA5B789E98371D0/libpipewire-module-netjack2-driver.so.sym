MODULE Linux arm64 9E52E91208E5FB2332DA5B789E98371D0 libpipewire-module-netjack2-driver.so
INFO CODE_ID 12E9529EE50823FB32DA5B789E98371D2427D2F9
PUBLIC 111b0 0 pipewire__module_init
STACK CFI INIT 6180 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 61b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 61f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 61f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 61fc x19: .cfa -16 + ^
STACK CFI 6234 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6240 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6250 340 .cfa: sp 0 + .ra: x30
STACK CFI 6258 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6344 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6350 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6488 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6494 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 64e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 64ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6564 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 656c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6590 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 6598 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 65a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 65ac x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 65c0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 65c4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 67bc x19: x19 x20: x20
STACK CFI 67c4 x23: x23 x24: x24
STACK CFI 67d8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 67e0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 67e4 x19: x19 x20: x20
STACK CFI 67f0 x23: x23 x24: x24
STACK CFI 67fc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6804 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 680c x19: x19 x20: x20
STACK CFI 6818 x23: x23 x24: x24
STACK CFI 6824 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 682c .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6838 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 683c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 6840 68 .cfa: sp 0 + .ra: x30
STACK CFI 6848 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6850 x19: .cfa -16 + ^
STACK CFI 68a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 68b0 38 .cfa: sp 0 + .ra: x30
STACK CFI 68b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 68cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 68d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 68e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 68f0 2c8c .cfa: sp 0 + .ra: x30
STACK CFI 68f8 .cfa: sp 480 +
STACK CFI 690c .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 6924 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 692c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 6944 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 70f0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 70f8 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 9580 360 .cfa: sp 0 + .ra: x30
STACK CFI 9588 .cfa: sp 112 +
STACK CFI 9594 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 959c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 95b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 95b8 v8: .cfa -16 + ^
STACK CFI 9764 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 976c .cfa: sp 112 + .ra: .cfa -56 + ^ v8: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 98e0 2ac .cfa: sp 0 + .ra: x30
STACK CFI 98e8 .cfa: sp 128 +
STACK CFI 98ec .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 98f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9908 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9a98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9aa0 .cfa: sp 128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9b90 8c .cfa: sp 0 + .ra: x30
STACK CFI 9b98 .cfa: sp 64 +
STACK CFI 9ba8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9c10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9c18 .cfa: sp 64 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9c20 de8 .cfa: sp 0 + .ra: x30
STACK CFI 9c28 .cfa: sp 352 +
STACK CFI 9c3c .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 9c44 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 9c54 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 9c60 x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 9c68 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI a030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a038 .cfa: sp 352 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT aa10 70 .cfa: sp 0 + .ra: x30
STACK CFI aa18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI aa44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI aa4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI aa5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI aa68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI aa78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT aa80 54 .cfa: sp 0 + .ra: x30
STACK CFI aa88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aa90 x19: .cfa -16 + ^
STACK CFI aacc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT aad4 618 .cfa: sp 0 + .ra: x30
STACK CFI aadc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI aae8 .cfa: sp 1200 + x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI ab50 x22: .cfa -72 + ^
STACK CFI ab58 x23: .cfa -64 + ^
STACK CFI ab60 x27: .cfa -32 + ^
STACK CFI ab68 x28: .cfa -24 + ^
STACK CFI ab70 v9: .cfa -8 + ^
STACK CFI ab7c x19: .cfa -96 + ^
STACK CFI ab84 x20: .cfa -88 + ^
STACK CFI ab88 x21: .cfa -80 + ^
STACK CFI ab8c x24: .cfa -56 + ^
STACK CFI ab90 v8: .cfa -16 + ^
STACK CFI aeb8 x19: x19
STACK CFI aebc x20: x20
STACK CFI aec0 x21: x21
STACK CFI aec4 x22: x22
STACK CFI aec8 x23: x23
STACK CFI aecc x24: x24
STACK CFI aed0 x27: x27
STACK CFI aed4 x28: x28
STACK CFI aed8 v8: v8
STACK CFI aedc v9: v9
STACK CFI aefc .cfa: sp 112 +
STACK CFI af04 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI af0c .cfa: sp 1200 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI b0c0 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI b0c4 x19: .cfa -96 + ^
STACK CFI b0c8 x20: .cfa -88 + ^
STACK CFI b0cc x21: .cfa -80 + ^
STACK CFI b0d0 x22: .cfa -72 + ^
STACK CFI b0d4 x23: .cfa -64 + ^
STACK CFI b0d8 x24: .cfa -56 + ^
STACK CFI b0dc x27: .cfa -32 + ^
STACK CFI b0e0 x28: .cfa -24 + ^
STACK CFI b0e4 v8: .cfa -16 + ^
STACK CFI b0e8 v9: .cfa -8 + ^
STACK CFI INIT b0f0 25c .cfa: sp 0 + .ra: x30
STACK CFI b0f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b10c .cfa: sp 1440 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b258 .cfa: sp 96 +
STACK CFI b268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b270 .cfa: sp 1440 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI b2bc x26: .cfa -24 + ^
STACK CFI b2c4 x27: .cfa -16 + ^
STACK CFI b2cc x25: .cfa -32 + ^
STACK CFI b314 x25: x25
STACK CFI b318 x26: x26
STACK CFI b31c x27: x27
STACK CFI b324 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI b33c x25: x25 x26: x26 x27: x27
STACK CFI b340 x25: .cfa -32 + ^
STACK CFI b344 x26: .cfa -24 + ^
STACK CFI b348 x27: .cfa -16 + ^
STACK CFI INIT b350 3c4 .cfa: sp 0 + .ra: x30
STACK CFI b358 .cfa: sp 288 +
STACK CFI b364 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b36c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b3b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b3b8 .cfa: sp 288 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI b3bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b428 x21: x21 x22: x22
STACK CFI b42c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b434 .cfa: sp 288 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b484 x21: x21 x22: x22
STACK CFI b488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b490 .cfa: sp 288 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b4c0 x21: x21 x22: x22
STACK CFI b4c4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b514 x21: x21 x22: x22
STACK CFI b518 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b668 x21: x21 x22: x22
STACK CFI b66c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b708 x21: x21 x22: x22
STACK CFI b710 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT b714 190 .cfa: sp 0 + .ra: x30
STACK CFI b71c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b72c .cfa: sp 1168 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b868 .cfa: sp 48 +
STACK CFI b874 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b87c .cfa: sp 1168 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b8a4 104 .cfa: sp 0 + .ra: x30
STACK CFI b8ac .cfa: sp 96 +
STACK CFI b8b0 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b8b8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b8c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b908 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI b9a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT b9b0 a40 .cfa: sp 0 + .ra: x30
STACK CFI b9b8 .cfa: sp 144 +
STACK CFI b9c8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b9d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b9dc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b9e8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ba94 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ba98 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI bbf8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI bc90 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI bc94 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI bd64 x25: x25 x26: x26
STACK CFI bd68 x27: x27 x28: x28
STACK CFI bd9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI bda4 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI bdbc x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI be38 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI be6c x25: x25 x26: x26
STACK CFI be70 x27: x27 x28: x28
STACK CFI be74 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI bf24 x25: x25 x26: x26
STACK CFI bf28 x27: x27 x28: x28
STACK CFI bf70 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c078 x25: x25 x26: x26
STACK CFI c07c x27: x27 x28: x28
STACK CFI c080 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c300 x25: x25 x26: x26
STACK CFI c304 x27: x27 x28: x28
STACK CFI c308 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c3e4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI c3e8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c3ec x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT c3f0 4b8 .cfa: sp 0 + .ra: x30
STACK CFI c3f8 .cfa: sp 448 +
STACK CFI c408 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c41c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c424 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c434 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c5a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c5a8 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT c8b0 118 .cfa: sp 0 + .ra: x30
STACK CFI c8b8 .cfa: sp 48 +
STACK CFI c8c4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c8cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c97c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c984 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI c9b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c9c4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT c9d0 98 .cfa: sp 0 + .ra: x30
STACK CFI c9d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c9e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c9fc x21: .cfa -16 + ^
STACK CFI ca24 x21: x21
STACK CFI ca2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ca34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI ca40 x21: x21
STACK CFI ca4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ca54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT ca70 1548 .cfa: sp 0 + .ra: x30
STACK CFI ca78 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ca8c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ca98 .cfa: sp 912 + x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI caf0 x23: .cfa -48 + ^
STACK CFI cafc x24: .cfa -40 + ^
STACK CFI cb18 x23: x23
STACK CFI cb1c x24: x24
STACK CFI cb3c .cfa: sp 96 +
STACK CFI cb48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cb50 .cfa: sp 912 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI cb54 x23: .cfa -48 + ^
STACK CFI cb60 x24: .cfa -40 + ^
STACK CFI cbbc x23: x23 x24: x24
STACK CFI cbc0 x23: .cfa -48 + ^
STACK CFI cbcc x24: .cfa -40 + ^
STACK CFI cc28 x23: x23
STACK CFI cc30 x24: x24
STACK CFI cc34 .cfa: sp 96 +
STACK CFI cc40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cc48 .cfa: sp 912 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI cc78 x23: x23 x24: x24
STACK CFI cca8 x23: .cfa -48 + ^
STACK CFI ccb4 x24: .cfa -40 + ^
STACK CFI ccb8 x25: .cfa -32 + ^
STACK CFI ccbc x26: .cfa -24 + ^
STACK CFI ccc0 x27: .cfa -16 + ^
STACK CFI ccc4 x28: .cfa -8 + ^
STACK CFI cf04 x23: x23
STACK CFI cf08 x24: x24
STACK CFI cf0c x25: x25
STACK CFI cf10 x26: x26
STACK CFI cf14 x27: x27
STACK CFI cf18 x28: x28
STACK CFI cf1c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI cf34 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d518 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI d55c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI df80 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI df84 x23: .cfa -48 + ^
STACK CFI df88 x24: .cfa -40 + ^
STACK CFI df8c x25: .cfa -32 + ^
STACK CFI df90 x26: .cfa -24 + ^
STACK CFI df94 x27: .cfa -16 + ^
STACK CFI df98 x28: .cfa -8 + ^
STACK CFI INIT dfc0 28 .cfa: sp 0 + .ra: x30
STACK CFI dfc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI dfd4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI dfdc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI dfe0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT dff0 230 .cfa: sp 0 + .ra: x30
STACK CFI dff8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e000 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e00c .cfa: sp 960 + x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI e160 .cfa: sp 48 +
STACK CFI e16c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e174 .cfa: sp 960 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT e220 1140 .cfa: sp 0 + .ra: x30
STACK CFI e228 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI e230 .cfa: x29 112 +
STACK CFI e244 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI e24c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI e258 v8: .cfa -16 + ^
STACK CFI e728 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e730 .cfa: x29 112 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT f360 230 .cfa: sp 0 + .ra: x30
STACK CFI f368 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f36c .cfa: x29 96 +
STACK CFI f370 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f390 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f4a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI f4a8 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT f590 fd4 .cfa: sp 0 + .ra: x30
STACK CFI f598 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI f5a0 .cfa: x29 112 +
STACK CFI f5a4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI f5c8 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI f9f8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI fa00 .cfa: x29 112 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 10564 238 .cfa: sp 0 + .ra: x30
STACK CFI 1056c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 10574 .cfa: x29 96 +
STACK CFI 10578 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 10594 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 106a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 106b0 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 107a0 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 107a8 .cfa: sp 144 +
STACK CFI 107b4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 107bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10808 .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 10810 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10820 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 10828 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 109f0 x21: x21 x22: x22
STACK CFI 109f8 x23: x23 x24: x24
STACK CFI 10a00 x25: x25 x26: x26
STACK CFI 10a08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10a14 .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 10a84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10a8c .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 10aa4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 10acc x21: x21 x22: x22
STACK CFI 10ad0 x23: x23 x24: x24
STACK CFI 10ad4 x25: x25 x26: x26
STACK CFI 10ad8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 10b18 x21: x21 x22: x22
STACK CFI 10b1c x23: x23 x24: x24
STACK CFI 10b20 x25: x25 x26: x26
STACK CFI 10b24 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 10b88 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 10bbc x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 10c00 x21: x21 x22: x22
STACK CFI 10c04 x23: x23 x24: x24
STACK CFI 10c08 x25: x25 x26: x26
STACK CFI 10c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10c1c .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 10c54 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 10c58 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10c5c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 10c60 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 10c64 268 .cfa: sp 0 + .ra: x30
STACK CFI 10c6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10c78 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10c84 .cfa: sp 704 + x21: .cfa -16 + ^
STACK CFI 10e64 .cfa: sp 48 +
STACK CFI 10e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 10e84 .cfa: sp 704 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 10ed0 90 .cfa: sp 0 + .ra: x30
STACK CFI 10ed8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10ee4 x19: .cfa -16 + ^
STACK CFI 10f58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10f60 160 .cfa: sp 0 + .ra: x30
STACK CFI 10f68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10f70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10fac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 10ffc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11004 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 110b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 110c0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 110c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 110d0 x19: .cfa -16 + ^
STACK CFI 11150 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 11158 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 11160 50 .cfa: sp 0 + .ra: x30
STACK CFI 11168 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11170 x19: .cfa -16 + ^
STACK CFI 111a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 111b0 910 .cfa: sp 0 + .ra: x30
STACK CFI 111b8 .cfa: sp 144 +
STACK CFI 111c4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 111cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 111d4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 11234 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 112e8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11364 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 115c4 x27: x27 x28: x28
STACK CFI 115cc x25: x25 x26: x26
STACK CFI 11674 x23: x23 x24: x24
STACK CFI 116a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 116ac .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 116c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 116c8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 116d4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 116ec x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 117e0 x23: x23 x24: x24
STACK CFI 117e4 x25: x25 x26: x26
STACK CFI 117e8 x27: x27 x28: x28
STACK CFI 117ec x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11800 x25: x25 x26: x26
STACK CFI 11804 x27: x27 x28: x28
STACK CFI 11840 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11858 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11860 x27: x27 x28: x28
STACK CFI 11864 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11958 x25: x25 x26: x26
STACK CFI 1195c x27: x27 x28: x28
STACK CFI 11990 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 119e4 x25: x25 x26: x26
STACK CFI 119e8 x27: x27 x28: x28
STACK CFI 119ec x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11a1c x27: x27 x28: x28
STACK CFI 11a20 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11a38 x27: x27 x28: x28
STACK CFI 11aa0 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 11ab4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 11ab8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11abc x27: .cfa -16 + ^ x28: .cfa -8 + ^
