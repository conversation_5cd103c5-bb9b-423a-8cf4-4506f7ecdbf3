MODULE Linux arm64 3E7A40E87C221F9B422CFCEFEE5089110 libpixbufloader-tiff.so
INFO CODE_ID E8407A3E227C9B1F422CFCEFEE508911CFDC9303
PUBLIC 3040 0 fill_vtable
PUBLIC 30a4 0 fill_info
STACK CFI INIT 1520 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1550 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1590 48 .cfa: sp 0 + .ra: x30
STACK CFI 1594 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 159c x19: .cfa -16 + ^
STACK CFI 15d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 15e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15f0 18 .cfa: sp 0 + .ra: x30
STACK CFI 15f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1600 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1610 1c .cfa: sp 0 + .ra: x30
STACK CFI 1618 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1624 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1630 1c .cfa: sp 0 + .ra: x30
STACK CFI 1638 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1644 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1650 84 .cfa: sp 0 + .ra: x30
STACK CFI 1658 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1690 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1698 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 16d4 1c .cfa: sp 0 + .ra: x30
STACK CFI 16dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 16f0 1c .cfa: sp 0 + .ra: x30
STACK CFI 16f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1704 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1710 1c .cfa: sp 0 + .ra: x30
STACK CFI 1718 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1720 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1730 34 .cfa: sp 0 + .ra: x30
STACK CFI 1740 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1758 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1764 18 .cfa: sp 0 + .ra: x30
STACK CFI 176c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1774 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1780 54 .cfa: sp 0 + .ra: x30
STACK CFI 1788 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 17b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 17bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 17d4 1c .cfa: sp 0 + .ra: x30
STACK CFI 17dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 17e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 17f0 ac .cfa: sp 0 + .ra: x30
STACK CFI 17f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1894 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18a0 18 .cfa: sp 0 + .ra: x30
STACK CFI 18a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 18b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18c0 5cc .cfa: sp 0 + .ra: x30
STACK CFI 18c8 .cfa: sp 160 +
STACK CFI 18d4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 18dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 18e8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 194c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1ac0 x23: x23 x24: x24
STACK CFI 1af0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1af8 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 1bd8 v8: .cfa -8 + ^
STACK CFI 1bf0 x25: .cfa -16 + ^
STACK CFI 1c58 x25: x25
STACK CFI 1c5c v8: v8
STACK CFI 1c60 x23: x23 x24: x24
STACK CFI 1cd0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1cd4 x23: x23 x24: x24
STACK CFI 1cdc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1d10 x23: x23 x24: x24
STACK CFI 1d28 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1d2c x23: x23 x24: x24
STACK CFI 1d48 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1db8 x23: x23 x24: x24
STACK CFI 1dbc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1dd4 x25: .cfa -16 + ^
STACK CFI 1e34 x25: x25
STACK CFI 1e78 x23: x23 x24: x24
STACK CFI 1e80 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1e84 x25: .cfa -16 + ^
STACK CFI 1e88 v8: .cfa -8 + ^
STACK CFI INIT 1e90 100 .cfa: sp 0 + .ra: x30
STACK CFI 1e98 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1ea0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1eb8 x21: .cfa -16 + ^
STACK CFI 1f1c x21: x21
STACK CFI 1f28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1f30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1f54 x21: .cfa -16 + ^
STACK CFI 1f8c x21: x21
STACK CFI INIT 1f90 e4 .cfa: sp 0 + .ra: x30
STACK CFI 1f98 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1fa4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1fe8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1ff0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2074 6c .cfa: sp 0 + .ra: x30
STACK CFI 207c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2084 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 20a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 20b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 20d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 20e0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 20e8 .cfa: sp 64 +
STACK CFI 20ec .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 20f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 20fc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 21b8 x21: x21 x22: x22
STACK CFI 21bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 21c4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2210 x21: x21 x22: x22
STACK CFI 2244 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 224c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2284 140 .cfa: sp 0 + .ra: x30
STACK CFI 228c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2298 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 22a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 22b8 x23: .cfa -16 + ^
STACK CFI 233c x19: x19 x20: x20
STACK CFI 2340 x21: x21 x22: x22
STACK CFI 2344 x23: x23
STACK CFI 2348 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2350 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2384 x19: x19 x20: x20
STACK CFI 238c x21: x21 x22: x22
STACK CFI 2390 x23: x23
STACK CFI 2394 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 239c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI INIT 23c4 98 .cfa: sp 0 + .ra: x30
STACK CFI 23cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 23d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 23e0 x21: .cfa -16 + ^
STACK CFI 2440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2448 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2460 a4c .cfa: sp 0 + .ra: x30
STACK CFI 2468 .cfa: sp 272 +
STACK CFI 2474 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2488 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 249c x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 24a4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2584 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2734 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 27a8 v8: v8 v9: v9
STACK CFI 2938 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 2974 v8: v8 v9: v9
STACK CFI 29b0 x27: x27 x28: x28
STACK CFI 2a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2a38 .cfa: sp 272 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 2a74 v8: v8 v9: v9
STACK CFI 2aac x27: x27 x28: x28
STACK CFI 2ab0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2e08 x27: x27 x28: x28
STACK CFI 2e0c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2e10 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 2e14 v8: v8 v9: v9
STACK CFI 2e40 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 2e44 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI 2e70 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2e74 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 2e78 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI 2ea4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2ea8 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI INIT 2eb0 40 .cfa: sp 0 + .ra: x30
STACK CFI 2eb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2ec4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2ef0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2f00 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2f08 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2f14 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2f88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2f90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2fa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2fb0 90 .cfa: sp 0 + .ra: x30
STACK CFI 2fb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2fc8 x19: .cfa -16 + ^
STACK CFI 2fe0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2fe8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3040 64 .cfa: sp 0 + .ra: x30
STACK CFI 3048 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 305c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30a4 5c .cfa: sp 0 + .ra: x30
STACK CFI 30ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30c0 .cfa: sp 0 + .ra: .ra x29: x29
