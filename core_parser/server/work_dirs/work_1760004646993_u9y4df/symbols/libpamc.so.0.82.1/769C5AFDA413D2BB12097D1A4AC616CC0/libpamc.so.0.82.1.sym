MODULE Linux arm64 769C5AFDA413D2BB12097D1A4AC616CC0 libpamc.so.0
INFO CODE_ID FD5A9C7613A4BBD212097D1A4AC616CCDBE6CFB6
PUBLIC 1b10 0 pamc_start
PUBLIC 1ce0 0 pamc_end
PUBLIC 1e70 0 pamc_disable
PUBLIC 1f90 0 pamc_load
PUBLIC 2334 0 pamc_converse
PUBLIC 2770 0 pamc_list_agents
STACK CFI INIT f60 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT f90 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT fd0 48 .cfa: sp 0 + .ra: x30
STACK CFI fd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI fdc x19: .cfa -16 + ^
STACK CFI 1014 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1020 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1030 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 103c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1044 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1050 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 105c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1060 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1064 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1198 x19: x19 x20: x20
STACK CFI 119c x21: x21 x22: x22
STACK CFI 11a0 x25: x25 x26: x26
STACK CFI 11c8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 11d0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 12cc x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 12d4 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI INIT 12e0 660 .cfa: sp 0 + .ra: x30
STACK CFI 12ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1304 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 1940 100 .cfa: sp 0 + .ra: x30
STACK CFI 1948 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1950 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 199c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 19a8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 19f4 x21: x21 x22: x22
STACK CFI 1a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a08 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1a28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1a40 d0 .cfa: sp 0 + .ra: x30
STACK CFI 1a50 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a58 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1aec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1af4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1b00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1b10 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 1b18 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1b28 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1b38 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1b98 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1ba4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1bac x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1c54 x19: x19 x20: x20
STACK CFI 1c58 x21: x21 x22: x22
STACK CFI 1c5c x23: x23 x24: x24
STACK CFI 1c60 x27: x27 x28: x28
STACK CFI 1c6c .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 1c74 .cfa: sp 112 + .ra: .cfa -104 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 1c90 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1cc4 x19: x19 x20: x20
STACK CFI 1cc8 x21: x21 x22: x22
STACK CFI 1ccc x23: x23 x24: x24
STACK CFI 1cd8 x27: x27 x28: x28
STACK CFI INIT 1ce0 190 .cfa: sp 0 + .ra: x30
STACK CFI 1ce8 .cfa: sp 96 +
STACK CFI 1cf4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1cfc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1d10 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1d28 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1d78 x25: .cfa -16 + ^
STACK CFI 1e0c x25: x25
STACK CFI 1e18 x19: x19 x20: x20
STACK CFI 1e4c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1e54 .cfa: sp 96 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 1e5c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1e64 x19: x19 x20: x20
STACK CFI 1e68 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1e6c x25: .cfa -16 + ^
STACK CFI INIT 1e70 118 .cfa: sp 0 + .ra: x30
STACK CFI 1e88 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1e90 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1e9c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1ed0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1ee0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1f20 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1f3c x23: .cfa -16 + ^
STACK CFI 1f68 x23: x23
STACK CFI 1f78 x23: .cfa -16 + ^
STACK CFI 1f84 x23: x23
STACK CFI INIT 1f90 3a4 .cfa: sp 0 + .ra: x30
STACK CFI 1f98 .cfa: sp 224 +
STACK CFI 1fa4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1fac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1fd0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1ff8 x21: x21 x22: x22
STACK CFI 2024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 202c .cfa: sp 224 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 2034 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2064 x21: x21 x22: x22
STACK CFI 206c x23: x23 x24: x24
STACK CFI 2070 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2088 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 212c x21: x21 x22: x22
STACK CFI 2130 x23: x23 x24: x24
STACK CFI 2134 x25: x25 x26: x26
STACK CFI 2138 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 221c x25: x25 x26: x26
STACK CFI 2228 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2298 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 229c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 22a0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 22a4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 22f4 x25: x25 x26: x26
STACK CFI 22f8 x21: x21 x22: x22
STACK CFI 22fc x23: x23 x24: x24
STACK CFI 2300 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 2334 438 .cfa: sp 0 + .ra: x30
STACK CFI 233c .cfa: sp 112 +
STACK CFI 2348 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2350 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2358 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 237c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2444 x23: x23 x24: x24
STACK CFI 2474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 247c .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 24ac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 24e0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2520 x25: x25 x26: x26
STACK CFI 2678 x23: x23 x24: x24
STACK CFI 267c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2680 x25: x25 x26: x26
STACK CFI 2684 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 26c0 x25: x25 x26: x26
STACK CFI 26c4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 26d4 x25: x25 x26: x26
STACK CFI 26d8 x23: x23 x24: x24
STACK CFI 26dc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 26e0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 26e4 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 2700 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2704 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2710 x25: x25 x26: x26
STACK CFI 272c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2738 x25: x25 x26: x26
STACK CFI 2754 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2768 x25: x25 x26: x26
STACK CFI INIT 2770 3e4 .cfa: sp 0 + .ra: x30
STACK CFI 2778 .cfa: sp 80 +
STACK CFI 277c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2784 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 27ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 27b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2ab8 x21: x21 x22: x22
STACK CFI 2ae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 2af0 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2b0c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2b14 x21: x21 x22: x22
STACK CFI 2b18 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2b20 x21: x21 x22: x22
STACK CFI 2b2c x21: .cfa -32 + ^ x22: .cfa -24 + ^
