MODULE Linux arm64 D85F98F7FB0673DD5F38BEAA187ABF790 libclidns-samba4.so.0
INFO CODE_ID F7985FD806FBDD735F38BEAA187ABF7930CAC8CF
PUBLIC 2974 0 dns_cli_request_send
PUBLIC 2d60 0 dns_cli_request_recv
PUBLIC 2e20 0 parse_resolvconf_fp
PUBLIC 3050 0 parse_resolvconf
STACK CFI INIT 19f0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a20 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a60 48 .cfa: sp 0 + .ra: x30
STACK CFI 1a64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a6c x19: .cfa -16 + ^
STACK CFI 1aa4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ab0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ac0 154 .cfa: sp 0 + .ra: x30
STACK CFI 1ac8 .cfa: sp 64 +
STACK CFI 1ad4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1adc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1ae4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1bd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1bd8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1c14 120 .cfa: sp 0 + .ra: x30
STACK CFI 1c1c .cfa: sp 64 +
STACK CFI 1c28 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1c34 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d18 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1d34 168 .cfa: sp 0 + .ra: x30
STACK CFI 1d3c .cfa: sp 64 +
STACK CFI 1d48 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1d50 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d58 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1e60 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1ea0 134 .cfa: sp 0 + .ra: x30
STACK CFI 1ea8 .cfa: sp 64 +
STACK CFI 1eb4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1ebc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1ec4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1f98 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1fd4 120 .cfa: sp 0 + .ra: x30
STACK CFI 1fdc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1ff0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1ff8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2038 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2040 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2080 x23: .cfa -16 + ^
STACK CFI 20d4 x23: x23
STACK CFI 20e8 x23: .cfa -16 + ^
STACK CFI 20ec x23: x23
STACK CFI INIT 20f4 d8 .cfa: sp 0 + .ra: x30
STACK CFI 20fc .cfa: sp 64 +
STACK CFI 2108 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2110 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2118 x21: .cfa -16 + ^
STACK CFI 21a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 21b0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 21d0 4d4 .cfa: sp 0 + .ra: x30
STACK CFI 21d8 .cfa: sp 128 +
STACK CFI 21e4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 21ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 21f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 22fc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 240c x25: x25 x26: x26
STACK CFI 2440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2448 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 251c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 253c x25: x25 x26: x26
STACK CFI 2540 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 25e0 x25: x25 x26: x26
STACK CFI 26a0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 26a4 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 26ac .cfa: sp 80 +
STACK CFI 26b8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 26c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26c8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 27c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 27cc .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2974 3e4 .cfa: sp 0 + .ra: x30
STACK CFI 297c .cfa: sp 288 +
STACK CFI 2988 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2994 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 29a0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 29ac x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2c4c .cfa: sp 288 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2d60 bc .cfa: sp 0 + .ra: x30
STACK CFI 2d68 .cfa: sp 64 +
STACK CFI 2d74 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2d7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d88 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2dfc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2e20 228 .cfa: sp 0 + .ra: x30
STACK CFI 2e28 .cfa: sp 144 +
STACK CFI 2e34 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2e40 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2e48 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2e54 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2e60 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2e6c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2fc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2fcc .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3050 88 .cfa: sp 0 + .ra: x30
STACK CFI 305c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3070 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3080 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 30c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 30cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
