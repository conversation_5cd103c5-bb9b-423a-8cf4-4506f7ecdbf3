MODULE Linux arm64 C696225748A11587013CDB9326882AEA0 libpixbufloader-heif.so
INFO CODE_ID 572296C6A1488715013CDB9326882AEAB41221CB
PUBLIC 15a4 0 fill_vtable
PUBLIC 15e0 0 fill_info
STACK CFI INIT 10b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1120 48 .cfa: sp 0 + .ra: x30
STACK CFI 1124 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 112c x19: .cfa -16 + ^
STACK CFI 1164 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1170 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1180 64 .cfa: sp 0 + .ra: x30
STACK CFI 1188 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1190 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1198 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11a8 x23: .cfa -16 + ^
STACK CFI 11dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 11e4 1c .cfa: sp 0 + .ra: x30
STACK CFI 11ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1200 378 .cfa: sp 0 + .ra: x30
STACK CFI 1208 .cfa: sp 128 +
STACK CFI 1214 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 121c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1238 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12b0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 13f0 x23: x23 x24: x24
STACK CFI 144c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1454 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 14ec x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1504 x23: x23 x24: x24
STACK CFI 1508 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1568 x23: x23 x24: x24
STACK CFI 1574 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 1580 24 .cfa: sp 0 + .ra: x30
STACK CFI 1588 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1598 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 15a4 38 .cfa: sp 0 + .ra: x30
STACK CFI 15ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 15e0 5c .cfa: sp 0 + .ra: x30
STACK CFI 15e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15fc .cfa: sp 0 + .ra: .ra x29: x29
