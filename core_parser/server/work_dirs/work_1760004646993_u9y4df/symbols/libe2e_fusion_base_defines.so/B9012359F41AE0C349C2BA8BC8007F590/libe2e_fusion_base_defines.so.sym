MODULE Linux arm64 B9012359F41AE0C349C2BA8BC8007F590 libe2e_fusion_base_defines.so
INFO CODE_ID 592301B91AF4C3E049C2BA8BC8007F59
FILE 0 /home/<USER>/agent/workspace/MAX/app/e2e_fusion/code/common/base_defines/global_flags.cpp
FILE 1 /opt/aarch64--glibc--bleeding-edge-2024.02-1/aarch64-buildroot-linux-gnu/include/c++/13.2.0/bits/basic_string.h
FILE 2 /opt/aarch64--glibc--bleeding-edge-2024.02-1/aarch64-buildroot-linux-gnu/include/c++/13.2.0/bits/basic_string.tcc
FILE 3 /opt/aarch64--glibc--bleeding-edge-2024.02-1/aarch64-buildroot-linux-gnu/include/c++/13.2.0/bits/char_traits.h
FILE 4 /opt/aarch64--glibc--bleeding-edge-2024.02-1/aarch64-buildroot-linux-gnu/include/c++/13.2.0/bits/new_allocator.h
FILE 5 /opt/aarch64--glibc--bleeding-edge-2024.02-1/aarch64-buildroot-linux-gnu/include/c++/13.2.0/bits/stl_iterator_base_funcs.h
FILE 6 /root/.conan/data/gflags/2.2.2/_/_/package/1a8f066e7cfee987b0dcb6ceb166de1cf347c3d0/include/gflags/gflags.h
FUNC 2140 104 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&)
2140 1c 631 1
215c 4 230 1
2160 c 631 1
216c 4 189 1
2170 8 635 1
2178 8 409 3
2180 4 221 2
2184 4 409 3
2188 8 223 2
2190 8 417 1
2198 4 368 3
219c 4 368 3
21a0 4 368 3
21a4 4 247 2
21a8 4 218 1
21ac 8 640 1
21b4 4 368 3
21b8 18 640 1
21d0 4 640 1
21d4 8 640 1
21dc 8 439 3
21e4 8 225 2
21ec 8 225 2
21f4 4 250 1
21f8 4 225 2
21fc 4 213 1
2200 4 250 1
2204 10 445 3
2214 4 445 3
2218 4 640 1
221c 18 636 1
2234 10 636 1
FUNC 2250 c8 0 void std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::_M_construct<char*>(char*, char*, std::forward_iterator_tag)
2250 1c 217 2
226c 4 217 2
2270 4 106 5
2274 c 217 2
2280 4 221 2
2284 8 223 2
228c 4 223 1
2290 4 417 1
2294 4 223 1
2298 4 417 1
229c 4 368 3
22a0 4 368 3
22a4 4 368 3
22a8 4 247 2
22ac 4 218 1
22b0 8 248 2
22b8 4 368 3
22bc 18 248 2
22d4 4 248 2
22d8 8 248 2
22e0 8 439 3
22e8 8 225 2
22f0 4 225 2
22f4 4 213 1
22f8 4 250 1
22fc 4 250 1
2300 c 445 3
230c 4 445 3
2310 4 445 3
2314 4 248 2
FUNC 2320 1714 0 _GLOBAL__sub_I_global_flags.cpp
2320 10 58 0
2330 8 565 6
2338 4 58 0
233c 10 565 6
234c 4 58 0
2350 4 565 6
2354 8 193 1
235c 4 9 0
2360 4 223 1
2364 4 9 0
2368 c 541 1
2374 8 9 0
237c 4 541 1
2380 4 193 1
2384 4 541 1
2388 28 9 0
23b0 8 9 0
23b8 8 585 6
23c0 8 9 0
23c8 4 585 6
23cc 4 9 0
23d0 4 585 6
23d4 4 9 0
23d8 4 585 6
23dc 4 9 0
23e0 8 565 6
23e8 4 12 0
23ec 4 9 0
23f0 4 12 0
23f4 8 9 0
23fc 24 12 0
2420 4 9 0
2424 4 12 0
2428 4 13 0
242c c 565 6
2438 4 13 0
243c 4 223 1
2440 8 193 1
2448 8 541 1
2450 4 193 1
2454 4 541 1
2458 4 46 0
245c 8 541 1
2464 28 13 0
248c 8 585 6
2494 4 13 0
2498 4 585 6
249c 4 585 6
24a0 4 13 0
24a4 4 585 6
24a8 4 13 0
24ac 4 13 0
24b0 c 565 6
24bc 8 13 0
24c4 4 565 6
24c8 4 14 0
24cc 4 13 0
24d0 4 565 6
24d4 4 223 1
24d8 8 193 1
24e0 4 14 0
24e4 c 541 1
24f0 4 193 1
24f4 8 541 1
24fc 28 14 0
2524 8 585 6
252c 4 14 0
2530 4 585 6
2534 4 585 6
2538 4 14 0
253c 4 585 6
2540 4 14 0
2544 4 14 0
2548 c 565 6
2554 8 14 0
255c 4 565 6
2560 4 15 0
2564 4 14 0
2568 4 565 6
256c 4 223 1
2570 8 193 1
2578 4 15 0
257c c 541 1
2588 4 193 1
258c 8 541 1
2594 28 15 0
25bc 8 585 6
25c4 4 15 0
25c8 4 585 6
25cc 4 585 6
25d0 4 15 0
25d4 4 585 6
25d8 4 15 0
25dc 4 15 0
25e0 c 565 6
25ec 8 15 0
25f4 4 565 6
25f8 4 16 0
25fc 4 15 0
2600 4 565 6
2604 4 223 1
2608 8 193 1
2610 4 16 0
2614 c 541 1
2620 4 193 1
2624 8 541 1
262c 28 16 0
2654 8 585 6
265c 4 16 0
2660 4 585 6
2664 4 585 6
2668 4 16 0
266c 4 585 6
2670 4 16 0
2674 4 16 0
2678 c 565 6
2684 8 16 0
268c 4 565 6
2690 4 17 0
2694 4 16 0
2698 4 565 6
269c 4 223 1
26a0 8 193 1
26a8 4 17 0
26ac c 541 1
26b8 4 193 1
26bc 8 541 1
26c4 28 17 0
26ec 8 585 6
26f4 4 17 0
26f8 4 585 6
26fc 4 585 6
2700 4 17 0
2704 4 585 6
2708 4 17 0
270c 4 17 0
2710 c 565 6
271c 8 17 0
2724 4 565 6
2728 4 18 0
272c 4 17 0
2730 4 565 6
2734 4 223 1
2738 8 193 1
2740 4 18 0
2744 c 541 1
2750 4 193 1
2754 8 541 1
275c 28 18 0
2784 8 585 6
278c 4 18 0
2790 4 585 6
2794 4 585 6
2798 4 18 0
279c 4 585 6
27a0 4 18 0
27a4 4 18 0
27a8 4 19 0
27ac 8 18 0
27b4 18 19 0
27cc 8 565 6
27d4 10 19 0
27e4 4 18 0
27e8 4 19 0
27ec c 565 6
27f8 4 22 0
27fc 4 223 1
2800 8 193 1
2808 4 22 0
280c c 541 1
2818 4 193 1
281c 8 541 1
2824 28 22 0
284c 8 585 6
2854 4 22 0
2858 4 585 6
285c 4 585 6
2860 4 22 0
2864 4 585 6
2868 4 22 0
286c 4 22 0
2870 c 565 6
287c 8 22 0
2884 8 565 6
288c 4 25 0
2890 4 22 0
2894 4 565 6
2898 4 223 1
289c 8 193 1
28a4 4 25 0
28a8 c 541 1
28b4 4 193 1
28b8 8 541 1
28c0 28 25 0
28e8 8 585 6
28f0 4 25 0
28f4 4 585 6
28f8 4 585 6
28fc 4 25 0
2900 4 585 6
2904 4 25 0
2908 4 25 0
290c c 565 6
2918 8 25 0
2920 8 565 6
2928 4 26 0
292c 4 25 0
2930 4 565 6
2934 4 223 1
2938 8 193 1
2940 4 26 0
2944 c 541 1
2950 4 193 1
2954 8 541 1
295c 28 26 0
2984 8 585 6
298c 4 26 0
2990 4 585 6
2994 4 585 6
2998 4 26 0
299c 4 585 6
29a0 4 26 0
29a4 4 26 0
29a8 c 565 6
29b4 8 26 0
29bc 8 565 6
29c4 4 27 0
29c8 4 26 0
29cc 4 565 6
29d0 4 223 1
29d4 8 193 1
29dc 4 27 0
29e0 c 541 1
29ec 4 193 1
29f0 8 541 1
29f8 28 27 0
2a20 8 585 6
2a28 4 27 0
2a2c 4 585 6
2a30 4 585 6
2a34 4 27 0
2a38 4 585 6
2a3c 4 27 0
2a40 4 27 0
2a44 c 565 6
2a50 8 27 0
2a58 8 565 6
2a60 4 28 0
2a64 4 27 0
2a68 4 565 6
2a6c 4 223 1
2a70 8 193 1
2a78 4 28 0
2a7c c 541 1
2a88 4 193 1
2a8c 8 541 1
2a94 28 28 0
2abc 8 585 6
2ac4 4 28 0
2ac8 4 585 6
2acc 4 585 6
2ad0 4 28 0
2ad4 4 585 6
2ad8 4 28 0
2adc 4 28 0
2ae0 c 565 6
2aec 8 28 0
2af4 8 565 6
2afc 4 31 0
2b00 4 28 0
2b04 4 565 6
2b08 4 223 1
2b0c 8 193 1
2b14 4 31 0
2b18 c 541 1
2b24 4 193 1
2b28 8 541 1
2b30 28 31 0
2b58 8 585 6
2b60 4 31 0
2b64 4 585 6
2b68 4 585 6
2b6c 4 31 0
2b70 4 585 6
2b74 4 31 0
2b78 4 31 0
2b7c c 565 6
2b88 8 31 0
2b90 8 565 6
2b98 4 32 0
2b9c 4 31 0
2ba0 4 565 6
2ba4 4 223 1
2ba8 8 193 1
2bb0 4 32 0
2bb4 c 541 1
2bc0 4 193 1
2bc4 8 541 1
2bcc 28 32 0
2bf4 8 585 6
2bfc 4 32 0
2c00 4 585 6
2c04 4 585 6
2c08 4 32 0
2c0c 4 585 6
2c10 4 32 0
2c14 4 32 0
2c18 4 565 6
2c1c 8 32 0
2c24 8 565 6
2c2c 4 33 0
2c30 4 565 6
2c34 4 40 0
2c38 4 32 0
2c3c 4 565 6
2c40 4 223 1
2c44 8 193 1
2c4c 4 33 0
2c50 c 541 1
2c5c 4 193 1
2c60 8 541 1
2c68 28 33 0
2c90 8 585 6
2c98 4 33 0
2c9c 4 585 6
2ca0 4 585 6
2ca4 4 33 0
2ca8 4 585 6
2cac 4 33 0
2cb0 4 33 0
2cb4 c 565 6
2cc0 8 33 0
2cc8 8 565 6
2cd0 4 34 0
2cd4 4 33 0
2cd8 4 565 6
2cdc 4 223 1
2ce0 8 193 1
2ce8 4 34 0
2cec c 541 1
2cf8 4 193 1
2cfc 8 541 1
2d04 28 34 0
2d2c 8 585 6
2d34 4 34 0
2d38 4 585 6
2d3c 4 585 6
2d40 4 34 0
2d44 4 585 6
2d48 4 34 0
2d4c 4 34 0
2d50 c 565 6
2d5c 8 34 0
2d64 8 565 6
2d6c 4 35 0
2d70 4 34 0
2d74 4 565 6
2d78 4 223 1
2d7c 8 193 1
2d84 4 35 0
2d88 c 541 1
2d94 4 193 1
2d98 8 541 1
2da0 28 35 0
2dc8 8 585 6
2dd0 4 35 0
2dd4 4 585 6
2dd8 4 585 6
2ddc 4 35 0
2de0 4 585 6
2de4 4 35 0
2de8 4 35 0
2dec c 565 6
2df8 8 35 0
2e00 8 565 6
2e08 4 36 0
2e0c 4 35 0
2e10 4 565 6
2e14 4 223 1
2e18 8 193 1
2e20 4 36 0
2e24 c 541 1
2e30 4 193 1
2e34 8 541 1
2e3c 28 36 0
2e64 8 585 6
2e6c 4 36 0
2e70 4 585 6
2e74 4 585 6
2e78 4 36 0
2e7c 4 585 6
2e80 4 36 0
2e84 4 36 0
2e88 c 565 6
2e94 8 36 0
2e9c 8 565 6
2ea4 4 37 0
2ea8 4 36 0
2eac 4 565 6
2eb0 4 223 1
2eb4 8 193 1
2ebc 4 37 0
2ec0 c 541 1
2ecc 4 193 1
2ed0 8 541 1
2ed8 28 37 0
2f00 8 585 6
2f08 4 37 0
2f0c 4 585 6
2f10 4 585 6
2f14 4 37 0
2f18 4 585 6
2f1c 4 37 0
2f20 4 37 0
2f24 c 565 6
2f30 8 37 0
2f38 8 565 6
2f40 4 38 0
2f44 4 37 0
2f48 4 565 6
2f4c 4 223 1
2f50 8 193 1
2f58 4 38 0
2f5c c 541 1
2f68 4 193 1
2f6c 8 541 1
2f74 28 38 0
2f9c 8 585 6
2fa4 4 38 0
2fa8 4 585 6
2fac 4 585 6
2fb0 4 38 0
2fb4 4 585 6
2fb8 4 38 0
2fbc 4 38 0
2fc0 c 565 6
2fcc 8 38 0
2fd4 8 565 6
2fdc 4 39 0
2fe0 4 38 0
2fe4 4 565 6
2fe8 4 223 1
2fec 8 193 1
2ff4 4 39 0
2ff8 c 541 1
3004 4 193 1
3008 8 541 1
3010 28 39 0
3038 8 585 6
3040 4 39 0
3044 4 585 6
3048 4 585 6
304c 4 39 0
3050 4 585 6
3054 4 39 0
3058 4 39 0
305c c 565 6
3068 8 39 0
3070 10 565 6
3080 4 39 0
3084 4 565 6
3088 4 223 1
308c 8 193 1
3094 4 40 0
3098 c 541 1
30a4 4 193 1
30a8 8 541 1
30b0 28 40 0
30d8 8 585 6
30e0 4 40 0
30e4 4 585 6
30e8 4 585 6
30ec 4 40 0
30f0 4 585 6
30f4 4 40 0
30f8 4 40 0
30fc c 565 6
3108 8 40 0
3110 4 565 6
3114 4 41 0
3118 4 40 0
311c 4 565 6
3120 4 223 1
3124 8 193 1
312c 4 41 0
3130 c 541 1
313c 4 193 1
3140 8 541 1
3148 28 41 0
3170 8 585 6
3178 4 41 0
317c 4 585 6
3180 4 585 6
3184 4 41 0
3188 4 585 6
318c 4 41 0
3190 4 41 0
3194 c 565 6
31a0 8 41 0
31a8 8 565 6
31b0 4 42 0
31b4 4 41 0
31b8 4 565 6
31bc 4 223 1
31c0 8 193 1
31c8 4 42 0
31cc c 541 1
31d8 4 193 1
31dc 8 541 1
31e4 28 42 0
320c 8 585 6
3214 4 42 0
3218 4 585 6
321c 4 585 6
3220 4 42 0
3224 4 585 6
3228 4 42 0
322c 4 42 0
3230 c 565 6
323c 8 42 0
3244 8 565 6
324c 4 43 0
3250 4 42 0
3254 4 565 6
3258 4 223 1
325c 8 193 1
3264 4 43 0
3268 c 541 1
3274 4 193 1
3278 8 541 1
3280 28 43 0
32a8 8 585 6
32b0 4 43 0
32b4 4 585 6
32b8 4 585 6
32bc 4 43 0
32c0 4 585 6
32c4 4 43 0
32c8 4 43 0
32cc c 565 6
32d8 8 43 0
32e0 8 565 6
32e8 4 44 0
32ec 4 43 0
32f0 4 565 6
32f4 4 223 1
32f8 8 193 1
3300 4 44 0
3304 c 541 1
3310 4 193 1
3314 8 541 1
331c 28 44 0
3344 8 585 6
334c 4 44 0
3350 4 585 6
3354 4 585 6
3358 4 44 0
335c 4 585 6
3360 4 44 0
3364 4 44 0
3368 c 565 6
3374 8 44 0
337c 8 565 6
3384 8 46 0
338c 4 44 0
3390 4 565 6
3394 4 223 1
3398 8 193 1
33a0 4 46 0
33a4 c 541 1
33b0 4 193 1
33b4 8 541 1
33bc 24 46 0
33e0 4 585 6
33e4 8 585 6
33ec 4 46 0
33f0 4 585 6
33f4 4 46 0
33f8 4 585 6
33fc 4 46 0
3400 4 46 0
3404 c 565 6
3410 8 46 0
3418 8 565 6
3420 4 47 0
3424 4 46 0
3428 4 565 6
342c 4 223 1
3430 8 193 1
3438 4 47 0
343c c 541 1
3448 4 193 1
344c 8 541 1
3454 28 47 0
347c 4 585 6
3480 4 585 6
3484 4 47 0
3488 4 585 6
348c 4 585 6
3490 4 47 0
3494 4 585 6
3498 4 47 0
349c 4 47 0
34a0 c 565 6
34ac 8 47 0
34b4 8 565 6
34bc 4 48 0
34c0 4 47 0
34c4 4 565 6
34c8 4 223 1
34cc 8 193 1
34d4 4 48 0
34d8 c 541 1
34e4 4 193 1
34e8 8 541 1
34f0 24 48 0
3514 4 585 6
3518 8 585 6
3520 4 48 0
3524 4 54 0
3528 4 585 6
352c 4 48 0
3530 4 585 6
3534 4 48 0
3538 4 48 0
353c c 565 6
3548 8 48 0
3550 4 565 6
3554 4 50 0
3558 4 48 0
355c 4 565 6
3560 4 223 1
3564 8 193 1
356c 4 50 0
3570 c 541 1
357c 4 193 1
3580 8 541 1
3588 28 50 0
35b0 8 585 6
35b8 4 50 0
35bc 4 585 6
35c0 4 585 6
35c4 4 50 0
35c8 4 585 6
35cc 4 50 0
35d0 4 50 0
35d4 c 565 6
35e0 8 50 0
35e8 8 565 6
35f0 4 51 0
35f4 4 50 0
35f8 4 565 6
35fc 4 223 1
3600 8 193 1
3608 4 51 0
360c c 541 1
3618 4 193 1
361c 8 541 1
3624 28 51 0
364c 8 585 6
3654 4 51 0
3658 4 585 6
365c 4 585 6
3660 4 51 0
3664 4 585 6
3668 4 51 0
366c 4 51 0
3670 c 565 6
367c 8 51 0
3684 8 565 6
368c 4 52 0
3690 4 51 0
3694 4 565 6
3698 4 223 1
369c 8 193 1
36a4 4 52 0
36a8 c 541 1
36b4 4 193 1
36b8 8 541 1
36c0 28 52 0
36e8 8 585 6
36f0 4 52 0
36f4 4 585 6
36f8 4 585 6
36fc 4 52 0
3700 4 585 6
3704 4 52 0
3708 4 52 0
370c c 565 6
3718 8 52 0
3720 8 565 6
3728 4 53 0
372c 4 52 0
3730 4 565 6
3734 4 223 1
3738 8 193 1
3740 4 53 0
3744 c 541 1
3750 4 193 1
3754 8 541 1
375c 28 53 0
3784 8 585 6
378c 4 53 0
3790 4 585 6
3794 4 585 6
3798 4 53 0
379c 4 585 6
37a0 4 53 0
37a4 4 53 0
37a8 c 565 6
37b4 8 53 0
37bc 10 565 6
37cc 4 53 0
37d0 4 565 6
37d4 4 223 1
37d8 8 193 1
37e0 4 54 0
37e4 c 541 1
37f0 4 193 1
37f4 8 541 1
37fc 28 54 0
3824 8 585 6
382c 4 54 0
3830 4 585 6
3834 4 585 6
3838 4 54 0
383c 4 585 6
3840 4 54 0
3844 4 54 0
3848 c 565 6
3854 8 54 0
385c 4 565 6
3860 4 55 0
3864 4 54 0
3868 4 565 6
386c 4 223 1
3870 8 193 1
3878 4 55 0
387c c 541 1
3888 4 193 1
388c 8 541 1
3894 28 55 0
38bc 8 585 6
38c4 4 55 0
38c8 4 585 6
38cc 4 585 6
38d0 4 55 0
38d4 4 585 6
38d8 4 55 0
38dc 4 55 0
38e0 c 565 6
38ec 8 55 0
38f4 4 565 6
38f8 4 57 0
38fc 4 55 0
3900 4 565 6
3904 4 223 1
3908 8 193 1
3910 4 57 0
3914 c 541 1
3920 4 193 1
3924 8 541 1
392c 28 57 0
3954 8 585 6
395c 4 57 0
3960 4 585 6
3964 4 585 6
3968 4 57 0
396c 4 585 6
3970 4 57 0
3974 4 57 0
3978 c 565 6
3984 8 57 0
398c 8 565 6
3994 4 58 0
3998 4 57 0
399c 4 565 6
39a0 4 223 1
39a4 8 193 1
39ac 4 58 0
39b0 c 541 1
39bc 4 193 1
39c0 8 541 1
39c8 28 58 0
39f0 8 585 6
39f8 4 58 0
39fc 4 585 6
3a00 4 58 0
3a04 4 585 6
3a08 4 58 0
3a0c c 58 0
3a18 10 58 0
3a28 4 58 0
3a2c 8 58 0
FUNC 3b20 64 0 fLS::StringFlagDestructor::~StringFlagDestructor()
3b20 8 587 6
3b28 4 588 6
3b2c 4 587 6
3b30 4 587 6
3b34 8 223 1
3b3c 8 264 1
3b44 4 289 1
3b48 8 168 4
3b50 4 589 6
3b54 8 223 1
3b5c 8 264 1
3b64 4 590 6
3b68 4 590 6
3b6c 4 289 1
3b70 4 168 4
3b74 4 168 4
3b78 4 590 6
3b7c 8 590 6
PUBLIC 2048 0 _init
PUBLIC 3a34 0 call_weak_fn
PUBLIC 3a50 0 deregister_tm_clones
PUBLIC 3a80 0 register_tm_clones
PUBLIC 3ac0 0 __do_global_dtors_aux
PUBLIC 3b10 0 frame_dummy
PUBLIC 3b84 0 _fini
STACK CFI INIT 3a50 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a80 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ac0 48 .cfa: sp 0 + .ra: x30
STACK CFI 3ac4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3acc x19: .cfa -16 + ^
STACK CFI 3b04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3b10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2140 104 .cfa: sp 0 + .ra: x30
STACK CFI 2144 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2154 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 215c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 21d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 21dc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2250 c8 .cfa: sp 0 + .ra: x30
STACK CFI 2254 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2264 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 226c x21: .cfa -32 + ^
STACK CFI 22dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 22e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3b20 64 .cfa: sp 0 + .ra: x30
STACK CFI 3b24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b30 x19: .cfa -16 + ^
STACK CFI 3b6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3b78 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3b80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2320 1714 .cfa: sp 0 + .ra: x30
STACK CFI 2324 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2330 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2340 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2350 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
