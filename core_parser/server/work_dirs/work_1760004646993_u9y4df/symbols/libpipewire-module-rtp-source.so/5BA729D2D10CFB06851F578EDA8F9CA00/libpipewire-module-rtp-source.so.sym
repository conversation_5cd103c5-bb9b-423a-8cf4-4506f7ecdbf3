MODULE Linux arm64 5BA729D2D10CFB06851F578EDA8F9CA00 libpipewire-module-rtp-source.so
INFO CODE_ID D229A75B0CD106FB851F578EDA8F9CA076E8ABF8
PUBLIC dd10 0 pipewire__module_init
STACK CFI INIT 2df0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e20 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e60 48 .cfa: sp 0 + .ra: x30
STACK CFI 2e64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e6c x19: .cfa -16 + ^
STACK CFI 2ea4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2eb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ec0 1c .cfa: sp 0 + .ra: x30
STACK CFI 2ec8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2ed4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2ee0 340 .cfa: sp 0 + .ra: x30
STACK CFI 2ee8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2fd4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2fe0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3118 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3124 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3174 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 317c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 31f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 31fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3220 50 .cfa: sp 0 + .ra: x30
STACK CFI 3228 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3240 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3248 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3254 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 325c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3268 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3270 50 .cfa: sp 0 + .ra: x30
STACK CFI 3278 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3280 x19: .cfa -16 + ^
STACK CFI 32b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 32c0 128 .cfa: sp 0 + .ra: x30
STACK CFI 32c8 .cfa: sp 112 +
STACK CFI 32dc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 32e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 32f0 x21: .cfa -16 + ^
STACK CFI 33dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 33e4 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 33f0 114 .cfa: sp 0 + .ra: x30
STACK CFI 33f8 .cfa: sp 96 +
STACK CFI 3408 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3414 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 34f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3500 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3504 f8 .cfa: sp 0 + .ra: x30
STACK CFI 350c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3518 x19: .cfa -16 + ^
STACK CFI 3554 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 355c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3600 30c .cfa: sp 0 + .ra: x30
STACK CFI 3608 .cfa: sp 96 +
STACK CFI 3614 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 361c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 369c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 36c0 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3708 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3710 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3910 54 .cfa: sp 0 + .ra: x30
STACK CFI 3918 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3920 x19: .cfa -16 + ^
STACK CFI 395c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3964 864 .cfa: sp 0 + .ra: x30
STACK CFI 396c .cfa: sp 176 +
STACK CFI 3978 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3980 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 39e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 39e8 .cfa: sp 176 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 3a38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a40 .cfa: sp 176 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 3b00 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3b04 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3b08 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3c10 x21: x21 x22: x22
STACK CFI 3c14 x23: x23 x24: x24
STACK CFI 3c18 x25: x25 x26: x26
STACK CFI 3c1c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3cac x21: x21 x22: x22
STACK CFI 3cb0 x23: x23 x24: x24
STACK CFI 3cb4 x25: x25 x26: x26
STACK CFI 3cb8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3d90 x21: x21 x22: x22
STACK CFI 3d94 x23: x23 x24: x24
STACK CFI 3d98 x25: x25 x26: x26
STACK CFI 3d9c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3e3c x21: x21 x22: x22
STACK CFI 3e44 x23: x23 x24: x24
STACK CFI 3e4c x25: x25 x26: x26
STACK CFI 3e54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e70 .cfa: sp 176 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 3ea0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 41a0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 41a4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 41a8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 41ac x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 41d0 200 .cfa: sp 0 + .ra: x30
STACK CFI 41d8 .cfa: sp 112 +
STACK CFI 41dc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 41e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 41f4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 43bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 43c4 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 43d0 50 .cfa: sp 0 + .ra: x30
STACK CFI 43d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43e0 x19: .cfa -16 + ^
STACK CFI 4418 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4420 104 .cfa: sp 0 + .ra: x30
STACK CFI 4428 .cfa: sp 96 +
STACK CFI 442c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4434 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4440 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 447c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4484 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 451c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4524 180 .cfa: sp 0 + .ra: x30
STACK CFI 452c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4538 .cfa: sp 2096 + x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 45c0 .cfa: sp 32 +
STACK CFI 45c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45d0 .cfa: sp 2096 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 46a4 28 .cfa: sp 0 + .ra: x30
STACK CFI 46ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 46b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 46c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 46c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 46d0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 46d8 .cfa: sp 64 +
STACK CFI 46dc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 46e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 46f8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 479c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 47a4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4894 2b28 .cfa: sp 0 + .ra: x30
STACK CFI 489c .cfa: sp 480 +
STACK CFI 48b0 .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 48c8 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 48d0 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 48e8 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 5040 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5048 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 73c0 fe8 .cfa: sp 0 + .ra: x30
STACK CFI 73c8 .cfa: sp 128 +
STACK CFI 73d4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 73dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7440 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7444 x23: .cfa -16 + ^
STACK CFI 7554 x21: x21 x22: x22 x23: x23
STACK CFI 757c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7580 x23: .cfa -16 + ^
STACK CFI 7678 x21: x21 x22: x22
STACK CFI 767c x23: x23
STACK CFI 7684 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 774c x21: x21 x22: x22
STACK CFI 7750 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7754 x23: .cfa -16 + ^
STACK CFI 7844 x21: x21 x22: x22 x23: x23
STACK CFI 7860 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7924 x21: x21 x22: x22
STACK CFI 7940 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7944 x23: .cfa -16 + ^
STACK CFI 7a48 x21: x21 x22: x22 x23: x23
STACK CFI 7a4c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7b14 x21: x21 x22: x22
STACK CFI 7b18 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7bdc x21: x21 x22: x22
STACK CFI 7be0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7be4 x23: .cfa -16 + ^
STACK CFI 7cf8 x21: x21 x22: x22 x23: x23
STACK CFI 7cfc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7dc4 x21: x21 x22: x22
STACK CFI 7e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e08 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 7e0c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7e10 x23: .cfa -16 + ^
STACK CFI 7f0c x21: x21 x22: x22 x23: x23
STACK CFI 7f10 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7f14 x23: .cfa -16 + ^
STACK CFI 805c x21: x21 x22: x22 x23: x23
STACK CFI 8060 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8154 x21: x21 x22: x22
STACK CFI 8158 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8198 x23: .cfa -16 + ^
STACK CFI 839c x21: x21 x22: x22 x23: x23
STACK CFI 83a0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 83a4 x23: .cfa -16 + ^
STACK CFI INIT 83b0 76c .cfa: sp 0 + .ra: x30
STACK CFI 83b8 .cfa: sp 272 +
STACK CFI 83c4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 83cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 83ec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 83f4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 83fc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 84a4 x21: x21 x22: x22
STACK CFI 84a8 x23: x23 x24: x24
STACK CFI 84ac x25: x25 x26: x26
STACK CFI 84b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 84c0 .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 84c4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 877c x27: x27 x28: x28
STACK CFI 8780 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8784 x27: x27 x28: x28
STACK CFI 8788 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 87d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 87e0 .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 87f8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8804 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8884 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 88b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 88d8 .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8b08 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8b0c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8b10 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8b14 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8b18 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 8b20 510 .cfa: sp 0 + .ra: x30
STACK CFI 8b28 .cfa: sp 160 +
STACK CFI 8b2c .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8b34 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 8b48 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 8b50 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 8b54 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 8b58 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 8c1c v8: .cfa -48 + ^
STACK CFI 8ccc v8: v8
STACK CFI 8d40 x21: x21 x22: x22
STACK CFI 8d44 x23: x23 x24: x24
STACK CFI 8d48 x25: x25 x26: x26
STACK CFI 8d4c x27: x27 x28: x28
STACK CFI 8d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8d60 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 8e54 v8: .cfa -48 + ^
STACK CFI 8ea8 v8: v8
STACK CFI 8ed8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8f14 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 8f2c v8: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 8f30 v8: v8
STACK CFI 8f60 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8f98 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 9030 510 .cfa: sp 0 + .ra: x30
STACK CFI 9038 .cfa: sp 160 +
STACK CFI 903c .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9044 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9058 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 9060 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9064 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 9068 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 912c v8: .cfa -48 + ^
STACK CFI 91dc v8: v8
STACK CFI 9250 x21: x21 x22: x22
STACK CFI 9254 x23: x23 x24: x24
STACK CFI 9258 x25: x25 x26: x26
STACK CFI 925c x27: x27 x28: x28
STACK CFI 9268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9270 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 9364 v8: .cfa -48 + ^
STACK CFI 93b8 v8: v8
STACK CFI 93e8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 941c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9424 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 943c v8: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 9440 v8: v8
STACK CFI 9470 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 94a8 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 9540 62c .cfa: sp 0 + .ra: x30
STACK CFI 9548 .cfa: sp 288 +
STACK CFI 9554 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 955c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 957c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9588 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 958c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9590 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9888 x21: x21 x22: x22
STACK CFI 988c x23: x23 x24: x24
STACK CFI 9890 x25: x25 x26: x26
STACK CFI 9894 x27: x27 x28: x28
STACK CFI 98bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 98c4 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 9934 x21: x21 x22: x22
STACK CFI 9938 x23: x23 x24: x24
STACK CFI 993c x25: x25 x26: x26
STACK CFI 9940 x27: x27 x28: x28
STACK CFI 9944 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9a74 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9acc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9af0 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 9b08 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9b58 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9b5c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9b60 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9b64 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9b68 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 9b70 75c .cfa: sp 0 + .ra: x30
STACK CFI 9b78 .cfa: sp 304 +
STACK CFI 9b84 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9b8c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9bac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9bb8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9bbc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9bc0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9c90 x21: x21 x22: x22
STACK CFI 9c94 x23: x23 x24: x24
STACK CFI 9c98 x25: x25 x26: x26
STACK CFI 9c9c x27: x27 x28: x28
STACK CFI 9cc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9ccc .cfa: sp 304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 9cf0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9d6c .cfa: sp 304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 9d84 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a2b8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a2bc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a2c0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a2c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a2c8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT a2d0 360 .cfa: sp 0 + .ra: x30
STACK CFI a2d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a2ec .cfa: sp 1568 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a354 .cfa: sp 96 +
STACK CFI a364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a36c .cfa: sp 1568 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI a370 x25: .cfa -32 + ^
STACK CFI a378 x26: .cfa -24 + ^
STACK CFI a380 x27: .cfa -16 + ^
STACK CFI a388 x28: .cfa -8 + ^
STACK CFI a570 x25: x25
STACK CFI a574 x26: x26
STACK CFI a578 x27: x27
STACK CFI a57c x28: x28
STACK CFI a580 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a61c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a620 x25: .cfa -32 + ^
STACK CFI a624 x26: .cfa -24 + ^
STACK CFI a628 x27: .cfa -16 + ^
STACK CFI a62c x28: .cfa -8 + ^
STACK CFI INIT a630 3c0 .cfa: sp 0 + .ra: x30
STACK CFI a638 .cfa: sp 128 +
STACK CFI a63c .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI a644 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI a658 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI a664 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI a66c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI a670 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI a790 x21: x21 x22: x22
STACK CFI a794 x23: x23 x24: x24
STACK CFI a798 x25: x25 x26: x26
STACK CFI a79c x27: x27 x28: x28
STACK CFI a7a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a7a8 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI a8c0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a8f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a8fc .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI a914 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI a9b8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a9cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a9f0 4c4 .cfa: sp 0 + .ra: x30
STACK CFI a9f8 .cfa: sp 448 +
STACK CFI aa08 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI aa1c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI aa24 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI aa34 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI aba0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI aba8 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT aeb4 20c .cfa: sp 0 + .ra: x30
STACK CFI aebc .cfa: sp 96 +
STACK CFI aec8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI aed4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI aedc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI aee8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI b050 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b058 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI b0a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b0ac .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT b0c0 fa0 .cfa: sp 0 + .ra: x30
STACK CFI b0c8 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI b0d8 .cfa: sp 1344 + x19: .cfa -112 + ^ x20: .cfa -104 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI b114 x21: .cfa -96 + ^
STACK CFI b118 x22: .cfa -88 + ^
STACK CFI b120 x23: .cfa -80 + ^
STACK CFI b128 x24: .cfa -72 + ^
STACK CFI b24c x27: .cfa -48 + ^
STACK CFI b254 x28: .cfa -40 + ^
STACK CFI b258 v8: .cfa -32 + ^
STACK CFI b25c v9: .cfa -24 + ^
STACK CFI b260 v10: .cfa -16 + ^
STACK CFI b264 v11: .cfa -8 + ^
STACK CFI b834 x21: x21
STACK CFI b838 x22: x22
STACK CFI b83c x23: x23
STACK CFI b840 x24: x24
STACK CFI b844 x27: x27
STACK CFI b848 x28: x28
STACK CFI b84c v8: v8
STACK CFI b850 v9: v9
STACK CFI b854 v10: v10
STACK CFI b858 v11: v11
STACK CFI b878 .cfa: sp 128 +
STACK CFI b888 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI b890 .cfa: sp 1344 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI b8a4 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b8cc v10: v10 v11: v11 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI b980 x27: .cfa -48 + ^
STACK CFI b988 x28: .cfa -40 + ^
STACK CFI b98c v8: .cfa -32 + ^
STACK CFI b990 v9: .cfa -24 + ^
STACK CFI b994 v10: .cfa -16 + ^
STACK CFI b998 v11: .cfa -8 + ^
STACK CFI b9b0 v10: v10 v11: v11 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI ba2c x27: .cfa -48 + ^
STACK CFI ba34 x28: .cfa -40 + ^
STACK CFI ba3c v8: .cfa -32 + ^
STACK CFI ba44 v9: .cfa -24 + ^
STACK CFI ba4c v10: .cfa -16 + ^
STACK CFI ba54 v11: .cfa -8 + ^
STACK CFI bbb0 x21: x21
STACK CFI bbb4 x22: x22
STACK CFI bbb8 x23: x23
STACK CFI bbbc x24: x24
STACK CFI bbc0 x27: x27
STACK CFI bbc4 x28: x28
STACK CFI bbc8 v8: v8
STACK CFI bbcc v9: v9
STACK CFI bbd0 v10: v10
STACK CFI bbd4 v11: v11
STACK CFI bbec v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI be38 v10: v10 v11: v11 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI be40 x21: x21
STACK CFI be48 x22: x22
STACK CFI be4c x23: x23
STACK CFI be50 x24: x24
STACK CFI be54 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI bea4 v10: v10 v11: v11 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI bfb8 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI bfe0 v10: v10 v11: v11 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI c028 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI c02c x21: .cfa -96 + ^
STACK CFI c030 x22: .cfa -88 + ^
STACK CFI c034 x23: .cfa -80 + ^
STACK CFI c038 x24: .cfa -72 + ^
STACK CFI c03c x27: .cfa -48 + ^
STACK CFI c040 x28: .cfa -40 + ^
STACK CFI c044 v8: .cfa -32 + ^
STACK CFI c048 v9: .cfa -24 + ^
STACK CFI c04c v10: .cfa -16 + ^
STACK CFI c050 v11: .cfa -8 + ^
STACK CFI c054 v10: v10 v11: v11 v8: v8 v9: v9 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI INIT c060 178 .cfa: sp 0 + .ra: x30
STACK CFI c068 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c078 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c084 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c08c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c09c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c0a8 .cfa: sp 624 + x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c164 .cfa: sp 96 +
STACK CFI c17c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c184 .cfa: sp 624 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT c1e0 6f0 .cfa: sp 0 + .ra: x30
STACK CFI c1e8 .cfa: sp 160 +
STACK CFI c1ec .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI c1f4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI c204 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI c20c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI c214 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI c254 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI c2ec v8: .cfa -32 + ^
STACK CFI c3b4 v8: v8
STACK CFI c3f4 x19: x19 x20: x20
STACK CFI c3fc x21: x21 x22: x22
STACK CFI c400 x23: x23 x24: x24
STACK CFI c404 x27: x27 x28: x28
STACK CFI c410 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI c418 .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI c464 v8: .cfa -32 + ^
STACK CFI c47c v8: v8
STACK CFI c5dc x27: x27 x28: x28
STACK CFI c608 x19: x19 x20: x20
STACK CFI c60c x21: x21 x22: x22
STACK CFI c610 x23: x23 x24: x24
STACK CFI c618 v8: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI c67c v8: v8
STACK CFI c704 x27: x27 x28: x28
STACK CFI c73c x19: x19 x20: x20
STACK CFI c744 x21: x21 x22: x22
STACK CFI c748 x23: x23 x24: x24
STACK CFI c74c x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI c764 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI c7bc x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI c810 x19: x19 x20: x20
STACK CFI c818 x21: x21 x22: x22
STACK CFI c81c x23: x23 x24: x24
STACK CFI c838 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI c8c0 x19: x19 x20: x20
STACK CFI c8c8 x21: x21 x22: x22
STACK CFI c8cc x23: x23 x24: x24
STACK CFI INIT c8d0 cec .cfa: sp 0 + .ra: x30
STACK CFI c8d8 .cfa: sp 320 +
STACK CFI c8e4 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI c8ec x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI c908 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI c910 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI c92c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI c958 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI cc90 x21: x21 x22: x22
STACK CFI cc94 x23: x23 x24: x24
STACK CFI cc98 x25: x25 x26: x26
STACK CFI cc9c x27: x27 x28: x28
STACK CFI ccc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cccc .cfa: sp 320 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI ce8c v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI ce90 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI ce98 v12: .cfa -16 + ^ v13: .cfa -8 + ^
STACK CFI cfbc v10: v10 v11: v11
STACK CFI cfc4 v8: v8 v9: v9
STACK CFI cfc8 v12: v12 v13: v13
STACK CFI cfd0 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI d074 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9
STACK CFI d134 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI d16c v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI d198 x21: x21 x22: x22
STACK CFI d19c x23: x23 x24: x24
STACK CFI d1a0 x25: x25 x26: x26
STACK CFI d1a8 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d1e8 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9
STACK CFI d26c x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI d2a4 x23: x23 x24: x24
STACK CFI d2ac x25: x25 x26: x26
STACK CFI d2b0 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d310 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI d328 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI d398 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d3c0 x21: x21 x22: x22
STACK CFI d3c8 x23: x23 x24: x24
STACK CFI d3cc x25: x25 x26: x26
STACK CFI d3d0 x27: x27 x28: x28
STACK CFI d3d4 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d400 x21: x21 x22: x22
STACK CFI d408 x23: x23 x24: x24
STACK CFI d40c x25: x25 x26: x26
STACK CFI d410 x27: x27 x28: x28
STACK CFI d414 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d42c x27: x27 x28: x28
STACK CFI d444 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d48c x27: x27 x28: x28
STACK CFI d4e0 x21: x21 x22: x22
STACK CFI d4e8 x23: x23 x24: x24
STACK CFI d4ec x25: x25 x26: x26
STACK CFI d4f0 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI d508 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI d50c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI d510 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI d514 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI d518 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d51c v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI d520 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI d524 v12: .cfa -16 + ^ v13: .cfa -8 + ^
STACK CFI d528 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI d554 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI d580 x21: x21 x22: x22
STACK CFI d588 x23: x23 x24: x24
STACK CFI d58c x25: x25 x26: x26
STACK CFI d590 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT d5c0 74c .cfa: sp 0 + .ra: x30
STACK CFI d5c8 .cfa: sp 176 +
STACK CFI d5cc .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI d5d8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI d5e8 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI d5f0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI d5f8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI d5fc x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI d6c4 v8: .cfa -48 + ^
STACK CFI d794 v8: v8
STACK CFI d7dc x19: x19 x20: x20
STACK CFI d7e4 x21: x21 x22: x22
STACK CFI d7e8 x23: x23 x24: x24
STACK CFI d7ec x25: x25 x26: x26
STACK CFI d7f8 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI d800 .cfa: sp 176 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI d84c v8: .cfa -48 + ^
STACK CFI d864 v8: v8
STACK CFI da70 x19: x19 x20: x20
STACK CFI da74 x21: x21 x22: x22
STACK CFI da78 x23: x23 x24: x24
STACK CFI da7c x25: x25 x26: x26
STACK CFI da84 v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI daf4 v8: v8
STACK CFI db6c x19: x19 x20: x20
STACK CFI db74 x21: x21 x22: x22
STACK CFI db78 x23: x23 x24: x24
STACK CFI db7c x25: x25 x26: x26
STACK CFI db80 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI db98 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI dbf0 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI dc44 x19: x19 x20: x20
STACK CFI dc4c x21: x21 x22: x22
STACK CFI dc50 x23: x23 x24: x24
STACK CFI dc54 x25: x25 x26: x26
STACK CFI dc70 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI dcf8 x19: x19 x20: x20
STACK CFI dd00 x21: x21 x22: x22
STACK CFI dd04 x23: x23 x24: x24
STACK CFI dd08 x25: x25 x26: x26
STACK CFI INIT dd10 d78 .cfa: sp 0 + .ra: x30
STACK CFI dd18 .cfa: sp 160 +
STACK CFI dd24 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI dd2c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI dd38 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI de20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI de28 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI de60 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI de68 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e2ec x25: x25 x26: x26
STACK CFI e2f4 x27: x27 x28: x28
STACK CFI e2f8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e520 x25: x25 x26: x26
STACK CFI e528 x27: x27 x28: x28
STACK CFI e52c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e5f8 x25: x25 x26: x26
STACK CFI e600 x27: x27 x28: x28
STACK CFI e604 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e61c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI e64c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e870 x25: x25 x26: x26
STACK CFI e874 x27: x27 x28: x28
STACK CFI e878 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e928 x25: x25 x26: x26
STACK CFI e92c x27: x27 x28: x28
STACK CFI e930 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e990 x25: x25 x26: x26
STACK CFI e994 x27: x27 x28: x28
STACK CFI e998 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ea64 x25: x25 x26: x26
STACK CFI ea68 x27: x27 x28: x28
STACK CFI ea80 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ea84 x27: .cfa -16 + ^ x28: .cfa -8 + ^
