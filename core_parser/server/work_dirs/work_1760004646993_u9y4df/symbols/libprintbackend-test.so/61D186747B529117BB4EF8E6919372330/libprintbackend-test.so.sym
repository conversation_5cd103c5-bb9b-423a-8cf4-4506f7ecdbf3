MODULE Linux arm64 61D186747B529117BB4EF8E6919372330 libprintbackend-test.so
INFO CODE_ID 7486D161527B1791BB4EF8E6919372333BB44C97
PUBLIC 2540 0 pb_module_init
PUBLIC 2604 0 pb_module_exit
PUBLIC 2620 0 gtk_print_backend_test_get_type
PUBLIC 2640 0 gtk_print_backend_test_new
PUBLIC 2660 0 pb_module_create
STACK CFI INIT 1a00 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a30 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a70 48 .cfa: sp 0 + .ra: x30
STACK CFI 1a74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a7c x19: .cfa -16 + ^
STACK CFI 1ab4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ac0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ad0 18 .cfa: sp 0 + .ra: x30
STACK CFI 1ad8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ae0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1af0 64 .cfa: sp 0 + .ra: x30
STACK CFI 1af8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b00 x19: .cfa -16 + ^
STACK CFI 1b4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1b54 134 .cfa: sp 0 + .ra: x30
STACK CFI 1b5c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1b64 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1b70 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1b84 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1ba0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1ba8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1c80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 1c90 cc .cfa: sp 0 + .ra: x30
STACK CFI 1c98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ca4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1d40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1d48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1d60 b0 .cfa: sp 0 + .ra: x30
STACK CFI 1d68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d74 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1de8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1e10 a8 .cfa: sp 0 + .ra: x30
STACK CFI 1e1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e2c x19: .cfa -16 + ^
STACK CFI 1ea0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1eb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1ec0 9c .cfa: sp 0 + .ra: x30
STACK CFI 1ec8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1ed4 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 1ee0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1f48 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 1f50 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1f60 e0 .cfa: sp 0 + .ra: x30
STACK CFI 1f68 .cfa: sp 64 +
STACK CFI 1f78 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1f80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1f8c x21: .cfa -16 + ^
STACK CFI 2034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 203c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2040 e4 .cfa: sp 0 + .ra: x30
STACK CFI 2048 .cfa: sp 96 +
STACK CFI 2054 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2070 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 207c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2088 x23: .cfa -16 + ^
STACK CFI 20c8 x19: x19 x20: x20
STACK CFI 20d0 x21: x21 x22: x22
STACK CFI 20d4 x23: x23
STACK CFI 20d8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 20dc x19: x19 x20: x20
STACK CFI 20e0 x21: x21 x22: x22
STACK CFI 20e4 x23: x23
STACK CFI 210c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2114 .cfa: sp 96 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2118 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 211c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2120 x23: .cfa -16 + ^
STACK CFI INIT 2124 74 .cfa: sp 0 + .ra: x30
STACK CFI 212c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2138 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 21a0 80 .cfa: sp 0 + .ra: x30
STACK CFI 21a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 21b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2220 22c .cfa: sp 0 + .ra: x30
STACK CFI 2228 .cfa: sp 96 +
STACK CFI 2234 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 223c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2244 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2250 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 225c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2368 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2450 ec .cfa: sp 0 + .ra: x30
STACK CFI 2458 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 246c .cfa: sp 8272 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 24dc .cfa: sp 48 +
STACK CFI 24e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 24f0 .cfa: sp 8272 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2540 c4 .cfa: sp 0 + .ra: x30
STACK CFI 2548 .cfa: sp 112 +
STACK CFI 2558 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2568 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 25f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2600 .cfa: sp 112 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2604 18 .cfa: sp 0 + .ra: x30
STACK CFI 260c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2614 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2620 20 .cfa: sp 0 + .ra: x30
STACK CFI 2628 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2634 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2640 20 .cfa: sp 0 + .ra: x30
STACK CFI 2648 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2654 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2660 18 .cfa: sp 0 + .ra: x30
STACK CFI 2668 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2670 .cfa: sp 0 + .ra: .ra x29: x29
