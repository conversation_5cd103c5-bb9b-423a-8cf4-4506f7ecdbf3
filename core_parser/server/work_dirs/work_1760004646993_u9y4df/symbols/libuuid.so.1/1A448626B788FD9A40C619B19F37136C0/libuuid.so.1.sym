MODULE Linux arm64 1A448626B788FD9A40C619B19F37136C0 libuuid.so.1
INFO CODE_ID 2686441A88B79AFD40C619B19F37136CA767A6D1
PUBLIC 41d0 0 uuid_clear
PUBLIC 41f0 0 uuid_compare
PUBLIC 42c0 0 uuid_copy
PUBLIC 42f0 0 __uuid_generate_time
PUBLIC 4884 0 __uuid_generate_time_cont
PUBLIC 48a0 0 uuid_generate_time
PUBLIC 48c0 0 uuid_generate_time_safe
PUBLIC 48e0 0 __uuid_generate_random
PUBLIC 4a00 0 uuid_generate_random
PUBLIC 4a64 0 uuid_generate
PUBLIC 4ae4 0 uuid_generate_md5
PUBLIC 4c50 0 uuid_generate_sha1
PUBLIC 4e14 0 uuid_is_null
PUBLIC 4e94 0 uuid_parse_range
PUBLIC 5060 0 uuid_parse
PUBLIC 50b4 0 uuid_unparse_lower
PUBLIC 50d4 0 uuid_unparse_upper
PUBLIC 5100 0 uuid_unparse
PUBLIC 5120 0 uuid_time
PUBLIC 51f0 0 uuid_type
PUBLIC 5254 0 uuid_variant
PUBLIC 52d0 0 uuid_get_template
STACK CFI INIT 16e0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1710 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1750 48 .cfa: sp 0 + .ra: x30
STACK CFI 1754 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 175c x19: .cfa -16 + ^
STACK CFI 1794 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 17a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17b0 70 .cfa: sp 0 + .ra: x30
STACK CFI 17bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 17ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 17f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1820 9f0 .cfa: sp 0 + .ra: x30
STACK CFI 1828 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1844 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1854 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1890 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 18a4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1998 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 21a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 2210 58 .cfa: sp 0 + .ra: x30
STACK CFI 2218 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2224 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2270 58 .cfa: sp 0 + .ra: x30
STACK CFI 2278 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2284 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 22d0 11c .cfa: sp 0 + .ra: x30
STACK CFI 22d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 22e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 22e8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 22f4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 23c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 23c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 23dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 23e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 23f0 100 .cfa: sp 0 + .ra: x30
STACK CFI 23f8 .cfa: sp 64 +
STACK CFI 2408 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2410 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 24e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 24ec .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 24f0 278 .cfa: sp 0 + .ra: x30
STACK CFI 24f8 .cfa: sp 112 +
STACK CFI 2504 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 250c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2518 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2520 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2528 x25: .cfa -16 + ^
STACK CFI 2658 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2660 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2770 534 .cfa: sp 0 + .ra: x30
STACK CFI 2778 .cfa: sp 128 +
STACK CFI 2784 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 278c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2794 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 27b8 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2a8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2a94 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2ca4 103c .cfa: sp 0 + .ra: x30
STACK CFI 2cac .cfa: sp 176 +
STACK CFI 2cbc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2cc4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2cdc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3cdc .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3ce0 100 .cfa: sp 0 + .ra: x30
STACK CFI 3ce8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3cf4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3d00 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3d0c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3d5c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 3d64 x25: .cfa -16 + ^
STACK CFI 3dc0 x25: x25
STACK CFI 3dcc x25: .cfa -16 + ^
STACK CFI 3dd8 x25: x25
STACK CFI INIT 3de0 1bc .cfa: sp 0 + .ra: x30
STACK CFI 3de8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3df0 .cfa: sp 1136 +
STACK CFI 3e28 x19: .cfa -32 + ^
STACK CFI 3e34 x20: .cfa -24 + ^
STACK CFI 3e38 x21: .cfa -16 + ^
STACK CFI 3e44 x22: .cfa -8 + ^
STACK CFI 3f00 x19: x19
STACK CFI 3f08 x20: x20
STACK CFI 3f0c x21: x21
STACK CFI 3f10 x22: x22
STACK CFI 3f30 .cfa: sp 48 +
STACK CFI 3f34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3f3c .cfa: sp 1136 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3f54 x19: x19
STACK CFI 3f5c x20: x20
STACK CFI 3f60 x21: x21
STACK CFI 3f64 x22: x22
STACK CFI 3f68 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3f74 x19: x19
STACK CFI 3f78 x20: x20
STACK CFI 3f7c x21: x21
STACK CFI 3f80 x22: x22
STACK CFI 3f8c x19: .cfa -32 + ^
STACK CFI 3f90 x20: .cfa -24 + ^
STACK CFI 3f94 x21: .cfa -16 + ^
STACK CFI 3f98 x22: .cfa -8 + ^
STACK CFI INIT 3fa0 230 .cfa: sp 0 + .ra: x30
STACK CFI 3fa8 .cfa: sp 128 +
STACK CFI 3fb4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3fbc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3fc4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3fd0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3ff8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4090 x23: x23 x24: x24
STACK CFI 4128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 4130 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 414c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 41a0 x23: x23 x24: x24
STACK CFI 41cc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 41d0 1c .cfa: sp 0 + .ra: x30
STACK CFI 41d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41f0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 41f8 .cfa: sp 64 +
STACK CFI 420c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4278 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4280 .cfa: sp 64 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 42c0 30 .cfa: sp 0 + .ra: x30
STACK CFI 42cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 42e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 42f0 1c .cfa: sp 0 + .ra: x30
STACK CFI 42f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4304 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4310 574 .cfa: sp 0 + .ra: x30
STACK CFI 4318 .cfa: sp 320 +
STACK CFI 431c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4324 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 432c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 440c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 443c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4448 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4508 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4614 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 461c .cfa: sp 320 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4624 x23: x23 x24: x24
STACK CFI 4628 x25: x25 x26: x26
STACK CFI 462c x27: x27 x28: x28
STACK CFI 469c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 46a4 .cfa: sp 320 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 47dc x23: x23 x24: x24
STACK CFI 47e4 x25: x25 x26: x26
STACK CFI 47e8 x27: x27 x28: x28
STACK CFI 47f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4820 x23: x23 x24: x24
STACK CFI 4824 x25: x25 x26: x26
STACK CFI 4828 x27: x27 x28: x28
STACK CFI 485c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4860 x25: x25 x26: x26
STACK CFI 4864 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4868 x23: x23 x24: x24
STACK CFI 486c x25: x25 x26: x26
STACK CFI 4870 x27: x27 x28: x28
STACK CFI 4878 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 487c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4880 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 4884 18 .cfa: sp 0 + .ra: x30
STACK CFI 488c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4894 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 48a0 18 .cfa: sp 0 + .ra: x30
STACK CFI 48a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 48b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 48c0 18 .cfa: sp 0 + .ra: x30
STACK CFI 48c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 48d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 48e0 11c .cfa: sp 0 + .ra: x30
STACK CFI 48e8 .cfa: sp 128 +
STACK CFI 48f4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 48fc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4910 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4928 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 492c x25: .cfa -16 + ^
STACK CFI 49a0 x21: x21 x22: x22
STACK CFI 49a4 x25: x25
STACK CFI 49d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 49dc .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 49e8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 49ec x25: .cfa -16 + ^
STACK CFI 49f0 x21: x21 x22: x22 x25: x25
STACK CFI 49f4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 49f8 x25: .cfa -16 + ^
STACK CFI INIT 4a00 64 .cfa: sp 0 + .ra: x30
STACK CFI 4a08 .cfa: sp 32 +
STACK CFI 4a18 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4a60 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4a64 80 .cfa: sp 0 + .ra: x30
STACK CFI 4a6c .cfa: sp 48 +
STACK CFI 4a7c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a84 x19: .cfa -16 + ^
STACK CFI 4acc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4ad4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4ae4 164 .cfa: sp 0 + .ra: x30
STACK CFI 4aec .cfa: sp 176 +
STACK CFI 4af4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4b00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4b10 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4c28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4c30 .cfa: sp 176 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4c50 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 4c58 .cfa: sp 224 +
STACK CFI 4c64 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c80 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4e08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4e10 .cfa: sp 224 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4e14 80 .cfa: sp 0 + .ra: x30
STACK CFI 4e1c .cfa: sp 48 +
STACK CFI 4e28 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4e70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4e78 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4e94 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 4e9c .cfa: sp 96 +
STACK CFI 4eac .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4eb4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4ed0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4edc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4f28 x21: x21 x22: x22
STACK CFI 4f2c x23: x23 x24: x24
STACK CFI 4f5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f64 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5048 x21: x21 x22: x22
STACK CFI 504c x23: x23 x24: x24
STACK CFI 5054 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5058 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 5060 54 .cfa: sp 0 + .ra: x30
STACK CFI 5068 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5070 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5098 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 50a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 50ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 50b4 20 .cfa: sp 0 + .ra: x30
STACK CFI 50bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 50c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 50d4 24 .cfa: sp 0 + .ra: x30
STACK CFI 50dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 50e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5100 20 .cfa: sp 0 + .ra: x30
STACK CFI 5108 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5114 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5120 c8 .cfa: sp 0 + .ra: x30
STACK CFI 5128 .cfa: sp 48 +
STACK CFI 513c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 51dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 51e4 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 51f0 64 .cfa: sp 0 + .ra: x30
STACK CFI 51f8 .cfa: sp 48 +
STACK CFI 5208 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5248 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5250 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5254 78 .cfa: sp 0 + .ra: x30
STACK CFI 525c .cfa: sp 48 +
STACK CFI 526c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 52c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 52c8 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 52d0 124 .cfa: sp 0 + .ra: x30
STACK CFI 52e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 52e8 x19: .cfa -16 + ^
STACK CFI 536c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5374 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5388 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5390 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 53a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 53ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 53c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 53d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 53e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
