MODULE Linux arm64 64F61E82DA8DFE69F704CF94AF06C0B80 libcli-spoolss-samba4.so.0
INFO CODE_ID 821EF6648DDA69FEF704CF94AF06C0B85A19AF0A
PUBLIC 22f0 0 rpccli_spoolss_getprinterdriver
PUBLIC 2450 0 rpccli_spoolss_getprinterdriver2
PUBLIC 25d4 0 rpccli_spoolss_getprinter
PUBLIC 2730 0 rpccli_spoolss_getjob
PUBLIC 2890 0 rpccli_spoolss_enumforms
PUBLIC 2a00 0 rpccli_spoolss_enumprintprocessors
PUBLIC 2b74 0 rpccli_spoolss_enumprintprocessordatatypes
PUBLIC 2cf0 0 rpccli_spoolss_enumports
PUBLIC 2e60 0 rpccli_spoolss_enummonitors
PUBLIC 2fd0 0 rpccli_spoolss_enumjobs
PUBLIC 3154 0 rpccli_spoolss_enumprinterdrivers
PUBLIC 32d0 0 rpccli_spoolss_enumprinters
PUBLIC 3444 0 rpccli_spoolss_getprinterdata
PUBLIC 35c0 0 rpccli_spoolss_enumprinterkey
PUBLIC 36d0 0 rpccli_spoolss_enumprinterdataex
PUBLIC 37c0 0 init_systemtime
PUBLIC 3840 0 spoolss_Time_to_time_t
PUBLIC 38f0 0 spoolss_timestr_to_NTTIME
PUBLIC 39f0 0 spoolss_driver_version_to_qword
PUBLIC 3af0 0 pull_spoolss_PrinterData
PUBLIC 3b30 0 push_spoolss_PrinterData
PUBLIC 3b80 0 spoolss_printerinfo2_to_setprinterinfo2
PUBLIC 3bf0 0 driver_info_ctr_to_info8
PUBLIC 3e20 0 spoolss_create_default_devmode
PUBLIC 3f40 0 spoolss_create_default_secdesc
PUBLIC 4210 0 spoolss_get_short_filesys_environment
PUBLIC 4280 0 spoolss_init_spoolss_UserLevel1
PUBLIC 4390 0 rpccli_spoolss_openprinter_ex
PUBLIC 44a0 0 rpccli_spoolss_addprinterex
STACK CFI INIT 2220 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2250 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2290 48 .cfa: sp 0 + .ra: x30
STACK CFI 2294 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 229c x19: .cfa -16 + ^
STACK CFI 22d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 22e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22f0 160 .cfa: sp 0 + .ra: x30
STACK CFI 22f8 .cfa: sp 144 +
STACK CFI 2304 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 230c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2318 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2324 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 233c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2360 x27: .cfa -16 + ^
STACK CFI 2364 x27: x27
STACK CFI 236c x27: .cfa -16 + ^
STACK CFI 23ac x27: x27
STACK CFI 23e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 23ec .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 23f0 x27: x27
STACK CFI 23f8 x27: .cfa -16 + ^
STACK CFI 2444 x27: x27
STACK CFI 244c x27: .cfa -16 + ^
STACK CFI INIT 2450 184 .cfa: sp 0 + .ra: x30
STACK CFI 2458 .cfa: sp 208 +
STACK CFI 2464 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 246c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2478 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 249c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 24d0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 24d4 x27: x27 x28: x28
STACK CFI 24dc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2524 x27: x27 x28: x28
STACK CFI 255c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2564 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2568 x27: x27 x28: x28
STACK CFI 2570 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 25c8 x27: x27 x28: x28
STACK CFI 25d0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 25d4 154 .cfa: sp 0 + .ra: x30
STACK CFI 25dc .cfa: sp 128 +
STACK CFI 25e8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 25f0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 25fc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2608 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 263c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2640 x25: x25 x26: x26
STACK CFI 2648 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2688 x25: x25 x26: x26
STACK CFI 26bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 26c4 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 26c8 x25: x25 x26: x26
STACK CFI 26d0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 271c x25: x25 x26: x26
STACK CFI 2724 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 2730 160 .cfa: sp 0 + .ra: x30
STACK CFI 2738 .cfa: sp 144 +
STACK CFI 2744 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 274c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2758 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2764 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 277c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 27a0 x27: .cfa -16 + ^
STACK CFI 27a4 x27: x27
STACK CFI 27ac x27: .cfa -16 + ^
STACK CFI 27ec x27: x27
STACK CFI 2824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 282c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 2830 x27: x27
STACK CFI 2838 x27: .cfa -16 + ^
STACK CFI 2884 x27: x27
STACK CFI 288c x27: .cfa -16 + ^
STACK CFI INIT 2890 168 .cfa: sp 0 + .ra: x30
STACK CFI 2898 .cfa: sp 144 +
STACK CFI 28a4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 28ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 28b8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 28c4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 28dc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2900 x27: .cfa -16 + ^
STACK CFI 2904 x27: x27
STACK CFI 290c x27: .cfa -16 + ^
STACK CFI 294c x27: x27
STACK CFI 2984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 298c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 2990 x27: x27
STACK CFI 2998 x27: .cfa -16 + ^
STACK CFI 29ec x27: x27
STACK CFI 29f4 x27: .cfa -16 + ^
STACK CFI INIT 2a00 174 .cfa: sp 0 + .ra: x30
STACK CFI 2a08 .cfa: sp 160 +
STACK CFI 2a14 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2a1c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2a28 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2a34 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2a40 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2a74 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2a78 x27: x27 x28: x28
STACK CFI 2a80 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2ac4 x27: x27 x28: x28
STACK CFI 2afc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2b04 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2b08 x27: x27 x28: x28
STACK CFI 2b10 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2b68 x27: x27 x28: x28
STACK CFI 2b70 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 2b74 174 .cfa: sp 0 + .ra: x30
STACK CFI 2b7c .cfa: sp 160 +
STACK CFI 2b88 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2b90 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2b9c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2ba8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2bb4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2be8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2bec x27: x27 x28: x28
STACK CFI 2bf4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2c38 x27: x27 x28: x28
STACK CFI 2c70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2c78 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2c7c x27: x27 x28: x28
STACK CFI 2c84 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2cdc x27: x27 x28: x28
STACK CFI 2ce4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 2cf0 168 .cfa: sp 0 + .ra: x30
STACK CFI 2cf8 .cfa: sp 144 +
STACK CFI 2d04 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2d0c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2d18 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2d24 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2d3c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2d60 x27: .cfa -16 + ^
STACK CFI 2d64 x27: x27
STACK CFI 2d6c x27: .cfa -16 + ^
STACK CFI 2dac x27: x27
STACK CFI 2de4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2dec .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 2df0 x27: x27
STACK CFI 2df8 x27: .cfa -16 + ^
STACK CFI 2e4c x27: x27
STACK CFI 2e54 x27: .cfa -16 + ^
STACK CFI INIT 2e60 168 .cfa: sp 0 + .ra: x30
STACK CFI 2e68 .cfa: sp 144 +
STACK CFI 2e74 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2e7c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2e88 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2e94 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2eac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2ed0 x27: .cfa -16 + ^
STACK CFI 2ed4 x27: x27
STACK CFI 2edc x27: .cfa -16 + ^
STACK CFI 2f1c x27: x27
STACK CFI 2f54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2f5c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 2f60 x27: x27
STACK CFI 2f68 x27: .cfa -16 + ^
STACK CFI 2fbc x27: x27
STACK CFI 2fc4 x27: .cfa -16 + ^
STACK CFI INIT 2fd0 184 .cfa: sp 0 + .ra: x30
STACK CFI 2fd8 .cfa: sp 176 +
STACK CFI 2fe4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2fec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2ffc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3008 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3020 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3050 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3054 x27: x27 x28: x28
STACK CFI 305c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 30a0 x27: x27 x28: x28
STACK CFI 30d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 30e0 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 30e4 x27: x27 x28: x28
STACK CFI 30ec x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3148 x27: x27 x28: x28
STACK CFI 3150 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 3154 174 .cfa: sp 0 + .ra: x30
STACK CFI 315c .cfa: sp 160 +
STACK CFI 3168 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3170 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 317c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3188 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3194 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 31c8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 31cc x27: x27 x28: x28
STACK CFI 31d4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3218 x27: x27 x28: x28
STACK CFI 3250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3258 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 325c x27: x27 x28: x28
STACK CFI 3264 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 32bc x27: x27 x28: x28
STACK CFI 32c4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 32d0 174 .cfa: sp 0 + .ra: x30
STACK CFI 32d8 .cfa: sp 160 +
STACK CFI 32e4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 32ec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 32f8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3304 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3310 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3344 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3348 x27: x27 x28: x28
STACK CFI 3350 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3394 x27: x27 x28: x28
STACK CFI 33cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 33d4 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 33d8 x27: x27 x28: x28
STACK CFI 33e0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3438 x27: x27 x28: x28
STACK CFI 3440 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 3444 178 .cfa: sp 0 + .ra: x30
STACK CFI 344c .cfa: sp 160 +
STACK CFI 3458 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3460 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3470 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 347c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3488 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3490 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 354c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3554 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 35c0 10c .cfa: sp 0 + .ra: x30
STACK CFI 35c8 .cfa: sp 144 +
STACK CFI 35d4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 35dc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 35e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 35f4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3604 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 360c x27: .cfa -16 + ^
STACK CFI 368c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 3694 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 36d0 ec .cfa: sp 0 + .ra: x30
STACK CFI 36d8 .cfa: sp 112 +
STACK CFI 36e4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 36ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 36f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3704 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 370c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 377c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3784 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 37c0 80 .cfa: sp 0 + .ra: x30
STACK CFI 37c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3828 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3830 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3834 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3840 b0 .cfa: sp 0 + .ra: x30
STACK CFI 3848 .cfa: sp 80 +
STACK CFI 385c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 38e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 38ec .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 38f0 fc .cfa: sp 0 + .ra: x30
STACK CFI 38f8 .cfa: sp 112 +
STACK CFI 3904 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 390c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3914 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 39ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 39b4 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 39f0 fc .cfa: sp 0 + .ra: x30
STACK CFI 39f8 .cfa: sp 96 +
STACK CFI 3a04 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3a0c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3a18 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3a24 x23: .cfa -16 + ^
STACK CFI 3ab4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3abc .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3af0 40 .cfa: sp 0 + .ra: x30
STACK CFI 3b08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3b30 4c .cfa: sp 0 + .ra: x30
STACK CFI 3b4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3b80 68 .cfa: sp 0 + .ra: x30
STACK CFI 3b88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3be0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3bf0 22c .cfa: sp 0 + .ra: x30
STACK CFI 3bf8 .cfa: sp 256 +
STACK CFI 3c04 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c18 x21: .cfa -16 + ^
STACK CFI 3d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3d0c .cfa: sp 256 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3e20 118 .cfa: sp 0 + .ra: x30
STACK CFI 3e28 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e30 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e3c x21: .cfa -16 + ^
STACK CFI 3f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3f20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3f30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3f40 2cc .cfa: sp 0 + .ra: x30
STACK CFI 3f48 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3f64 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3f7c .cfa: sp 992 + x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 411c .cfa: sp 80 +
STACK CFI 4130 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4138 .cfa: sp 992 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4210 6c .cfa: sp 0 + .ra: x30
STACK CFI 4218 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4228 x19: .cfa -16 + ^
STACK CFI 4240 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 424c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4274 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4280 108 .cfa: sp 0 + .ra: x30
STACK CFI 4288 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4290 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 429c x21: .cfa -16 + ^
STACK CFI 4368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4370 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4380 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4390 10c .cfa: sp 0 + .ra: x30
STACK CFI 4398 .cfa: sp 176 +
STACK CFI 43a4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 43b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 43b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 43c4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 444c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4454 .cfa: sp 176 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 44a0 128 .cfa: sp 0 + .ra: x30
STACK CFI 44a8 .cfa: sp 224 +
STACK CFI 44ac .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 44b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 44c8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 44d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 44e0 x25: .cfa -16 + ^
STACK CFI 4578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4580 .cfa: sp 224 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
