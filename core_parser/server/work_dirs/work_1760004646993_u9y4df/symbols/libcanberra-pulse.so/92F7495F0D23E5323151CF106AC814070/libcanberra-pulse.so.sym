MODULE Linux arm64 92F7495F0D23E5323151CF106AC814070 libcanberra-pulse.so
INFO CODE_ID 5F49F792230D32E53151CF106AC814077E31DEF9
PUBLIC 38a0 0 pulse_driver_destroy
PUBLIC 3a84 0 pulse_driver_open
PUBLIC 3d20 0 pulse_driver_change_device
PUBLIC 3df0 0 pulse_driver_change_props
PUBLIC 4090 0 pulse_driver_cancel
PUBLIC 4310 0 pulse_driver_cache
PUBLIC 4794 0 pulse_driver_play
PUBLIC 5080 0 driver_playing
STACK CFI INIT 20c0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20f0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2130 48 .cfa: sp 0 + .ra: x30
STACK CFI 2134 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 213c x19: .cfa -16 + ^
STACK CFI 2174 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2180 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2190 7c .cfa: sp 0 + .ra: x30
STACK CFI 21cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2204 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2210 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2218 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2220 x19: .cfa -16 + ^
STACK CFI 2288 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2290 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 22d0 7c .cfa: sp 0 + .ra: x30
STACK CFI 22d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 22e0 x19: .cfa -16 + ^
STACK CFI 2304 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 230c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2350 254 .cfa: sp 0 + .ra: x30
STACK CFI 2358 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2360 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 237c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2384 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2388 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 23a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2478 x19: x19 x20: x20
STACK CFI 2480 x23: x23 x24: x24
STACK CFI 2484 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 248c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 24bc x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 24f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 24fc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2504 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 2540 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2544 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 258c x19: x19 x20: x20
STACK CFI 2598 x23: x23 x24: x24
STACK CFI 259c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 25a4 100 .cfa: sp 0 + .ra: x30
STACK CFI 25ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 25b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 25bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2618 x19: x19 x20: x20
STACK CFI 261c x21: x21 x22: x22
STACK CFI 2624 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2634 x19: x19 x20: x20
STACK CFI 263c x21: x21 x22: x22
STACK CFI 2640 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2648 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2694 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2698 x19: x19 x20: x20
STACK CFI 26a0 x21: x21 x22: x22
STACK CFI INIT 26a4 12c .cfa: sp 0 + .ra: x30
STACK CFI 26ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 26b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 26f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 26f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 274c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2754 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 27bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 27c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 27d0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 27d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 281c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2838 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2840 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 28c0 234 .cfa: sp 0 + .ra: x30
STACK CFI 28c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 28d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2978 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2980 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 29bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 29c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2af4 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 2afc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b08 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2b6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2b74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2b94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2b9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2ce4 40c .cfa: sp 0 + .ra: x30
STACK CFI 2cec .cfa: sp 96 +
STACK CFI 2cf8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2d0c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2ea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2eb0 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 2f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2f58 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 30f0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 30f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3104 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 3178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3180 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 31c0 fc .cfa: sp 0 + .ra: x30
STACK CFI 31c8 .cfa: sp 64 +
STACK CFI 31d4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 31dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31e4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3268 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 32c0 364 .cfa: sp 0 + .ra: x30
STACK CFI 32c8 .cfa: sp 80 +
STACK CFI 32d4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 32dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 32f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3338 x23: .cfa -16 + ^
STACK CFI 33c4 x23: x23
STACK CFI 33f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 33fc .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3428 x23: x23
STACK CFI 3430 x23: .cfa -16 + ^
STACK CFI 348c x23: x23
STACK CFI 35cc x23: .cfa -16 + ^
STACK CFI 35fc x23: x23
STACK CFI 3600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3608 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3618 x23: x23
STACK CFI 3620 x23: .cfa -16 + ^
STACK CFI INIT 3624 274 .cfa: sp 0 + .ra: x30
STACK CFI 362c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3638 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 363c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 367c x19: x19 x20: x20
STACK CFI 3680 x21: x21 x22: x22
STACK CFI 3684 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 368c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3690 x23: .cfa -16 + ^
STACK CFI 3784 x23: x23
STACK CFI 3788 x23: .cfa -16 + ^
STACK CFI 37a4 x19: x19 x20: x20
STACK CFI 37a8 x21: x21 x22: x22
STACK CFI 37ac x23: x23
STACK CFI 37b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 37b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 37f8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 3834 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3838 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 383c x23: .cfa -16 + ^
STACK CFI 3844 x23: x23
STACK CFI 3880 x23: .cfa -16 + ^
STACK CFI 3894 x23: x23
STACK CFI INIT 38a0 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 38a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 38b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 38bc x21: .cfa -16 + ^
STACK CFI 3920 x19: x19 x20: x20
STACK CFI 3928 x21: x21
STACK CFI 3930 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3938 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 39d8 x19: x19 x20: x20 x21: x21
STACK CFI 39e8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 39f4 x19: x19 x20: x20
STACK CFI 39fc x21: x21
STACK CFI 3a44 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 3a84 294 .cfa: sp 0 + .ra: x30
STACK CFI 3a8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3a94 x21: .cfa -16 + ^
STACK CFI 3a9c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3ba4 x19: x19 x20: x20
STACK CFI 3bb0 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 3bb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3bc8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3be4 x19: x19 x20: x20
STACK CFI 3bec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3bf8 x19: x19 x20: x20
STACK CFI 3c00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c18 x19: x19 x20: x20
STACK CFI 3c1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c28 x19: x19 x20: x20
STACK CFI 3c70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3cbc x19: x19 x20: x20
STACK CFI 3cc4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 3d20 d0 .cfa: sp 0 + .ra: x30
STACK CFI 3d28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3d48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3df0 2a0 .cfa: sp 0 + .ra: x30
STACK CFI 3df8 .cfa: sp 64 +
STACK CFI 3e04 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e18 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e5c x21: .cfa -16 + ^
STACK CFI 3ea4 x21: x21
STACK CFI 3ed0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ed8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3eec x21: x21
STACK CFI 408c x21: .cfa -16 + ^
STACK CFI INIT 4090 280 .cfa: sp 0 + .ra: x30
STACK CFI 4098 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 40a0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 40a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 40d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 418c x19: x19 x20: x20
STACK CFI 4190 x21: x21 x22: x22
STACK CFI 419c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 41a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 41d4 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 41e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 41f0 x21: x21 x22: x22
STACK CFI 41f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4238 x19: x19 x20: x20
STACK CFI 4280 x21: x21 x22: x22
STACK CFI 42c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 4310 484 .cfa: sp 0 + .ra: x30
STACK CFI 4318 .cfa: sp 256 +
STACK CFI 4328 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4344 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 434c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 435c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4410 x19: x19 x20: x20
STACK CFI 4414 x23: x23 x24: x24
STACK CFI 4440 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4448 .cfa: sp 256 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 444c x19: x19 x20: x20
STACK CFI 4450 x23: x23 x24: x24
STACK CFI 4454 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 45f8 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 4608 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4614 x23: x23 x24: x24
STACK CFI 461c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4628 x19: x19 x20: x20
STACK CFI 4630 x23: x23 x24: x24
STACK CFI 4634 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 467c x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 46c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 46d4 x19: x19 x20: x20
STACK CFI 4718 x23: x23 x24: x24
STACK CFI 471c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4778 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 477c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4780 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4788 x19: x19 x20: x20
STACK CFI 4790 x23: x23 x24: x24
STACK CFI INIT 4794 8e8 .cfa: sp 0 + .ra: x30
STACK CFI 479c .cfa: sp 464 +
STACK CFI 47a8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 47c4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 47d4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 47f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4808 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4878 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4a84 x27: x27 x28: x28
STACK CFI 4aac x19: x19 x20: x20
STACK CFI 4ab0 x23: x23 x24: x24
STACK CFI 4ab4 x25: x25 x26: x26
STACK CFI 4ae0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4ae8 .cfa: sp 464 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4aec x27: x27 x28: x28
STACK CFI 4af4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4b00 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4b5c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4b68 x23: x23 x24: x24
STACK CFI 4b70 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4b7c x19: x19 x20: x20
STACK CFI 4b84 x23: x23 x24: x24
STACK CFI 4b88 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4bd0 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 4c14 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4c54 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4df0 x27: x27 x28: x28
STACK CFI 4df4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4e28 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4e6c x23: x23 x24: x24
STACK CFI 4e70 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4e84 x27: x27 x28: x28
STACK CFI 4e8c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4e90 x27: x27 x28: x28
STACK CFI 4e98 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4f68 x27: x27 x28: x28
STACK CFI 4f6c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4fa8 x27: x27 x28: x28
STACK CFI 4fb0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4ffc x27: x27 x28: x28
STACK CFI 5000 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5048 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 504c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5050 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5054 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5058 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 505c x27: x27 x28: x28
STACK CFI 5068 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5074 x27: x27 x28: x28
STACK CFI INIT 5080 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 5088 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5094 x21: .cfa -16 + ^
STACK CFI 50a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 50f8 x19: x19 x20: x20
STACK CFI 5100 x21: x21
STACK CFI 5104 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 510c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5118 x19: x19 x20: x20 x21: x21
STACK CFI 5128 x21: .cfa -16 + ^
STACK CFI 5134 x21: x21
STACK CFI 513c x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 5148 x19: x19 x20: x20
STACK CFI 5150 x21: x21
STACK CFI 5198 x21: .cfa -16 + ^
STACK CFI 51d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5218 x19: x19 x20: x20
STACK CFI 5220 x21: x21
