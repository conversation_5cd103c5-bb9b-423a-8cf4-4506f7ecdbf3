MODULE Linux arm64 41EFB6BB67F326D6F1241F07F21ACE380 libpipewire-module-roc-source.so
INFO CODE_ID BBB6EF41F367D626F1241F07F21ACE38DB39DAC6
PUBLIC 53b0 0 pipewire__module_init
STACK CFI INIT 16d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1700 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1740 48 .cfa: sp 0 + .ra: x30
STACK CFI 1744 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 174c x19: .cfa -16 + ^
STACK CFI 1784 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1790 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 17a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17b0 x19: .cfa -16 + ^
STACK CFI 17e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 17f0 2c8c .cfa: sp 0 + .ra: x30
STACK CFI 17f8 .cfa: sp 480 +
STACK CFI 180c .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 1824 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 182c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 1844 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 1ff0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1ff8 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 4480 54 .cfa: sp 0 + .ra: x30
STACK CFI 4488 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4490 x19: .cfa -16 + ^
STACK CFI 44cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 44d4 110 .cfa: sp 0 + .ra: x30
STACK CFI 44f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44fc x19: .cfa -16 + ^
STACK CFI 4528 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4530 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4548 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 45b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 45e4 220 .cfa: sp 0 + .ra: x30
STACK CFI 45ec .cfa: sp 80 +
STACK CFI 45f8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4600 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4620 x21: .cfa -16 + ^
STACK CFI 46b8 x21: x21
STACK CFI 46e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 46e8 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4738 x21: x21
STACK CFI 478c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 47b4 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 47cc x21: .cfa -16 + ^
STACK CFI 47fc x21: x21
STACK CFI 4800 x21: .cfa -16 + ^
STACK CFI INIT 4804 104 .cfa: sp 0 + .ra: x30
STACK CFI 480c .cfa: sp 96 +
STACK CFI 4810 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4818 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4824 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4860 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4868 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4910 ac .cfa: sp 0 + .ra: x30
STACK CFI 4918 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4920 x19: .cfa -16 + ^
STACK CFI 49ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 49b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 49c0 50 .cfa: sp 0 + .ra: x30
STACK CFI 49c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 49d0 x19: .cfa -16 + ^
STACK CFI 4a08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4a10 998 .cfa: sp 0 + .ra: x30
STACK CFI 4a18 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4a24 .cfa: sp 1552 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4b00 x21: .cfa -48 + ^
STACK CFI 4b04 x22: .cfa -40 + ^
STACK CFI 4b08 x23: .cfa -32 + ^
STACK CFI 4b0c x24: .cfa -24 + ^
STACK CFI 4db4 x21: x21
STACK CFI 4dbc x22: x22
STACK CFI 4dc4 x23: x23
STACK CFI 4dc8 x24: x24
STACK CFI 4de8 .cfa: sp 80 +
STACK CFI 4df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4dfc .cfa: sp 1552 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4e24 x21: x21
STACK CFI 4e2c x22: x22
STACK CFI 4e30 x23: x23
STACK CFI 4e34 x24: x24
STACK CFI 4e38 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4f34 x25: .cfa -16 + ^
STACK CFI 4fa0 x25: x25
STACK CFI 4fd4 x21: x21
STACK CFI 4fd8 x22: x22
STACK CFI 4fdc x23: x23
STACK CFI 4fe0 x24: x24
STACK CFI 4fe4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5038 x21: x21
STACK CFI 503c x22: x22
STACK CFI 5040 x23: x23
STACK CFI 5044 x24: x24
STACK CFI 5048 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 50c8 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5128 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 51a0 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 51bc x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 51c4 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5234 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5260 x21: x21
STACK CFI 5268 x22: x22
STACK CFI 526c x23: x23
STACK CFI 5270 x24: x24
STACK CFI 5274 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 52e0 x21: x21
STACK CFI 52e4 x22: x22
STACK CFI 52e8 x23: x23
STACK CFI 52ec x24: x24
STACK CFI 52f0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5390 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5394 x21: .cfa -48 + ^
STACK CFI 5398 x22: .cfa -40 + ^
STACK CFI 539c x23: .cfa -32 + ^
STACK CFI 53a0 x24: .cfa -24 + ^
STACK CFI 53a4 x25: .cfa -16 + ^
STACK CFI INIT 53b0 8f0 .cfa: sp 0 + .ra: x30
STACK CFI 53b8 .cfa: sp 96 +
STACK CFI 53c4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 53cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 53d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 53dc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 57e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 57f0 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
