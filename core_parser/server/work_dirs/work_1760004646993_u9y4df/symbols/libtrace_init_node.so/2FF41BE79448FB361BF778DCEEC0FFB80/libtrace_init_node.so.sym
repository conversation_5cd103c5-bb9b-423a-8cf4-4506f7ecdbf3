MODULE Linux arm64 2FF41BE79448FB361BF778DCEEC0FFB80 libtrace_init_node.so
INFO CODE_ID E71BF42F489436FB1BF778DCEEC0FFB8
PUBLIC 2358 0 _init
PUBLIC 2580 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&) [clone .constprop.0]
PUBLIC 2690 0 _GLOBAL__sub_I_trace_init_node.cpp
PUBLIC 2898 0 call_weak_fn
PUBLIC 28b0 0 deregister_tm_clones
PUBLIC 28e0 0 register_tm_clones
PUBLIC 2920 0 __do_global_dtors_aux
PUBLIC 2970 0 frame_dummy
PUBLIC 2980 0 lios::tracing::TraceInitNode::Exit()
PUBLIC 2990 0 lios::tracing::TraceInitNode::Init(int, char**)
PUBLIC 2a70 0 lios_class_loader_destroy_TraceInitNode
PUBLIC 2b00 0 lios_class_loader_create_TraceInitNode
PUBLIC 2de0 0 std::_Rb_tree<std::type_index, std::pair<std::type_index const, std::type_index>, std::_Select1st<std::pair<std::type_index const, std::type_index> >, std::less<std::type_index>, std::allocator<std::pair<std::type_index const, std::type_index> > >::_M_erase(std::_Rb_tree_node<std::pair<std::type_index const, std::type_index> >*) [clone .isra.0]
PUBLIC 2f60 0 cereal::detail::PolymorphicCasters::~PolymorphicCasters()
PUBLIC 30c0 0 lios::tracing::TraceInitNode::~TraceInitNode()
PUBLIC 3500 0 lios::tracing::TraceInitNode::~TraceInitNode()
PUBLIC 3930 0 lios::config::settings::ParamConfig::~ParamConfig()
PUBLIC 3a20 0 lios::config::settings::NodeConfig::~NodeConfig()
PUBLIC 3e30 0 std::vector<lios::config::settings::NodeConfig::MessageTaskConfig, std::allocator<lios::config::settings::NodeConfig::MessageTaskConfig> >::~vector()
PUBLIC 3f60 0 std::vector<lios::config::settings::NodeConfig::TimerTaskConfig, std::allocator<lios::config::settings::NodeConfig::TimerTaskConfig> >::~vector()
PUBLIC 4090 0 std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::clear()
PUBLIC 4140 0 cereal::detail::StaticObject<cereal::detail::PolymorphicCasters>::create()
PUBLIC 41e0 0 _fini
STACK CFI INIT 28b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2920 48 .cfa: sp 0 + .ra: x30
STACK CFI 2924 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 292c x19: .cfa -16 + ^
STACK CFI 2964 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2970 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2980 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2990 d4 .cfa: sp 0 + .ra: x30
STACK CFI 2994 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 29a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 29ac x21: .cfa -48 + ^
STACK CFI 2a5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2a60 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2580 104 .cfa: sp 0 + .ra: x30
STACK CFI 2584 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2594 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 259c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 261c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2de0 180 .cfa: sp 0 + .ra: x30
STACK CFI 2de8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2df0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2df8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2e04 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2e28 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2e2c x27: .cfa -16 + ^
STACK CFI 2e80 x21: x21 x22: x22
STACK CFI 2e84 x27: x27
STACK CFI 2ea0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 2ebc x21: x21 x22: x22 x27: x27
STACK CFI 2ed8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 2ef4 x21: x21 x22: x22 x27: x27
STACK CFI 2f30 x25: x25 x26: x26
STACK CFI 2f58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 2f60 158 .cfa: sp 0 + .ra: x30
STACK CFI 2f64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2f6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2f78 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 30a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 30a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 30b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 30c0 434 .cfa: sp 0 + .ra: x30
STACK CFI 30c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 30d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 30e4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 34a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 34a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 34f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3500 42c .cfa: sp 0 + .ra: x30
STACK CFI 3504 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3518 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3524 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 38ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 38f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3930 e8 .cfa: sp 0 + .ra: x30
STACK CFI 3934 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3940 x21: .cfa -16 + ^
STACK CFI 394c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3a08 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3a14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3a20 404 .cfa: sp 0 + .ra: x30
STACK CFI 3a24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3a30 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3a3c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3dd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3e20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2a70 84 .cfa: sp 0 + .ra: x30
STACK CFI 2a78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a84 x19: .cfa -16 + ^
STACK CFI 2adc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2ae4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2af0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3e30 130 .cfa: sp 0 + .ra: x30
STACK CFI 3e34 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e3c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e44 x21: .cfa -16 + ^
STACK CFI 3f38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3f3c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3f5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3f60 130 .cfa: sp 0 + .ra: x30
STACK CFI 3f64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3f6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3f74 x21: .cfa -16 + ^
STACK CFI 4068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 406c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 408c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4090 ac .cfa: sp 0 + .ra: x30
STACK CFI 4094 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 409c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 40a4 x21: .cfa -16 + ^
STACK CFI 4138 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2b00 2dc .cfa: sp 0 + .ra: x30
STACK CFI 2b04 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2b28 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2d08 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 4140 a0 .cfa: sp 0 + .ra: x30
STACK CFI 4144 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 414c x19: .cfa -16 + ^
STACK CFI 416c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4170 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 41dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2690 208 .cfa: sp 0 + .ra: x30
STACK CFI 2694 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 26a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26b4 x21: .cfa -16 + ^
STACK CFI 2878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 287c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
