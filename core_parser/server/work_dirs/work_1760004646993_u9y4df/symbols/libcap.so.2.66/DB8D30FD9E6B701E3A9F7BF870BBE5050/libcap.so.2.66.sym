MODULE Linux arm64 DB8D30FD9E6B701E3A9F7BF870BBE5050 libcap.so.2
INFO CODE_ID FD308DDB6B9E1E703A9F7BF870BBE5059236F78F
PUBLIC 2e30 0 _libcap_initialize
PUBLIC 3610 0 cap_max_bits
PUBLIC 37a4 0 _libcap_strdup
PUBLIC 3890 0 cap_iab_init
PUBLIC 38c4 0 cap_iab_dup
PUBLIC 3960 0 cap_new_launcher
PUBLIC 39c0 0 cap_func_launcher
PUBLIC 3a10 0 cap_free
PUBLIC 3b04 0 cap_init
PUBLIC 3bc0 0 cap_dup
PUBLIC 3d30 0 psx_load_syscalls
PUBLIC 3d50 0 cap_set_syscall
PUBLIC 3d90 0 cap_get_proc
PUBLIC 3df0 0 cap_set_proc
PUBLIC 3e14 0 capgetp
PUBLIC 3ea0 0 cap_get_pid
PUBLIC 3f14 0 capsetp
PUBLIC 3fa4 0 cap_get_bound
PUBLIC 3fd0 0 cap_drop_bound
PUBLIC 4060 0 cap_get_ambient
PUBLIC 4150 0 cap_set_ambient
PUBLIC 4224 0 cap_reset_ambient
PUBLIC 4244 0 cap_get_secbits
PUBLIC 4270 0 cap_set_secbits
PUBLIC 4300 0 cap_prctl
PUBLIC 4320 0 cap_prctlw
PUBLIC 43a0 0 cap_launcher_callback
PUBLIC 4420 0 cap_launcher_setuid
PUBLIC 44a0 0 cap_launcher_setgroups
PUBLIC 4540 0 cap_launcher_set_mode
PUBLIC 45c0 0 cap_launcher_set_iab
PUBLIC 4680 0 cap_launcher_set_chroot
PUBLIC 4704 0 cap_size
PUBLIC 4770 0 cap_copy_ext
PUBLIC 4914 0 cap_copy_int
PUBLIC 4a54 0 cap_copy_int_check
PUBLIC 4ab0 0 cap_get_flag
PUBLIC 4b80 0 cap_set_flag
PUBLIC 4e64 0 cap_iab_set_proc
PUBLIC 4ee4 0 cap_clear
PUBLIC 4f64 0 cap_clear_flag
PUBLIC 51b0 0 cap_set_mode
PUBLIC 5340 0 cap_setuid
PUBLIC 54f0 0 cap_setgroups
PUBLIC 5520 0 cap_launch
PUBLIC 58f0 0 cap_compare
PUBLIC 59f4 0 cap_get_mode
PUBLIC 5b14 0 cap_fill_flag
PUBLIC 5c00 0 cap_fill
PUBLIC 5c20 0 cap_iab_get_vector
PUBLIC 5d30 0 cap_iab_set_vector
PUBLIC 5eb0 0 cap_iab_fill
PUBLIC 6040 0 cap_iab_get_proc
PUBLIC 6130 0 cap_iab_compare
PUBLIC 6220 0 cap_from_text
PUBLIC 6930 0 cap_from_name
PUBLIC 6974 0 cap_to_name
PUBLIC 6a60 0 cap_to_text
PUBLIC 71e0 0 cap_mode_name
PUBLIC 7270 0 cap_iab_to_text
PUBLIC 7540 0 cap_iab_from_text
PUBLIC 76d0 0 cap_proc_root
PUBLIC 7710 0 cap_iab_get_pid
PUBLIC 7930 0 cap_get_fd
PUBLIC 7a00 0 cap_get_file
PUBLIC 7ad0 0 cap_get_nsowner
PUBLIC 7b50 0 cap_set_fd
PUBLIC 7c50 0 cap_set_file
PUBLIC 7d50 0 cap_set_nsowner
PUBLIC 7dd0 0 __so_start
STACK CFI INIT 2f40 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f70 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fb0 48 .cfa: sp 0 + .ra: x30
STACK CFI 2fb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2fbc x19: .cfa -16 + ^
STACK CFI 2ff4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3000 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3010 1c .cfa: sp 0 + .ra: x30
STACK CFI 3018 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3024 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3030 58 .cfa: sp 0 + .ra: x30
STACK CFI 3048 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3058 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3090 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 3098 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 30a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 30b0 x21: .cfa -16 + ^
STACK CFI 3124 x19: x19 x20: x20
STACK CFI 3128 x21: x21
STACK CFI 3130 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3138 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 31cc x19: x19 x20: x20
STACK CFI 31d4 x21: x21
STACK CFI 31d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 31e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 323c x19: x19 x20: x20
STACK CFI 3240 x21: x21
STACK CFI INIT 3250 18 .cfa: sp 0 + .ra: x30
STACK CFI 3258 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3260 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3270 18 .cfa: sp 0 + .ra: x30
STACK CFI 3278 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3280 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3290 88 .cfa: sp 0 + .ra: x30
STACK CFI 32a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 32b0 x19: .cfa -16 + ^
STACK CFI 32c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3300 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3320 78 .cfa: sp 0 + .ra: x30
STACK CFI 3334 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3340 x19: .cfa -16 + ^
STACK CFI 335c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3380 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 33a0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 33a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3414 x19: x19 x20: x20
STACK CFI 3418 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3420 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3430 x19: x19 x20: x20
STACK CFI INIT 3450 1bc .cfa: sp 0 + .ra: x30
STACK CFI 3458 .cfa: sp 48 +
STACK CFI 345c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3464 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 35b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 35c0 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2e30 d0 .cfa: sp 0 + .ra: x30
STACK CFI 2e38 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2e44 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2e50 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2e98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2ea0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3610 20 .cfa: sp 0 + .ra: x30
STACK CFI 3618 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3624 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3630 174 .cfa: sp 0 + .ra: x30
STACK CFI 3638 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3640 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3650 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3674 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 367c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3680 x23: .cfa -16 + ^
STACK CFI 3798 x23: x23
STACK CFI 379c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 37a4 e8 .cfa: sp 0 + .ra: x30
STACK CFI 37ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 37b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 37d4 x21: .cfa -16 + ^
STACK CFI 3824 x19: x19 x20: x20
STACK CFI 382c x21: x21
STACK CFI 3830 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3838 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3840 x19: x19 x20: x20
STACK CFI 3850 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 385c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3870 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 3878 x19: x19 x20: x20
STACK CFI 3880 x21: x21
STACK CFI INIT 3890 34 .cfa: sp 0 + .ra: x30
STACK CFI 3898 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 38bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 38c4 94 .cfa: sp 0 + .ra: x30
STACK CFI 38cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 38d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 393c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3944 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3960 60 .cfa: sp 0 + .ra: x30
STACK CFI 3968 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3970 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3980 x21: .cfa -16 + ^
STACK CFI 39b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 39b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 39c0 4c .cfa: sp 0 + .ra: x30
STACK CFI 39c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 39d4 x19: .cfa -16 + ^
STACK CFI 39fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3a04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3a10 f4 .cfa: sp 0 + .ra: x30
STACK CFI 3a20 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a28 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3a8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a94 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3afc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3b04 bc .cfa: sp 0 + .ra: x30
STACK CFI 3b0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3b84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3b8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3ba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3bac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3bc0 94 .cfa: sp 0 + .ra: x30
STACK CFI 3bc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3bd0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c40 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3c54 d4 .cfa: sp 0 + .ra: x30
STACK CFI 3c60 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3cec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3cf4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3d1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3d30 20 .cfa: sp 0 + .ra: x30
STACK CFI 3d38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3d50 40 .cfa: sp 0 + .ra: x30
STACK CFI 3d58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3d78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3d7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3d90 60 .cfa: sp 0 + .ra: x30
STACK CFI 3d98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3da0 x19: .cfa -16 + ^
STACK CFI 3dc8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3dd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3de8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3df0 24 .cfa: sp 0 + .ra: x30
STACK CFI 3df8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3e14 88 .cfa: sp 0 + .ra: x30
STACK CFI 3e1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e28 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3e74 x19: x19 x20: x20
STACK CFI 3e78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3e80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3e84 x19: x19 x20: x20
STACK CFI INIT 3ea0 74 .cfa: sp 0 + .ra: x30
STACK CFI 3ea8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3eb0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ee4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3f14 90 .cfa: sp 0 + .ra: x30
STACK CFI 3f1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f28 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3f7c x19: x19 x20: x20
STACK CFI 3f80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3f88 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3f8c x19: x19 x20: x20
STACK CFI INIT 3fa4 24 .cfa: sp 0 + .ra: x30
STACK CFI 3fac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3fb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3fd0 88 .cfa: sp 0 + .ra: x30
STACK CFI 3ff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4000 x19: .cfa -16 + ^
STACK CFI 401c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4040 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4060 58 .cfa: sp 0 + .ra: x30
STACK CFI 4068 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4084 x19: .cfa -16 + ^
STACK CFI 409c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 40a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 40c0 90 .cfa: sp 0 + .ra: x30
STACK CFI 40c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 40d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 40d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4120 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4134 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4150 d4 .cfa: sp 0 + .ra: x30
STACK CFI 4158 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4184 x19: .cfa -16 + ^
STACK CFI 41b0 x19: x19
STACK CFI 41b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 41bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 41d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 420c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4220 x19: x19
STACK CFI INIT 4224 20 .cfa: sp 0 + .ra: x30
STACK CFI 422c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4238 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4244 24 .cfa: sp 0 + .ra: x30
STACK CFI 424c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4258 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4270 88 .cfa: sp 0 + .ra: x30
STACK CFI 4294 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42a0 x19: .cfa -16 + ^
STACK CFI 42bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 42e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4300 18 .cfa: sp 0 + .ra: x30
STACK CFI 4308 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4310 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4320 80 .cfa: sp 0 + .ra: x30
STACK CFI 4340 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4350 x19: .cfa -16 + ^
STACK CFI 437c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4388 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 43a0 7c .cfa: sp 0 + .ra: x30
STACK CFI 43a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 43f0 x19: x19 x20: x20
STACK CFI 43f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4400 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4404 x19: x19 x20: x20
STACK CFI INIT 4420 80 .cfa: sp 0 + .ra: x30
STACK CFI 4428 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4434 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4474 x19: x19 x20: x20
STACK CFI 447c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4484 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4488 x19: x19 x20: x20
STACK CFI INIT 44a0 9c .cfa: sp 0 + .ra: x30
STACK CFI 44a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 44c0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4508 x19: x19 x20: x20
STACK CFI 4510 x21: x21 x22: x22
STACK CFI 4514 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 451c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4520 x19: x19 x20: x20
STACK CFI 4524 x21: x21 x22: x22
STACK CFI INIT 4540 80 .cfa: sp 0 + .ra: x30
STACK CFI 4548 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4554 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4594 x19: x19 x20: x20
STACK CFI 459c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 45a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 45a8 x19: x19 x20: x20
STACK CFI INIT 45c0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 45c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 45d0 x21: .cfa -16 + ^
STACK CFI 45d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4624 x19: x19 x20: x20
STACK CFI 4630 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 4638 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4654 x19: x19 x20: x20
STACK CFI 4670 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 4680 84 .cfa: sp 0 + .ra: x30
STACK CFI 4688 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4698 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 46d8 x19: x19 x20: x20
STACK CFI 46e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 46e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 46ec x19: x19 x20: x20
STACK CFI INIT 4704 6c .cfa: sp 0 + .ra: x30
STACK CFI 4714 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 471c x19: .cfa -16 + ^
STACK CFI 4760 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4770 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 4778 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4784 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4790 x21: .cfa -16 + ^
STACK CFI 48ac x19: x19 x20: x20
STACK CFI 48b0 x21: x21
STACK CFI 48b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 48bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 48dc x19: x19 x20: x20
STACK CFI 48e0 x21: x21
STACK CFI 48f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4908 x19: x19 x20: x20
STACK CFI 4910 x21: x21
STACK CFI INIT 4914 140 .cfa: sp 0 + .ra: x30
STACK CFI 491c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4928 x19: .cfa -16 + ^
STACK CFI 4a1c x19: x19
STACK CFI 4a20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4a28 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4a34 x19: x19
STACK CFI 4a48 x19: .cfa -16 + ^
STACK CFI 4a4c x19: x19
STACK CFI INIT 4a54 54 .cfa: sp 0 + .ra: x30
STACK CFI 4a5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4a8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4aa0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4ab0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 4ab8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4acc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4ad8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4b4c x19: x19 x20: x20
STACK CFI 4b54 x21: x21 x22: x22
STACK CFI 4b58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4b60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4b64 x19: x19 x20: x20
STACK CFI 4b68 x21: x21 x22: x22
STACK CFI INIT 4b80 120 .cfa: sp 0 + .ra: x30
STACK CFI 4b88 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4b94 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4ba0 x23: .cfa -16 + ^
STACK CFI 4bbc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4c58 x19: x19 x20: x20
STACK CFI 4c60 x21: x21 x22: x22
STACK CFI 4c64 x23: x23
STACK CFI 4c68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4c70 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4c74 x19: x19 x20: x20
STACK CFI 4c78 x21: x21 x22: x22
STACK CFI 4c7c x23: x23
STACK CFI 4c94 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 4c98 x19: x19 x20: x20
STACK CFI 4c9c x23: x23
STACK CFI INIT 4ca0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 4ca8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4cb4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4cbc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4cd0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4d70 x19: x19 x20: x20
STACK CFI 4d84 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 4d8c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4db8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4e2c x25: x25 x26: x26
STACK CFI 4e38 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4e54 x25: x25 x26: x26
STACK CFI 4e5c x19: x19 x20: x20
STACK CFI INIT 4e64 80 .cfa: sp 0 + .ra: x30
STACK CFI 4e6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4e78 x19: .cfa -16 + ^
STACK CFI 4ebc x19: x19
STACK CFI 4ec0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4ec8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4ecc x19: x19
STACK CFI INIT 4ee4 80 .cfa: sp 0 + .ra: x30
STACK CFI 4eec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ef8 x19: .cfa -16 + ^
STACK CFI 4f38 x19: x19
STACK CFI 4f40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4f48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4f4c x19: x19
STACK CFI INIT 4f64 8c .cfa: sp 0 + .ra: x30
STACK CFI 4f6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f80 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4fc4 x19: x19 x20: x20
STACK CFI 4fcc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4fd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4fd8 x19: x19 x20: x20
STACK CFI INIT 4ff0 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 4ff8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5004 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5138 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5140 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 51b0 24 .cfa: sp 0 + .ra: x30
STACK CFI 51b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 51c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 51d4 168 .cfa: sp 0 + .ra: x30
STACK CFI 51dc .cfa: sp 80 +
STACK CFI 51e8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 51f0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 51f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5230 x23: .cfa -16 + ^
STACK CFI 529c x23: x23
STACK CFI 52cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 52d4 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 532c x23: x23
STACK CFI 5338 x23: .cfa -16 + ^
STACK CFI INIT 5340 24 .cfa: sp 0 + .ra: x30
STACK CFI 5348 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5354 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5364 184 .cfa: sp 0 + .ra: x30
STACK CFI 536c .cfa: sp 80 +
STACK CFI 5378 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5384 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5390 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5498 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 54f0 30 .cfa: sp 0 + .ra: x30
STACK CFI 54f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5504 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5520 3d0 .cfa: sp 0 + .ra: x30
STACK CFI 5528 .cfa: sp 112 +
STACK CFI 5534 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 553c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5558 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5620 x21: x21 x22: x22
STACK CFI 564c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5654 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5678 x21: x21 x22: x22
STACK CFI 5680 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5684 x21: x21 x22: x22
STACK CFI 569c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 56a0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 56a4 x25: .cfa -16 + ^
STACK CFI 56a8 x23: x23 x24: x24 x25: x25
STACK CFI 56b0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 573c x25: .cfa -16 + ^
STACK CFI 584c x25: x25
STACK CFI 585c x25: .cfa -16 + ^
STACK CFI 5860 x25: x25
STACK CFI 587c x25: .cfa -16 + ^
STACK CFI 5880 x25: x25
STACK CFI 58a4 x25: .cfa -16 + ^
STACK CFI 58a8 x25: x25
STACK CFI 58b0 x25: .cfa -16 + ^
STACK CFI 58b4 x25: x25
STACK CFI 58d0 x25: .cfa -16 + ^
STACK CFI INIT 58f0 104 .cfa: sp 0 + .ra: x30
STACK CFI 58f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5900 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5908 x21: .cfa -16 + ^
STACK CFI 59d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 59e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 59f4 120 .cfa: sp 0 + .ra: x30
STACK CFI 59fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5a04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5a2c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5a68 x21: x21 x22: x22
STACK CFI 5a74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5aa8 x21: x21 x22: x22
STACK CFI 5ab0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5ae0 x21: x21 x22: x22
STACK CFI 5ae8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5b04 x21: x21 x22: x22
STACK CFI 5b0c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5b10 x21: x21 x22: x22
STACK CFI INIT 5b14 e4 .cfa: sp 0 + .ra: x30
STACK CFI 5b1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5b24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5b5c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5bb8 x21: x21 x22: x22
STACK CFI 5bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5bcc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5be0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5bf0 x21: x21 x22: x22
STACK CFI INIT 5c00 20 .cfa: sp 0 + .ra: x30
STACK CFI 5c08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5c14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5c20 108 .cfa: sp 0 + .ra: x30
STACK CFI 5c30 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5c38 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5c44 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5c68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5c70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5cd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5ce0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5d30 180 .cfa: sp 0 + .ra: x30
STACK CFI 5d38 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5d44 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5d50 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5d68 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5df4 x19: x19 x20: x20
STACK CFI 5dfc x21: x21 x22: x22
STACK CFI 5e00 x23: x23 x24: x24
STACK CFI 5e04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5e0c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5e6c x19: x19 x20: x20
STACK CFI 5e74 x21: x21 x22: x22
STACK CFI 5e78 x23: x23 x24: x24
STACK CFI 5e7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5e84 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5e88 x19: x19 x20: x20
STACK CFI 5e8c x21: x21 x22: x22
STACK CFI 5e90 x23: x23 x24: x24
STACK CFI 5ea4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5ea8 x19: x19 x20: x20
STACK CFI 5eac x21: x21 x22: x22
STACK CFI INIT 5eb0 190 .cfa: sp 0 + .ra: x30
STACK CFI 5eb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5ec0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ed8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5f70 x21: x21 x22: x22
STACK CFI 5f7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6014 x21: x21 x22: x22
STACK CFI 6028 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6038 x21: x21 x22: x22
STACK CFI INIT 6040 f0 .cfa: sp 0 + .ra: x30
STACK CFI 6048 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6050 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6060 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6098 x23: .cfa -16 + ^
STACK CFI 60e4 x19: x19 x20: x20
STACK CFI 60e8 x23: x23
STACK CFI 60f4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 60fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6114 x23: x23
STACK CFI 6118 x19: x19 x20: x20
STACK CFI 611c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6128 x19: x19 x20: x20
STACK CFI INIT 6130 ec .cfa: sp 0 + .ra: x30
STACK CFI 6138 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6140 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6148 v8: .cfa -16 + ^
STACK CFI 6200 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI 6208 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6220 708 .cfa: sp 0 + .ra: x30
STACK CFI 6228 .cfa: sp 128 +
STACK CFI 6234 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 624c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6254 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6270 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 62a4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 62a8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 632c x19: x19 x20: x20
STACK CFI 6334 x21: x21 x22: x22
STACK CFI 6338 x23: x23 x24: x24
STACK CFI 633c x25: x25 x26: x26
STACK CFI 6370 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 6378 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6384 x19: x19 x20: x20
STACK CFI 638c x25: x25 x26: x26
STACK CFI 6394 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 639c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 63a0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6644 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6654 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 68c4 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 68c8 x19: x19 x20: x20
STACK CFI 68cc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6904 x19: x19 x20: x20
STACK CFI 6908 x21: x21 x22: x22
STACK CFI 690c x23: x23 x24: x24
STACK CFI 6910 x25: x25 x26: x26
STACK CFI 6918 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 691c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6920 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6924 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 6930 44 .cfa: sp 0 + .ra: x30
STACK CFI 6938 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6940 x19: .cfa -32 + ^
STACK CFI 696c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6974 e4 .cfa: sp 0 + .ra: x30
STACK CFI 697c .cfa: sp 48 +
STACK CFI 698c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 69b8 x19: .cfa -16 + ^
STACK CFI 6a04 x19: x19
STACK CFI 6a08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6a10 .cfa: sp 48 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6a44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6a4c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6a50 x19: x19
STACK CFI 6a54 x19: .cfa -16 + ^
STACK CFI INIT 6a60 778 .cfa: sp 0 + .ra: x30
STACK CFI 6a68 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6a74 .cfa: sp 1744 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6ab0 x23: .cfa -48 + ^
STACK CFI 6abc x21: .cfa -64 + ^
STACK CFI 6ac0 x22: .cfa -56 + ^
STACK CFI 6ac4 x24: .cfa -40 + ^
STACK CFI 6ac8 x25: .cfa -32 + ^
STACK CFI 6acc x26: .cfa -24 + ^
STACK CFI 6ad0 x27: .cfa -16 + ^
STACK CFI 6ad4 x28: .cfa -8 + ^
STACK CFI 6c6c x21: x21
STACK CFI 6c70 x22: x22
STACK CFI 6c74 x23: x23
STACK CFI 6c78 x24: x24
STACK CFI 6c7c x25: x25
STACK CFI 6c80 x26: x26
STACK CFI 6c84 x27: x27
STACK CFI 6c88 x28: x28
STACK CFI 6c8c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6ef4 x21: x21
STACK CFI 6ef8 x22: x22
STACK CFI 6efc x23: x23
STACK CFI 6f00 x24: x24
STACK CFI 6f04 x25: x25
STACK CFI 6f08 x26: x26
STACK CFI 6f0c x27: x27
STACK CFI 6f10 x28: x28
STACK CFI 6f34 .cfa: sp 96 +
STACK CFI 6f3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6f44 .cfa: sp 1744 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 6f54 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7144 x21: x21
STACK CFI 714c x22: x22
STACK CFI 7154 x23: x23
STACK CFI 7158 x24: x24
STACK CFI 715c x25: x25
STACK CFI 7160 x26: x26
STACK CFI 7164 x27: x27
STACK CFI 7168 x28: x28
STACK CFI 716c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 71b4 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 71b8 x21: .cfa -64 + ^
STACK CFI 71bc x22: .cfa -56 + ^
STACK CFI 71c0 x23: .cfa -48 + ^
STACK CFI 71c4 x24: .cfa -40 + ^
STACK CFI 71c8 x25: .cfa -32 + ^
STACK CFI 71cc x26: .cfa -24 + ^
STACK CFI 71d0 x27: .cfa -16 + ^
STACK CFI 71d4 x28: .cfa -8 + ^
STACK CFI INIT 71e0 90 .cfa: sp 0 + .ra: x30
STACK CFI 71e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7218 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7224 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 723c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7248 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 724c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 725c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7260 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7270 2cc .cfa: sp 0 + .ra: x30
STACK CFI 7278 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7288 .cfa: sp 1664 + x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 72f4 .cfa: sp 80 +
STACK CFI 7300 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 7308 .cfa: sp 1664 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 732c x23: .cfa -32 + ^
STACK CFI 7344 x19: .cfa -64 + ^
STACK CFI 734c x20: .cfa -56 + ^
STACK CFI 7354 x24: .cfa -24 + ^
STACK CFI 7504 x19: x19
STACK CFI 7508 x20: x20
STACK CFI 750c x23: x23
STACK CFI 7510 x24: x24
STACK CFI 752c x19: .cfa -64 + ^
STACK CFI 7530 x20: .cfa -56 + ^
STACK CFI 7534 x23: .cfa -32 + ^
STACK CFI 7538 x24: .cfa -24 + ^
STACK CFI INIT 7540 190 .cfa: sp 0 + .ra: x30
STACK CFI 7548 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7554 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7578 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 75d0 x19: x19 x20: x20
STACK CFI 75dc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 75e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 76b0 x19: x19 x20: x20
STACK CFI 76c8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 2df0 38 .cfa: sp 0 + .ra: x30
STACK CFI 2df8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e00 x19: .cfa -16 + ^
STACK CFI 2e20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 76d0 3c .cfa: sp 0 + .ra: x30
STACK CFI 76d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 76e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7710 218 .cfa: sp 0 + .ra: x30
STACK CFI 7718 .cfa: sp 224 +
STACK CFI 772c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7764 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7780 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 779c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 77b4 x25: .cfa -16 + ^
STACK CFI 7864 x25: x25
STACK CFI 7884 x19: x19 x20: x20
STACK CFI 7888 x23: x23 x24: x24
STACK CFI 78b4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 78bc .cfa: sp 224 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 790c x23: x23 x24: x24 x25: x25
STACK CFI 7910 x19: x19 x20: x20
STACK CFI 791c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7920 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7924 x25: .cfa -16 + ^
STACK CFI INIT 7930 c8 .cfa: sp 0 + .ra: x30
STACK CFI 7938 .cfa: sp 80 +
STACK CFI 7944 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 794c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 796c x21: .cfa -16 + ^
STACK CFI 799c x21: x21
STACK CFI 79cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 79d4 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 79e8 x21: x21
STACK CFI 79f4 x21: .cfa -16 + ^
STACK CFI INIT 7a00 c8 .cfa: sp 0 + .ra: x30
STACK CFI 7a08 .cfa: sp 80 +
STACK CFI 7a14 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7a1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7a3c x21: .cfa -16 + ^
STACK CFI 7a6c x21: x21
STACK CFI 7a9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7aa4 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7ab8 x21: x21
STACK CFI 7ac4 x21: .cfa -16 + ^
STACK CFI INIT 7ad0 7c .cfa: sp 0 + .ra: x30
STACK CFI 7ad8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ae4 x19: .cfa -16 + ^
STACK CFI 7b1c x19: x19
STACK CFI 7b20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7b28 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7b2c x19: x19
STACK CFI 7b44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7b50 f8 .cfa: sp 0 + .ra: x30
STACK CFI 7b58 .cfa: sp 224 +
STACK CFI 7b64 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7b6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7ba4 x21: .cfa -16 + ^
STACK CFI 7bdc x21: x21
STACK CFI 7c04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7c0c .cfa: sp 224 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7c34 x21: .cfa -16 + ^
STACK CFI 7c38 x21: x21
STACK CFI 7c44 x21: .cfa -16 + ^
STACK CFI INIT 7c50 f8 .cfa: sp 0 + .ra: x30
STACK CFI 7c58 .cfa: sp 224 +
STACK CFI 7c64 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7c6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7ca4 x21: .cfa -16 + ^
STACK CFI 7cdc x21: x21
STACK CFI 7d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7d0c .cfa: sp 224 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7d34 x21: .cfa -16 + ^
STACK CFI 7d38 x21: x21
STACK CFI 7d44 x21: .cfa -16 + ^
STACK CFI INIT 7d50 7c .cfa: sp 0 + .ra: x30
STACK CFI 7d58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7d64 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7da0 x19: x19 x20: x20
STACK CFI 7da8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7db0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7db4 x19: x19 x20: x20
STACK CFI INIT 7dd0 358 .cfa: sp 0 + .ra: x30
STACK CFI 7dd8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7e00 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 8130 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f00 24 .cfa: sp 0 + .ra: x30
STACK CFI 2f04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f1c .cfa: sp 0 + .ra: .ra x29: x29
