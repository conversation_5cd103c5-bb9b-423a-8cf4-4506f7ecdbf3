MODULE Linux arm64 78B0CA616DD77421EC8D0C35A91DA3640 libply-boot-client.so.5
INFO CODE_ID 61CAB078D76D2174EC8D0C35A91DA364040BFDC7
PUBLIC 2840 0 ply_boot_client_new
PUBLIC 28a0 0 ply_boot_client_free
PUBLIC 28f0 0 ply_boot_client_connect
PUBLIC 2de0 0 ply_boot_client_ping_daemon
PUBLIC 2e34 0 ply_boot_client_update_daemon
PUBLIC 2e90 0 ply_boot_client_change_mode
PUBLIC 2ee4 0 ply_boot_client_system_update
PUBLIC 2f40 0 ply_boot_client_tell_daemon_to_change_root
PUBLIC 2fc0 0 ply_boot_client_tell_daemon_to_display_message
PUBLIC 3040 0 ply_boot_client_tell_daemon_to_hide_message
PUBLIC 30c0 0 ply_boot_client_tell_daemon_system_is_initialized
PUBLIC 3114 0 ply_boot_client_ask_daemon_for_password
PUBLIC 3170 0 ply_boot_client_ask_daemon_for_cached_passwords
PUBLIC 31c4 0 ply_boot_client_ask_daemon_question
PUBLIC 3220 0 ply_boot_client_ask_daemon_to_watch_for_keystroke
PUBLIC 3274 0 ply_boot_client_ask_daemon_to_ignore_keystroke
PUBLIC 32d0 0 ply_boot_client_tell_daemon_to_show_splash
PUBLIC 3324 0 ply_boot_client_tell_daemon_to_hide_splash
PUBLIC 3380 0 ply_boot_client_tell_daemon_to_deactivate
PUBLIC 33d4 0 ply_boot_client_tell_daemon_to_reactivate
PUBLIC 3430 0 ply_boot_client_tell_daemon_to_quit
PUBLIC 34d4 0 ply_boot_client_tell_daemon_to_reload
PUBLIC 3530 0 ply_boot_client_tell_daemon_to_progress_pause
PUBLIC 3560 0 ply_boot_client_tell_daemon_to_progress_unpause
PUBLIC 3590 0 ply_boot_client_ask_daemon_has_active_vt
PUBLIC 35c0 0 ply_boot_client_tell_daemon_about_error
PUBLIC 35f0 0 ply_boot_client_flush
PUBLIC 3660 0 ply_boot_client_disconnect
PUBLIC 36d0 0 ply_boot_client_attach_to_event_loop
STACK CFI INIT 1a20 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a50 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a90 48 .cfa: sp 0 + .ra: x30
STACK CFI 1a94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a9c x19: .cfa -16 + ^
STACK CFI 1ad4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ae0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1af0 50 .cfa: sp 0 + .ra: x30
STACK CFI 1af8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b04 x19: .cfa -16 + ^
STACK CFI 1b38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1b40 e0 .cfa: sp 0 + .ra: x30
STACK CFI 1b48 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1b50 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1b68 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1b70 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1b78 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1bd8 x19: x19 x20: x20
STACK CFI 1be4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1bec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1bf0 x19: x19 x20: x20
STACK CFI 1bf8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1c00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1c20 e4 .cfa: sp 0 + .ra: x30
STACK CFI 1c28 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1c30 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1c48 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1c50 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1c58 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1cb8 x19: x19 x20: x20
STACK CFI 1cc4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1ccc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1cd0 x19: x19 x20: x20
STACK CFI 1cd8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1ce0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1d04 7c .cfa: sp 0 + .ra: x30
STACK CFI 1d0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d14 x19: .cfa -16 + ^
STACK CFI 1d44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1d4c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1d54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1d5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1d80 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 1d88 .cfa: sp 240 +
STACK CFI 1d94 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1d9c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1da4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1e08 .cfa: sp 240 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1f40 44c .cfa: sp 0 + .ra: x30
STACK CFI 1f48 .cfa: sp 112 +
STACK CFI 1f54 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1f5c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1f9c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2078 x19: x19 x20: x20
STACK CFI 20b8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 20c0 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 20e8 x19: x19 x20: x20
STACK CFI 2100 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 212c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2168 x23: x23 x24: x24
STACK CFI 216c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2188 x23: x23 x24: x24
STACK CFI 21ac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 21b0 x25: .cfa -16 + ^
STACK CFI 21b4 x23: x23 x24: x24 x25: x25
STACK CFI 21d8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 21dc x25: .cfa -16 + ^
STACK CFI 21e0 x25: x25
STACK CFI 21ec x23: x23 x24: x24
STACK CFI 2204 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2208 x25: .cfa -16 + ^
STACK CFI 22d4 x23: x23 x24: x24
STACK CFI 22d8 x25: x25
STACK CFI 22dc x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 22e0 x23: x23 x24: x24
STACK CFI 22e4 x25: x25
STACK CFI 22e8 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 22f4 x23: x23 x24: x24
STACK CFI 22f8 x25: x25
STACK CFI 2320 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2324 x25: .cfa -16 + ^
STACK CFI 234c x19: x19 x20: x20 x23: x23 x24: x24 x25: x25
STACK CFI 2370 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2374 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2378 x25: .cfa -16 + ^
STACK CFI 237c x19: x19 x20: x20 x23: x23 x24: x24 x25: x25
STACK CFI 2380 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2384 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2388 x25: .cfa -16 + ^
STACK CFI INIT 2390 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 2398 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 23ac x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 2418 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2420 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 243c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2498 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2564 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 256c .cfa: sp 64 +
STACK CFI 2578 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2580 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2588 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 26b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 26b8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2840 5c .cfa: sp 0 + .ra: x30
STACK CFI 2848 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2858 x19: .cfa -16 + ^
STACK CFI 2894 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 28a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 28b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 28b8 x19: .cfa -16 + ^
STACK CFI 28e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 28f0 4e8 .cfa: sp 0 + .ra: x30
STACK CFI 28f8 .cfa: sp 256 +
STACK CFI 2904 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2918 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2938 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2994 x19: x19 x20: x20
STACK CFI 2998 x21: x21 x22: x22
STACK CFI 299c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 29a4 .cfa: sp 256 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 29a8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 29ec x23: x23 x24: x24
STACK CFI 29f0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2c14 x23: x23 x24: x24
STACK CFI 2c1c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2d24 x23: x23 x24: x24
STACK CFI 2d48 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2d4c x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2d70 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2d74 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2d78 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2d9c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2da0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2da4 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2dc8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2dcc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2dd0 x23: x23 x24: x24
STACK CFI 2dd4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 2de0 54 .cfa: sp 0 + .ra: x30
STACK CFI 2e0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2e34 54 .cfa: sp 0 + .ra: x30
STACK CFI 2e60 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2e90 54 .cfa: sp 0 + .ra: x30
STACK CFI 2ebc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2ee4 54 .cfa: sp 0 + .ra: x30
STACK CFI 2f10 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2f40 80 .cfa: sp 0 + .ra: x30
STACK CFI 2f48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2f78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2fc0 80 .cfa: sp 0 + .ra: x30
STACK CFI 2fc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2fd8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2ff8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3040 80 .cfa: sp 0 + .ra: x30
STACK CFI 3048 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3058 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3078 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 30c0 54 .cfa: sp 0 + .ra: x30
STACK CFI 30ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3114 54 .cfa: sp 0 + .ra: x30
STACK CFI 3140 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3170 54 .cfa: sp 0 + .ra: x30
STACK CFI 319c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 31c4 54 .cfa: sp 0 + .ra: x30
STACK CFI 31f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3220 54 .cfa: sp 0 + .ra: x30
STACK CFI 324c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3274 54 .cfa: sp 0 + .ra: x30
STACK CFI 32a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 32d0 54 .cfa: sp 0 + .ra: x30
STACK CFI 32fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3324 54 .cfa: sp 0 + .ra: x30
STACK CFI 3350 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3380 54 .cfa: sp 0 + .ra: x30
STACK CFI 33ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 33d4 54 .cfa: sp 0 + .ra: x30
STACK CFI 3400 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3430 a4 .cfa: sp 0 + .ra: x30
STACK CFI 3438 .cfa: sp 32 +
STACK CFI 3444 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 34ac .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 34d4 54 .cfa: sp 0 + .ra: x30
STACK CFI 3500 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3530 30 .cfa: sp 0 + .ra: x30
STACK CFI 3538 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3544 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3560 30 .cfa: sp 0 + .ra: x30
STACK CFI 3568 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3574 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3590 30 .cfa: sp 0 + .ra: x30
STACK CFI 3598 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 35c0 30 .cfa: sp 0 + .ra: x30
STACK CFI 35c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 35f0 68 .cfa: sp 0 + .ra: x30
STACK CFI 35f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3600 x19: .cfa -16 + ^
STACK CFI 362c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3634 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3660 68 .cfa: sp 0 + .ra: x30
STACK CFI 3668 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3670 x19: .cfa -16 + ^
STACK CFI 369c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 36a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 36d0 dc .cfa: sp 0 + .ra: x30
STACK CFI 36d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 36e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3734 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3740 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
