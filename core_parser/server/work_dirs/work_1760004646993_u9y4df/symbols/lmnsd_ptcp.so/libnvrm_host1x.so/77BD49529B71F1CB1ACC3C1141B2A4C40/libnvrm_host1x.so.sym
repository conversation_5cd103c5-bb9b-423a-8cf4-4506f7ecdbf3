MODULE Linux arm64 77BD49529B71F1CB1ACC3C1141B2A4C40 libnvrm_host1x.so
INFO CODE_ID 5249BD77719BCBF11ACC3C1141B2A4C4FFFF2FD3
PUBLIC 3130 0 NvRmChannelGetBackEnd
PUBLIC 3220 0 NvRmChannelOpenDeviceFile
PUBLIC 3330 0 NvRmChannelOpen
PUBLIC 3450 0 NvRmChannelClose
PUBLIC 3520 0 NvRmChannelSetErrNotifier
PUBLIC 3640 0 NvRmChannelSetSubmitTimeoutEx
PUBLIC 3750 0 NvRmChannelSetSubmitTimeout
PUBLIC 3850 0 NvRmChannelMapCmdBuf
PUBLIC 3970 0 NvRmChannelMapRelocs
PUBLIC 3a90 0 NvRmChannelSubmit
PUBLIC 3d50 0 NvRmChannelGetModuleSyncPoint
PUBLIC 3e70 0 NvRmChannelGetClientManagedSyncPoint
PUBLIC 3f90 0 NvRmChannelFreeClientManagedSyncPoint
PUBLIC 4080 0 NvRmChannelGetModuleClockRate
PUBLIC 4190 0 NvRmChannelSetModuleClockRate
PUBLIC 42a0 0 NvRmChannelSetModuleBandwidth
PUBLIC 43b0 0 NvRmChannelSetModulePixelRate
PUBLIC 44c0 0 NvRmChannelRegRd
PUBLIC 4600 0 NvRmChannelRegWr
PUBLIC 4740 0 NvRmChannelBlockRegRd
PUBLIC 4880 0 NvRmChannelBlockRegWr
PUBLIC 49c0 0 NvRmChannelSyncPointRead
PUBLIC 4aa0 0 NvRmChannelGatherFilterEnabled
PUBLIC 4b60 0 NvRmChannelNumSyncPoints
PUBLIC 4c50 0 NvRmChannelMapAtSubmitEnabled
PUBLIC 4d10 0 NvRmChannelSyncPointIncr
PUBLIC 4df0 0 NvRmChannelSyncPointWait
PUBLIC 4ef0 0 NvRmChannelSyncPointWaitTimeout
PUBLIC 5020 0 NvRmChannelSyncPointWaitexTimeout
PUBLIC 5160 0 NvRmChannelSyncPointWaitmexTimeout
PUBLIC 52b0 0 NvRmFenceWait
PUBLIC 53c0 0 NvRmFencePutToFile
PUBLIC 54e0 0 NvRmFenceGetFromFile
PUBLIC 55f0 0 NvRmHostModuleCheck
PUBLIC 56e0 0 NvRmHostCreateEventPollFd
PUBLIC 57e0 0 NvRmHostTriggerEventPollFd
PUBLIC 5900 0 NvRmHost1xChannelGetModuleClockRate
PUBLIC 5a10 0 NvRmHost1xChannelSetModuleClockRate
PUBLIC 5b20 0 NvRmHost1xChannelSetModulePixelRate
PUBLIC 5c30 0 NvRmHost1xChannelSetModuleBandwidth
PUBLIC 5d40 0 NvRmChannelSyncPointFree
PUBLIC 86f0 0 NvRmChannelGetHost1xHandlePRIVATE
PUBLIC 8da0 0 NvEngineCounterInitialize
PUBLIC 8e40 0 NvEngineCounterGetNext
PUBLIC 8e70 0 NvRmHost1xSelectEmuSyncpt
PUBLIC 9190 0 NvRmHost1xGetDefaultOpenAttrs
PUBLIC 9210 0 NvRmHost1xOpen
PUBLIC 92c0 0 NvRmHost1xClose
PUBLIC 9360 0 NvRmHost1xChannelOpen
PUBLIC 9450 0 NvRmHost1xChannelClose
PUBLIC 94f0 0 NvRmHost1xChannelPinCommandBufferPRIVATE
PUBLIC 95f0 0 NvRmHost1xChannelPinDataBuffer
PUBLIC 9700 0 NvRmHost1xChannelUnpin
PUBLIC 97a0 0 NvRmHost1xChannelGetPinAddressPRIVATE
PUBLIC 9880 0 NvRmHost1xGetDefaultSyncpointAllocateAttrs
PUBLIC 9900 0 NvRmHost1xSyncpointAllocate
PUBLIC 99e0 0 NvRmHost1xSyncpointFree
PUBLIC 9a80 0 NvRmHost1xSyncpointGetId
PUBLIC 9b20 0 NvRmHost1xSyncpointIncrement
PUBLIC 9bd0 0 NvRmHost1xSyncpointAttachChannel
PUBLIC 9c80 0 NvRmHost1xSyncpointDetachChannel
PUBLIC 9d20 0 NvRmHost1xSyncpointRead
PUBLIC 9e00 0 NvRmHost1xSyncpointCreateMemHandle
PUBLIC 9e20 0 NvRmHost1xSyncpointCreateReadOnlyMemHandle
PUBLIC 9e40 0 NvRmHost1xSyncpointCreateFullReadOnlyMemHandle
PUBLIC 9e60 0 NvRmHost1xSyncpointMemHandleExport
PUBLIC 9e80 0 NvRmHost1xSyncpointMemHandleImport
PUBLIC 9ea0 0 NvRmHost1xWaiterAllocate
PUBLIC 9f50 0 NvRmHost1xWaiterAllocateWithEventService
PUBLIC a030 0 NvRmHost1xWaiterCreateEventNotifier
PUBLIC a0e0 0 NvRmHost1xWaiterArmSyncpointEvent
PUBLIC a1c0 0 NvRmHost1xWaiterFree
PUBLIC a260 0 NvRmHost1xWaiterDisarmSyncpointEvent
PUBLIC a300 0 NvRmHost1xSyncpointWait
PUBLIC a400 0 NvRmHost1xChannelSubmitPRIVATE
PUBLIC a5b0 0 NvRmHost1xGetFdPRIVATE
PUBLIC a650 0 NvRmHost1xChannelGetFdPRIVATE
PUBLIC a6f0 0 NvRmHost1xChannelGetClassPRIVATE
PUBLIC a790 0 NvRmHost1xChannelGetInstanceNumPRIVATE
PUBLIC a830 0 NvRmHost1xChannelPinGetUIdPRIVATE
PUBLIC a970 0 NvRmHost1xChannelIsSyncpointAttachedPRIVATE
PUBLIC a990 0 NvRmHost1xChannelCheckIsDeterministicPRIVATE
PUBLIC a9e0 0 NvRmHost1xChannelIsValidSyncptPRIVATE
PUBLIC aa30 0 NvRmHost1xHasWaitCommandsPRIVATE
PUBLIC aab0 0 NvRmHost1xSyncpointGetFd
PUBLIC ab50 0 NvRmHost1xSyncpointCreateDmaBufFd
PUBLIC ac00 0 NvRmHost1xSyncpointCreateFullDmaBufFd
PUBLIC acf0 0 NvRmHost1xChannelPinDataBufferFd
PUBLIC e0f0 0 NvRmHost1xSyncpointGetFd_LINUX_NVHOST
STACK CFI INIT 3060 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3090 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 30d0 48 .cfa: sp 0 + .ra: x30
STACK CFI 30d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30dc x19: .cfa -16 + ^
STACK CFI 3114 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3120 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ec10 c .cfa: sp 0 + .ra: x30
