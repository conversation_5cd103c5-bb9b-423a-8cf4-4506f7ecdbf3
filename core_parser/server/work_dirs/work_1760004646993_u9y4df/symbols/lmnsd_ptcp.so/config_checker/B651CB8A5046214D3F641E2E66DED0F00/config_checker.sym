MODULE Linux arm64 B651CB8A5046214D3F641E2E66DED0F00 config_checker
INFO CODE_ID 8ACB51B646504D213F641E2E66DED0F0
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 3f90 24 0 init_have_lse_atomics
3f90 4 45 0
3f94 4 46 0
3f98 4 45 0
3f9c 4 46 0
3fa0 4 47 0
3fa4 4 47 0
3fa8 4 48 0
3fac 4 47 0
3fb0 4 48 0
PUBLIC 2e78 0 _init
PUBLIC 32c0 0 main
PUBLIC 3fc0 0 _start
PUBLIC 3ff4 0 call_weak_fn
PUBLIC 4010 0 deregister_tm_clones
PUBLIC 4040 0 register_tm_clones
PUBLIC 4080 0 __do_global_dtors_aux
PUBLIC 40d0 0 frame_dummy
PUBLIC 40e0 0 Usage()
PUBLIC 41a0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, unsigned long, std::allocator<char> const&) [clone .part.0]
PUBLIC 41c0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::assign(char const*) [clone .isra.0]
PUBLIC 4200 0 std::_Sp_counted_ptr_inplace<lios::config::GlobalConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 4210 0 std::_Sp_counted_ptr_inplace<lios::config::QosMap, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 4220 0 std::_Sp_counted_ptr_inplace<lios::config::DagGraphConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 4230 0 std::_Sp_counted_ptr_inplace<lios::config::NodeConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 4240 0 std::_Sp_counted_ptr_inplace<lios::config::AppConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 4250 0 std::_Sp_counted_ptr_inplace<lios::config::AppConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 4260 0 std::_Sp_counted_ptr_inplace<lios::config::GlobalConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 4270 0 std::_Sp_counted_ptr_inplace<lios::config::QosMap, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 4280 0 std::_Sp_counted_ptr_inplace<lios::config::DagGraphConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 4290 0 std::_Sp_counted_ptr_inplace<lios::config::NodeConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 42a0 0 std::_Sp_counted_ptr_inplace<lios::config::NodeConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 42b0 0 std::_Sp_counted_ptr_inplace<lios::config::DagGraphConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 42c0 0 std::_Sp_counted_ptr_inplace<lios::config::QosMap, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 42d0 0 std::_Sp_counted_ptr_inplace<lios::config::GlobalConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 42e0 0 std::_Sp_counted_ptr_inplace<lios::config::GlobalConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 4350 0 std::_Sp_counted_ptr_inplace<lios::config::AppConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 4360 0 std::_Sp_counted_ptr_inplace<lios::config::AppConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 43d0 0 std::_Sp_counted_ptr_inplace<lios::config::QosMap, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 4440 0 std::_Sp_counted_ptr_inplace<lios::config::DagGraphConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 44b0 0 std::_Sp_counted_ptr_inplace<lios::config::NodeConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 4520 0 lios::config_checker::ConfigChecker::~ConfigChecker()
PUBLIC 4560 0 lios::config_checker::ConfigChecker::~ConfigChecker()
PUBLIC 45c0 0 std::filesystem::__cxx11::path::~path()
PUBLIC 4610 0 lios::config_checker::ConfigChecker::ConfigChecker(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 4730 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release_last_use_cold()
PUBLIC 47b0 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 4850 0 std::filesystem::__cxx11::path::path<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::filesystem::__cxx11::path>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::filesystem::__cxx11::path::format)
PUBLIC 49b0 0 std::__shared_count<(__gnu_cxx::_Lock_policy)2>::__shared_count(std::__shared_count<(__gnu_cxx::_Lock_policy)2> const&)
PUBLIC 4a00 0 std::_Sp_counted_ptr_inplace<lios::config::AppConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 4a10 0 std::_Sp_counted_ptr_inplace<lios::config::GlobalConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 4a20 0 std::_Sp_counted_ptr_inplace<lios::config::QosMap, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 4a30 0 std::_Sp_counted_ptr_inplace<lios::config::DagGraphConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 4a40 0 std::_Sp_counted_ptr_inplace<lios::config::NodeConfig, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 4a50 0 std::shared_ptr<lios::config::AppConfig> lios::config_checker::ParseProtoTxt<lios::config::AppConfig>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 4ed0 0 std::shared_ptr<lios::config::NodeConfig> lios::config_checker::ParseProtoTxt<lios::config::NodeConfig>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 5350 0 std::shared_ptr<lios::config::DagGraphConfig> lios::config_checker::ParseProtoTxt<lios::config::DagGraphConfig>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 57d0 0 std::shared_ptr<lios::config::QosMap> lios::config_checker::ParseProtoTxt<lios::config::QosMap>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 5c50 0 std::shared_ptr<lios::config::GlobalConfig> lios::config_checker::ParseProtoTxt<lios::config::GlobalConfig>(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 60d0 0 lios::config_checker::ConfigChecker::Load()
PUBLIC 7260 0 __aarch64_ldadd4_acq_rel
PUBLIC 7290 0 _fini
STACK CFI INIT 3fc0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4010 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4040 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4080 48 .cfa: sp 0 + .ra: x30
STACK CFI 4084 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 408c x19: .cfa -16 + ^
STACK CFI 40c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 40d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4200 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4210 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4220 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4230 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4240 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40e0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 40e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40f4 x19: .cfa -16 + ^
STACK CFI 4194 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4250 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4260 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4270 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4280 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4290 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41a0 14 .cfa: sp 0 + .ra: x30
STACK CFI 41a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 42a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 42d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41c0 3c .cfa: sp 0 + .ra: x30
STACK CFI 41c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 41f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 42e0 70 .cfa: sp 0 + .ra: x30
STACK CFI 42e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42f4 x19: .cfa -16 + ^
STACK CFI 4338 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 433c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 434c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4350 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4360 70 .cfa: sp 0 + .ra: x30
STACK CFI 4364 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4374 x19: .cfa -16 + ^
STACK CFI 43b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 43bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 43cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 43d0 70 .cfa: sp 0 + .ra: x30
STACK CFI 43d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43e4 x19: .cfa -16 + ^
STACK CFI 4428 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 442c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 443c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4440 70 .cfa: sp 0 + .ra: x30
STACK CFI 4444 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4454 x19: .cfa -16 + ^
STACK CFI 4498 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 449c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 44ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 44b0 70 .cfa: sp 0 + .ra: x30
STACK CFI 44b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44c4 x19: .cfa -16 + ^
STACK CFI 4508 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 450c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 451c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4520 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4560 54 .cfa: sp 0 + .ra: x30
STACK CFI 4564 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 457c x19: .cfa -16 + ^
STACK CFI 45b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 45c0 50 .cfa: sp 0 + .ra: x30
STACK CFI 45c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45d0 x19: .cfa -16 + ^
STACK CFI 4600 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4604 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 460c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4610 120 .cfa: sp 0 + .ra: x30
STACK CFI 4614 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4620 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4628 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 46c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 46c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4730 78 .cfa: sp 0 + .ra: x30
STACK CFI 4734 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4744 x19: .cfa -16 + ^
STACK CFI 4778 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 477c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 478c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4798 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 47b0 9c .cfa: sp 0 + .ra: x30
STACK CFI 47b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47c0 x19: .cfa -16 + ^
STACK CFI 4800 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4804 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4830 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 483c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4848 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4850 154 .cfa: sp 0 + .ra: x30
STACK CFI 4854 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4868 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4870 x21: .cfa -32 + ^
STACK CFI 48f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 48f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 49b0 4c .cfa: sp 0 + .ra: x30
STACK CFI 49e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 49f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a10 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a30 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a50 480 .cfa: sp 0 + .ra: x30
STACK CFI 4a54 .cfa: sp 768 +
STACK CFI 4a60 .ra: .cfa -760 + ^ x29: .cfa -768 + ^
STACK CFI 4a68 x19: .cfa -752 + ^ x20: .cfa -744 + ^
STACK CFI 4a74 x21: .cfa -736 + ^ x22: .cfa -728 + ^ x23: .cfa -720 + ^ x24: .cfa -712 + ^
STACK CFI 4a80 x25: .cfa -704 + ^ x26: .cfa -696 + ^ x27: .cfa -688 + ^ x28: .cfa -680 + ^
STACK CFI 4d24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4d28 .cfa: sp 768 + .ra: .cfa -760 + ^ x19: .cfa -752 + ^ x20: .cfa -744 + ^ x21: .cfa -736 + ^ x22: .cfa -728 + ^ x23: .cfa -720 + ^ x24: .cfa -712 + ^ x25: .cfa -704 + ^ x26: .cfa -696 + ^ x27: .cfa -688 + ^ x28: .cfa -680 + ^ x29: .cfa -768 + ^
STACK CFI INIT 4ed0 480 .cfa: sp 0 + .ra: x30
STACK CFI 4ed4 .cfa: sp 768 +
STACK CFI 4ee0 .ra: .cfa -760 + ^ x29: .cfa -768 + ^
STACK CFI 4ee8 x19: .cfa -752 + ^ x20: .cfa -744 + ^
STACK CFI 4ef4 x21: .cfa -736 + ^ x22: .cfa -728 + ^ x23: .cfa -720 + ^ x24: .cfa -712 + ^
STACK CFI 4f00 x25: .cfa -704 + ^ x26: .cfa -696 + ^ x27: .cfa -688 + ^ x28: .cfa -680 + ^
STACK CFI 51a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 51a8 .cfa: sp 768 + .ra: .cfa -760 + ^ x19: .cfa -752 + ^ x20: .cfa -744 + ^ x21: .cfa -736 + ^ x22: .cfa -728 + ^ x23: .cfa -720 + ^ x24: .cfa -712 + ^ x25: .cfa -704 + ^ x26: .cfa -696 + ^ x27: .cfa -688 + ^ x28: .cfa -680 + ^ x29: .cfa -768 + ^
STACK CFI INIT 5350 480 .cfa: sp 0 + .ra: x30
STACK CFI 5354 .cfa: sp 768 +
STACK CFI 5360 .ra: .cfa -760 + ^ x29: .cfa -768 + ^
STACK CFI 5368 x19: .cfa -752 + ^ x20: .cfa -744 + ^
STACK CFI 5374 x21: .cfa -736 + ^ x22: .cfa -728 + ^ x23: .cfa -720 + ^ x24: .cfa -712 + ^
STACK CFI 5380 x25: .cfa -704 + ^ x26: .cfa -696 + ^ x27: .cfa -688 + ^ x28: .cfa -680 + ^
STACK CFI 5624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5628 .cfa: sp 768 + .ra: .cfa -760 + ^ x19: .cfa -752 + ^ x20: .cfa -744 + ^ x21: .cfa -736 + ^ x22: .cfa -728 + ^ x23: .cfa -720 + ^ x24: .cfa -712 + ^ x25: .cfa -704 + ^ x26: .cfa -696 + ^ x27: .cfa -688 + ^ x28: .cfa -680 + ^ x29: .cfa -768 + ^
STACK CFI INIT 57d0 480 .cfa: sp 0 + .ra: x30
STACK CFI 57d4 .cfa: sp 768 +
STACK CFI 57e0 .ra: .cfa -760 + ^ x29: .cfa -768 + ^
STACK CFI 57e8 x19: .cfa -752 + ^ x20: .cfa -744 + ^
STACK CFI 57f4 x21: .cfa -736 + ^ x22: .cfa -728 + ^ x23: .cfa -720 + ^ x24: .cfa -712 + ^
STACK CFI 5800 x25: .cfa -704 + ^ x26: .cfa -696 + ^ x27: .cfa -688 + ^ x28: .cfa -680 + ^
STACK CFI 5aa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5aa8 .cfa: sp 768 + .ra: .cfa -760 + ^ x19: .cfa -752 + ^ x20: .cfa -744 + ^ x21: .cfa -736 + ^ x22: .cfa -728 + ^ x23: .cfa -720 + ^ x24: .cfa -712 + ^ x25: .cfa -704 + ^ x26: .cfa -696 + ^ x27: .cfa -688 + ^ x28: .cfa -680 + ^ x29: .cfa -768 + ^
STACK CFI INIT 5c50 480 .cfa: sp 0 + .ra: x30
STACK CFI 5c54 .cfa: sp 768 +
STACK CFI 5c60 .ra: .cfa -760 + ^ x29: .cfa -768 + ^
STACK CFI 5c68 x19: .cfa -752 + ^ x20: .cfa -744 + ^
STACK CFI 5c74 x21: .cfa -736 + ^ x22: .cfa -728 + ^ x23: .cfa -720 + ^ x24: .cfa -712 + ^
STACK CFI 5c80 x25: .cfa -704 + ^ x26: .cfa -696 + ^ x27: .cfa -688 + ^ x28: .cfa -680 + ^
STACK CFI 5f24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5f28 .cfa: sp 768 + .ra: .cfa -760 + ^ x19: .cfa -752 + ^ x20: .cfa -744 + ^ x21: .cfa -736 + ^ x22: .cfa -728 + ^ x23: .cfa -720 + ^ x24: .cfa -712 + ^ x25: .cfa -704 + ^ x26: .cfa -696 + ^ x27: .cfa -688 + ^ x28: .cfa -680 + ^ x29: .cfa -768 + ^
STACK CFI INIT 60d0 1184 .cfa: sp 0 + .ra: x30
STACK CFI 60d4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 60dc x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 60e4 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 60f8 x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 6100 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 6678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 667c .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT 32c0 ccc .cfa: sp 0 + .ra: x30
STACK CFI 32c4 .cfa: sp 528 +
STACK CFI 32d8 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI 32e4 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 32f0 x21: .cfa -496 + ^ x22: .cfa -488 + ^
STACK CFI 3308 x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI 3460 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3464 .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x25: .cfa -464 + ^ x26: .cfa -456 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^ x29: .cfa -528 + ^
STACK CFI INIT 7260 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f90 24 .cfa: sp 0 + .ra: x30
STACK CFI 3f94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3fac .cfa: sp 0 + .ra: .ra x29: x29
