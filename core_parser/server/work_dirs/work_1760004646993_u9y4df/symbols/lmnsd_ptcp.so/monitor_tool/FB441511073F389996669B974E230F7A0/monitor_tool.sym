MODULE Linux arm64 FB441511073F389996669B974E230F7A0 monitor_tool
INFO CODE_ID 111544FB3F07993896669B974E230F7A
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 5480 24 0 init_have_lse_atomics
5480 4 45 0
5484 4 46 0
5488 4 45 0
548c 4 46 0
5490 4 47 0
5494 4 47 0
5498 4 48 0
549c 4 47 0
54a0 4 48 0
PUBLIC 4bd0 0 _init
PUBLIC 5000 0 main
PUBLIC 53c0 0 _GLOBAL__sub_I_monitor_tool.cpp
PUBLIC 54c0 0 _start
PUBLIC 54f4 0 call_weak_fn
PUBLIC 5510 0 deregister_tm_clones
PUBLIC 5540 0 register_tm_clones
PUBLIC 5580 0 __do_global_dtors_aux
PUBLIC 55d0 0 frame_dummy
PUBLIC 55e0 0 PrintHelp()
PUBLIC 5680 0 ParamAnalysis(int, char**, lios::monitor::MonitorRequest&, int&)
PUBLIC 5820 0 std::ctype<char>::do_widen(char) const
PUBLIC 5830 0 cereal::detail::OutputArchiveBase::rtti()
PUBLIC 5840 0 cereal::detail::InputArchiveBase::rtti()
PUBLIC 5850 0 cereal::Exception::~Exception()
PUBLIC 5870 0 cereal::Exception::~Exception()
PUBLIC 58b0 0 std::_Rb_tree<std::type_index, std::pair<std::type_index const, std::type_index>, std::_Select1st<std::pair<std::type_index const, std::type_index> >, std::less<std::type_index>, std::allocator<std::pair<std::type_index const, std::type_index> > >::_M_erase(std::_Rb_tree_node<std::pair<std::type_index const, std::type_index> >*) [clone .isra.0]
PUBLIC 5a30 0 cereal::detail::PolymorphicCasters::~PolymorphicCasters()
PUBLIC 5b90 0 lios::monitor::MonitorRequest::~MonitorRequest()
PUBLIC 5c20 0 lios::monitor::MonitorRequest::~MonitorRequest()
PUBLIC 5cb0 0 lios::monitor::MonitorResponse::~MonitorResponse()
PUBLIC 5dc0 0 lios::monitor::MonitorResponse::~MonitorResponse()
PUBLIC 5ec0 0 std::__cxx11::to_string(long)
PUBLIC 61b0 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release_last_use_cold()
PUBLIC 6230 0 cereal::OutputArchive<cereal::PortableBinaryOutputArchive, 1u>::~OutputArchive()
PUBLIC 6520 0 cereal::InputArchive<cereal::PortableBinaryInputArchive, 1u>::~InputArchive()
PUBLIC 6820 0 cereal::PortableBinaryInputArchive::~PortableBinaryInputArchive()
PUBLIC 6b20 0 cereal::InputArchive<cereal::PortableBinaryInputArchive, 1u>::~InputArchive()
PUBLIC 6e20 0 cereal::PortableBinaryInputArchive::~PortableBinaryInputArchive()
PUBLIC 7120 0 cereal::PortableBinaryOutputArchive::~PortableBinaryOutputArchive()
PUBLIC 7410 0 cereal::OutputArchive<cereal::PortableBinaryOutputArchive, 1u>::~OutputArchive()
PUBLIC 76f0 0 cereal::PortableBinaryOutputArchive::~PortableBinaryOutputArchive()
PUBLIC 79d0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > std::operator+<char, std::char_traits<char>, std::allocator<char> >(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&)
PUBLIC 7b00 0 lios::monitor::MonitorRequest::FromBuffer(std::vector<char, std::allocator<char> > const&)
PUBLIC 8fb0 0 std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::~_Hashtable()
PUBLIC 9080 0 std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::clear()
PUBLIC 9130 0 std::enable_if<cereal::traits::is_output_serializable<cereal::BinaryData<char>, cereal::PortableBinaryOutputArchive>::value, void>::type cereal::save<cereal::PortableBinaryOutputArchive, char, std::char_traits<char>, std::allocator<char> >(cereal::PortableBinaryOutputArchive&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 95c0 0 lios::monitor::MonitorRequest::ToBuffer(std::vector<char, std::allocator<char> >&) const
PUBLIC a210 0 lios::monitor::MonitorResponse::ToBuffer(std::vector<char, std::allocator<char> >&) const
PUBLIC b530 0 std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC b660 0 lios::monitor::MonitorResponse::FromBuffer(std::vector<char, std::allocator<char> > const&)
PUBLIC cf90 0 __aarch64_ldadd4_acq_rel
PUBLIC cfc0 0 _fini
STACK CFI INIT 54c0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5510 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5540 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5580 48 .cfa: sp 0 + .ra: x30
STACK CFI 5584 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 558c x19: .cfa -16 + ^
STACK CFI 55c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 55d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5820 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5830 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5840 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5850 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5870 38 .cfa: sp 0 + .ra: x30
STACK CFI 5874 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5884 x19: .cfa -16 + ^
STACK CFI 58a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 58b0 180 .cfa: sp 0 + .ra: x30
STACK CFI 58b8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 58c0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 58c8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 58d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 58f8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 58fc x27: .cfa -16 + ^
STACK CFI 5950 x21: x21 x22: x22
STACK CFI 5954 x27: x27
STACK CFI 5970 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 598c x21: x21 x22: x22 x27: x27
STACK CFI 59a8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 59c4 x21: x21 x22: x22 x27: x27
STACK CFI 5a00 x25: x25 x26: x26
STACK CFI 5a28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 5a30 158 .cfa: sp 0 + .ra: x30
STACK CFI 5a34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5a3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5a48 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5b74 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5b84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 5b90 90 .cfa: sp 0 + .ra: x30
STACK CFI 5b94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5bac x19: .cfa -16 + ^
STACK CFI 5c10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5c14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5c1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5c20 8c .cfa: sp 0 + .ra: x30
STACK CFI 5c24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c3c x19: .cfa -16 + ^
STACK CFI 5ca8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5cb0 108 .cfa: sp 0 + .ra: x30
STACK CFI 5cb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5cbc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5cd0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5da4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5da8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5db4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5dc0 100 .cfa: sp 0 + .ra: x30
STACK CFI 5dc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5dcc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5de0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5ec0 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 5ec4 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 5edc x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 5ee8 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 5ef0 x23: .cfa -240 + ^
STACK CFI 6098 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 609c .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x29: .cfa -288 + ^
STACK CFI INIT 55e0 98 .cfa: sp 0 + .ra: x30
STACK CFI 55e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 55f4 x19: .cfa -16 + ^
STACK CFI 563c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5640 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5680 194 .cfa: sp 0 + .ra: x30
STACK CFI 5684 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 568c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 56a0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 56ac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 56b8 x25: .cfa -16 + ^
STACK CFI 5778 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 577c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 57f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 57f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 61b0 78 .cfa: sp 0 + .ra: x30
STACK CFI 61b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 61c4 x19: .cfa -16 + ^
STACK CFI 61f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 61fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 620c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6218 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6230 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 6234 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 623c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6244 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6260 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6328 x25: .cfa -16 + ^
STACK CFI 639c x25: x25
STACK CFI 64c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 64c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 64d8 x25: .cfa -16 + ^
STACK CFI 6500 x25: x25
STACK CFI 6510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 6520 2fc .cfa: sp 0 + .ra: x30
STACK CFI 6524 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 652c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6534 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6550 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6658 x25: .cfa -16 + ^
STACK CFI 66dc x25: x25
STACK CFI 67c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 67cc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 67e0 x25: .cfa -16 + ^
STACK CFI 6808 x25: x25
STACK CFI 6818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 6820 2fc .cfa: sp 0 + .ra: x30
STACK CFI 6824 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 682c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6834 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6850 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6958 x25: .cfa -16 + ^
STACK CFI 69dc x25: x25
STACK CFI 6ac8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6acc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 6ae0 x25: .cfa -16 + ^
STACK CFI 6b08 x25: x25
STACK CFI 6b18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 6b20 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 6b24 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6b2c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6b34 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6b50 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6c58 x25: .cfa -16 + ^
STACK CFI 6cdc x25: x25
STACK CFI 6dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6dd8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 6dec x25: .cfa -16 + ^
STACK CFI INIT 6e20 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 6e24 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6e2c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6e34 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6e50 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6f58 x25: .cfa -16 + ^
STACK CFI 6fdc x25: x25
STACK CFI 70d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 70d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 70ec x25: .cfa -16 + ^
STACK CFI INIT 7120 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 7124 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 712c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7134 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7150 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7218 x25: .cfa -16 + ^
STACK CFI 728c x25: x25
STACK CFI 73b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 73b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 73c8 x25: .cfa -16 + ^
STACK CFI 73f0 x25: x25
STACK CFI 7400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 7410 2dc .cfa: sp 0 + .ra: x30
STACK CFI 7414 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 741c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7424 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7440 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7508 x25: .cfa -16 + ^
STACK CFI 757c x25: x25
STACK CFI 76ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 76b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 76c4 x25: .cfa -16 + ^
STACK CFI INIT 76f0 2dc .cfa: sp 0 + .ra: x30
STACK CFI 76f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 76fc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7704 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7720 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 77e8 x25: .cfa -16 + ^
STACK CFI 785c x25: x25
STACK CFI 798c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7990 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 79a4 x25: .cfa -16 + ^
STACK CFI INIT 79d0 128 .cfa: sp 0 + .ra: x30
STACK CFI 79d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 79e8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 79f8 x21: .cfa -16 + ^
STACK CFI 7a84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7a88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7b00 14ac .cfa: sp 0 + .ra: x30
STACK CFI 7b04 .cfa: sp 1104 +
STACK CFI 7b10 .ra: .cfa -1096 + ^ x29: .cfa -1104 + ^
STACK CFI 7b18 x19: .cfa -1088 + ^ x20: .cfa -1080 + ^
STACK CFI 7b28 x21: .cfa -1072 + ^ x22: .cfa -1064 + ^ x23: .cfa -1056 + ^ x24: .cfa -1048 + ^
STACK CFI 7b30 x25: .cfa -1040 + ^ x26: .cfa -1032 + ^
STACK CFI 7b38 x27: .cfa -1024 + ^ x28: .cfa -1016 + ^
STACK CFI 8344 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8348 .cfa: sp 1104 + .ra: .cfa -1096 + ^ x19: .cfa -1088 + ^ x20: .cfa -1080 + ^ x21: .cfa -1072 + ^ x22: .cfa -1064 + ^ x23: .cfa -1056 + ^ x24: .cfa -1048 + ^ x25: .cfa -1040 + ^ x26: .cfa -1032 + ^ x27: .cfa -1024 + ^ x28: .cfa -1016 + ^ x29: .cfa -1104 + ^
STACK CFI INIT 8fb0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 8fb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8fbc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8fc4 x21: .cfa -16 + ^
STACK CFI 906c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9070 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 907c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5000 3b4 .cfa: sp 0 + .ra: x30
STACK CFI 5004 .cfa: sp 528 +
STACK CFI 5010 .ra: .cfa -520 + ^ x29: .cfa -528 + ^
STACK CFI 5018 x19: .cfa -512 + ^ x20: .cfa -504 + ^
STACK CFI 5034 x23: .cfa -480 + ^ x24: .cfa -472 + ^
STACK CFI 503c x21: .cfa -496 + ^ x22: .cfa -488 + ^
STACK CFI 5058 x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI 5100 x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI 5244 x25: x25 x26: x26
STACK CFI 525c x21: x21 x22: x22
STACK CFI 5260 x23: x23 x24: x24
STACK CFI 5264 x27: x27 x28: x28
STACK CFI 5290 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5294 .cfa: sp 528 + .ra: .cfa -520 + ^ x19: .cfa -512 + ^ x20: .cfa -504 + ^ x21: .cfa -496 + ^ x22: .cfa -488 + ^ x23: .cfa -480 + ^ x24: .cfa -472 + ^ x27: .cfa -448 + ^ x28: .cfa -440 + ^ x29: .cfa -528 + ^
STACK CFI 52a0 x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI 52dc x25: x25 x26: x26
STACK CFI 531c x27: x27 x28: x28
STACK CFI 5320 x21: x21 x22: x22
STACK CFI 5324 x23: x23 x24: x24
STACK CFI 5334 x21: .cfa -496 + ^ x22: .cfa -488 + ^
STACK CFI 5338 x23: .cfa -480 + ^ x24: .cfa -472 + ^
STACK CFI 533c x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI 5340 x27: .cfa -448 + ^ x28: .cfa -440 + ^
STACK CFI 5344 x25: x25 x26: x26
STACK CFI 5374 x25: .cfa -464 + ^ x26: .cfa -456 + ^
STACK CFI 53b0 x25: x25 x26: x26
STACK CFI INIT 9080 ac .cfa: sp 0 + .ra: x30
STACK CFI 9084 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 908c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9094 x21: .cfa -16 + ^
STACK CFI 9128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9130 488 .cfa: sp 0 + .ra: x30
STACK CFI 9134 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 913c x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 9150 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 92d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 92d4 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x29: .cfa -256 + ^
STACK CFI 92e0 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 92e4 x25: x25 x26: x26
STACK CFI 9348 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 93e8 x25: x25 x26: x26
STACK CFI 9448 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 94f4 x25: x25 x26: x26
STACK CFI 9528 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 9544 x25: x25 x26: x26
STACK CFI 955c x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 957c x25: x25 x26: x26
STACK CFI 9590 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 95a0 x25: x25 x26: x26
STACK CFI INIT 95c0 c44 .cfa: sp 0 + .ra: x30
STACK CFI 95c4 .cfa: sp 1184 +
STACK CFI 95d0 .ra: .cfa -1176 + ^ x29: .cfa -1184 + ^
STACK CFI 95d8 x19: .cfa -1168 + ^ x20: .cfa -1160 + ^
STACK CFI 95e0 x21: .cfa -1152 + ^ x22: .cfa -1144 + ^
STACK CFI 95e8 x23: .cfa -1136 + ^ x24: .cfa -1128 + ^
STACK CFI 95f4 x25: .cfa -1120 + ^ x26: .cfa -1112 + ^ x27: .cfa -1104 + ^ x28: .cfa -1096 + ^
STACK CFI 9ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9ca4 .cfa: sp 1184 + .ra: .cfa -1176 + ^ x19: .cfa -1168 + ^ x20: .cfa -1160 + ^ x21: .cfa -1152 + ^ x22: .cfa -1144 + ^ x23: .cfa -1136 + ^ x24: .cfa -1128 + ^ x25: .cfa -1120 + ^ x26: .cfa -1112 + ^ x27: .cfa -1104 + ^ x28: .cfa -1096 + ^ x29: .cfa -1184 + ^
STACK CFI INIT a210 131c .cfa: sp 0 + .ra: x30
STACK CFI a214 .cfa: sp 1216 +
STACK CFI a220 .ra: .cfa -1208 + ^ x29: .cfa -1216 + ^
STACK CFI a228 x19: .cfa -1200 + ^ x20: .cfa -1192 + ^
STACK CFI a230 x21: .cfa -1184 + ^ x22: .cfa -1176 + ^
STACK CFI a264 x23: .cfa -1168 + ^ x24: .cfa -1160 + ^ x27: .cfa -1136 + ^ x28: .cfa -1128 + ^
STACK CFI a390 x25: .cfa -1152 + ^ x26: .cfa -1144 + ^
STACK CFI aadc x25: x25 x26: x26
STACK CFI aae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI aae8 .cfa: sp 1216 + .ra: .cfa -1208 + ^ x19: .cfa -1200 + ^ x20: .cfa -1192 + ^ x21: .cfa -1184 + ^ x22: .cfa -1176 + ^ x23: .cfa -1168 + ^ x24: .cfa -1160 + ^ x25: .cfa -1152 + ^ x26: .cfa -1144 + ^ x27: .cfa -1136 + ^ x28: .cfa -1128 + ^ x29: .cfa -1216 + ^
STACK CFI ada0 x25: x25 x26: x26
STACK CFI add4 x25: .cfa -1152 + ^ x26: .cfa -1144 + ^
STACK CFI b480 x25: x25 x26: x26
STACK CFI b514 x25: .cfa -1152 + ^ x26: .cfa -1144 + ^
STACK CFI INIT b530 12c .cfa: sp 0 + .ra: x30
STACK CFI b534 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b540 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b548 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b5ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b5f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b660 1928 .cfa: sp 0 + .ra: x30
STACK CFI b664 .cfa: sp 1296 +
STACK CFI b670 .ra: .cfa -1288 + ^ x29: .cfa -1296 + ^
STACK CFI b678 x19: .cfa -1280 + ^ x20: .cfa -1272 + ^
STACK CFI b680 x21: .cfa -1264 + ^ x22: .cfa -1256 + ^
STACK CFI b6b4 x23: .cfa -1248 + ^ x24: .cfa -1240 + ^ x25: .cfa -1232 + ^ x26: .cfa -1224 + ^
STACK CFI ba40 x27: .cfa -1216 + ^ x28: .cfa -1208 + ^
STACK CFI bd68 x27: x27 x28: x28
STACK CFI c094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI c098 .cfa: sp 1296 + .ra: .cfa -1288 + ^ x19: .cfa -1280 + ^ x20: .cfa -1272 + ^ x21: .cfa -1264 + ^ x22: .cfa -1256 + ^ x23: .cfa -1248 + ^ x24: .cfa -1240 + ^ x25: .cfa -1232 + ^ x26: .cfa -1224 + ^ x27: .cfa -1216 + ^ x28: .cfa -1208 + ^ x29: .cfa -1296 + ^
STACK CFI c2b8 x27: x27 x28: x28
STACK CFI c2bc x27: .cfa -1216 + ^ x28: .cfa -1208 + ^
STACK CFI c31c x27: x27 x28: x28
STACK CFI c440 x27: .cfa -1216 + ^ x28: .cfa -1208 + ^
STACK CFI c564 x27: x27 x28: x28
STACK CFI c59c x27: .cfa -1216 + ^ x28: .cfa -1208 + ^
STACK CFI c61c x27: x27 x28: x28
STACK CFI ca68 x27: .cfa -1216 + ^ x28: .cfa -1208 + ^
STACK CFI cbb4 x27: x27 x28: x28
STACK CFI cc00 x27: .cfa -1216 + ^ x28: .cfa -1208 + ^
STACK CFI cc0c x27: x27 x28: x28
STACK CFI cc88 x27: .cfa -1216 + ^ x28: .cfa -1208 + ^
STACK CFI INIT 53c0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 53d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 53e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 541c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5420 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT cf90 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5480 24 .cfa: sp 0 + .ra: x30
STACK CFI 5484 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 549c .cfa: sp 0 + .ra: .ra x29: x29
