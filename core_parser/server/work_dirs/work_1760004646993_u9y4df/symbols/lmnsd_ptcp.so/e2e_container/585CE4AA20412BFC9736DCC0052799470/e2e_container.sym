MODULE Linux arm64 585CE4AA20412BFC9736DCC0052799470 e2e_container
INFO CODE_ID AAE45C584120FC2B9736DCC005279947
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 45d0 24 0 init_have_lse_atomics
45d0 4 45 0
45d4 4 46 0
45d8 4 45 0
45dc 4 46 0
45e0 4 47 0
45e4 4 47 0
45e8 4 48 0
45ec 4 47 0
45f0 4 48 0
PUBLIC 3b20 0 _init
PUBLIC 3f00 0 main
PUBLIC 4600 0 _start
PUBLIC 4634 0 call_weak_fn
PUBLIC 4650 0 deregister_tm_clones
PUBLIC 4680 0 register_tm_clones
PUBLIC 46c0 0 __do_global_dtors_aux
PUBLIC 4710 0 frame_dummy
PUBLIC 4720 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&) [clone .constprop.0]
PUBLIC 4830 0 std::_Sp_counted_ptr<decltype(nullptr), (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 4840 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 4850 0 std::filesystem::__cxx11::path::~path()
PUBLIC 48a0 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release_last_use_cold()
PUBLIC 4960 0 lios::app::ModuleLoader::~ModuleLoader()
PUBLIC 4bb0 0 std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::~vector()
PUBLIC 4c40 0 std::vector<lios::config::settings::AppConfig::ModuleConfig, std::allocator<lios::config::settings::AppConfig::ModuleConfig> >::~vector()
PUBLIC 4d10 0 std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::clear()
PUBLIC 4dc0 0 std::vector<lios::config::settings::RpcConfig, std::allocator<lios::config::settings::RpcConfig> >::~vector()
PUBLIC 4f00 0 std::vector<lios::config::settings::IpcConfig, std::allocator<lios::config::settings::IpcConfig> >::~vector()
PUBLIC 5080 0 std::vector<lios::config::settings::NodeConfig, std::allocator<lios::config::settings::NodeConfig> >::~vector()
PUBLIC 5440 0 std::vector<lios::config::settings::DagGraphConfig, std::allocator<lios::config::settings::DagGraphConfig> >::~vector()
PUBLIC 6430 0 lios::config::settings::AppConfig::~AppConfig()
PUBLIC 6b60 0 lios::app::NodeLoader::~NodeLoader()
PUBLIC 6b90 0 lios::config::parser::AppConfigCenter::~AppConfigCenter()
PUBLIC 6e00 0 lios::config::parser::AppConfigCenter::~AppConfigCenter()
PUBLIC 7080 0 std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_deallocate_buckets()
PUBLIC 70a0 0 std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC 71d0 0 std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_Hashtable<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > const*>(std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > const*, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > const*, unsigned long, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > const&, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > const&, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&, std::integral_constant<bool, true>)
PUBLIC 7720 0 lios::config::settings::GlobalConfig::GlobalConfig()
PUBLIC 8440 0 lios::config::parser::AppConfigCenter::Instance()
PUBLIC 88d0 0 __aarch64_ldadd4_acq_rel
PUBLIC 8900 0 _fini
STACK CFI INIT 4600 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4650 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4680 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 46c0 48 .cfa: sp 0 + .ra: x30
STACK CFI 46c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 46cc x19: .cfa -16 + ^
STACK CFI 4704 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4710 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4830 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4840 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4720 104 .cfa: sp 0 + .ra: x30
STACK CFI 4724 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4734 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 473c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 47b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 47bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4850 50 .cfa: sp 0 + .ra: x30
STACK CFI 4854 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4860 x19: .cfa -16 + ^
STACK CFI 4890 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4894 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 489c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 48a0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 48a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 48bc x19: .cfa -16 + ^
STACK CFI 48f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 48f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 491c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4928 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4950 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4960 24c .cfa: sp 0 + .ra: x30
STACK CFI 4964 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 496c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4978 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4984 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4994 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 49a0 x27: .cfa -16 + ^
STACK CFI 4a2c x23: x23 x24: x24
STACK CFI 4a30 x25: x25 x26: x26
STACK CFI 4a34 x27: x27
STACK CFI 4b1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4b20 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 4b34 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 4b9c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 4ba8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4bb0 90 .cfa: sp 0 + .ra: x30
STACK CFI 4bb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4bbc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4bc4 x21: .cfa -16 + ^
STACK CFI 4c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4c1c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4c40 c8 .cfa: sp 0 + .ra: x30
STACK CFI 4c44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c4c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4c54 x21: .cfa -16 + ^
STACK CFI 4ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4ce4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4d10 ac .cfa: sp 0 + .ra: x30
STACK CFI 4d14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4d1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4d24 x21: .cfa -16 + ^
STACK CFI 4db8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4dc0 13c .cfa: sp 0 + .ra: x30
STACK CFI 4dc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4dcc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4dd4 x21: .cfa -16 + ^
STACK CFI 4ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4ed8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4ef8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4f00 174 .cfa: sp 0 + .ra: x30
STACK CFI 4f04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4f0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4f14 x21: .cfa -16 + ^
STACK CFI 504c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5050 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5070 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5080 3b4 .cfa: sp 0 + .ra: x30
STACK CFI 5084 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5090 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 50a4 x23: .cfa -16 + ^
STACK CFI 53b0 x23: x23
STACK CFI 53cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 53d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5420 x23: x23
STACK CFI 5430 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5440 fe8 .cfa: sp 0 + .ra: x30
STACK CFI 5444 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 544c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5460 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5464 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5468 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 546c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 62c0 x19: x19 x20: x20
STACK CFI 62c4 x23: x23 x24: x24
STACK CFI 62c8 x25: x25 x26: x26
STACK CFI 62cc x27: x27 x28: x28
STACK CFI 62ec .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 62f0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 6408 x19: x19 x20: x20
STACK CFI 6410 x23: x23 x24: x24
STACK CFI 6414 x25: x25 x26: x26
STACK CFI 6418 x27: x27 x28: x28
STACK CFI 6424 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 6430 724 .cfa: sp 0 + .ra: x30
STACK CFI 6434 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6440 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 644c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6ab4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6ab8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6b50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6b60 30 .cfa: sp 0 + .ra: x30
STACK CFI 6b64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6b6c x19: .cfa -16 + ^
STACK CFI 6b8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6b90 270 .cfa: sp 0 + .ra: x30
STACK CFI 6b94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6ba4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6bb0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6de8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6dec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6e00 27c .cfa: sp 0 + .ra: x30
STACK CFI 6e04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6e14 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6e20 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7064 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7068 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7080 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 70a0 12c .cfa: sp 0 + .ra: x30
STACK CFI 70a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 70b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 70b8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 715c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7160 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 71d0 54c .cfa: sp 0 + .ra: x30
STACK CFI 71d4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 71ec x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 71f8 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 7208 x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 729c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 72fc x27: x27 x28: x28
STACK CFI 732c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7330 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 75b8 x27: x27 x28: x28
STACK CFI 760c x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 76e0 x27: x27 x28: x28
STACK CFI 7708 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 7720 d14 .cfa: sp 0 + .ra: x30
STACK CFI 7724 .cfa: sp 576 +
STACK CFI 7730 .ra: .cfa -568 + ^ x29: .cfa -576 + ^
STACK CFI 773c x19: .cfa -560 + ^ x20: .cfa -552 + ^
STACK CFI 7758 x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^
STACK CFI 7764 x25: .cfa -512 + ^ x26: .cfa -504 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^
STACK CFI 8064 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8068 .cfa: sp 576 + .ra: .cfa -568 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^ x25: .cfa -512 + ^ x26: .cfa -504 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^ x29: .cfa -576 + ^
STACK CFI INIT 8440 488 .cfa: sp 0 + .ra: x30
STACK CFI 8444 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 846c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 84a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 84a4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x29: .cfa -160 + ^
STACK CFI 84b4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 84b8 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 84bc x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 84c0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 8774 x21: x21 x22: x22
STACK CFI 8778 x23: x23 x24: x24
STACK CFI 877c x25: x25 x26: x26
STACK CFI 8780 x27: x27 x28: x28
STACK CFI 8788 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 878c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 8790 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 8794 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 3f00 6cc .cfa: sp 0 + .ra: x30
STACK CFI 3f04 .cfa: sp 1088 +
STACK CFI 3f14 .ra: .cfa -1080 + ^ x29: .cfa -1088 + ^
STACK CFI 3f2c x19: .cfa -1072 + ^ x20: .cfa -1064 + ^ x27: .cfa -1008 + ^ x28: .cfa -1000 + ^
STACK CFI 3f38 x23: .cfa -1040 + ^ x24: .cfa -1032 + ^
STACK CFI 3f44 x21: .cfa -1056 + ^ x22: .cfa -1048 + ^
STACK CFI 3fec x21: x21 x22: x22
STACK CFI 3ff0 x23: x23 x24: x24
STACK CFI 4020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 4024 .cfa: sp 1088 + .ra: .cfa -1080 + ^ x19: .cfa -1072 + ^ x20: .cfa -1064 + ^ x21: .cfa -1056 + ^ x22: .cfa -1048 + ^ x23: .cfa -1040 + ^ x24: .cfa -1032 + ^ x27: .cfa -1008 + ^ x28: .cfa -1000 + ^ x29: .cfa -1088 + ^
STACK CFI 4080 x25: .cfa -1024 + ^ x26: .cfa -1016 + ^
STACK CFI 4310 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 4330 x21: .cfa -1056 + ^ x22: .cfa -1048 + ^ x23: .cfa -1040 + ^ x24: .cfa -1032 + ^
STACK CFI 433c x25: .cfa -1024 + ^ x26: .cfa -1016 + ^
STACK CFI 43b0 x25: x25 x26: x26
STACK CFI 43b4 x25: .cfa -1024 + ^ x26: .cfa -1016 + ^
STACK CFI 440c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 4410 x21: .cfa -1056 + ^ x22: .cfa -1048 + ^
STACK CFI 4414 x23: .cfa -1040 + ^ x24: .cfa -1032 + ^
STACK CFI 4418 x25: .cfa -1024 + ^ x26: .cfa -1016 + ^
STACK CFI 445c x25: x25 x26: x26
STACK CFI 44a4 x25: .cfa -1024 + ^ x26: .cfa -1016 + ^
STACK CFI 44f8 x25: x25 x26: x26
STACK CFI 451c x25: .cfa -1024 + ^ x26: .cfa -1016 + ^
STACK CFI 4534 x25: x25 x26: x26
STACK CFI 4538 x25: .cfa -1024 + ^ x26: .cfa -1016 + ^
STACK CFI 4560 x25: x25 x26: x26
STACK CFI 4564 x25: .cfa -1024 + ^ x26: .cfa -1016 + ^
STACK CFI 4580 x25: x25 x26: x26
STACK CFI INIT 88d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45d0 24 .cfa: sp 0 + .ra: x30
STACK CFI 45d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 45ec .cfa: sp 0 + .ra: .ra x29: x29
