MODULE Linux arm64 752C18259CE22693A1B88C1A35A7BDC20 app_control
INFO CODE_ID 25182C75E29C9326A1B88C1A35A7BDC2
PUBLIC 1588 0 _init
PUBLIC 1700 0 main
PUBLIC 1880 0 _start
PUBLIC 18b4 0 call_weak_fn
PUBLIC 18d0 0 deregister_tm_clones
PUBLIC 1900 0 register_tm_clones
PUBLIC 1940 0 __do_global_dtors_aux
PUBLIC 1990 0 frame_dummy
PUBLIC 19a0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::assign(char const*) [clone .isra.0]
PUBLIC 19e0 0 std::basic_ostream<char, std::char_traits<char> >& std::endl<char, std::char_traits<char> >(std::basic_ostream<char, std::char_traits<char> >&) [clone .isra.0]
PUBLIC 1a50 0 help()
PUBLIC 1b00 0 bool std::operator==<char, std::char_traits<char>, std::allocator<char> >(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, char const*)
PUBLIC 1b70 0 _fini
STACK CFI INIT 1880 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1900 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1940 48 .cfa: sp 0 + .ra: x30
STACK CFI 1944 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 194c x19: .cfa -16 + ^
STACK CFI 1984 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1990 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19a0 3c .cfa: sp 0 + .ra: x30
STACK CFI 19a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 19d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 19e0 6c .cfa: sp 0 + .ra: x30
STACK CFI 19e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19ec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1a50 a8 .cfa: sp 0 + .ra: x30
STACK CFI 1a54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a64 x19: .cfa -16 + ^
STACK CFI 1af4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1b00 70 .cfa: sp 0 + .ra: x30
STACK CFI 1b04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1b0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1b1c x21: .cfa -16 + ^
STACK CFI 1b3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1b40 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1b6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1700 180 .cfa: sp 0 + .ra: x30
STACK CFI 1704 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1718 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 172c x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^
STACK CFI 1820 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1824 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x29: .cfa -144 + ^
