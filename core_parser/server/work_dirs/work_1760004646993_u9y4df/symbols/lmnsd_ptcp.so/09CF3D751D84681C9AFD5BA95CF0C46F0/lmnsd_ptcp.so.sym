MODULE Linux arm64 09CF3D751D84681C9AFD5BA95CF0C46F0 lmnsd_ptcp.so
INFO CODE_ID 753DCF09841D1C689AFD5BA95CF0C46F2DBA0325
PUBLIC 1a54 0 nsd_ptcpQueryInterface
PUBLIC 1bf0 0 nsdsel_ptcpQueryInterface
PUBLIC 1c60 0 nsdpoll_ptcpQueryInterface
PUBLIC 1cc0 0 nsd_ptcpConstruct
PUBLIC 1d20 0 nsdsel_ptcpConstruct
PUBLIC 1d94 0 nsdsel_ptcpDestruct
PUBLIC 1de4 0 nsd_ptcpDestruct
PUBLIC 3890 0 nsdpoll_ptcpConstruct
PUBLIC 3994 0 nsdpoll_ptcpDestruct
PUBLIC 3e70 0 nsd_ptcpClassExit
PUBLIC 3f50 0 nsd_ptcpClassInit
PUBLIC 40a0 0 nsdsel_ptcpClassExit
PUBLIC 4100 0 nsdsel_ptcpClassInit
PUBLIC 41e0 0 nsdpoll_ptcpClassExit
PUBLIC 4270 0 nsdpoll_ptcpClassInit
PUBLIC 4350 0 modInit
STACK CFI INIT 17d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1800 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1840 48 .cfa: sp 0 + .ra: x30
STACK CFI 1844 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 184c x19: .cfa -16 + ^
STACK CFI 1884 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1890 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18a0 28 .cfa: sp 0 + .ra: x30
STACK CFI 18ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 18c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18d0 24 .cfa: sp 0 + .ra: x30
STACK CFI 18dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 18ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18f4 24 .cfa: sp 0 + .ra: x30
STACK CFI 18fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1910 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1920 2c .cfa: sp 0 + .ra: x30
STACK CFI 1930 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1940 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1950 1c .cfa: sp 0 + .ra: x30
STACK CFI 1958 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1964 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1970 24 .cfa: sp 0 + .ra: x30
STACK CFI 197c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 198c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1994 24 .cfa: sp 0 + .ra: x30
STACK CFI 19a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 19b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 19c0 24 .cfa: sp 0 + .ra: x30
STACK CFI 19cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 19dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 19e4 24 .cfa: sp 0 + .ra: x30
STACK CFI 19f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a10 44 .cfa: sp 0 + .ra: x30
STACK CFI 1a18 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1a4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1a54 168 .cfa: sp 0 + .ra: x30
STACK CFI 1a70 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1bac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1bc0 2c .cfa: sp 0 + .ra: x30
STACK CFI 1bcc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1be4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1bf0 68 .cfa: sp 0 + .ra: x30
STACK CFI 1bf8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1c48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c50 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1c60 5c .cfa: sp 0 + .ra: x30
STACK CFI 1c68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1cac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1cb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1cc0 5c .cfa: sp 0 + .ra: x30
STACK CFI 1cc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1cd4 x19: .cfa -16 + ^
STACK CFI 1d0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1d14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1d20 74 .cfa: sp 0 + .ra: x30
STACK CFI 1d28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1d84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1d8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1d94 50 .cfa: sp 0 + .ra: x30
STACK CFI 1d9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1da4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1de4 88 .cfa: sp 0 + .ra: x30
STACK CFI 1dec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1df4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1dfc x21: .cfa -16 + ^
STACK CFI 1e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1e70 44 .cfa: sp 0 + .ra: x30
STACK CFI 1e88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ea8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1eb4 44 .cfa: sp 0 + .ra: x30
STACK CFI 1ecc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1eec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1f00 44 .cfa: sp 0 + .ra: x30
STACK CFI 1f18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1f38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1f44 3c .cfa: sp 0 + .ra: x30
STACK CFI 1f5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1f74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1f80 40 .cfa: sp 0 + .ra: x30
STACK CFI 1f98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1fb4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1fc0 40 .cfa: sp 0 + .ra: x30
STACK CFI 1fd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ff4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2000 3c .cfa: sp 0 + .ra: x30
STACK CFI 2018 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2030 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2040 3c .cfa: sp 0 + .ra: x30
STACK CFI 2058 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2070 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2080 40 .cfa: sp 0 + .ra: x30
STACK CFI 2098 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 20b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 20c0 3c .cfa: sp 0 + .ra: x30
STACK CFI 20d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 20f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2100 68 .cfa: sp 0 + .ra: x30
STACK CFI 2114 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 211c x19: .cfa -16 + ^
STACK CFI 213c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2148 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2170 ac .cfa: sp 0 + .ra: x30
STACK CFI 2178 .cfa: sp 32 +
STACK CFI 2184 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 21c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 21d0 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2220 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 2228 .cfa: sp 48 +
STACK CFI 2238 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2240 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 22dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 22e4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 23d4 2fc .cfa: sp 0 + .ra: x30
STACK CFI 23dc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 23ec .cfa: sp 1280 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2434 x23: .cfa -48 + ^
STACK CFI 2444 x24: .cfa -40 + ^
STACK CFI 246c x23: x23
STACK CFI 2470 x24: x24
STACK CFI 2490 .cfa: sp 96 +
STACK CFI 24a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 24a8 .cfa: sp 1280 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 24ac x25: .cfa -32 + ^
STACK CFI 24b4 x26: .cfa -24 + ^
STACK CFI 24bc x27: .cfa -16 + ^
STACK CFI 25b4 x23: x23
STACK CFI 25b8 x24: x24
STACK CFI 25bc x25: x25
STACK CFI 25c0 x26: x26
STACK CFI 25c4 x27: x27
STACK CFI 25f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2604 x25: x25
STACK CFI 2608 x26: x26
STACK CFI 260c x27: x27
STACK CFI 2610 x23: x23 x24: x24
STACK CFI 2650 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2684 x25: x25
STACK CFI 268c x26: x26
STACK CFI 2690 x27: x27
STACK CFI 2694 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 26a8 x25: x25
STACK CFI 26b0 x26: x26
STACK CFI 26b4 x27: x27
STACK CFI 26b8 x23: x23 x24: x24
STACK CFI 26bc x23: .cfa -48 + ^
STACK CFI 26c0 x24: .cfa -40 + ^
STACK CFI 26c4 x25: .cfa -32 + ^
STACK CFI 26c8 x26: .cfa -24 + ^
STACK CFI 26cc x27: .cfa -16 + ^
STACK CFI INIT 26d0 8a0 .cfa: sp 0 + .ra: x30
STACK CFI 26d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 26f0 .cfa: sp 1280 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 27b4 x27: .cfa -16 + ^
STACK CFI 27b8 x28: .cfa -8 + ^
STACK CFI 2c48 x27: x27
STACK CFI 2c4c x28: x28
STACK CFI 2c54 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2cd8 x27: x27
STACK CFI 2cdc x28: x28
STACK CFI 2d18 .cfa: sp 96 +
STACK CFI 2d2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2d34 .cfa: sp 1280 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2d7c x27: x27
STACK CFI 2d80 x28: x28
STACK CFI 2d9c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2e38 x27: x27 x28: x28
STACK CFI 2e60 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2e64 x27: x27
STACK CFI 2e68 x28: x28
STACK CFI 2e6c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2e94 x27: x27
STACK CFI 2e98 x28: x28
STACK CFI 2eec x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2f4c x27: x27
STACK CFI 2f54 x28: x28
STACK CFI 2f60 x27: .cfa -16 + ^
STACK CFI 2f64 x28: .cfa -8 + ^
STACK CFI INIT 2f70 c8 .cfa: sp 0 + .ra: x30
STACK CFI 2f78 .cfa: sp 48 +
STACK CFI 2f88 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f98 x19: .cfa -16 + ^
STACK CFI 2fe4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2fec .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3040 e8 .cfa: sp 0 + .ra: x30
STACK CFI 3048 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3058 .cfa: sp 1088 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 30d0 .cfa: sp 48 +
STACK CFI 30dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 30e4 .cfa: sp 1088 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3130 70 .cfa: sp 0 + .ra: x30
STACK CFI 3138 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3144 x19: .cfa -16 + ^
STACK CFI 3170 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3178 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 31a0 268 .cfa: sp 0 + .ra: x30
STACK CFI 31a8 .cfa: sp 144 +
STACK CFI 31b8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 31c0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 31c8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 31d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3220 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 32d8 x25: x25 x26: x26
STACK CFI 330c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3314 .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3318 x25: x25 x26: x26
STACK CFI 3360 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3390 x25: x25 x26: x26
STACK CFI 3394 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 33c0 x25: x25 x26: x26
STACK CFI 33c4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 33e4 x25: x25 x26: x26
STACK CFI 33e8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 33f8 x25: x25 x26: x26
STACK CFI 3404 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 3410 4c .cfa: sp 0 + .ra: x30
STACK CFI 3418 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3424 x19: .cfa -16 + ^
STACK CFI 3454 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3460 e4 .cfa: sp 0 + .ra: x30
STACK CFI 3478 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3480 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 34b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 34bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 353c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3544 ec .cfa: sp 0 + .ra: x30
STACK CFI 354c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3554 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3560 x21: .cfa -16 + ^
STACK CFI 35cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 35d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3630 120 .cfa: sp 0 + .ra: x30
STACK CFI 3638 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 364c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3654 x21: .cfa -16 + ^
STACK CFI 36bc x19: x19 x20: x20
STACK CFI 36c4 x21: x21
STACK CFI 36c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 36d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 370c x19: x19 x20: x20
STACK CFI 3710 x21: x21
STACK CFI 3730 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 3750 138 .cfa: sp 0 + .ra: x30
STACK CFI 3758 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3760 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3770 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 37a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 37ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 37d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 37e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 37e4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 384c x23: x23 x24: x24
STACK CFI INIT 3890 104 .cfa: sp 0 + .ra: x30
STACK CFI 3898 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 38a4 x21: .cfa -16 + ^
STACK CFI 38b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3900 x19: x19 x20: x20
STACK CFI 3908 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 3910 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 398c x19: x19 x20: x20
STACK CFI INIT 3994 a0 .cfa: sp 0 + .ra: x30
STACK CFI 399c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 39a8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 39bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 39f8 x21: x21 x22: x22
STACK CFI 3a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 3a34 298 .cfa: sp 0 + .ra: x30
STACK CFI 3a3c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3a4c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3a54 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3a64 .cfa: sp 608 + x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3a8c x25: .cfa -16 + ^
STACK CFI 3b20 x25: x25
STACK CFI 3b44 .cfa: sp 80 +
STACK CFI 3b54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3b5c .cfa: sp 608 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 3b6c x25: x25
STACK CFI 3b9c x25: .cfa -16 + ^
STACK CFI 3c80 x25: x25
STACK CFI 3ca8 x25: .cfa -16 + ^
STACK CFI 3cb4 x25: x25
STACK CFI 3cc0 x25: .cfa -16 + ^
STACK CFI 3cc4 x25: x25
STACK CFI INIT 3cd0 19c .cfa: sp 0 + .ra: x30
STACK CFI 3cd8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3cec .cfa: sp 2128 + x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 3dcc .cfa: sp 64 +
STACK CFI 3ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3de4 .cfa: sp 2128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3e70 d8 .cfa: sp 0 + .ra: x30
STACK CFI 3e78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e84 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e90 x21: .cfa -16 + ^
STACK CFI 3f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3f50 14c .cfa: sp 0 + .ra: x30
STACK CFI 3f58 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3f64 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3f7c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3fb0 x23: .cfa -16 + ^
STACK CFI 4060 x23: x23
STACK CFI 407c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4084 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4088 x23: x23
STACK CFI 4094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 40a0 5c .cfa: sp 0 + .ra: x30
STACK CFI 40a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 40bc x19: .cfa -16 + ^
STACK CFI 40f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4100 dc .cfa: sp 0 + .ra: x30
STACK CFI 4108 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4110 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 411c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4134 x23: .cfa -16 + ^
STACK CFI 41ac x23: x23
STACK CFI 41bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 41c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 41c8 x23: x23
STACK CFI 41d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 41e0 5c .cfa: sp 0 + .ra: x30
STACK CFI 41e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41fc x19: .cfa -16 + ^
STACK CFI 4230 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4240 28 .cfa: sp 0 + .ra: x30
STACK CFI 4248 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 425c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4270 dc .cfa: sp 0 + .ra: x30
STACK CFI 4278 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4280 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 428c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 42a4 x23: .cfa -16 + ^
STACK CFI 431c x23: x23
STACK CFI 432c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4334 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4338 x23: x23
STACK CFI 4344 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4350 fc .cfa: sp 0 + .ra: x30
STACK CFI 4358 .cfa: sp 64 +
STACK CFI 4364 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 436c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4378 x21: .cfa -16 + ^
STACK CFI 4410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4418 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
