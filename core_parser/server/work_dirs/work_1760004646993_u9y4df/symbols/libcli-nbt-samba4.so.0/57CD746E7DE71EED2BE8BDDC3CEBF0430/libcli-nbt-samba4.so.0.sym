MODULE Linux arm64 57CD746E7DE71EED2BE8BDDC3CEBF0430 libcli-nbt-samba4.so.0
INFO CODE_ID 6E74CD57E77DED1E2BE8BDDC3CEBF043F1563960
PUBLIC 2a04 0 nbt_name_socket_handle_response_packet
PUBLIC 3210 0 nbt_name_socket_init
PUBLIC 3310 0 nbt_name_request_send
PUBLIC 35c0 0 nbt_name_reply_send
PUBLIC 3780 0 nbt_name_request_recv
PUBLIC 3810 0 nbt_set_incoming_handler
PUBLIC 3854 0 nbt_set_unexpected_handler
PUBLIC 38a0 0 nbt_rcode_to_ntstatus
PUBLIC 3960 0 nbt_name_query_send
PUBLIC 3a90 0 nbt_name_query_recv
PUBLIC 3ca4 0 nbt_name_query
PUBLIC 3ce0 0 nbt_name_status_send
PUBLIC 3df0 0 nbt_name_status_recv
PUBLIC 3fa4 0 nbt_name_status
PUBLIC 3fe0 0 nbt_name_register_send
PUBLIC 41d0 0 nbt_name_register_recv
PUBLIC 4694 0 nbt_name_register
PUBLIC 46d0 0 nbt_name_register_bcast_send
PUBLIC 4810 0 nbt_name_register_bcast_recv
PUBLIC 48a0 0 nbt_name_register_bcast
PUBLIC 49b0 0 nbt_name_register_wins_send
PUBLIC 4c00 0 nbt_name_register_wins_recv
PUBLIC 4cd0 0 nbt_name_register_wins
PUBLIC 4e00 0 nbt_name_refresh_send
PUBLIC 4fb0 0 nbt_name_refresh_recv
PUBLIC 52d4 0 nbt_name_refresh
PUBLIC 5310 0 nbt_name_refresh_wins_send
PUBLIC 5570 0 nbt_name_refresh_wins_recv
PUBLIC 5644 0 nbt_name_refresh_wins
PUBLIC 5770 0 nbt_name_release_send
PUBLIC 5920 0 nbt_name_release_recv
PUBLIC 5a94 0 nbt_name_release
PUBLIC 5ad0 0 startlmhosts
PUBLIC 5b60 0 getlmhostsent
PUBLIC 5f54 0 endlmhosts
PUBLIC 5f70 0 resolve_lmhosts_file_as_sockaddr
STACK CFI INIT 2650 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2680 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 26c0 48 .cfa: sp 0 + .ra: x30
STACK CFI 26c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 26cc x19: .cfa -16 + ^
STACK CFI 2704 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2710 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2720 168 .cfa: sp 0 + .ra: x30
STACK CFI 2728 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2730 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 27c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 27c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2890 174 .cfa: sp 0 + .ra: x30
STACK CFI 2898 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 28ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 28f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 28f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 29dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 29e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2a04 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 2a0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2a74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2a88 x21: .cfa -16 + ^
STACK CFI 2b1c x21: x21
STACK CFI 2b2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2bb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2bc0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2bc4 x21: x21
STACK CFI 2bc8 x21: .cfa -16 + ^
STACK CFI 2bd8 x21: x21
STACK CFI INIT 2bf0 620 .cfa: sp 0 + .ra: x30
STACK CFI 2bf8 .cfa: sp 144 +
STACK CFI 2c08 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2c14 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2c68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2c70 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 2c80 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2c88 x27: .cfa -16 + ^
STACK CFI 2c9c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2d3c x25: x25 x26: x26
STACK CFI 2d6c x23: x23 x24: x24
STACK CFI 2d70 x27: x27
STACK CFI 2e5c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2ea8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 2ec4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2f28 x23: x23 x24: x24
STACK CFI 2f2c x25: x25 x26: x26
STACK CFI 2f30 x27: x27
STACK CFI 2f34 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2f60 x23: x23 x24: x24
STACK CFI 2f64 x25: x25 x26: x26
STACK CFI 2f68 x27: x27
STACK CFI 2f6c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2f80 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 3048 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 3074 x23: x23 x24: x24
STACK CFI 3078 x25: x25 x26: x26
STACK CFI 307c x27: x27
STACK CFI 30a8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 30bc x23: x23 x24: x24
STACK CFI 30c0 x25: x25 x26: x26
STACK CFI 30c4 x27: x27
STACK CFI 30c8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 30d0 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 30d8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 30fc x23: x23 x24: x24
STACK CFI 313c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 317c x23: x23 x24: x24
STACK CFI 31c8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 31cc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 31d0 x27: .cfa -16 + ^
STACK CFI 31d4 x25: x25 x26: x26 x27: x27
STACK CFI INIT 3210 100 .cfa: sp 0 + .ra: x30
STACK CFI 3218 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3228 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 32e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 32e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3310 2ac .cfa: sp 0 + .ra: x30
STACK CFI 3318 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3320 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 332c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3338 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 334c x25: .cfa -16 + ^
STACK CFI 3498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 34a0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 34d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 34dc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 35c0 1bc .cfa: sp 0 + .ra: x30
STACK CFI 35c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 35d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 35d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 36b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 36b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 36dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 36e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3718 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3780 8c .cfa: sp 0 + .ra: x30
STACK CFI 3790 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3798 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 37d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 37dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 37f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3810 44 .cfa: sp 0 + .ra: x30
STACK CFI 3818 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3820 x19: .cfa -16 + ^
STACK CFI 384c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3854 44 .cfa: sp 0 + .ra: x30
STACK CFI 385c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3864 x19: .cfa -16 + ^
STACK CFI 3890 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 38a0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 38a8 .cfa: sp 80 +
STACK CFI 38bc .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3944 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 394c .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3960 130 .cfa: sp 0 + .ra: x30
STACK CFI 3968 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3978 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3984 x21: .cfa -16 + ^
STACK CFI 3a50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3a58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3a88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3a90 214 .cfa: sp 0 + .ra: x30
STACK CFI 3a98 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3aa0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3aa8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3aec .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 3af4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3b64 x23: x23 x24: x24
STACK CFI 3b68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3b70 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 3bc4 x25: .cfa -16 + ^
STACK CFI 3c54 x23: x23 x24: x24
STACK CFI 3c5c x25: x25
STACK CFI 3c60 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3c7c x23: x23 x24: x24
STACK CFI 3c84 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3c9c x23: x23 x24: x24
STACK CFI INIT 3ca4 38 .cfa: sp 0 + .ra: x30
STACK CFI 3cac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3cb4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3ce0 108 .cfa: sp 0 + .ra: x30
STACK CFI 3ce8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3cf8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d04 x21: .cfa -16 + ^
STACK CFI 3db4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3dbc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3df0 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 3df8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e08 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3e44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3e4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3ec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3ecc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3fa4 38 .cfa: sp 0 + .ra: x30
STACK CFI 3fac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3fb4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3fe0 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 3fe8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3ff8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4004 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 407c x23: .cfa -16 + ^
STACK CFI 4180 x23: x23
STACK CFI 4184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 418c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4190 x23: x23
STACK CFI 41b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 41bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 41d0 174 .cfa: sp 0 + .ra: x30
STACK CFI 41d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 41e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 41e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4224 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 422c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4244 x23: .cfa -16 + ^
STACK CFI 42a0 x23: x23
STACK CFI 42a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 42ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 432c x23: x23
STACK CFI 4330 x23: .cfa -16 + ^
STACK CFI INIT 4344 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 434c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4358 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4370 x21: .cfa -16 + ^
STACK CFI 43ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 43f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4438 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4440 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4460 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4480 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 44f0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 44f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4504 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 451c x21: .cfa -16 + ^
STACK CFI 45c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 45c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 45dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 45e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4654 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 465c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4680 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4694 38 .cfa: sp 0 + .ra: x30
STACK CFI 469c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 46a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 46c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 46d0 13c .cfa: sp 0 + .ra: x30
STACK CFI 46d8 .cfa: sp 64 +
STACK CFI 46e8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 46f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4704 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 47ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 47f4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4810 88 .cfa: sp 0 + .ra: x30
STACK CFI 4818 .cfa: sp 48 +
STACK CFI 4828 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4830 x19: .cfa -16 + ^
STACK CFI 487c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4884 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 48a0 10c .cfa: sp 0 + .ra: x30
STACK CFI 48a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 48b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 48c4 x21: .cfa -16 + ^
STACK CFI 4928 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4930 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4968 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 49a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 49b0 24c .cfa: sp 0 + .ra: x30
STACK CFI 49b8 .cfa: sp 80 +
STACK CFI 49c8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 49d8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 49e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4a40 x23: .cfa -16 + ^
STACK CFI 4b30 x23: x23
STACK CFI 4b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4b78 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4b8c x23: x23
STACK CFI 4bf8 x23: .cfa -16 + ^
STACK CFI INIT 4c00 d0 .cfa: sp 0 + .ra: x30
STACK CFI 4c08 .cfa: sp 64 +
STACK CFI 4c14 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4c28 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4cb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4cbc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4cd0 128 .cfa: sp 0 + .ra: x30
STACK CFI 4cd8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4ce0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4cf8 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 4d6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4d74 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4db0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 4e00 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 4e08 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4e18 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4e24 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4e80 x23: .cfa -16 + ^
STACK CFI 4f74 x23: x23
STACK CFI 4f78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4f80 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4f84 x23: x23
STACK CFI 4fa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4fb0 174 .cfa: sp 0 + .ra: x30
STACK CFI 4fb8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4fc0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4fc8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 500c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5024 x23: .cfa -16 + ^
STACK CFI 5080 x23: x23
STACK CFI 5084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 508c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 510c x23: x23
STACK CFI 5110 x23: .cfa -16 + ^
STACK CFI INIT 5124 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 512c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5138 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5150 x21: .cfa -16 + ^
STACK CFI 51f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 51fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 521c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5294 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 529c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 52b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 52c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 52d4 38 .cfa: sp 0 + .ra: x30
STACK CFI 52dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 52e4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5310 25c .cfa: sp 0 + .ra: x30
STACK CFI 5318 .cfa: sp 80 +
STACK CFI 5328 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 533c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5348 x23: .cfa -16 + ^
STACK CFI 54f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 54fc .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5570 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5578 .cfa: sp 64 +
STACK CFI 5584 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 558c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5598 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5628 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5630 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5644 128 .cfa: sp 0 + .ra: x30
STACK CFI 564c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5654 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 566c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 56e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 56e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 571c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5724 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5770 1ac .cfa: sp 0 + .ra: x30
STACK CFI 5778 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5788 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5794 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 58e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 58f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5920 174 .cfa: sp 0 + .ra: x30
STACK CFI 5928 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5930 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5938 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 597c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5994 x23: .cfa -16 + ^
STACK CFI 59f0 x23: x23
STACK CFI 59f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 59fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5a7c x23: x23
STACK CFI 5a80 x23: .cfa -16 + ^
STACK CFI INIT 5a94 38 .cfa: sp 0 + .ra: x30
STACK CFI 5a9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5aa4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5ac4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5ad0 90 .cfa: sp 0 + .ra: x30
STACK CFI 5ad8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ae8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5b04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5b0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5b60 3f4 .cfa: sp 0 + .ra: x30
STACK CFI 5b68 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5b84 .cfa: sp 1184 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5dc4 .cfa: sp 96 +
STACK CFI 5ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5de4 .cfa: sp 1184 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5f54 18 .cfa: sp 0 + .ra: x30
STACK CFI 5f5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5f64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5f70 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 5f78 .cfa: sp 288 +
STACK CFI 5f84 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5f94 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5fa0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5fbc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 600c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 60f8 x25: x25 x26: x26
STACK CFI 6130 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 6138 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6190 x25: x25 x26: x26
STACK CFI 6194 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 61c0 x25: x25 x26: x26
STACK CFI 6214 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6248 x25: x25 x26: x26
STACK CFI 6250 x25: .cfa -32 + ^ x26: .cfa -24 + ^
