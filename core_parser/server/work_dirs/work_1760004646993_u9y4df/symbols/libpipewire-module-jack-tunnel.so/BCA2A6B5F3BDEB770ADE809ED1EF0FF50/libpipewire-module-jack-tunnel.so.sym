MODULE Linux arm64 BCA2A6B5F3BDEB770ADE809ED1EF0FF50 libpipewire-module-jack-tunnel.so
INFO CODE_ID B5A6A2BCBDF377EB0ADE809ED1EF0FF513D95578
PUBLIC ce30 0 pipewire__module_init
STACK CFI INIT 5840 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5870 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 58b0 48 .cfa: sp 0 + .ra: x30
STACK CFI 58b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 58bc x19: .cfa -16 + ^
STACK CFI 58f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5900 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5910 340 .cfa: sp 0 + .ra: x30
STACK CFI 5918 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5a04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5a10 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5b48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5b54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5ba4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5bac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5c24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5c2c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5c50 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 5c58 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5c60 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5c6c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5c80 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5c84 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5e7c x19: x19 x20: x20
STACK CFI 5e84 x23: x23 x24: x24
STACK CFI 5e98 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5ea0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5ea4 x19: x19 x20: x20
STACK CFI 5eb0 x23: x23 x24: x24
STACK CFI 5ebc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5ec4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5ecc x19: x19 x20: x20
STACK CFI 5ed8 x23: x23 x24: x24
STACK CFI 5ee4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5eec .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5ef8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5efc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 5f00 50 .cfa: sp 0 + .ra: x30
STACK CFI 5f08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5f10 x19: .cfa -16 + ^
STACK CFI 5f48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5f50 38 .cfa: sp 0 + .ra: x30
STACK CFI 5f58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5f6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5f74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5f80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5f90 68 .cfa: sp 0 + .ra: x30
STACK CFI 5f98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5fc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5fd0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5ff0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6000 2c8c .cfa: sp 0 + .ra: x30
STACK CFI 6008 .cfa: sp 480 +
STACK CFI 601c .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 6034 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 603c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 6054 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 6800 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6808 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 8c90 360 .cfa: sp 0 + .ra: x30
STACK CFI 8c98 .cfa: sp 112 +
STACK CFI 8ca4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8cac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8cc0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8cc8 v8: .cfa -16 + ^
STACK CFI 8e74 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8e7c .cfa: sp 112 + .ra: .cfa -56 + ^ v8: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8ff0 2ac .cfa: sp 0 + .ra: x30
STACK CFI 8ff8 .cfa: sp 128 +
STACK CFI 8ffc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9004 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9018 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 91a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 91b0 .cfa: sp 128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 92a0 de8 .cfa: sp 0 + .ra: x30
STACK CFI 92a8 .cfa: sp 352 +
STACK CFI 92bc .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 92c4 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 92d4 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 92e0 x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 92e8 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 96b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 96b8 .cfa: sp 352 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT a090 70 .cfa: sp 0 + .ra: x30
STACK CFI a098 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a0c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a0cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a0dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a0e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a0f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a100 24 .cfa: sp 0 + .ra: x30
STACK CFI a108 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a118 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a124 54 .cfa: sp 0 + .ra: x30
STACK CFI a12c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a134 x19: .cfa -16 + ^
STACK CFI a170 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a180 4e0 .cfa: sp 0 + .ra: x30
STACK CFI a188 .cfa: sp 272 +
STACK CFI a194 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI a19c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI a1e8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI a204 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI a20c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI a210 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI a218 v8: .cfa -16 + ^
STACK CFI a2d4 x19: x19 x20: x20
STACK CFI a2d8 x21: x21 x22: x22
STACK CFI a2dc x23: x23 x24: x24
STACK CFI a2e0 x27: x27 x28: x28
STACK CFI a2e4 v8: v8
STACK CFI a30c .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI a314 .cfa: sp 272 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI a594 v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI a5e0 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI a5ec .cfa: sp 272 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI a648 v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI a64c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI a650 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI a654 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI a658 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI a65c v8: .cfa -16 + ^
STACK CFI INIT a660 e4 .cfa: sp 0 + .ra: x30
STACK CFI a668 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a678 x19: .cfa -16 + ^
STACK CFI a6bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a6dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a73c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a744 294 .cfa: sp 0 + .ra: x30
STACK CFI a74c .cfa: sp 192 +
STACK CFI a758 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a768 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a798 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a808 x27: x27 x28: x28
STACK CFI a840 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI a848 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI a84c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a864 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a950 x23: x23 x24: x24
STACK CFI a954 x27: x27 x28: x28
STACK CFI a958 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a9cc x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI a9d0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a9d4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT a9e0 c4 .cfa: sp 0 + .ra: x30
STACK CFI a9e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a9f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a9fc x21: .cfa -16 + ^
STACK CFI aa14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI aa1c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI aa64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI aa88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT aaa4 364 .cfa: sp 0 + .ra: x30
STACK CFI aaac .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI aab4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI aac0 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI aaf4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI ab04 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI ab08 v8: .cfa -48 + ^
STACK CFI abc8 x19: x19 x20: x20
STACK CFI abcc x27: x27 x28: x28
STACK CFI abd0 v8: v8
STACK CFI ac04 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI ac0c .cfa: sp 144 + .ra: .cfa -136 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI ac24 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI ac2c .cfa: sp 144 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI ace4 v8: v8 x19: x19 x20: x20 x27: x27 x28: x28
STACK CFI acfc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI ad04 .cfa: sp 144 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT ae10 104 .cfa: sp 0 + .ra: x30
STACK CFI ae18 .cfa: sp 96 +
STACK CFI ae1c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ae24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ae30 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI ae6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ae74 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI af0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT af14 384 .cfa: sp 0 + .ra: x30
STACK CFI af1c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI af2c .cfa: sp 1376 + x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI af6c v8: .cfa -16 + ^
STACK CFI af74 x19: .cfa -96 + ^
STACK CFI af78 x20: .cfa -88 + ^
STACK CFI af80 x21: .cfa -80 + ^
STACK CFI af84 x22: .cfa -72 + ^
STACK CFI af88 x27: .cfa -32 + ^
STACK CFI af8c x28: .cfa -24 + ^
STACK CFI aff4 x19: x19
STACK CFI aff8 x20: x20
STACK CFI affc x21: x21
STACK CFI b000 x22: x22
STACK CFI b004 x27: x27
STACK CFI b008 x28: x28
STACK CFI b00c v8: v8
STACK CFI b02c .cfa: sp 112 +
STACK CFI b038 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b040 .cfa: sp 1376 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI b278 v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI b27c x19: .cfa -96 + ^
STACK CFI b280 x20: .cfa -88 + ^
STACK CFI b284 x21: .cfa -80 + ^
STACK CFI b288 x22: .cfa -72 + ^
STACK CFI b28c x27: .cfa -32 + ^
STACK CFI b290 x28: .cfa -24 + ^
STACK CFI b294 v8: .cfa -16 + ^
STACK CFI INIT b2a0 60 .cfa: sp 0 + .ra: x30
STACK CFI b2a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b2b4 x19: .cfa -16 + ^
STACK CFI b2cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b2d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI b2f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b300 25c .cfa: sp 0 + .ra: x30
STACK CFI b308 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b31c .cfa: sp 1440 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b468 .cfa: sp 96 +
STACK CFI b478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b480 .cfa: sp 1440 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI b4cc x26: .cfa -24 + ^
STACK CFI b4d4 x27: .cfa -16 + ^
STACK CFI b4dc x25: .cfa -32 + ^
STACK CFI b524 x25: x25
STACK CFI b528 x26: x26
STACK CFI b52c x27: x27
STACK CFI b534 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI b54c x25: x25 x26: x26 x27: x27
STACK CFI b550 x25: .cfa -32 + ^
STACK CFI b554 x26: .cfa -24 + ^
STACK CFI b558 x27: .cfa -16 + ^
STACK CFI INIT b560 850 .cfa: sp 0 + .ra: x30
STACK CFI b568 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b57c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b588 .cfa: sp 672 + x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b5c4 .cfa: sp 96 +
STACK CFI b5d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI b5d8 .cfa: sp 672 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI b5f4 x21: .cfa -64 + ^
STACK CFI b5f8 x22: .cfa -56 + ^
STACK CFI b5fc x23: .cfa -48 + ^
STACK CFI b600 x24: .cfa -40 + ^
STACK CFI b604 x25: .cfa -32 + ^
STACK CFI b608 x26: .cfa -24 + ^
STACK CFI b7a8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI b7e8 .cfa: sp 96 +
STACK CFI b7fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI b804 .cfa: sp 672 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI b81c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b98c x21: x21
STACK CFI b990 x22: x22
STACK CFI b994 x23: x23
STACK CFI b998 x24: x24
STACK CFI b99c x25: x25
STACK CFI b9a0 x26: x26
STACK CFI b9a4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ba2c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI ba30 x21: .cfa -64 + ^
STACK CFI ba34 x22: .cfa -56 + ^
STACK CFI bbf0 x21: x21
STACK CFI bbf4 x22: x22
STACK CFI bbf8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI bc34 x21: x21
STACK CFI bc38 x22: x22
STACK CFI bc3c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI bd2c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI bd5c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI bd94 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI bd98 x21: .cfa -64 + ^
STACK CFI bd9c x22: .cfa -56 + ^
STACK CFI bda0 x23: .cfa -48 + ^
STACK CFI bda4 x24: .cfa -40 + ^
STACK CFI bda8 x25: .cfa -32 + ^
STACK CFI bdac x26: .cfa -24 + ^
STACK CFI INIT bdb0 188 .cfa: sp 0 + .ra: x30
STACK CFI bdb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bdc4 .cfa: sp 1168 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI be1c x21: .cfa -16 + ^
STACK CFI be24 x22: .cfa -8 + ^
STACK CFI bed4 x21: x21
STACK CFI bed8 x22: x22
STACK CFI bef8 .cfa: sp 48 +
STACK CFI bf00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bf08 .cfa: sp 1168 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI bf1c x21: x21 x22: x22
STACK CFI bf30 x21: .cfa -16 + ^
STACK CFI bf34 x22: .cfa -8 + ^
STACK CFI INIT bf40 20c .cfa: sp 0 + .ra: x30
STACK CFI bf48 .cfa: sp 320 +
STACK CFI bf5c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI bf64 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI bf74 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI bf84 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI bf90 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT c150 94 .cfa: sp 0 + .ra: x30
STACK CFI c158 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c160 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c1d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c1dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT c1e4 50 .cfa: sp 0 + .ra: x30
STACK CFI c1ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c1f4 x19: .cfa -16 + ^
STACK CFI c22c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c234 4b4 .cfa: sp 0 + .ra: x30
STACK CFI c23c .cfa: sp 448 +
STACK CFI c24c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c260 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c268 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c278 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c3e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c3e8 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT c6f0 118 .cfa: sp 0 + .ra: x30
STACK CFI c6f8 .cfa: sp 48 +
STACK CFI c704 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c70c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c7bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c7c4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI c7f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c804 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT c810 98 .cfa: sp 0 + .ra: x30
STACK CFI c818 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c820 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c83c x21: .cfa -16 + ^
STACK CFI c864 x21: x21
STACK CFI c86c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c874 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI c880 x21: x21
STACK CFI c88c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c894 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT c8b0 3c4 .cfa: sp 0 + .ra: x30
STACK CFI c8b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c8c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c8d8 x21: .cfa -16 + ^
STACK CFI cbf0 x21: x21
STACK CFI cbf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cbfc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI cc4c x21: x21
STACK CFI cc50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI cc58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI cc6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT cc74 1b8 .cfa: sp 0 + .ra: x30
STACK CFI cc7c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI cc88 .cfa: sp 4224 +
STACK CFI ccb4 x19: .cfa -80 + ^
STACK CFI ccbc x20: .cfa -72 + ^
STACK CFI ccc0 x21: .cfa -64 + ^
STACK CFI ccc4 x22: .cfa -56 + ^
STACK CFI cccc x23: .cfa -48 + ^
STACK CFI ccd4 x24: .cfa -40 + ^
STACK CFI cce4 x25: .cfa -32 + ^
STACK CFI ccec x26: .cfa -24 + ^
STACK CFI ccf4 x27: .cfa -16 + ^
STACK CFI cd98 x20: x20
STACK CFI cda0 x19: x19
STACK CFI cda8 x21: x21
STACK CFI cdac x22: x22
STACK CFI cdb0 x23: x23
STACK CFI cdb4 x24: x24
STACK CFI cdb8 x25: x25
STACK CFI cdbc x26: x26
STACK CFI cdc0 x27: x27
STACK CFI cdc4 .cfa: sp 96 +
STACK CFI cdc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI cdd0 .cfa: sp 4224 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI cdf4 .cfa: sp 96 +
STACK CFI cdf8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ce00 .cfa: sp 4224 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ce04 x19: .cfa -80 + ^
STACK CFI ce08 x20: .cfa -72 + ^
STACK CFI ce0c x21: .cfa -64 + ^
STACK CFI ce10 x22: .cfa -56 + ^
STACK CFI ce14 x23: .cfa -48 + ^
STACK CFI ce18 x24: .cfa -40 + ^
STACK CFI ce1c x25: .cfa -32 + ^
STACK CFI ce20 x26: .cfa -24 + ^
STACK CFI ce24 x27: .cfa -16 + ^
STACK CFI INIT ce30 ad0 .cfa: sp 0 + .ra: x30
STACK CFI ce38 .cfa: sp 144 +
STACK CFI ce44 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ce4c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ce58 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ceb8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI cfbc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d250 x27: x27 x28: x28
STACK CFI d300 x25: x25 x26: x26
STACK CFI d334 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI d33c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI d36c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d384 x27: x27 x28: x28
STACK CFI d458 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d638 x25: x25 x26: x26
STACK CFI d63c x27: x27 x28: x28
STACK CFI d640 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d644 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d64c x27: x27 x28: x28
STACK CFI d650 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d69c x27: x27 x28: x28
STACK CFI d6cc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d744 x27: x27 x28: x28
STACK CFI d748 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d778 x27: x27 x28: x28
STACK CFI d77c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d7b0 x27: x27 x28: x28
STACK CFI d7b4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d7d0 x27: x27 x28: x28
STACK CFI d83c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d86c x27: x27 x28: x28
STACK CFI d870 x25: x25 x26: x26
STACK CFI d884 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d888 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d8ac x27: x27 x28: x28
STACK CFI d8b4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
