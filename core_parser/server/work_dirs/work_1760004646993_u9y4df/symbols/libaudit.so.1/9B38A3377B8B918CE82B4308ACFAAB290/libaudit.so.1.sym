MODULE Linux arm64 9B38A3377B8B918CE82B4308ACFAAB290 libaudit.so.1
INFO CODE_ID 37A3389B8B7B8C91E82B4308ACFAAB291F32BBC9
PUBLIC 30d0 0 audit_format_signal_info
PUBLIC 32f0 0 audit_getloginuid
PUBLIC 33e4 0 audit_get_session
PUBLIC 34e0 0 audit_rule_syscall_data
PUBLIC 3550 0 audit_rule_init_data
PUBLIC 3570 0 audit_rule_create_data
PUBLIC 35b0 0 audit_rule_free_data
PUBLIC 35d0 0 audit_number_to_errmsg
PUBLIC 36b4 0 audit_can_control
PUBLIC 3740 0 audit_can_write
PUBLIC 37c4 0 audit_can_read
PUBLIC 3850 0 set_aumessage_mode
PUBLIC 3880 0 audit_msg
PUBLIC 3a90 0 audit_update_watch_perms
PUBLIC 3b44 0 audit_setloginuid
PUBLIC 42d0 0 get_auditfail_action
PUBLIC 4314 0 audit_open
PUBLIC 4420 0 audit_close
PUBLIC 4450 0 audit_get_reply
PUBLIC 4720 0 __audit_send
PUBLIC 49f4 0 audit_reset_backlog_wait_time_actual
PUBLIC 4ac0 0 audit_send
PUBLIC 4b30 0 audit_request_status
PUBLIC 4ba4 0 audit_is_enabled
PUBLIC 4e90 0 audit_get_features
PUBLIC 4ef4 0 audit_reset_lost
PUBLIC 4fe0 0 audit_set_enabled
PUBLIC 50b0 0 audit_set_failure
PUBLIC 5184 0 audit_set_pid
PUBLIC 52d0 0 audit_set_rate_limit
PUBLIC 53a4 0 audit_set_backlog_limit
PUBLIC 5480 0 audit_set_backlog_wait_time
PUBLIC 5554 0 audit_set_feature
PUBLIC 5624 0 audit_set_loginuid_immutable
PUBLIC 5650 0 audit_request_features
PUBLIC 5704 0 audit_request_rules_list_data
PUBLIC 5780 0 audit_request_signal_info
PUBLIC 57f0 0 audit_add_rule_data
PUBLIC 58a0 0 audit_delete_rule_data
PUBLIC 5954 0 audit_trim_subtrees
PUBLIC 59d0 0 audit_make_equivalent
PUBLIC 5bd0 0 audit_name_to_field
PUBLIC 5d40 0 audit_rule_interfield_comp_data
PUBLIC 6200 0 audit_field_to_name
PUBLIC 6280 0 audit_name_to_uringop
PUBLIC 63f4 0 audit_name_to_syscall
PUBLIC 6bb0 0 audit_uringop_to_name
PUBLIC 6c10 0 audit_rule_io_uringbyname_data
PUBLIC 6d00 0 audit_syscall_to_name
PUBLIC 6ef0 0 audit_name_to_flag
PUBLIC 7064 0 audit_flag_to_name
PUBLIC 70b4 0 audit_name_to_action
PUBLIC 7230 0 audit_action_to_name
PUBLIC 7280 0 audit_name_to_msg_type
PUBLIC 74c0 0 audit_msg_type_to_name
PUBLIC 7550 0 audit_name_to_machine
PUBLIC 76d0 0 audit_detect_machine
PUBLIC 7740 0 audit_machine_to_name
PUBLIC 77a0 0 audit_machine_to_elf
PUBLIC 7810 0 audit_elf_to_machine
PUBLIC 7880 0 audit_rule_syscallbyname_data
PUBLIC 7980 0 audit_add_watch_dir
PUBLIC 7ae0 0 audit_add_watch
PUBLIC 7b04 0 audit_determine_machine
PUBLIC 7cf0 0 audit_operator_to_symbol
PUBLIC 7d70 0 audit_name_to_errno
PUBLIC 7f10 0 audit_errno_to_name
PUBLIC 7f70 0 audit_name_to_ftype
PUBLIC 80e4 0 audit_ftype_to_name
PUBLIC 8170 0 audit_name_to_fstype
PUBLIC 82f4 0 audit_rule_fieldpair_data
PUBLIC 8ea0 0 audit_fstype_to_name
PUBLIC 8f44 0 audit_value_needs_encoding
PUBLIC 8fb0 0 audit_encode_value
PUBLIC 9140 0 audit_encode_nv_string
PUBLIC 9290 0 audit_log_user_message
PUBLIC 94c0 0 audit_log_user_comm_message
PUBLIC 97e0 0 audit_log_acct_message
PUBLIC 9b14 0 audit_log_user_avc_message
PUBLIC 9d70 0 audit_log_semanage_message
PUBLIC a274 0 audit_log_user_command
STACK CFI INIT 2ea0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ed0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f10 48 .cfa: sp 0 + .ra: x30
STACK CFI 2f14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f1c x19: .cfa -16 + ^
STACK CFI 2f54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2f60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2f70 8c .cfa: sp 0 + .ra: x30
STACK CFI 2f78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f80 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2fc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2fd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3000 cc .cfa: sp 0 + .ra: x30
STACK CFI 300c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3060 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3078 x19: x19 x20: x20
STACK CFI 307c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3084 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 30c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 30d0 21c .cfa: sp 0 + .ra: x30
STACK CFI 30d8 .cfa: sp 304 +
STACK CFI 30dc .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 30e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 30f4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3100 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3108 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 32bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 32c4 .cfa: sp 304 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 32f0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 32f8 .cfa: sp 80 +
STACK CFI 3304 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3318 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3340 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 33a0 x19: x19 x20: x20
STACK CFI 33a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 33a8 x19: x19 x20: x20
STACK CFI 33d4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 33dc .cfa: sp 80 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 33e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 33e4 f4 .cfa: sp 0 + .ra: x30
STACK CFI 33ec .cfa: sp 80 +
STACK CFI 33f8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 340c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3434 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3494 x19: x19 x20: x20
STACK CFI 3498 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 349c x19: x19 x20: x20
STACK CFI 34c8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 34d0 .cfa: sp 80 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 34d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 34e0 6c .cfa: sp 0 + .ra: x30
STACK CFI 34fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 353c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3544 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3550 20 .cfa: sp 0 + .ra: x30
STACK CFI 3558 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3564 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3570 38 .cfa: sp 0 + .ra: x30
STACK CFI 3578 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3584 x19: .cfa -16 + ^
STACK CFI 35a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 35b0 18 .cfa: sp 0 + .ra: x30
STACK CFI 35b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 35d0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 35d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 362c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3634 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3644 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 365c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 366c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3688 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3698 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 36b4 84 .cfa: sp 0 + .ra: x30
STACK CFI 36bc .cfa: sp 48 +
STACK CFI 36c8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 36d0 x19: .cfa -16 + ^
STACK CFI 372c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3734 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3740 84 .cfa: sp 0 + .ra: x30
STACK CFI 3748 .cfa: sp 48 +
STACK CFI 3754 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 375c x19: .cfa -16 + ^
STACK CFI 37b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 37c0 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 37c4 84 .cfa: sp 0 + .ra: x30
STACK CFI 37cc .cfa: sp 48 +
STACK CFI 37d8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37e0 x19: .cfa -16 + ^
STACK CFI 383c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3844 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3850 28 .cfa: sp 0 + .ra: x30
STACK CFI 3858 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3868 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3880 138 .cfa: sp 0 + .ra: x30
STACK CFI 3888 .cfa: sp 320 +
STACK CFI 3898 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 3934 x19: .cfa -192 + ^
STACK CFI 395c x19: x19
STACK CFI 3980 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3988 .cfa: sp 320 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 39b4 x19: .cfa -192 + ^
STACK CFI INIT 39c0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 39c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 39d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 39dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 39f4 x23: .cfa -16 + ^
STACK CFI 3a4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3a54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 3a90 b4 .cfa: sp 0 + .ra: x30
STACK CFI 3a98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3b14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3b44 164 .cfa: sp 0 + .ra: x30
STACK CFI 3b4c .cfa: sp 112 +
STACK CFI 3b58 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3b60 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3b6c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI 3bc8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3c3c x23: x23 x24: x24
STACK CFI 3c70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI 3c78 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 3c8c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3c98 x23: x23 x24: x24
STACK CFI 3ca4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 3cb0 104 .cfa: sp 0 + .ra: x30
STACK CFI 3cb8 .cfa: sp 96 +
STACK CFI 3cc8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3cd0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d24 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3db4 124 .cfa: sp 0 + .ra: x30
STACK CFI 3dbc .cfa: sp 176 +
STACK CFI 3dc8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3dd0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e84 .cfa: sp 176 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3ee0 3e8 .cfa: sp 0 + .ra: x30
STACK CFI 3ee8 .cfa: sp 368 +
STACK CFI 3ef8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3f04 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3f84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3f8c .cfa: sp 368 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 3fe0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4010 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 401c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 413c x23: x23 x24: x24
STACK CFI 4144 x25: x25 x26: x26
STACK CFI 4148 x27: x27 x28: x28
STACK CFI 4188 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 41ac x23: x23 x24: x24
STACK CFI 41b0 x25: x25 x26: x26
STACK CFI 41b4 x27: x27 x28: x28
STACK CFI 41b8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 41ec x23: x23 x24: x24
STACK CFI 41f0 x25: x25 x26: x26
STACK CFI 41f4 x27: x27 x28: x28
STACK CFI 4268 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4288 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 42b4 x23: x23 x24: x24
STACK CFI 42bc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 42c0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 42c4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 42d0 44 .cfa: sp 0 + .ra: x30
STACK CFI 42d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 430c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4314 104 .cfa: sp 0 + .ra: x30
STACK CFI 431c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4330 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4368 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 436c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 43a8 x21: x21 x22: x22
STACK CFI 43ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 43b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 43d4 x21: x21 x22: x22
STACK CFI 43d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4414 x21: x21 x22: x22
STACK CFI INIT 4420 28 .cfa: sp 0 + .ra: x30
STACK CFI 4428 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4434 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 443c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4440 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4450 2d0 .cfa: sp 0 + .ra: x30
STACK CFI 4458 .cfa: sp 112 +
STACK CFI 4468 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4480 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 448c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 44a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 44ac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4554 x19: x19 x20: x20
STACK CFI 455c x21: x21 x22: x22
STACK CFI 4560 x23: x23 x24: x24
STACK CFI 4590 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 4598 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 45c4 x19: x19 x20: x20
STACK CFI 45cc x21: x21 x22: x22
STACK CFI 45d0 x23: x23 x24: x24
STACK CFI 45d4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 45fc x19: x19 x20: x20
STACK CFI 4604 x21: x21 x22: x22
STACK CFI 4608 x23: x23 x24: x24
STACK CFI 4610 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4628 x19: x19 x20: x20
STACK CFI 462c x21: x21 x22: x22
STACK CFI 4630 x23: x23 x24: x24
STACK CFI 4634 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4650 x19: x19 x20: x20
STACK CFI 4658 x21: x21 x22: x22
STACK CFI 465c x23: x23 x24: x24
STACK CFI 4664 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 46ac x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 46b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 46e0 x19: x19 x20: x20
STACK CFI 46e4 x21: x21 x22: x22
STACK CFI 46e8 x23: x23 x24: x24
STACK CFI 46ec x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4704 x19: x19 x20: x20
STACK CFI 4708 x21: x21 x22: x22
STACK CFI 470c x23: x23 x24: x24
STACK CFI 4714 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4718 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 471c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 4720 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 4728 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4738 .cfa: sp 18128 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4758 x21: .cfa -48 + ^
STACK CFI 4764 x22: .cfa -40 + ^
STACK CFI 478c x23: .cfa -32 + ^
STACK CFI 4790 x24: .cfa -24 + ^
STACK CFI 4798 x25: .cfa -16 + ^
STACK CFI 4850 x21: x21
STACK CFI 4854 x22: x22
STACK CFI 4858 x23: x23
STACK CFI 485c x24: x24
STACK CFI 4860 x25: x25
STACK CFI 4884 .cfa: sp 80 +
STACK CFI 4890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4898 .cfa: sp 18128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 4988 x23: x23 x24: x24 x25: x25
STACK CFI 4998 x21: x21
STACK CFI 49a0 x22: x22
STACK CFI 49b8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 49c0 x23: x23
STACK CFI 49c8 x24: x24
STACK CFI 49cc x25: x25
STACK CFI 49d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 49dc x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 49e0 x21: .cfa -48 + ^
STACK CFI 49e4 x22: .cfa -40 + ^
STACK CFI 49e8 x23: .cfa -32 + ^
STACK CFI 49ec x24: .cfa -24 + ^
STACK CFI 49f0 x25: .cfa -16 + ^
STACK CFI INIT 49f4 cc .cfa: sp 0 + .ra: x30
STACK CFI 49fc .cfa: sp 96 +
STACK CFI 4a0c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4ab4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4abc .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4ac0 68 .cfa: sp 0 + .ra: x30
STACK CFI 4ac8 .cfa: sp 32 +
STACK CFI 4ad8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4b1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4b24 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4b30 74 .cfa: sp 0 + .ra: x30
STACK CFI 4b38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4b9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4ba4 188 .cfa: sp 0 + .ra: x30
STACK CFI 4bac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4bb8 .cfa: sp 9088 +
STACK CFI 4bd8 x19: .cfa -32 + ^
STACK CFI 4bdc x20: .cfa -24 + ^
STACK CFI 4c00 x21: .cfa -16 + ^
STACK CFI 4c08 x22: .cfa -8 + ^
STACK CFI 4c74 x21: x21
STACK CFI 4c78 x22: x22
STACK CFI 4c94 x19: x19
STACK CFI 4c9c x20: x20
STACK CFI 4cc0 .cfa: sp 48 +
STACK CFI 4cc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4ccc .cfa: sp 9088 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4cd0 x19: x19
STACK CFI 4cd4 x20: x20
STACK CFI 4ce0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4ce8 x19: x19
STACK CFI 4cec x20: x20
STACK CFI 4cf0 x21: x21
STACK CFI 4cf4 x22: x22
STACK CFI 4cfc x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4d00 x21: x21
STACK CFI 4d04 x22: x22
STACK CFI 4d0c x19: x19
STACK CFI 4d14 x20: x20
STACK CFI 4d1c x19: .cfa -32 + ^
STACK CFI 4d20 x20: .cfa -24 + ^
STACK CFI 4d24 x21: .cfa -16 + ^
STACK CFI 4d28 x22: .cfa -8 + ^
STACK CFI INIT 4d30 160 .cfa: sp 0 + .ra: x30
STACK CFI 4d38 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4d44 .cfa: sp 9088 +
STACK CFI 4d68 x19: .cfa -32 + ^
STACK CFI 4d6c x20: .cfa -24 + ^
STACK CFI 4d90 x21: .cfa -16 + ^
STACK CFI 4d98 x22: .cfa -8 + ^
STACK CFI 4e04 x19: x19
STACK CFI 4e08 x20: x20
STACK CFI 4e0c x21: x21
STACK CFI 4e10 x22: x22
STACK CFI 4e40 .cfa: sp 48 +
STACK CFI 4e44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4e4c .cfa: sp 9088 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4e50 x19: x19
STACK CFI 4e54 x20: x20
STACK CFI 4e58 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4e64 x19: x19
STACK CFI 4e6c x20: x20
STACK CFI 4e74 x21: x21
STACK CFI 4e78 x22: x22
STACK CFI 4e80 x19: .cfa -32 + ^
STACK CFI 4e84 x20: .cfa -24 + ^
STACK CFI 4e88 x21: .cfa -16 + ^
STACK CFI 4e8c x22: .cfa -8 + ^
STACK CFI INIT 4e90 64 .cfa: sp 0 + .ra: x30
STACK CFI 4e98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ea0 x19: .cfa -16 + ^
STACK CFI 4ec8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4ed0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4ee8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4ef4 e4 .cfa: sp 0 + .ra: x30
STACK CFI 4efc .cfa: sp 96 +
STACK CFI 4f08 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4fc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4fcc .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4fe0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 4fe8 .cfa: sp 96 +
STACK CFI 4ff8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5008 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 509c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 50a4 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 50b0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 50b8 .cfa: sp 96 +
STACK CFI 50c8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 50d8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5180 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5184 144 .cfa: sp 0 + .ra: x30
STACK CFI 518c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 51a0 .cfa: sp 9136 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5270 .cfa: sp 48 +
STACK CFI 5280 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5288 .cfa: sp 9136 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 52d0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 52d8 .cfa: sp 96 +
STACK CFI 52e8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 52f8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5398 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 53a0 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 53a4 d4 .cfa: sp 0 + .ra: x30
STACK CFI 53ac .cfa: sp 96 +
STACK CFI 53bc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 53cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 546c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5474 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5480 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5488 .cfa: sp 96 +
STACK CFI 5498 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 54a8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5550 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5554 d0 .cfa: sp 0 + .ra: x30
STACK CFI 555c .cfa: sp 64 +
STACK CFI 556c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5578 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5620 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5624 24 .cfa: sp 0 + .ra: x30
STACK CFI 562c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5638 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5650 b4 .cfa: sp 0 + .ra: x30
STACK CFI 5658 .cfa: sp 64 +
STACK CFI 5668 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5678 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 56f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5700 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5704 7c .cfa: sp 0 + .ra: x30
STACK CFI 570c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5720 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5778 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5780 6c .cfa: sp 0 + .ra: x30
STACK CFI 5788 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 579c x19: .cfa -16 + ^
STACK CFI 57b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 57bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 57e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 57f0 ac .cfa: sp 0 + .ra: x30
STACK CFI 5804 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5814 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 583c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5888 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 58a0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 58b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 58c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 58e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5940 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5954 74 .cfa: sp 0 + .ra: x30
STACK CFI 595c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5970 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 59c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 59d0 130 .cfa: sp 0 + .ra: x30
STACK CFI 59d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 59e8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 59f4 x25: .cfa -16 + ^
STACK CFI 5af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 5b00 cc .cfa: sp 0 + .ra: x30
STACK CFI 5b08 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5b10 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5b1c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5b28 x23: .cfa -16 + ^
STACK CFI 5b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5b98 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5bbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5bc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5bd0 170 .cfa: sp 0 + .ra: x30
STACK CFI 5bd8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5be4 .cfa: x29 80 +
STACK CFI 5bf8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 5d24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5d2c .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5d40 4b8 .cfa: sp 0 + .ra: x30
STACK CFI 5d48 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5d50 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5d68 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5d80 x23: .cfa -16 + ^
STACK CFI 5e2c x19: x19 x20: x20
STACK CFI 5e34 x23: x23
STACK CFI 5e3c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5e44 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5ff8 x19: x19 x20: x20
STACK CFI 6000 x23: x23
STACK CFI 6014 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 601c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 60f0 x19: x19 x20: x20
STACK CFI 60f4 x23: x23
STACK CFI 60f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 614c x19: x19 x20: x20
STACK CFI 6154 x23: x23
STACK CFI 6158 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 615c x19: x19 x20: x20
STACK CFI 6164 x23: x23
STACK CFI 6168 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 6184 x19: x19 x20: x20
STACK CFI 618c x23: x23
STACK CFI 6190 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 61b8 x19: x19 x20: x20 x23: x23
STACK CFI 61c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 61c4 x19: x19 x20: x20
STACK CFI 61cc x23: x23
STACK CFI 61d0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 61d4 x19: x19 x20: x20
STACK CFI 61dc x23: x23
STACK CFI 61e8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 61ec x19: x19 x20: x20
STACK CFI 61f4 x23: x23
STACK CFI INIT 6200 80 .cfa: sp 0 + .ra: x30
STACK CFI 6208 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 625c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6268 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6270 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6280 174 .cfa: sp 0 + .ra: x30
STACK CFI 6288 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6294 .cfa: x29 80 +
STACK CFI 62a8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 63d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 63e0 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 63f4 7bc .cfa: sp 0 + .ra: x30
STACK CFI 63fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6408 .cfa: x29 80 +
STACK CFI 640c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6420 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 648c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6494 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6bb0 5c .cfa: sp 0 + .ra: x30
STACK CFI 6bb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6be8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6bfc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6c00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6c10 ec .cfa: sp 0 + .ra: x30
STACK CFI 6c18 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6c20 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6c8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6c94 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6cc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6cd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6cf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6d00 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 6d08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6d3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6d44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6d90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6d98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6e10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6e1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6ef0 174 .cfa: sp 0 + .ra: x30
STACK CFI 6ef8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6f04 .cfa: x29 80 +
STACK CFI 6f18 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7050 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7064 50 .cfa: sp 0 + .ra: x30
STACK CFI 706c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 708c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 70a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 70a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 70b4 178 .cfa: sp 0 + .ra: x30
STACK CFI 70bc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 70c8 .cfa: x29 80 +
STACK CFI 70dc x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7218 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7230 50 .cfa: sp 0 + .ra: x30
STACK CFI 7238 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7258 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7270 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7274 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7280 23c .cfa: sp 0 + .ra: x30
STACK CFI 7288 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7294 .cfa: x29 96 +
STACK CFI 729c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 72b0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 7430 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 7438 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 74c0 8c .cfa: sp 0 + .ra: x30
STACK CFI 74c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 751c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7528 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7540 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7550 17c .cfa: sp 0 + .ra: x30
STACK CFI 7558 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7564 .cfa: x29 80 +
STACK CFI 7578 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 76a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 76b0 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 76d0 70 .cfa: sp 0 + .ra: x30
STACK CFI 76d8 .cfa: sp 416 +
STACK CFI 76e8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 772c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7734 .cfa: sp 416 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7740 58 .cfa: sp 0 + .ra: x30
STACK CFI 7748 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7774 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7788 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 778c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 77a0 68 .cfa: sp 0 + .ra: x30
STACK CFI 77bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 77e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 77ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 77fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7810 68 .cfa: sp 0 + .ra: x30
STACK CFI 782c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7850 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 785c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 786c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7880 f8 .cfa: sp 0 + .ra: x30
STACK CFI 7888 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7890 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 78f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7900 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7938 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7970 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7980 15c .cfa: sp 0 + .ra: x30
STACK CFI 7988 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7990 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 799c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 79a4 x25: .cfa -16 + ^
STACK CFI 7a84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 7a8c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7ae0 24 .cfa: sp 0 + .ra: x30
STACK CFI 7ae8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7af4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7b04 1ec .cfa: sp 0 + .ra: x30
STACK CFI 7b0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7b18 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7ba8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7bb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7bdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7be4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7cf0 80 .cfa: sp 0 + .ra: x30
STACK CFI 7cf8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7d4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7d58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7d60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7d70 19c .cfa: sp 0 + .ra: x30
STACK CFI 7d78 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7d7c .cfa: x29 96 +
STACK CFI 7d80 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7d90 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7da4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 7eec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 7ef4 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7f10 5c .cfa: sp 0 + .ra: x30
STACK CFI 7f18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7f44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7f5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7f60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7f70 174 .cfa: sp 0 + .ra: x30
STACK CFI 7f78 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7f84 .cfa: x29 80 +
STACK CFI 7f98 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 80c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 80d0 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 80e4 84 .cfa: sp 0 + .ra: x30
STACK CFI 80ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8140 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 814c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8158 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8170 184 .cfa: sp 0 + .ra: x30
STACK CFI 8178 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8184 .cfa: x29 80 +
STACK CFI 8198 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 82d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 82e0 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 82f4 ba8 .cfa: sp 0 + .ra: x30
STACK CFI 82fc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8304 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8310 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8324 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8370 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8374 x27: .cfa -16 + ^
STACK CFI 83bc x19: x19 x20: x20
STACK CFI 83c0 x23: x23 x24: x24
STACK CFI 83c4 x25: x25 x26: x26
STACK CFI 83c8 x27: x27
STACK CFI 83cc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 83e4 x25: x25 x26: x26 x27: x27
STACK CFI 8408 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 84b4 x19: x19 x20: x20
STACK CFI 84b8 x23: x23 x24: x24
STACK CFI 84bc x27: x27
STACK CFI 84c4 x25: x25 x26: x26
STACK CFI 84cc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 84d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 866c x19: x19 x20: x20
STACK CFI 8674 x23: x23 x24: x24
STACK CFI 8678 x25: x25 x26: x26
STACK CFI 867c x27: x27
STACK CFI 8680 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 86ac x19: x19 x20: x20
STACK CFI 86b4 x23: x23 x24: x24
STACK CFI 86b8 x25: x25 x26: x26
STACK CFI 86bc x27: x27
STACK CFI 86c0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8854 x19: x19 x20: x20
STACK CFI 885c x23: x23 x24: x24
STACK CFI 8860 x25: x25 x26: x26
STACK CFI 8864 x27: x27
STACK CFI 8868 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 88f0 x19: x19 x20: x20
STACK CFI 88f8 x23: x23 x24: x24
STACK CFI 88fc x25: x25 x26: x26
STACK CFI 8900 x27: x27
STACK CFI 8904 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 89d8 x19: x19 x20: x20
STACK CFI 89e0 x23: x23 x24: x24
STACK CFI 89e4 x25: x25 x26: x26
STACK CFI 89e8 x27: x27
STACK CFI 89ec x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8a10 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8a4c x19: x19 x20: x20
STACK CFI 8a54 x23: x23 x24: x24
STACK CFI 8a58 x25: x25 x26: x26
STACK CFI 8a5c x27: x27
STACK CFI 8a60 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8ad4 x19: x19 x20: x20
STACK CFI 8adc x23: x23 x24: x24
STACK CFI 8ae0 x25: x25 x26: x26
STACK CFI 8ae4 x27: x27
STACK CFI 8ae8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8b20 x25: x25 x26: x26 x27: x27
STACK CFI 8b48 x19: x19 x20: x20
STACK CFI 8b50 x23: x23 x24: x24
STACK CFI 8b54 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8bf0 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27
STACK CFI 8bf4 x23: x23 x24: x24
STACK CFI 8bfc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8c00 x19: x19 x20: x20
STACK CFI 8c08 x23: x23 x24: x24
STACK CFI 8c0c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8c2c x25: x25 x26: x26 x27: x27
STACK CFI 8c50 x19: x19 x20: x20
STACK CFI 8c58 x23: x23 x24: x24
STACK CFI 8c5c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8c88 x19: x19 x20: x20
STACK CFI 8c90 x23: x23 x24: x24
STACK CFI 8c94 x25: x25 x26: x26
STACK CFI 8c98 x27: x27
STACK CFI 8c9c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8ca0 x19: x19 x20: x20
STACK CFI 8ca8 x23: x23 x24: x24
STACK CFI 8cac x25: x25 x26: x26
STACK CFI 8cb0 x27: x27
STACK CFI 8cbc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8cf4 x25: x25 x26: x26 x27: x27
STACK CFI 8d14 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8d18 x19: x19 x20: x20
STACK CFI 8d20 x23: x23 x24: x24
STACK CFI 8d24 x25: x25 x26: x26
STACK CFI 8d28 x27: x27
STACK CFI 8d2c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8d30 x19: x19 x20: x20
STACK CFI 8d38 x23: x23 x24: x24
STACK CFI 8d3c x25: x25 x26: x26
STACK CFI 8d40 x27: x27
STACK CFI 8d44 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8d48 x19: x19 x20: x20
STACK CFI 8d50 x23: x23 x24: x24
STACK CFI 8d54 x25: x25 x26: x26
STACK CFI 8d58 x27: x27
STACK CFI 8d5c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8d60 x19: x19 x20: x20
STACK CFI 8d68 x23: x23 x24: x24
STACK CFI 8d6c x25: x25 x26: x26
STACK CFI 8d70 x27: x27
STACK CFI 8d74 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8d78 x19: x19 x20: x20
STACK CFI 8d80 x23: x23 x24: x24
STACK CFI 8d84 x25: x25 x26: x26
STACK CFI 8d88 x27: x27
STACK CFI 8d8c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8d90 x19: x19 x20: x20
STACK CFI 8d98 x23: x23 x24: x24
STACK CFI 8d9c x25: x25 x26: x26
STACK CFI 8da0 x27: x27
STACK CFI 8da4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8dc0 x19: x19 x20: x20
STACK CFI 8dc8 x23: x23 x24: x24
STACK CFI 8dcc x25: x25 x26: x26
STACK CFI 8dd0 x27: x27
STACK CFI 8dd4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8df4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8df8 x19: x19 x20: x20
STACK CFI 8e00 x23: x23 x24: x24
STACK CFI 8e04 x25: x25 x26: x26
STACK CFI 8e08 x27: x27
STACK CFI 8e0c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8e10 x19: x19 x20: x20
STACK CFI 8e18 x23: x23 x24: x24
STACK CFI 8e1c x25: x25 x26: x26
STACK CFI 8e20 x27: x27
STACK CFI 8e24 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8e28 x19: x19 x20: x20
STACK CFI 8e30 x23: x23 x24: x24
STACK CFI 8e34 x25: x25 x26: x26
STACK CFI 8e38 x27: x27
STACK CFI 8e3c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8e40 x19: x19 x20: x20
STACK CFI 8e48 x23: x23 x24: x24
STACK CFI 8e4c x25: x25 x26: x26
STACK CFI 8e50 x27: x27
STACK CFI 8e54 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8e58 x19: x19 x20: x20
STACK CFI 8e60 x23: x23 x24: x24
STACK CFI 8e64 x25: x25 x26: x26
STACK CFI 8e68 x27: x27
STACK CFI 8e6c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8e90 x19: x19 x20: x20
STACK CFI 8e98 x23: x23 x24: x24
STACK CFI INIT 8ea0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 8ea8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8f14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8f20 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8f34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8f44 64 .cfa: sp 0 + .ra: x30
STACK CFI 8f4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8f8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8f98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8f9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8fb0 8c .cfa: sp 0 + .ra: x30
STACK CFI 8fb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9014 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 901c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9024 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 902c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9040 100 .cfa: sp 0 + .ra: x30
STACK CFI 9048 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 905c .cfa: sp 4160 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 90f4 .cfa: sp 48 +
STACK CFI 9100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9108 .cfa: sp 4160 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9140 14c .cfa: sp 0 + .ra: x30
STACK CFI 9148 .cfa: sp 64 +
STACK CFI 9154 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 915c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9168 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 91f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 91fc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9290 22c .cfa: sp 0 + .ra: x30
STACK CFI 9298 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 92a8 .cfa: sp 9184 + x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 92d0 x19: .cfa -64 + ^
STACK CFI 92d8 x20: .cfa -56 + ^
STACK CFI 92e0 x23: .cfa -32 + ^
STACK CFI 92e8 x24: .cfa -24 + ^
STACK CFI 92f0 x25: .cfa -16 + ^
STACK CFI 92f8 x26: .cfa -8 + ^
STACK CFI 93dc x19: x19
STACK CFI 93e0 x20: x20
STACK CFI 93e4 x23: x23
STACK CFI 93e8 x24: x24
STACK CFI 93ec x25: x25
STACK CFI 93f0 x26: x26
STACK CFI 9414 .cfa: sp 80 +
STACK CFI 941c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 9424 .cfa: sp 9184 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 94a0 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 94a4 x19: .cfa -64 + ^
STACK CFI 94a8 x20: .cfa -56 + ^
STACK CFI 94ac x23: .cfa -32 + ^
STACK CFI 94b0 x24: .cfa -24 + ^
STACK CFI 94b4 x25: .cfa -16 + ^
STACK CFI 94b8 x26: .cfa -8 + ^
STACK CFI INIT 94c0 31c .cfa: sp 0 + .ra: x30
STACK CFI 94c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 94d8 .cfa: sp 17408 + x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9504 x19: .cfa -80 + ^
STACK CFI 950c x20: .cfa -72 + ^
STACK CFI 9514 x23: .cfa -48 + ^
STACK CFI 951c x24: .cfa -40 + ^
STACK CFI 9524 x25: .cfa -32 + ^
STACK CFI 952c x26: .cfa -24 + ^
STACK CFI 9534 x27: .cfa -16 + ^
STACK CFI 953c x28: .cfa -8 + ^
STACK CFI 9660 x19: x19
STACK CFI 9664 x20: x20
STACK CFI 9668 x23: x23
STACK CFI 966c x24: x24
STACK CFI 9670 x25: x25
STACK CFI 9674 x26: x26
STACK CFI 9678 x27: x27
STACK CFI 967c x28: x28
STACK CFI 96a0 .cfa: sp 96 +
STACK CFI 96a8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 96b0 .cfa: sp 17408 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 97b8 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 97bc x19: .cfa -80 + ^
STACK CFI 97c0 x20: .cfa -72 + ^
STACK CFI 97c4 x23: .cfa -48 + ^
STACK CFI 97c8 x24: .cfa -40 + ^
STACK CFI 97cc x25: .cfa -32 + ^
STACK CFI 97d0 x26: .cfa -24 + ^
STACK CFI 97d4 x27: .cfa -16 + ^
STACK CFI 97d8 x28: .cfa -8 + ^
STACK CFI INIT 97e0 334 .cfa: sp 0 + .ra: x30
STACK CFI 97e8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 97fc .cfa: sp 9280 + x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9830 x19: .cfa -80 + ^
STACK CFI 9838 x20: .cfa -72 + ^
STACK CFI 9840 x23: .cfa -48 + ^
STACK CFI 9848 x24: .cfa -40 + ^
STACK CFI 9850 x25: .cfa -32 + ^
STACK CFI 9858 x26: .cfa -24 + ^
STACK CFI 9968 x19: x19
STACK CFI 996c x20: x20
STACK CFI 9970 x23: x23
STACK CFI 9974 x24: x24
STACK CFI 9978 x25: x25
STACK CFI 997c x26: x26
STACK CFI 99a0 .cfa: sp 96 +
STACK CFI 99ac .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 99b4 .cfa: sp 9280 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 9af8 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 9afc x19: .cfa -80 + ^
STACK CFI 9b00 x20: .cfa -72 + ^
STACK CFI 9b04 x23: .cfa -48 + ^
STACK CFI 9b08 x24: .cfa -40 + ^
STACK CFI 9b0c x25: .cfa -32 + ^
STACK CFI 9b10 x26: .cfa -24 + ^
STACK CFI INIT 9b14 25c .cfa: sp 0 + .ra: x30
STACK CFI 9b1c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9b2c .cfa: sp 9200 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9b4c x21: .cfa -64 + ^
STACK CFI 9b54 x22: .cfa -56 + ^
STACK CFI 9b5c x23: .cfa -48 + ^
STACK CFI 9b64 x24: .cfa -40 + ^
STACK CFI 9b6c x25: .cfa -32 + ^
STACK CFI 9b74 x26: .cfa -24 + ^
STACK CFI 9b7c x27: .cfa -16 + ^
STACK CFI 9c44 x21: x21
STACK CFI 9c48 x22: x22
STACK CFI 9c4c x23: x23
STACK CFI 9c50 x24: x24
STACK CFI 9c54 x25: x25
STACK CFI 9c58 x26: x26
STACK CFI 9c5c x27: x27
STACK CFI 9c80 .cfa: sp 96 +
STACK CFI 9c8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9c94 .cfa: sp 9200 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 9d2c x21: x21
STACK CFI 9d30 x22: x22
STACK CFI 9d34 x23: x23
STACK CFI 9d38 x24: x24
STACK CFI 9d3c x25: x25
STACK CFI 9d40 x26: x26
STACK CFI 9d44 x27: x27
STACK CFI 9d54 x21: .cfa -64 + ^
STACK CFI 9d58 x22: .cfa -56 + ^
STACK CFI 9d5c x23: .cfa -48 + ^
STACK CFI 9d60 x24: .cfa -40 + ^
STACK CFI 9d64 x25: .cfa -32 + ^
STACK CFI 9d68 x26: .cfa -24 + ^
STACK CFI 9d6c x27: .cfa -16 + ^
STACK CFI INIT 9d70 504 .cfa: sp 0 + .ra: x30
STACK CFI 9d78 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9d94 .cfa: sp 9360 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9df0 x27: .cfa -16 + ^
STACK CFI 9df8 x28: .cfa -8 + ^
STACK CFI 9f98 x27: x27
STACK CFI 9f9c x28: x28
STACK CFI 9fc0 .cfa: sp 96 +
STACK CFI 9fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9fdc .cfa: sp 9360 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI a028 x27: x27
STACK CFI a02c x28: x28
STACK CFI a034 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a268 x27: x27 x28: x28
STACK CFI a26c x27: .cfa -16 + ^
STACK CFI a270 x28: .cfa -8 + ^
STACK CFI INIT a274 3a4 .cfa: sp 0 + .ra: x30
STACK CFI a27c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a28c .cfa: sp 25600 + x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a2c0 x23: .cfa -48 + ^
STACK CFI a2d4 x19: .cfa -80 + ^
STACK CFI a2d8 x20: .cfa -72 + ^
STACK CFI a2e0 x24: .cfa -40 + ^
STACK CFI a2e4 x25: .cfa -32 + ^
STACK CFI a2ec x26: .cfa -24 + ^
STACK CFI a348 x27: .cfa -16 + ^
STACK CFI a34c x28: .cfa -8 + ^
STACK CFI a4c4 x19: x19
STACK CFI a4c8 x20: x20
STACK CFI a4cc x23: x23
STACK CFI a4d0 x24: x24
STACK CFI a4d4 x25: x25
STACK CFI a4d8 x26: x26
STACK CFI a4dc x27: x27
STACK CFI a4e0 x28: x28
STACK CFI a504 .cfa: sp 96 +
STACK CFI a50c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI a514 .cfa: sp 25600 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI a52c x27: x27 x28: x28
STACK CFI a534 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a5a8 x27: x27 x28: x28
STACK CFI a5b8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a5d4 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a5d8 x19: .cfa -80 + ^
STACK CFI a5dc x20: .cfa -72 + ^
STACK CFI a5e0 x23: .cfa -48 + ^
STACK CFI a5e4 x24: .cfa -40 + ^
STACK CFI a5e8 x25: .cfa -32 + ^
STACK CFI a5ec x26: .cfa -24 + ^
STACK CFI a5f0 x27: .cfa -16 + ^
STACK CFI a5f4 x28: .cfa -8 + ^
STACK CFI a5f8 x27: x27 x28: x28
STACK CFI a5fc x19: x19
STACK CFI a604 x20: x20
STACK CFI a608 x23: x23
STACK CFI a60c x24: x24
STACK CFI a610 x25: x25
STACK CFI a614 x26: x26
