MODULE Linux arm64 87C82571DB4B0C873AFFB5BB0EFD29CF0 libpixbufloader-xpm.so
INFO CODE_ID 7125C8874BDB870C3AFFB5BB0EFD29CF42BD609F
PUBLIC 2960 0 fill_vtable
PUBLIC 29b0 0 fill_info
STACK CFI INIT 14f0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1520 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1560 48 .cfa: sp 0 + .ra: x30
STACK CFI 1564 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 156c x19: .cfa -16 + ^
STACK CFI 15a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 15b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15c0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 15c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 15d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1654 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 165c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 166c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1674 5c .cfa: sp 0 + .ra: x30
STACK CFI 16a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 16d0 b18 .cfa: sp 0 + .ra: x30
STACK CFI 16d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 16ec .cfa: sp 656 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1820 x21: .cfa -64 + ^
STACK CFI 1824 x22: .cfa -56 + ^
STACK CFI 1828 x25: .cfa -32 + ^
STACK CFI 182c x26: .cfa -24 + ^
STACK CFI 1830 x27: .cfa -16 + ^
STACK CFI 1834 x28: .cfa -8 + ^
STACK CFI 1c74 x21: x21
STACK CFI 1c78 x22: x22
STACK CFI 1c7c x25: x25
STACK CFI 1c80 x26: x26
STACK CFI 1c84 x27: x27
STACK CFI 1c88 x28: x28
STACK CFI 1c8c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1d90 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1de4 .cfa: sp 96 +
STACK CFI 1df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 1dfc .cfa: sp 656 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1e78 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1ea8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1f30 x21: x21
STACK CFI 1f34 x22: x22
STACK CFI 1f38 x25: x25
STACK CFI 1f3c x26: x26
STACK CFI 1f40 x27: x27
STACK CFI 1f44 x28: x28
STACK CFI 1f48 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1f94 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1fdc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2140 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2144 x21: .cfa -64 + ^
STACK CFI 2148 x22: .cfa -56 + ^
STACK CFI 214c x25: .cfa -32 + ^
STACK CFI 2150 x26: .cfa -24 + ^
STACK CFI 2154 x27: .cfa -16 + ^
STACK CFI 2158 x28: .cfa -8 + ^
STACK CFI 215c x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 21dc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 21f0 8c .cfa: sp 0 + .ra: x30
STACK CFI 21f8 .cfa: sp 64 +
STACK CFI 2208 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2218 x19: .cfa -16 + ^
STACK CFI 2270 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2278 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2280 b0 .cfa: sp 0 + .ra: x30
STACK CFI 2288 .cfa: sp 64 +
STACK CFI 2298 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 22a8 x19: .cfa -16 + ^
STACK CFI 2324 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 232c .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2330 14c .cfa: sp 0 + .ra: x30
STACK CFI 2338 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2348 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 23c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 23c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 23f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 23f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2480 164 .cfa: sp 0 + .ra: x30
STACK CFI 2488 .cfa: sp 80 +
STACK CFI 2494 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 249c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 251c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2524 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2558 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 25b0 x21: x21 x22: x22
STACK CFI 25e0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 25e4 e0 .cfa: sp 0 + .ra: x30
STACK CFI 25ec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 25f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2600 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2630 x21: x21 x22: x22
STACK CFI 2634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 263c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2688 x21: x21 x22: x22
STACK CFI 2694 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 269c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 26c4 bc .cfa: sp 0 + .ra: x30
STACK CFI 26cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 26dc .cfa: sp 1088 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2760 .cfa: sp 48 +
STACK CFI 276c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2774 .cfa: sp 1088 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2780 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 2788 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2794 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 27d0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 27d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 2840 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2848 x25: .cfa -16 + ^
STACK CFI 28cc x19: x19 x20: x20
STACK CFI 28d8 x25: x25
STACK CFI 28dc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 28e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 28f8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^
STACK CFI 290c x19: x19 x20: x20
STACK CFI 2910 x25: x25
STACK CFI 2950 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2954 x25: .cfa -16 + ^
STACK CFI INIT 2960 4c .cfa: sp 0 + .ra: x30
STACK CFI 2968 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 297c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 29b0 5c .cfa: sp 0 + .ra: x30
STACK CFI 29b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 29cc .cfa: sp 0 + .ra: .ra x29: x29
