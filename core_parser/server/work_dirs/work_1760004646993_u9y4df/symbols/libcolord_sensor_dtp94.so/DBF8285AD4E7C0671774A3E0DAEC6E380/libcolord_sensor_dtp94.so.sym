MODULE Linux arm64 DBF8285AD4E7C0671774A3E0DAEC6E380 libcolord_sensor_dtp94.so
INFO CODE_ID 5A28F8DBE7D467C01774A3E0DAEC6E3849C2D5C0
PUBLIC 1a00 0 cd_sensor_get_sample_async
PUBLIC 1af4 0 cd_sensor_get_sample_finish
PUBLIC 1b70 0 cd_sensor_lock_async
PUBLIC 1c44 0 cd_sensor_lock_finish
PUBLIC 1cc0 0 cd_sensor_unlock_async
PUBLIC 1d94 0 cd_sensor_unlock_finish
PUBLIC 1e10 0 cd_sensor_dump_device
PUBLIC 1e40 0 cd_sensor_coldplug
PUBLIC 1ed0 0 dtp94_device_error_quark
PUBLIC 1f20 0 dtp94_device_send_data
PUBLIC 2160 0 dtp94_device_get_serial
PUBLIC 2300 0 dtp94_rc_parse
PUBLIC 23b0 0 dtp94_rc_to_string
PUBLIC 2570 0 dtp94_device_send_cmd
PUBLIC 2880 0 dtp94_device_setup
PUBLIC 2b60 0 dtp94_device_take_sample
STACK CFI INIT 1810 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1840 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1880 48 .cfa: sp 0 + .ra: x30
STACK CFI 1884 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 188c x19: .cfa -16 + ^
STACK CFI 18c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 18d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18e0 34 .cfa: sp 0 + .ra: x30
STACK CFI 18e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 18f0 x19: .cfa -16 + ^
STACK CFI 190c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1914 e4 .cfa: sp 0 + .ra: x30
STACK CFI 191c .cfa: sp 48 +
STACK CFI 1928 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1930 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 19bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 19c4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1a00 f4 .cfa: sp 0 + .ra: x30
STACK CFI 1a08 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1a10 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1a1c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1a28 x23: .cfa -16 + ^
STACK CFI 1aa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1aac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1abc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1ac4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1af4 74 .cfa: sp 0 + .ra: x30
STACK CFI 1afc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b04 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1b2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b34 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1b60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1b70 d4 .cfa: sp 0 + .ra: x30
STACK CFI 1b78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1b80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1b8c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1bf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1c00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1c14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1c28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1c44 74 .cfa: sp 0 + .ra: x30
STACK CFI 1c4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1c7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1cc0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 1cc8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1cd0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1cdc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1d5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d64 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1d78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1d94 74 .cfa: sp 0 + .ra: x30
STACK CFI 1d9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1da4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1dcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1dd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1e10 30 .cfa: sp 0 + .ra: x30
STACK CFI 1e18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1e34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1e40 8c .cfa: sp 0 + .ra: x30
STACK CFI 1e48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e58 x19: .cfa -16 + ^
STACK CFI 1ec4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ed0 4c .cfa: sp 0 + .ra: x30
STACK CFI 1ed8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ee0 x19: .cfa -16 + ^
STACK CFI 1ef4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1efc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1f14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f20 240 .cfa: sp 0 + .ra: x30
STACK CFI 1f28 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1f30 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1f3c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1f48 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1f54 x25: .cfa -16 + ^
STACK CFI 1ff8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2000 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2160 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 2168 .cfa: sp 192 +
STACK CFI 2174 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 217c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 21a4 x21: .cfa -16 + ^
STACK CFI 2230 x21: x21
STACK CFI 2258 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2260 .cfa: sp 192 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2264 x21: x21
STACK CFI 226c x21: .cfa -16 + ^
STACK CFI 2298 x21: x21
STACK CFI 22a0 x21: .cfa -16 + ^
STACK CFI 22c4 x21: x21
STACK CFI 22cc x21: .cfa -16 + ^
STACK CFI 22d0 x21: x21
STACK CFI 22fc x21: .cfa -16 + ^
STACK CFI INIT 2300 a8 .cfa: sp 0 + .ra: x30
STACK CFI 2308 .cfa: sp 32 +
STACK CFI 2318 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2384 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 238c .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 23b0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 23b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 23e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 23f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2570 308 .cfa: sp 0 + .ra: x30
STACK CFI 2578 .cfa: sp 256 +
STACK CFI 2584 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 258c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2598 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 25f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 25f4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2668 x27: .cfa -16 + ^
STACK CFI 2680 x23: x23 x24: x24
STACK CFI 2688 x25: x25 x26: x26
STACK CFI 268c x27: x27
STACK CFI 26bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 26c4 .cfa: sp 256 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 26ec x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2744 x23: x23 x24: x24
STACK CFI 274c x25: x25 x26: x26
STACK CFI 2750 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 27a0 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 27a8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 27ac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 27b0 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 2800 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2840 x27: x27
STACK CFI 2844 x27: .cfa -16 + ^
STACK CFI 2864 x27: x27
STACK CFI 2868 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 286c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2870 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2874 x27: .cfa -16 + ^
STACK CFI INIT 2880 180 .cfa: sp 0 + .ra: x30
STACK CFI 2888 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2890 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 28d4 x21: .cfa -16 + ^
STACK CFI 28f4 x21: x21
STACK CFI 2900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2908 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 29b0 x21: x21
STACK CFI INIT 2a00 160 .cfa: sp 0 + .ra: x30
STACK CFI 2a08 .cfa: sp 64 +
STACK CFI 2a14 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a24 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2af4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2afc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2b60 25c .cfa: sp 0 + .ra: x30
STACK CFI 2b68 .cfa: sp 208 +
STACK CFI 2b74 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2b7c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2b88 x21: .cfa -32 + ^
STACK CFI 2c48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2c50 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 2cd4 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 2d24 v8: v8 v9: v9
STACK CFI 2db8 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI INIT 2dc0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 2dc8 .cfa: sp 64 +
STACK CFI 2dd4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2ddc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2de8 x21: .cfa -16 + ^
STACK CFI 2e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2e84 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
