MODULE Linux arm64 B71B38FAFA71CED422B1EB1F5533AE480 libcryptsetup-token-systemd-fido2.so
INFO CODE_ID FA381BB771FAD4CE22B1EB1F5533AE486BDEB667
PUBLIC 1524 0 cryptsetup_token_version
PUBLIC 1544 0 cryptsetup_token_open_pin
PUBLIC 19f0 0 cryptsetup_token_open
PUBLIC 1a20 0 cryptsetup_token_buffer_free
PUBLIC 1a90 0 cryptsetup_token_dump
PUBLIC 1df0 0 cryptsetup_token_validate
STACK CFI INIT ff0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1020 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1060 48 .cfa: sp 0 + .ra: x30
STACK CFI 1064 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 106c x19: .cfa -16 + ^
STACK CFI 10a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10c0 168 .cfa: sp 0 + .ra: x30
STACK CFI 10c8 .cfa: sp 112 +
STACK CFI 10d4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 10dc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 10f8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1104 x27: .cfa -16 + ^
STACK CFI 1118 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 112c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1190 x23: x23 x24: x24
STACK CFI 1194 x25: x25 x26: x26
STACK CFI 1198 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 119c x23: x23 x24: x24
STACK CFI 11a0 x25: x25 x26: x26
STACK CFI 11dc x19: x19 x20: x20
STACK CFI 11e4 x27: x27
STACK CFI 11e8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 11f0 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 1210 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1214 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1218 x27: .cfa -16 + ^
STACK CFI 121c x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 1220 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1224 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 1230 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 1238 .cfa: sp 144 +
STACK CFI 1244 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1264 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1268 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 126c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1270 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1354 x27: .cfa -16 + ^
STACK CFI 13e4 x27: x27
STACK CFI 1428 x19: x19 x20: x20
STACK CFI 142c x21: x21 x22: x22
STACK CFI 1430 x23: x23 x24: x24
STACK CFI 1434 x25: x25 x26: x26
STACK CFI 1438 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1440 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 14c4 x27: .cfa -16 + ^
STACK CFI 14c8 x27: x27
STACK CFI 1504 x27: .cfa -16 + ^
STACK CFI 1508 x27: x27
STACK CFI INIT 1524 20 .cfa: sp 0 + .ra: x30
STACK CFI 152c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 153c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1544 4a8 .cfa: sp 0 + .ra: x30
STACK CFI 154c .cfa: sp 208 +
STACK CFI 1558 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1560 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1580 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1588 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1590 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 15dc x27: .cfa -16 + ^
STACK CFI 1748 x27: x27
STACK CFI 17a4 x21: x21 x22: x22
STACK CFI 17a8 x23: x23 x24: x24
STACK CFI 17ac x25: x25 x26: x26
STACK CFI 17b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 17b8 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 1804 x27: .cfa -16 + ^
STACK CFI 1814 x27: x27
STACK CFI 181c x27: .cfa -16 + ^
STACK CFI 1840 x27: x27
STACK CFI 1850 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 1874 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1878 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 187c x27: .cfa -16 + ^
STACK CFI 1880 x27: x27
STACK CFI 18a4 x27: .cfa -16 + ^
STACK CFI 18a8 x27: x27
STACK CFI 18cc x27: .cfa -16 + ^
STACK CFI 18d0 x27: x27
STACK CFI 18f4 x27: .cfa -16 + ^
STACK CFI 18f8 x27: x27
STACK CFI 191c x27: .cfa -16 + ^
STACK CFI 19e4 x27: x27
STACK CFI 19e8 x27: .cfa -16 + ^
STACK CFI INIT 19f0 2c .cfa: sp 0 + .ra: x30
STACK CFI 19f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a20 70 .cfa: sp 0 + .ra: x30
STACK CFI 1a30 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a38 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1a64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1a84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1a90 358 .cfa: sp 0 + .ra: x30
STACK CFI 1a98 .cfa: sp 176 +
STACK CFI 1aa4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1acc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1ad0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1ad4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1ad8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1b48 x27: .cfa -16 + ^
STACK CFI 1c74 x19: x19 x20: x20
STACK CFI 1c78 x21: x21 x22: x22
STACK CFI 1c7c x23: x23 x24: x24
STACK CFI 1c80 x25: x25 x26: x26
STACK CFI 1c84 x27: x27
STACK CFI 1c88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c90 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 1d10 x19: x19 x20: x20
STACK CFI 1d14 x21: x21 x22: x22
STACK CFI 1d18 x23: x23 x24: x24
STACK CFI 1d1c x25: x25 x26: x26
STACK CFI 1d20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1d28 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 1d4c x27: x27
STACK CFI 1da8 x27: .cfa -16 + ^
STACK CFI 1dac x27: x27
STACK CFI 1de4 x27: .cfa -16 + ^
STACK CFI INIT 1df0 304 .cfa: sp 0 + .ra: x30
STACK CFI 1df8 .cfa: sp 64 +
STACK CFI 1e04 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1e0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1f80 x21: .cfa -16 + ^
STACK CFI 1fb0 x21: x21
STACK CFI 1fe8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1ff0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1ff4 x21: .cfa -16 + ^
STACK CFI 2014 x21: x21
STACK CFI 2054 x21: .cfa -16 + ^
STACK CFI 2058 x21: x21
STACK CFI 2078 x21: .cfa -16 + ^
STACK CFI 2098 x21: x21
STACK CFI 20f0 x21: .cfa -16 + ^
