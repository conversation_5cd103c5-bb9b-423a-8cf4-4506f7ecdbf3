MODULE Linux arm64 D9BA5CBA28AAC4420F2A47FD9B1CF25C0 liblog_upload_trigger.so
INFO CODE_ID BA5CBAD9AA2842C40F2A47FD9B1CF25C
PUBLIC 1468 0 _init
PUBLIC 15d0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&) [clone .constprop.0]
PUBLIC 16e0 0 _GLOBAL__sub_I_upload_log_trigger_impl.cpp
PUBLIC 18a0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&) [clone .constprop.0]
PUBLIC 19b0 0 _GLOBAL__sub_I_upload_log_trigger_impl_lidds.cpp
PUBLIC 1b70 0 call_weak_fn
PUBLIC 1b90 0 deregister_tm_clones
PUBLIC 1bc0 0 register_tm_clones
PUBLIC 1c00 0 __do_global_dtors_aux
PUBLIC 1c50 0 frame_dummy
PUBLIC 1c60 0 lios::log_upload_trigger::Init()
PUBLIC 1c70 0 lios::log_upload_trigger::Trigger()
PUBLIC 1c80 0 lios::log_upload_trigger::UploadLogTrigger::UploadLogTrigger()
PUBLIC 1cd0 0 lios::log_upload_trigger::UploadLogTrigger::GetInstance()
PUBLIC 1d60 0 lios::log_upload_trigger::UploadLogTrigger::TriggerLogUpload()
PUBLIC 1dc0 0 lios::log_upload_trigger::UploadLogTrigger::~UploadLogTrigger()
PUBLIC 1de0 0 lios::log_upload_trigger::UploadLogTrigger::UploadLogTriggerImpl::Create()
PUBLIC 1e20 0 lios::log_upload_trigger::UploadLogTriggerImplLiDDS::Trigger(lios::log_upload_trigger::TriggerType const&)
PUBLIC 1e30 0 lios::log_upload_trigger::UploadLogTriggerImplLiDDS::~UploadLogTriggerImplLiDDS()
PUBLIC 1e40 0 lios::log_upload_trigger::UploadLogTriggerImplLiDDS::~UploadLogTriggerImplLiDDS()
PUBLIC 1e48 0 _fini
STACK CFI INIT 1b90 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1bc0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c00 48 .cfa: sp 0 + .ra: x30
STACK CFI 1c04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c0c x19: .cfa -16 + ^
STACK CFI 1c44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1c50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1dc0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c80 4c .cfa: sp 0 + .ra: x30
STACK CFI 1c8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1cc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1cc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1cd0 90 .cfa: sp 0 + .ra: x30
STACK CFI 1cd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1cdc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1cfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1d00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1d44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1d48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1d60 60 .cfa: sp 0 + .ra: x30
STACK CFI 1d64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1db8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1dbc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 15d0 104 .cfa: sp 0 + .ra: x30
STACK CFI 15d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 15e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 15ec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 166c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1de0 40 .cfa: sp 0 + .ra: x30
STACK CFI 1de4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1df0 x19: .cfa -16 + ^
STACK CFI 1e1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 16e0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 16e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 170c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 189c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1e20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e40 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18a0 104 .cfa: sp 0 + .ra: x30
STACK CFI 18a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 18b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 18bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 193c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 19b0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 19b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 19c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 19dc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1b6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
