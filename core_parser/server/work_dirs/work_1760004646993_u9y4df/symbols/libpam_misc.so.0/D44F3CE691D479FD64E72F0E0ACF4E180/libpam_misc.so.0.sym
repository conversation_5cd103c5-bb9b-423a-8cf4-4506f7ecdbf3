MODULE Linux arm64 D44F3CE691D479FD64E72F0E0ACF4E180 libpam_misc.so.0
INFO CODE_ID E63C4FD4D491FD7964E72F0E0ACF4E1807DA066F
PUBLIC 1924 0 pam_misc_drop_env
PUBLIC 19a0 0 pam_misc_paste_env
PUBLIC 1a00 0 pam_misc_setenv
PUBLIC 1ae4 0 misc_conv
STACK CFI INIT 11b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1220 48 .cfa: sp 0 + .ra: x30
STACK CFI 1224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 122c x19: .cfa -16 + ^
STACK CFI 1264 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1270 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1280 24 .cfa: sp 0 + .ra: x30
STACK CFI 1288 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 129c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 12a4 70 .cfa: sp 0 + .ra: x30
STACK CFI 12ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12b4 x19: .cfa -16 + ^
STACK CFI 12e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 12f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1314 12c .cfa: sp 0 + .ra: x30
STACK CFI 131c .cfa: sp 48 +
STACK CFI 132c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1338 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 13a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13ac .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1440 4e4 .cfa: sp 0 + .ra: x30
STACK CFI 1448 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1460 .cfa: sp 4944 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 14a8 x23: .cfa -48 + ^
STACK CFI 14ac x24: .cfa -40 + ^
STACK CFI 1504 x23: x23 x24: x24
STACK CFI 150c x23: .cfa -48 + ^
STACK CFI 1510 x24: .cfa -40 + ^
STACK CFI 153c x28: .cfa -8 + ^
STACK CFI 1558 x27: .cfa -16 + ^
STACK CFI 15f0 x27: x27
STACK CFI 15f8 x28: x28
STACK CFI 1600 x23: x23
STACK CFI 1604 x24: x24
STACK CFI 1628 .cfa: sp 96 +
STACK CFI 163c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 1644 .cfa: sp 4944 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 170c x27: x27
STACK CFI 1710 x28: x28
STACK CFI 1750 x23: x23
STACK CFI 1754 x24: x24
STACK CFI 1758 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 17a8 x27: x27
STACK CFI 17b0 x28: x28
STACK CFI 17b4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1804 x27: x27
STACK CFI 1808 x28: x28
STACK CFI 180c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1870 x27: x27
STACK CFI 1874 x28: x28
STACK CFI 1878 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 18d0 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 18e0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 18ec x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 18fc x23: x23
STACK CFI 1900 x24: x24
STACK CFI 1904 x27: x27
STACK CFI 1908 x28: x28
STACK CFI 1914 x23: .cfa -48 + ^
STACK CFI 1918 x24: .cfa -40 + ^
STACK CFI 191c x27: .cfa -16 + ^
STACK CFI 1920 x28: .cfa -8 + ^
STACK CFI INIT 1924 74 .cfa: sp 0 + .ra: x30
STACK CFI 192c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1934 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 193c x21: .cfa -16 + ^
STACK CFI 1990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 19a0 58 .cfa: sp 0 + .ra: x30
STACK CFI 19b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19b8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 19e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1a00 e4 .cfa: sp 0 + .ra: x30
STACK CFI 1a08 .cfa: sp 64 +
STACK CFI 1a14 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1a1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1a28 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1ad8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1ae0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1ae4 3a0 .cfa: sp 0 + .ra: x30
STACK CFI 1aec .cfa: sp 144 +
STACK CFI 1af8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1b00 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1b20 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1b2c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1b34 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1b54 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1bb8 x19: x19 x20: x20
STACK CFI 1bc0 x25: x25 x26: x26
STACK CFI 1bc8 x23: x23 x24: x24
STACK CFI 1bcc x27: x27 x28: x28
STACK CFI 1bd0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1cd4 x19: x19 x20: x20
STACK CFI 1cd8 x23: x23 x24: x24
STACK CFI 1cdc x25: x25 x26: x26
STACK CFI 1ce0 x27: x27 x28: x28
STACK CFI 1d0c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1d14 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1e18 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1e1c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1e20 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1e24 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1e28 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1e50 x25: x25 x26: x26
STACK CFI 1e54 x19: x19 x20: x20
STACK CFI 1e58 x23: x23 x24: x24
STACK CFI 1e5c x27: x27 x28: x28
STACK CFI 1e60 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
