MODULE Linux arm64 B941D3BF8B3F88B56A8E513F6DA113990 libutil-tdb-samba4.so.0
INFO CODE_ID BFD341B93F8BB5886A8E513F6DA11399BA285611
PUBLIC 10e0 0 make_tdb_data
PUBLIC 1100 0 tdb_data_equal
PUBLIC 1144 0 tdb_data_is_empty
PUBLIC 1180 0 string_tdb_data
PUBLIC 11c0 0 string_term_tdb_data
PUBLIC 1200 0 tdb_data_talloc_copy
PUBLIC 1280 0 tdb_lock_bystring
PUBLIC 12c0 0 tdb_unlock_bystring
PUBLIC 1300 0 tdb_read_lock_bystring
PUBLIC 1340 0 tdb_read_unlock_bystring
PUBLIC 1380 0 tdb_fetch_int32
PUBLIC 1420 0 tdb_store_int32
PUBLIC 14b0 0 tdb_fetch_uint32
PUBLIC 1504 0 tdb_store_uint32
PUBLIC 15a0 0 tdb_store_bystring
PUBLIC 1600 0 tdb_fetch_bystring
PUBLIC 1640 0 tdb_delete_bystring
PUBLIC 1680 0 tdb_change_int32_atomic
PUBLIC 1730 0 tdb_change_uint32_atomic
PUBLIC 1820 0 map_nt_error_from_tdb
PUBLIC 1860 0 map_unix_error_from_tdb
PUBLIC 18a0 0 tdb_fetch_talloc
STACK CFI INIT f60 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT f90 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT fd0 48 .cfa: sp 0 + .ra: x30
STACK CFI fd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI fdc x19: .cfa -16 + ^
STACK CFI 1014 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1020 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1030 2c .cfa: sp 0 + .ra: x30
STACK CFI 1038 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1050 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1060 34 .cfa: sp 0 + .ra: x30
STACK CFI 1068 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1084 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 108c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1094 48 .cfa: sp 0 + .ra: x30
STACK CFI 109c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10ac x19: .cfa -16 + ^
STACK CFI 10d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10e0 18 .cfa: sp 0 + .ra: x30
STACK CFI 10e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1100 44 .cfa: sp 0 + .ra: x30
STACK CFI 1118 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1130 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1144 34 .cfa: sp 0 + .ra: x30
STACK CFI 114c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 115c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1168 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 116c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1180 38 .cfa: sp 0 + .ra: x30
STACK CFI 1188 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1194 x19: .cfa -16 + ^
STACK CFI 11b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 11c0 38 .cfa: sp 0 + .ra: x30
STACK CFI 11c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11d4 x19: .cfa -16 + ^
STACK CFI 11f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1200 80 .cfa: sp 0 + .ra: x30
STACK CFI 1208 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1210 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1218 x21: .cfa -16 + ^
STACK CFI 1258 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1260 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1280 38 .cfa: sp 0 + .ra: x30
STACK CFI 1288 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1290 x19: .cfa -16 + ^
STACK CFI 12b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12c0 38 .cfa: sp 0 + .ra: x30
STACK CFI 12c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12d0 x19: .cfa -16 + ^
STACK CFI 12f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1300 38 .cfa: sp 0 + .ra: x30
STACK CFI 1308 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1310 x19: .cfa -16 + ^
STACK CFI 1330 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1340 38 .cfa: sp 0 + .ra: x30
STACK CFI 1348 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1350 x19: .cfa -16 + ^
STACK CFI 1370 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1380 98 .cfa: sp 0 + .ra: x30
STACK CFI 1388 .cfa: sp 48 +
STACK CFI 1394 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 139c x19: .cfa -16 + ^
STACK CFI 140c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1414 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1420 8c .cfa: sp 0 + .ra: x30
STACK CFI 1428 .cfa: sp 48 +
STACK CFI 1434 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 143c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 14a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14a8 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 14b0 54 .cfa: sp 0 + .ra: x30
STACK CFI 14b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 14c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 14fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1504 94 .cfa: sp 0 + .ra: x30
STACK CFI 150c .cfa: sp 48 +
STACK CFI 1518 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1520 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 158c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1594 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 15a0 58 .cfa: sp 0 + .ra: x30
STACK CFI 15a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 15b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15c0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 15f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1600 38 .cfa: sp 0 + .ra: x30
STACK CFI 1608 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1610 x19: .cfa -16 + ^
STACK CFI 1630 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1640 38 .cfa: sp 0 + .ra: x30
STACK CFI 1648 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1650 x19: .cfa -16 + ^
STACK CFI 1670 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1680 ac .cfa: sp 0 + .ra: x30
STACK CFI 1688 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1690 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1698 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 16a4 x23: .cfa -16 + ^
STACK CFI 1704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 170c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1730 ec .cfa: sp 0 + .ra: x30
STACK CFI 1738 .cfa: sp 80 +
STACK CFI 1744 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 174c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1758 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1764 x23: .cfa -16 + ^
STACK CFI 17b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 17b8 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1820 40 .cfa: sp 0 + .ra: x30
STACK CFI 1828 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1840 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 184c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1854 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1860 40 .cfa: sp 0 + .ra: x30
STACK CFI 1868 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1884 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1890 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1894 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18a0 ac .cfa: sp 0 + .ra: x30
STACK CFI 18a8 .cfa: sp 64 +
STACK CFI 18b8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 18c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1928 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1930 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
