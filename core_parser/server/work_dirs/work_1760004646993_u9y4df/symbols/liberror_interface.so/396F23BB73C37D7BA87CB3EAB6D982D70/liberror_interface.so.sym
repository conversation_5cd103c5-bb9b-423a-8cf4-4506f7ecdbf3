MODULE Linux arm64 396F23BB73C37D7BA87CB3EAB6D982D70 liberror_interface.so
INFO CODE_ID BB236F39C3737B7DA87CB3EAB6D982D7
PUBLIC 1610 0 _init
PUBLIC 1790 0 _GLOBAL__sub_I_error_nvmedia.cpp
PUBLIC 1964 0 call_weak_fn
PUBLIC 1980 0 deregister_tm_clones
PUBLIC 19b0 0 register_tm_clones
PUBLIC 19f0 0 __do_global_dtors_aux
PUBLIC 1a40 0 frame_dummy
PUBLIC 1a50 0 lios::error::NvMediaErrorStr(int)
PUBLIC 1b00 0 std::unordered_map<int, char const*, std::hash<int>, std::equal_to<int>, std::allocator<std::pair<int const, char const*> > >::~unordered_map()
PUBLIC 1b70 0 std::_Hashtable<int, std::pair<int const, char const*>, std::allocator<std::pair<int const, char const*> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::clear()
PUBLIC 1bc0 0 std::_Hashtable<int, std::pair<int const, char const*>, std::allocator<std::pair<int const, char const*> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_deallocate_buckets()
PUBLIC 1be0 0 std::_Hashtable<int, std::pair<int const, char const*>, std::allocator<std::pair<int const, char const*> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC 1d10 0 std::_Hashtable<int, std::pair<int const, char const*>, std::allocator<std::pair<int const, char const*> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_Hashtable<std::pair<int const, char const*> const*>(std::pair<int const, char const*> const*, std::pair<int const, char const*> const*, unsigned long, std::hash<int> const&, std::equal_to<int> const&, std::allocator<std::pair<int const, char const*> > const&, std::integral_constant<bool, true>)
PUBLIC 205c 0 _fini
STACK CFI INIT 1980 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 19f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 19f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19fc x19: .cfa -16 + ^
STACK CFI 1a34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1a40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b00 6c .cfa: sp 0 + .ra: x30
STACK CFI 1b04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b0c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1b5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1b68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1a50 a4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b70 4c .cfa: sp 0 + .ra: x30
STACK CFI 1b74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1b7c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1bb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1bc0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1be0 12c .cfa: sp 0 + .ra: x30
STACK CFI 1be4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1bf0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1bf8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1c9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1ca0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1d10 34c .cfa: sp 0 + .ra: x30
STACK CFI 1d14 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1d2c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1d34 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1d40 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1d4c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1e38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1e3c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1790 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 1794 .cfa: sp 304 + .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 17b4 x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 1954 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1960 .cfa: sp 304 + .ra: .cfa -296 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x29: .cfa -304 + ^
