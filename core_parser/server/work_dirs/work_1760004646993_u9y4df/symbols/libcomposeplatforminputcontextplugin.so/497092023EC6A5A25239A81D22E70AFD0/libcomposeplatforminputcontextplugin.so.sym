MODULE Linux arm64 497092023EC6A5A25239A81D22E70AFD0 libcomposeplatforminputcontextplugin.so
INFO CODE_ID 02927049C63EA2A55239A81D22E70AFDF5742343
PUBLIC 2a50 0 qt_plugin_query_metadata
PUBLIC 2a70 0 qt_plugin_instance
STACK CFI INIT 2890 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 28c0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2900 48 .cfa: sp 0 + .ra: x30
STACK CFI 2904 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 290c x19: .cfa -16 + ^
STACK CFI 2944 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2950 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2960 18 .cfa: sp 0 + .ra: x30
STACK CFI 2968 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2970 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2980 88 .cfa: sp 0 + .ra: x30
STACK CFI 2988 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29a0 x19: .cfa -16 + ^
STACK CFI 29e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2a00 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2a10 38 .cfa: sp 0 + .ra: x30
STACK CFI 2a18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2a2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2a34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2a38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2c20 24 .cfa: sp 0 + .ra: x30
STACK CFI 2c28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2c50 3c .cfa: sp 0 + .ra: x30
STACK CFI 2c58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c68 x19: .cfa -16 + ^
STACK CFI 2c84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2c90 5c .cfa: sp 0 + .ra: x30
STACK CFI 2ca4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2cac x19: .cfa -16 + ^
STACK CFI 2cd0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2cd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2ce0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2a50 20 .cfa: sp 0 + .ra: x30
STACK CFI 2a58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2a68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2a70 120 .cfa: sp 0 + .ra: x30
STACK CFI 2a78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ad4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2adc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2ae4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2b3c x21: x21 x22: x22
STACK CFI 2b4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2b88 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2b8c x21: x21 x22: x22
STACK CFI INIT 2b90 70 .cfa: sp 0 + .ra: x30
STACK CFI 2ba0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ba8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2bd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2bdc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2bec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2c00 18 .cfa: sp 0 + .ra: x30
STACK CFI 2c08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2cf0 1c .cfa: sp 0 + .ra: x30
STACK CFI 2cf8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2d10 1c .cfa: sp 0 + .ra: x30
STACK CFI 2d18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2d30 44 .cfa: sp 0 + .ra: x30
STACK CFI 2d38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d44 x19: .cfa -16 + ^
STACK CFI 2d6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2d74 2c .cfa: sp 0 + .ra: x30
STACK CFI 2d7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2d94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2da0 38 .cfa: sp 0 + .ra: x30
STACK CFI 2da8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2dbc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2dc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2dc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2de0 38 .cfa: sp 0 + .ra: x30
STACK CFI 2de8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2df8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2e00 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2e08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2e20 50 .cfa: sp 0 + .ra: x30
STACK CFI 2e28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e34 x19: .cfa -16 + ^
STACK CFI 2e68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2e70 98 .cfa: sp 0 + .ra: x30
STACK CFI 2e78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2e80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ea8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2eb8 x21: .cfa -16 + ^
STACK CFI 2efc x21: x21
STACK CFI 2f00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2f10 490 .cfa: sp 0 + .ra: x30
STACK CFI 2f18 .cfa: sp 128 +
STACK CFI 2f1c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2f24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 301c .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 3068 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3070 x23: .cfa -16 + ^
STACK CFI 3158 x21: x21 x22: x22
STACK CFI 315c x23: x23
STACK CFI 3160 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 317c x21: x21 x22: x22 x23: x23
STACK CFI 3194 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 319c x23: .cfa -16 + ^
STACK CFI 32b8 x21: x21 x22: x22
STACK CFI 32bc x23: x23
STACK CFI 32c0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 32fc x21: x21 x22: x22 x23: x23
STACK CFI 3330 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 3394 x21: x21 x22: x22 x23: x23
STACK CFI 3398 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 339c x23: .cfa -16 + ^
STACK CFI INIT 33a0 134 .cfa: sp 0 + .ra: x30
STACK CFI 33a8 .cfa: sp 80 +
STACK CFI 33b4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3478 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 34d4 2c .cfa: sp 0 + .ra: x30
STACK CFI 34dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 34f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3500 2c4 .cfa: sp 0 + .ra: x30
STACK CFI 3508 .cfa: sp 224 +
STACK CFI 350c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3514 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3538 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3570 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3578 .cfa: sp 224 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 35dc x23: .cfa -16 + ^
STACK CFI 36d8 x23: x23
STACK CFI 3704 x23: .cfa -16 + ^
STACK CFI 3708 x23: x23
STACK CFI 370c x23: .cfa -16 + ^
STACK CFI 37b0 x23: x23
STACK CFI 37c0 x23: .cfa -16 + ^
STACK CFI INIT 37c4 18 .cfa: sp 0 + .ra: x30
STACK CFI 37cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 37d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 37e0 70 .cfa: sp 0 + .ra: x30
STACK CFI 37f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37f8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 382c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 383c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3850 7c .cfa: sp 0 + .ra: x30
STACK CFI 3858 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3860 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 386c x21: .cfa -16 + ^
STACK CFI 3890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3898 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 38d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2850 24 .cfa: sp 0 + .ra: x30
STACK CFI 2854 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 286c .cfa: sp 0 + .ra: .ra x29: x29
