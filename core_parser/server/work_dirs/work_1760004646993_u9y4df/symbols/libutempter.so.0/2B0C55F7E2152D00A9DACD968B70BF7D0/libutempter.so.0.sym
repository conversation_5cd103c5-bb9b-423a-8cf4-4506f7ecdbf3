MODULE Linux arm64 2B0C55F7E2152D00A9DACD968B70BF7D0 libutempter.so.0
INFO CODE_ID F7550C2B15E2002DA9DACD968B70BF7D51E5A6D5
PUBLIC cb0 0 utempter_add_record
PUBLIC d60 0 utempter_remove_record
PUBLIC e10 0 utempter_remove_added_record
PUBLIC e50 0 utempter_set_helper
PUBLIC e70 0 addToUtmp
PUBLIC e90 0 removeFromUtmp
PUBLIC eb0 0 removeLineFromUtmp
STACK CFI INIT a30 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT a60 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT aa0 48 .cfa: sp 0 + .ra: x30
STACK CFI aa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aac x19: .cfa -16 + ^
STACK CFI ae4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT af0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b00 1b0 .cfa: sp 0 + .ra: x30
STACK CFI b08 .cfa: sp 400 +
STACK CFI b14 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b28 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b68 x23: .cfa -16 + ^
STACK CFI ba0 x23: x23
STACK CFI bd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bdc .cfa: sp 400 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI c1c x23: x23
STACK CFI c28 x23: .cfa -16 + ^
STACK CFI INIT cb0 a8 .cfa: sp 0 + .ra: x30
STACK CFI cb8 .cfa: sp 80 +
STACK CFI ccc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI cd8 x19: .cfa -16 + ^
STACK CFI d4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI d54 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT d60 a8 .cfa: sp 0 + .ra: x30
STACK CFI d68 .cfa: sp 64 +
STACK CFI d7c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d88 x19: .cfa -16 + ^
STACK CFI dfc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e04 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT e10 38 .cfa: sp 0 + .ra: x30
STACK CFI e20 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e50 20 .cfa: sp 0 + .ra: x30
STACK CFI e58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e70 1c .cfa: sp 0 + .ra: x30
STACK CFI e78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e90 18 .cfa: sp 0 + .ra: x30
STACK CFI e98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ea0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT eb0 1c .cfa: sp 0 + .ra: x30
STACK CFI eb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ec4 .cfa: sp 0 + .ra: .ra x29: x29
