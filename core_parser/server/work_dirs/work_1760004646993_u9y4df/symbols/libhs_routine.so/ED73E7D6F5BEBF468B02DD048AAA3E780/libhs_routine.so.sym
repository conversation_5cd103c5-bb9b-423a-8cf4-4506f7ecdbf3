MODULE Linux arm64 ED73E7D6F5BEBF468B02DD048AAA3E780 libhs_routine.so
INFO CODE_ID D6E773EDBEF546BF8B02DD048AAA3E78
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC a070 24 0 init_have_lse_atomics
a070 4 45 0
a074 4 46 0
a078 4 45 0
a07c 4 46 0
a080 4 47 0
a084 4 47 0
a088 4 48 0
a08c 4 47 0
a090 4 48 0
PUBLIC 6568 0 _init
PUBLIC 6d40 0 __static_initialization_and_destruction_0()
PUBLIC 83d0 0 _GLOBAL__sub_I_summary.cc
PUBLIC 83e0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&) [clone .constprop.0]
PUBLIC 84f0 0 void std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::_M_construct<char*>(char*, char*, std::forward_iterator_tag) [clone .isra.0]
PUBLIC 85c0 0 __static_initialization_and_destruction_0()
PUBLIC a060 0 _GLOBAL__sub_I_condition.cc
PUBLIC a094 0 call_weak_fn
PUBLIC a0b0 0 deregister_tm_clones
PUBLIC a0e0 0 register_tm_clones
PUBLIC a120 0 __do_global_dtors_aux
PUBLIC a170 0 frame_dummy
PUBLIC a180 0 void std::__adjust_heap<__gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, long, double, __gnu_cxx::__ops::_Iter_less_iter>(__gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, long, long, double, __gnu_cxx::__ops::_Iter_less_iter) [clone .isra.0]
PUBLIC a280 0 void std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::_M_construct<char*>(char*, char*, std::forward_iterator_tag) [clone .isra.0]
PUBLIC a350 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&) [clone .constprop.0]
PUBLIC a460 0 std::_Rb_tree<hesai::sys::StatusRank, std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<hesai::sys::StatusRank>, std::allocator<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_M_erase(std::_Rb_tree_node<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >*) [clone .isra.0]
PUBLIC a790 0 std::_Rb_tree<int, std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<int>, std::allocator<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_M_erase(std::_Rb_tree_node<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >*) [clone .isra.0]
PUBLIC aac0 0 hesai::routine::CalibSummary::CalibSummary(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >)
PUBLIC ac10 0 hesai::routine::CalibSummary::CalStatistic(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&)
PUBLIC ce00 0 hesai::routine::CalibSummary::Init()
PUBLIC fbe0 0 hesai::routine::CalibSummary::Submit()
PUBLIC 10da0 0 std::ctype<char>::do_widen(char) const
PUBLIC 10db0 0 std::_Sp_counted_ptr<hesai::sys::PathManager*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 10dc0 0 std::_Sp_counted_ptr<hesai::sys::PathManager*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 10dd0 0 std::_Sp_counted_ptr<hesai::sys::PathManager*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 10de0 0 std::_Sp_counted_ptr<hesai::sys::PathManager*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 10df0 0 std::vector<int, std::allocator<int> >::~vector()
PUBLIC 10e10 0 std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::~vector()
PUBLIC 10ea0 0 std::_Sp_counted_ptr<hesai::sys::PathManager*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 10f30 0 std::map<hesai::sys::StatusRank, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::less<hesai::sys::StatusRank>, std::allocator<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::~map()
PUBLIC 10fc0 0 std::map<int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::less<int>, std::allocator<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::~map()
PUBLIC 11050 0 std::__cxx11::to_string(int)
PUBLIC 11320 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release_last_use_cold()
PUBLIC 113a0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > std::operator+<char, std::char_traits<char>, std::allocator<char> >(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >&&)
PUBLIC 114d0 0 FormatLiLog::LogError(char const*)
PUBLIC 11970 0 hesai::LiLogger::LiLogger(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, hesai::log_rank_t)
PUBLIC 11be0 0 hesai::Logger::log(hesai::log_rank_t, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 13de0 0 hesai::LiLogger::~LiLogger()
PUBLIC 14a90 0 std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::vector(std::initializer_list<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > const&)
PUBLIC 14cb0 0 std::map<hesai::sys::StatusRank, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::less<hesai::sys::StatusRank>, std::allocator<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::map(std::initializer_list<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<hesai::sys::StatusRank> const&, std::allocator<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&)
PUBLIC 14f30 0 std::map<int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::less<int>, std::allocator<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::map(std::initializer_list<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<int> const&, std::allocator<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&)
PUBLIC 151b0 0 std::vector<std::experimental::filesystem::v1::__cxx11::path::_Cmpt, std::allocator<std::experimental::filesystem::v1::__cxx11::path::_Cmpt> >::~vector()
PUBLIC 15410 0 std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::vector<char const* const*, void>(char const* const*, char const* const*, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > const&)
PUBLIC 15690 0 std::vector<double, std::allocator<double> >::~vector()
PUBLIC 156b0 0 void std::vector<double, std::allocator<double> >::_M_realloc_insert<double const&>(__gnu_cxx::__normal_iterator<double*, std::vector<double, std::allocator<double> > >, double const&)
PUBLIC 15830 0 hesai::sys::PathManager::~PathManager()
PUBLIC 158b0 0 std::_Rb_tree<hesai::io::ply::Type, std::pair<hesai::io::ply::Type const, int>, std::_Select1st<std::pair<hesai::io::ply::Type const, int> >, std::less<hesai::io::ply::Type>, std::allocator<std::pair<hesai::io::ply::Type const, int> > >::_M_erase(std::_Rb_tree_node<std::pair<hesai::io::ply::Type const, int> >*) [clone .isra.0]
PUBLIC 15a30 0 std::_Rb_tree<hesai::sys::StatusRank, std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<hesai::sys::StatusRank>, std::allocator<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_M_erase(std::_Rb_tree_node<std::pair<hesai::sys::StatusRank const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >*) [clone .isra.0]
PUBLIC 15d60 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName>, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> > >::_M_erase(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> >*) [clone .isra.0]
PUBLIC 16090 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type>, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> > >::_M_erase(std::_Rb_tree_node<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> >*) [clone .isra.0]
PUBLIC 163c0 0 std::_Rb_tree<int, std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::_Select1st<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >, std::less<int>, std::allocator<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > >::_M_erase(std::_Rb_tree_node<std::pair<int const, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >*) [clone .isra.0]
PUBLIC 166f0 0 hesai::io::ConvertInputInsData(hesai::io::InputInsData const&, double, int)
PUBLIC 168a0 0 hesai::routine::VehicleCondition::VehicleCondition()
PUBLIC 168c0 0 hesai::routine::VehicleCondition::Reset()
PUBLIC 168e0 0 hesai::routine::VehicleCondition::AddInsData(hesai::io::InputInsData const&, double)
PUBLIC 16e40 0 std::map<hesai::io::ply::Type, int, std::less<hesai::io::ply::Type>, std::allocator<std::pair<hesai::io::ply::Type const, int> > >::~map()
PUBLIC 16e80 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, hesai::io::ply::FieldName, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> > >::~map()
PUBLIC 16f10 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, hesai::io::ply::Type, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> > >::~map()
PUBLIC 16fa0 0 hesai::ds::PoseStamped::Info[abi:cxx11]() const
PUBLIC 18040 0 hesai::ds::InsKR::Info[abi:cxx11]() const
PUBLIC 19120 0 std::vector<int, std::allocator<int> >::vector(std::initializer_list<int>, std::allocator<int> const&)
PUBLIC 191f0 0 std::map<hesai::io::ply::Type, int, std::less<hesai::io::ply::Type>, std::allocator<std::pair<hesai::io::ply::Type const, int> > >::map(std::initializer_list<std::pair<hesai::io::ply::Type const, int> >, std::less<hesai::io::ply::Type> const&, std::allocator<std::pair<hesai::io::ply::Type const, int> > const&)
PUBLIC 19350 0 Eigen::Matrix<double, 3, 1, 0, 3, 1> hesai::ds::RPYfromQuaternion<double>(Eigen::Quaternion<double, 0> const&)
PUBLIC 19740 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type>, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> > >::_M_get_insert_unique_pos(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 198a0 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, hesai::io::ply::Type, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> > >::map(std::initializer_list<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > const&, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::Type> > const&)
PUBLIC 19b60 0 std::_Rb_tree<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName>, std::_Select1st<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> > >::_M_get_insert_unique_pos(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 19cc0 0 std::map<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, hesai::io::ply::FieldName, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> > >::map(std::initializer_list<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> >, std::less<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > const&, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, hesai::io::ply::FieldName> > const&)
PUBLIC 19f80 0 __aarch64_ldadd4_acq_rel
PUBLIC 19fb0 0 _fini
STACK CFI INIT a0b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT a0e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT a120 48 .cfa: sp 0 + .ra: x30
STACK CFI a124 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a12c x19: .cfa -16 + ^
STACK CFI a164 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a170 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10da0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10db0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10dc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10dd0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10de0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a180 100 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10df0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT a280 c8 .cfa: sp 0 + .ra: x30
STACK CFI a284 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a294 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a29c x21: .cfa -32 + ^
STACK CFI a308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a30c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 10e10 90 .cfa: sp 0 + .ra: x30
STACK CFI 10e14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10e1c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10e24 x21: .cfa -16 + ^
STACK CFI 10e78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 10e7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 10e9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a350 104 .cfa: sp 0 + .ra: x30
STACK CFI a354 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a364 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a36c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a3e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a3ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 10ea0 88 .cfa: sp 0 + .ra: x30
STACK CFI 10ea4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10eac x19: .cfa -16 + ^
STACK CFI 10f18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10f1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 10f24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a460 330 .cfa: sp 0 + .ra: x30
STACK CFI a468 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a470 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a478 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a484 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a4a8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a4ac x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a60c x21: x21 x22: x22
STACK CFI a610 x27: x27 x28: x28
STACK CFI a734 x25: x25 x26: x26
STACK CFI a788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 10f30 88 .cfa: sp 0 + .ra: x30
STACK CFI 10f34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10f3c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10fb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a790 330 .cfa: sp 0 + .ra: x30
STACK CFI a798 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a7a0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a7a8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a7b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a7d8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a7dc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a93c x21: x21 x22: x22
STACK CFI a940 x27: x27 x28: x28
STACK CFI aa64 x25: x25 x26: x26
STACK CFI aab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 10fc0 88 .cfa: sp 0 + .ra: x30
STACK CFI 10fc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10fcc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 11044 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 11050 2cc .cfa: sp 0 + .ra: x30
STACK CFI 11054 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 11068 x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 11074 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 11204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11208 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x29: .cfa -272 + ^
STACK CFI INIT aac0 150 .cfa: sp 0 + .ra: x30
STACK CFI aac4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI aad8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI aae8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI aaf0 x23: .cfa -32 + ^
STACK CFI ab8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ab90 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 11320 78 .cfa: sp 0 + .ra: x30
STACK CFI 11324 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11334 x19: .cfa -16 + ^
STACK CFI 11368 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1136c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1137c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 11388 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 113a0 128 .cfa: sp 0 + .ra: x30
STACK CFI 113a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 113b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 113c8 x21: .cfa -16 + ^
STACK CFI 11454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 11458 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 114d0 49c .cfa: sp 0 + .ra: x30
STACK CFI 114d4 .cfa: sp 592 +
STACK CFI 114e0 .ra: .cfa -584 + ^ x29: .cfa -592 + ^
STACK CFI 114ec x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x22: .cfa -552 + ^
STACK CFI 114f4 x23: .cfa -544 + ^ x24: .cfa -536 + ^
STACK CFI 114fc x25: .cfa -528 + ^ x26: .cfa -520 + ^
STACK CFI 11504 x27: .cfa -512 + ^ x28: .cfa -504 + ^
STACK CFI 11800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 11804 .cfa: sp 592 + .ra: .cfa -584 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x22: .cfa -552 + ^ x23: .cfa -544 + ^ x24: .cfa -536 + ^ x25: .cfa -528 + ^ x26: .cfa -520 + ^ x27: .cfa -512 + ^ x28: .cfa -504 + ^ x29: .cfa -592 + ^
STACK CFI INIT 11970 264 .cfa: sp 0 + .ra: x30
STACK CFI 11974 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 11984 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 11990 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 119a4 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 11af0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 11af4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 11be0 21fc .cfa: sp 0 + .ra: x30
STACK CFI 11be4 .cfa: sp 1216 +
STACK CFI 11bf0 .ra: .cfa -1208 + ^ x29: .cfa -1216 + ^
STACK CFI 11bf8 x19: .cfa -1200 + ^ x20: .cfa -1192 + ^
STACK CFI 11c40 x21: .cfa -1184 + ^ x22: .cfa -1176 + ^
STACK CFI 11c44 x23: .cfa -1168 + ^ x24: .cfa -1160 + ^
STACK CFI 11c48 x25: .cfa -1152 + ^ x26: .cfa -1144 + ^
STACK CFI 11c4c x27: .cfa -1136 + ^ x28: .cfa -1128 + ^
STACK CFI 11c50 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 11c54 x21: .cfa -1184 + ^ x22: .cfa -1176 + ^
STACK CFI 11c5c x23: .cfa -1168 + ^ x24: .cfa -1160 + ^
STACK CFI 11c60 x25: .cfa -1152 + ^ x26: .cfa -1144 + ^
STACK CFI 11c64 x27: .cfa -1136 + ^ x28: .cfa -1128 + ^
STACK CFI 12390 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 12394 x21: .cfa -1184 + ^ x22: .cfa -1176 + ^
STACK CFI 12398 x23: .cfa -1168 + ^ x24: .cfa -1160 + ^
STACK CFI 1239c x25: .cfa -1152 + ^ x26: .cfa -1144 + ^
STACK CFI 123a0 x27: .cfa -1136 + ^ x28: .cfa -1128 + ^
STACK CFI 12b64 x21: x21 x22: x22
STACK CFI 12b68 x23: x23 x24: x24
STACK CFI 12b6c x25: x25 x26: x26
STACK CFI 12b70 x27: x27 x28: x28
STACK CFI 12b9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12ba0 .cfa: sp 1216 + .ra: .cfa -1208 + ^ x19: .cfa -1200 + ^ x20: .cfa -1192 + ^ x29: .cfa -1216 + ^
STACK CFI 12ba8 x21: .cfa -1184 + ^ x22: .cfa -1176 + ^
STACK CFI 12bb0 x23: .cfa -1168 + ^ x24: .cfa -1160 + ^
STACK CFI 12bb8 x25: .cfa -1152 + ^ x26: .cfa -1144 + ^
STACK CFI 12bbc x27: .cfa -1136 + ^ x28: .cfa -1128 + ^
STACK CFI 1309c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 130a4 x21: .cfa -1184 + ^ x22: .cfa -1176 + ^
STACK CFI 130ac x23: .cfa -1168 + ^ x24: .cfa -1160 + ^
STACK CFI 130b4 x25: .cfa -1152 + ^ x26: .cfa -1144 + ^
STACK CFI 130b8 x27: .cfa -1136 + ^ x28: .cfa -1128 + ^
STACK CFI 13838 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 13854 x21: .cfa -1184 + ^ x22: .cfa -1176 + ^
STACK CFI 13858 x23: .cfa -1168 + ^ x24: .cfa -1160 + ^
STACK CFI 1385c x25: .cfa -1152 + ^ x26: .cfa -1144 + ^
STACK CFI 13860 x27: .cfa -1136 + ^ x28: .cfa -1128 + ^
STACK CFI 13868 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1386c x21: .cfa -1184 + ^ x22: .cfa -1176 + ^
STACK CFI 13870 x23: .cfa -1168 + ^ x24: .cfa -1160 + ^
STACK CFI 13874 x25: .cfa -1152 + ^ x26: .cfa -1144 + ^
STACK CFI 13878 x27: .cfa -1136 + ^ x28: .cfa -1128 + ^
STACK CFI INIT 13de0 cac .cfa: sp 0 + .ra: x30
STACK CFI 13de4 .cfa: sp 368 + .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 13df4 x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 13ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13ed8 .cfa: sp 368 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x29: .cfa -368 + ^
STACK CFI 13ee0 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 13ee8 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 140a0 x21: x21 x22: x22
STACK CFI 140a4 x23: x23 x24: x24
STACK CFI 140a8 x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 141c4 x21: x21 x22: x22
STACK CFI 141c8 x23: x23 x24: x24
STACK CFI 141cc x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI 14820 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 14824 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 14828 x23: .cfa -320 + ^ x24: .cfa -312 + ^
STACK CFI INIT 14a90 21c .cfa: sp 0 + .ra: x30
STACK CFI 14a94 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 14a9c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 14aac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 14ad8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 14ae8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 14ba8 x21: x21 x22: x22
STACK CFI 14bd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 14bdc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 14be4 x21: x21 x22: x22
STACK CFI 14bf4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 14bfc x21: x21 x22: x22
STACK CFI 14c00 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 14c28 x21: x21 x22: x22
STACK CFI 14c2c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT 14cb0 278 .cfa: sp 0 + .ra: x30
STACK CFI 14cb4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 14cbc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 14cd0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 14cec x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 14d10 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 14d18 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 14dbc x21: x21 x22: x22
STACK CFI 14dc0 x27: x27 x28: x28
STACK CFI 14dec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 14df0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 14eb0 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 14eb4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 14eb8 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 14f30 278 .cfa: sp 0 + .ra: x30
STACK CFI 14f34 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 14f3c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 14f50 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 14f6c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 14f90 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 14f98 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 1503c x21: x21 x22: x22
STACK CFI 15040 x27: x27 x28: x28
STACK CFI 1506c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 15070 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 15130 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 15134 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 15138 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 151b0 254 .cfa: sp 0 + .ra: x30
STACK CFI 151b4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 151c0 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 151d4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 151d8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 151dc x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 15350 x19: x19 x20: x20
STACK CFI 15354 x23: x23 x24: x24
STACK CFI 15358 x27: x27 x28: x28
STACK CFI 1537c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 15380 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 153e4 x19: x19 x20: x20
STACK CFI 153ec x23: x23 x24: x24
STACK CFI 153f0 x27: x27 x28: x28
STACK CFI 15400 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI INIT 15410 278 .cfa: sp 0 + .ra: x30
STACK CFI 15414 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1541c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 15430 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1543c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1549c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 15554 x27: x27 x28: x28
STACK CFI 15588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1558c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 15594 x27: x27 x28: x28
STACK CFI 155b4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 155dc x27: x27 x28: x28
STACK CFI 155e0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 155e4 x27: x27 x28: x28
STACK CFI 15638 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 15684 x27: x27 x28: x28
STACK CFI INIT 6d40 168c .cfa: sp 0 + .ra: x30
STACK CFI 6d44 .cfa: sp 2528 +
STACK CFI 6d58 .ra: .cfa -2520 + ^ x29: .cfa -2528 + ^
STACK CFI 6d60 x19: .cfa -2512 + ^ x20: .cfa -2504 + ^
STACK CFI 6d6c x21: .cfa -2496 + ^ x22: .cfa -2488 + ^
STACK CFI 6d78 x23: .cfa -2480 + ^ x24: .cfa -2472 + ^
STACK CFI 6d88 x25: .cfa -2464 + ^ x26: .cfa -2456 + ^ x27: .cfa -2448 + ^ x28: .cfa -2440 + ^
STACK CFI 7eb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7ebc .cfa: sp 2528 + .ra: .cfa -2520 + ^ x19: .cfa -2512 + ^ x20: .cfa -2504 + ^ x21: .cfa -2496 + ^ x22: .cfa -2488 + ^ x23: .cfa -2480 + ^ x24: .cfa -2472 + ^ x25: .cfa -2464 + ^ x26: .cfa -2456 + ^ x27: .cfa -2448 + ^ x28: .cfa -2440 + ^ x29: .cfa -2528 + ^
STACK CFI INIT 15690 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 156b0 180 .cfa: sp 0 + .ra: x30
STACK CFI 156b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 156bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 156cc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 156d8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 15760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 15764 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT ac10 21e8 .cfa: sp 0 + .ra: x30
STACK CFI ac14 .cfa: sp 1168 +
STACK CFI ac20 .ra: .cfa -1160 + ^ x29: .cfa -1168 + ^
STACK CFI ac28 x19: .cfa -1152 + ^ x20: .cfa -1144 + ^
STACK CFI ac48 v10: .cfa -1056 + ^ v11: .cfa -1048 + ^ v8: .cfa -1072 + ^ v9: .cfa -1064 + ^ x21: .cfa -1136 + ^ x22: .cfa -1128 + ^ x23: .cfa -1120 + ^ x24: .cfa -1112 + ^ x25: .cfa -1104 + ^ x26: .cfa -1096 + ^ x27: .cfa -1088 + ^ x28: .cfa -1080 + ^
STACK CFI c1d8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c1dc .cfa: sp 1168 + .ra: .cfa -1160 + ^ v10: .cfa -1056 + ^ v11: .cfa -1048 + ^ v8: .cfa -1072 + ^ v9: .cfa -1064 + ^ x19: .cfa -1152 + ^ x20: .cfa -1144 + ^ x21: .cfa -1136 + ^ x22: .cfa -1128 + ^ x23: .cfa -1120 + ^ x24: .cfa -1112 + ^ x25: .cfa -1104 + ^ x26: .cfa -1096 + ^ x27: .cfa -1088 + ^ x28: .cfa -1080 + ^ x29: .cfa -1168 + ^
STACK CFI INIT 15830 78 .cfa: sp 0 + .ra: x30
STACK CFI 15834 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15840 x19: .cfa -16 + ^
STACK CFI 15898 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1589c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 158a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 83d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ce00 2ddc .cfa: sp 0 + .ra: x30
STACK CFI ce04 .cfa: sp 1248 +
STACK CFI ce10 .ra: .cfa -1240 + ^ x29: .cfa -1248 + ^
STACK CFI ce18 x19: .cfa -1232 + ^ x20: .cfa -1224 + ^
STACK CFI ce30 x21: .cfa -1216 + ^ x22: .cfa -1208 + ^ x23: .cfa -1200 + ^ x24: .cfa -1192 + ^ x25: .cfa -1184 + ^ x26: .cfa -1176 + ^ x27: .cfa -1168 + ^ x28: .cfa -1160 + ^
STACK CFI e354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e358 .cfa: sp 1248 + .ra: .cfa -1240 + ^ x19: .cfa -1232 + ^ x20: .cfa -1224 + ^ x21: .cfa -1216 + ^ x22: .cfa -1208 + ^ x23: .cfa -1200 + ^ x24: .cfa -1192 + ^ x25: .cfa -1184 + ^ x26: .cfa -1176 + ^ x27: .cfa -1168 + ^ x28: .cfa -1160 + ^ x29: .cfa -1248 + ^
STACK CFI INIT fbe0 11bc .cfa: sp 0 + .ra: x30
STACK CFI fbe4 .cfa: sp 1584 +
STACK CFI fbf0 .ra: .cfa -1576 + ^ x29: .cfa -1584 + ^
STACK CFI fbf8 x19: .cfa -1568 + ^ x20: .cfa -1560 + ^
STACK CFI fc04 x21: .cfa -1552 + ^ x22: .cfa -1544 + ^ x23: .cfa -1536 + ^ x24: .cfa -1528 + ^
STACK CFI fc14 x25: .cfa -1520 + ^ x26: .cfa -1512 + ^ x27: .cfa -1504 + ^ x28: .cfa -1496 + ^
STACK CFI fdf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI fdf4 .cfa: sp 1584 + .ra: .cfa -1576 + ^ x19: .cfa -1568 + ^ x20: .cfa -1560 + ^ x21: .cfa -1552 + ^ x22: .cfa -1544 + ^ x23: .cfa -1536 + ^ x24: .cfa -1528 + ^ x25: .cfa -1520 + ^ x26: .cfa -1512 + ^ x27: .cfa -1504 + ^ x28: .cfa -1496 + ^ x29: .cfa -1584 + ^
STACK CFI INIT 83e0 104 .cfa: sp 0 + .ra: x30
STACK CFI 83e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 83f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 83fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 847c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 84f0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 84f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8504 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 850c x21: .cfa -32 + ^
STACK CFI 857c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8580 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 158b0 180 .cfa: sp 0 + .ra: x30
STACK CFI 158b8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 158c0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 158c8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 158d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 158f8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 158fc x27: .cfa -16 + ^
STACK CFI 15950 x21: x21 x22: x22
STACK CFI 15954 x27: x27
STACK CFI 15970 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 1598c x21: x21 x22: x22 x27: x27
STACK CFI 159a8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 159c4 x21: x21 x22: x22 x27: x27
STACK CFI 15a00 x25: x25 x26: x26
STACK CFI 15a28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 16e40 3c .cfa: sp 0 + .ra: x30
STACK CFI 16e44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16e4c x19: .cfa -16 + ^
STACK CFI 16e78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 15a30 330 .cfa: sp 0 + .ra: x30
STACK CFI 15a38 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 15a40 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 15a48 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 15a54 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 15a78 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 15a7c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 15bdc x21: x21 x22: x22
STACK CFI 15be0 x27: x27 x28: x28
STACK CFI 15d04 x25: x25 x26: x26
STACK CFI 15d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 15d60 330 .cfa: sp 0 + .ra: x30
STACK CFI 15d68 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 15d70 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 15d78 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 15d84 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 15da8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 15dac x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 15f0c x21: x21 x22: x22
STACK CFI 15f10 x27: x27 x28: x28
STACK CFI 16034 x25: x25 x26: x26
STACK CFI 16088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 16e80 88 .cfa: sp 0 + .ra: x30
STACK CFI 16e84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16e8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 16f04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 16090 330 .cfa: sp 0 + .ra: x30
STACK CFI 16098 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 160a0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 160a8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 160b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 160d8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 160dc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1623c x21: x21 x22: x22
STACK CFI 16240 x27: x27 x28: x28
STACK CFI 16364 x25: x25 x26: x26
STACK CFI 163b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 16f10 88 .cfa: sp 0 + .ra: x30
STACK CFI 16f14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16f1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 16f94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 163c0 330 .cfa: sp 0 + .ra: x30
STACK CFI 163c8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 163d0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 163d8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 163e4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 16408 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1640c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1656c x21: x21 x22: x22
STACK CFI 16570 x27: x27 x28: x28
STACK CFI 16694 x25: x25 x26: x26
STACK CFI 166e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 166f0 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 166f4 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 16700 x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 16720 x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 16738 x25: .cfa -208 + ^
STACK CFI 16848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1684c .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x29: .cfa -272 + ^
STACK CFI INIT 168a0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 168c0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16fa0 1094 .cfa: sp 0 + .ra: x30
STACK CFI 16fa4 .cfa: sp 1568 +
STACK CFI 16fb0 .ra: .cfa -1560 + ^ x29: .cfa -1568 + ^
STACK CFI 16fbc x19: .cfa -1552 + ^ x20: .cfa -1544 + ^ x21: .cfa -1536 + ^ x22: .cfa -1528 + ^
STACK CFI 16fc4 x23: .cfa -1520 + ^ x24: .cfa -1512 + ^
STACK CFI 16fd0 x25: .cfa -1504 + ^ x26: .cfa -1496 + ^ x27: .cfa -1488 + ^ x28: .cfa -1480 + ^
STACK CFI 16fe8 v8: .cfa -1472 + ^
STACK CFI 17bd8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 17bdc .cfa: sp 1568 + .ra: .cfa -1560 + ^ v8: .cfa -1472 + ^ x19: .cfa -1552 + ^ x20: .cfa -1544 + ^ x21: .cfa -1536 + ^ x22: .cfa -1528 + ^ x23: .cfa -1520 + ^ x24: .cfa -1512 + ^ x25: .cfa -1504 + ^ x26: .cfa -1496 + ^ x27: .cfa -1488 + ^ x28: .cfa -1480 + ^ x29: .cfa -1568 + ^
STACK CFI INIT 18040 10d4 .cfa: sp 0 + .ra: x30
STACK CFI 18044 .cfa: sp 1568 +
STACK CFI 18050 .ra: .cfa -1560 + ^ x29: .cfa -1568 + ^
STACK CFI 1805c x19: .cfa -1552 + ^ x20: .cfa -1544 + ^ x21: .cfa -1536 + ^ x22: .cfa -1528 + ^
STACK CFI 18064 x23: .cfa -1520 + ^ x24: .cfa -1512 + ^
STACK CFI 18070 x25: .cfa -1504 + ^ x26: .cfa -1496 + ^ x27: .cfa -1488 + ^ x28: .cfa -1480 + ^
STACK CFI 18088 v8: .cfa -1472 + ^
STACK CFI 18cb8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 18cbc .cfa: sp 1568 + .ra: .cfa -1560 + ^ v8: .cfa -1472 + ^ x19: .cfa -1552 + ^ x20: .cfa -1544 + ^ x21: .cfa -1536 + ^ x22: .cfa -1528 + ^ x23: .cfa -1520 + ^ x24: .cfa -1512 + ^ x25: .cfa -1504 + ^ x26: .cfa -1496 + ^ x27: .cfa -1488 + ^ x28: .cfa -1480 + ^ x29: .cfa -1568 + ^
STACK CFI INIT 19120 d0 .cfa: sp 0 + .ra: x30
STACK CFI 19124 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1912c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 19138 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 19190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19194 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 191b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 191b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 191f0 158 .cfa: sp 0 + .ra: x30
STACK CFI 191f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 191fc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 19204 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 19220 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1922c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1929c x19: x19 x20: x20
STACK CFI 192a0 x21: x21 x22: x22
STACK CFI 192ac .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 192b0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 19350 3f0 .cfa: sp 0 + .ra: x30
STACK CFI 19354 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 19364 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 1936c v8: .cfa -96 + ^ v9: .cfa -88 + ^
STACK CFI 19374 v12: .cfa -64 + ^ v13: .cfa -56 + ^
STACK CFI 1937c v10: .cfa -80 + ^ v11: .cfa -72 + ^
STACK CFI 193a4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 193dc v14: .cfa -48 + ^
STACK CFI 1943c v14: v14
STACK CFI 19480 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19484 .cfa: sp 144 + .ra: .cfa -136 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v12: .cfa -64 + ^ v13: .cfa -56 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI 195a8 v14: .cfa -48 + ^
STACK CFI 195ac v14: v14
STACK CFI 19730 v14: .cfa -48 + ^
STACK CFI 19738 v14: v14
STACK CFI 1973c v14: .cfa -48 + ^
STACK CFI INIT 168e0 558 .cfa: sp 0 + .ra: x30
STACK CFI 168e4 .cfa: sp 496 + .ra: .cfa -488 + ^ x29: .cfa -496 + ^
STACK CFI 168fc x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI 16910 v8: .cfa -448 + ^ v9: .cfa -440 + ^
STACK CFI 16920 x21: .cfa -464 + ^
STACK CFI 16998 x21: x21
STACK CFI 169c0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 169c4 .cfa: sp 496 + .ra: .cfa -488 + ^ v8: .cfa -448 + ^ v9: .cfa -440 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x29: .cfa -496 + ^
STACK CFI 169d8 x21: .cfa -464 + ^
STACK CFI 16a24 v10: .cfa -432 + ^ v11: .cfa -424 + ^
STACK CFI 16a40 v10: v10 v11: v11
STACK CFI 16a44 v10: .cfa -432 + ^ v11: .cfa -424 + ^
STACK CFI 16ce8 x21: x21
STACK CFI 16cec v10: v10 v11: v11
STACK CFI 16cf0 v10: .cfa -432 + ^ v11: .cfa -424 + ^ x21: .cfa -464 + ^
STACK CFI 16d90 v10: v10 v11: v11 x21: x21
STACK CFI 16d94 x21: .cfa -464 + ^
STACK CFI 16d98 v10: .cfa -432 + ^ v11: .cfa -424 + ^
STACK CFI 16d9c v10: v10 v11: v11
STACK CFI 16db8 v10: .cfa -432 + ^ v11: .cfa -424 + ^
STACK CFI 16dc0 v10: v10 v11: v11
STACK CFI 16de8 v10: .cfa -432 + ^ v11: .cfa -424 + ^
STACK CFI 16e24 v10: v10 v11: v11
STACK CFI 16e28 v10: .cfa -432 + ^ v11: .cfa -424 + ^
STACK CFI INIT 19740 154 .cfa: sp 0 + .ra: x30
STACK CFI 19744 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1974c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 19758 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 19760 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 19768 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 19824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 19828 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 198a0 2bc .cfa: sp 0 + .ra: x30
STACK CFI 198a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 198ac x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 198c0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 198dc x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 198fc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 19904 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 199f0 x21: x21 x22: x22
STACK CFI 199f4 x27: x27 x28: x28
STACK CFI 19a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 19a24 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 19ae4 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 19ae8 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 19aec x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 19b60 154 .cfa: sp 0 + .ra: x30
STACK CFI 19b64 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 19b6c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 19b78 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 19b80 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 19b88 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 19c44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 19c48 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 19cc0 2bc .cfa: sp 0 + .ra: x30
STACK CFI 19cc4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 19ccc x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 19ce0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 19cfc x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 19d1c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 19d24 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 19e10 x21: x21 x22: x22
STACK CFI 19e14 x27: x27 x28: x28
STACK CFI 19e40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 19e44 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 19f04 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI 19f08 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 19f0c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI INIT 85c0 1a94 .cfa: sp 0 + .ra: x30
STACK CFI 85c4 .cfa: sp 2608 +
STACK CFI 85d8 .ra: .cfa -2600 + ^ x29: .cfa -2608 + ^
STACK CFI 85e4 x19: .cfa -2592 + ^ x20: .cfa -2584 + ^ x21: .cfa -2576 + ^ x22: .cfa -2568 + ^
STACK CFI 85fc x23: .cfa -2560 + ^ x24: .cfa -2552 + ^ x25: .cfa -2544 + ^ x26: .cfa -2536 + ^
STACK CFI 8608 v8: .cfa -2512 + ^ x27: .cfa -2528 + ^ x28: .cfa -2520 + ^
STACK CFI 9998 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 999c .cfa: sp 2608 + .ra: .cfa -2600 + ^ v8: .cfa -2512 + ^ x19: .cfa -2592 + ^ x20: .cfa -2584 + ^ x21: .cfa -2576 + ^ x22: .cfa -2568 + ^ x23: .cfa -2560 + ^ x24: .cfa -2552 + ^ x25: .cfa -2544 + ^ x26: .cfa -2536 + ^ x27: .cfa -2528 + ^ x28: .cfa -2520 + ^ x29: .cfa -2608 + ^
STACK CFI INIT a060 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19f80 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT a070 24 .cfa: sp 0 + .ra: x30
STACK CFI a074 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a08c .cfa: sp 0 + .ra: .ra x29: x29
