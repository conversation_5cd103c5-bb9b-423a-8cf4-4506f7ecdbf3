MODULE Linux arm64 7A4A3935A1D5833D3C0CB8E2B49D970E0 libpixbufloader-tga.so
INFO CODE_ID 35394A7AD5A13D833C0CB8E2B49D970E2539BC03
PUBLIC 2b74 0 fill_vtable
PUBLIC 2bb0 0 fill_info
STACK CFI INIT 1140 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1170 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 11b0 48 .cfa: sp 0 + .ra: x30
STACK CFI 11b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 11bc x19: .cfa -16 + ^
STACK CFI 11f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1200 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1210 88 .cfa: sp 0 + .ra: x30
STACK CFI 1218 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1220 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1280 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1288 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 12a0 148 .cfa: sp 0 + .ra: x30
STACK CFI 12a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12b8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1334 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 133c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 13f0 158 .cfa: sp 0 + .ra: x30
STACK CFI 13f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1404 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1410 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1430 x23: .cfa -16 + ^
STACK CFI 146c x23: x23
STACK CFI 1498 x19: x19 x20: x20
STACK CFI 149c x21: x21 x22: x22
STACK CFI 14a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 14a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 14b4 x23: x23
STACK CFI 14bc x19: x19 x20: x20
STACK CFI 14c4 x21: x21 x22: x22
STACK CFI 14c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 14d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 14f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 151c x23: .cfa -16 + ^
STACK CFI 1524 x23: x23
STACK CFI INIT 1550 17c .cfa: sp 0 + .ra: x30
STACK CFI 1558 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1560 x21: .cfa -16 + ^
STACK CFI 1568 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 161c x19: x19 x20: x20
STACK CFI 1628 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 1630 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1680 x19: x19 x20: x20
STACK CFI 16a8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 16d0 12c .cfa: sp 0 + .ra: x30
STACK CFI 16dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 16ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 176c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 17a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 17ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 17c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 1800 60 .cfa: sp 0 + .ra: x30
STACK CFI 1808 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1810 x19: .cfa -16 + ^
STACK CFI 1830 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1838 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1860 80 .cfa: sp 0 + .ra: x30
STACK CFI 1868 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1870 x19: .cfa -16 + ^
STACK CFI 18b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 18b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 18e0 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 18e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 18f4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 190c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 193c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1944 x25: .cfa -16 + ^
STACK CFI 199c x21: x21 x22: x22
STACK CFI 19a4 x19: x19 x20: x20
STACK CFI 19ac x23: x23 x24: x24
STACK CFI 19b0 x25: x25
STACK CFI 19b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 19bc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 19c0 x19: x19 x20: x20
STACK CFI 19c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 19d0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 19d4 x19: x19 x20: x20
STACK CFI 19d8 x23: x23 x24: x24
STACK CFI 19dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 19e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 19e8 x23: x23 x24: x24
STACK CFI 19f8 x19: x19 x20: x20
STACK CFI 19fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1a04 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 1a58 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 1a5c x19: x19 x20: x20
STACK CFI 1a64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1a6c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1a90 238 .cfa: sp 0 + .ra: x30
STACK CFI 1a98 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1aa0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1af4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1afc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 1b00 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1b04 x23: .cfa -16 + ^
STACK CFI 1c04 x21: x21 x22: x22
STACK CFI 1c08 x23: x23
STACK CFI 1c4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1c90 x21: x21 x22: x22
STACK CFI 1c98 x23: x23
STACK CFI 1c9c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1cc0 x21: x21 x22: x22
STACK CFI 1cc4 x23: x23
STACK CFI INIT 1cd0 418 .cfa: sp 0 + .ra: x30
STACK CFI 1cd8 .cfa: sp 128 +
STACK CFI 1cdc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1ce4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1cfc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1d68 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1da8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1ecc x25: x25 x26: x26
STACK CFI 1f1c x21: x21 x22: x22
STACK CFI 1f28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 1f30 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1ffc x25: x25 x26: x26
STACK CFI 200c x21: x21 x22: x22
STACK CFI 2038 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2064 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2068 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 20b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 20b8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 20e0 x25: x25 x26: x26
STACK CFI 20e4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 20f0 6e8 .cfa: sp 0 + .ra: x30
STACK CFI 20f8 .cfa: sp 176 +
STACK CFI 20fc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2104 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 211c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2180 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2184 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 231c x21: x21 x22: x22
STACK CFI 2320 x25: x25 x26: x26
STACK CFI 2384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 238c .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2764 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 279c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 27c4 x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 27d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 27d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 27e0 394 .cfa: sp 0 + .ra: x30
STACK CFI 27e8 .cfa: sp 80 +
STACK CFI 27f4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 27fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2804 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2914 x23: .cfa -16 + ^
STACK CFI 2974 x23: x23
STACK CFI 29a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 29b0 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2a2c x23: .cfa -16 + ^
STACK CFI 2a6c x23: x23
STACK CFI 2ab8 x23: .cfa -16 + ^
STACK CFI 2ac8 x23: x23
STACK CFI 2b00 x23: .cfa -16 + ^
STACK CFI 2b34 x23: x23
STACK CFI 2b3c x23: .cfa -16 + ^
STACK CFI 2b40 x23: x23
STACK CFI INIT 2b74 38 .cfa: sp 0 + .ra: x30
STACK CFI 2b7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2bb0 5c .cfa: sp 0 + .ra: x30
STACK CFI 2bb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2bcc .cfa: sp 0 + .ra: .ra x29: x29
