MODULE Linux arm64 46E82019B5E3D0543946EEEB72E82E960 libpipewire-module-raop-discover.so
INFO CODE_ID 1920E846E3B554D03946EEEB72E82E96B541D96D
PUBLIC 2bd0 0 pipewire__module_init
STACK CFI INIT 14b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1520 48 .cfa: sp 0 + .ra: x30
STACK CFI 1524 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 152c x19: .cfa -16 + ^
STACK CFI 1564 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1570 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1580 50 .cfa: sp 0 + .ra: x30
STACK CFI 1588 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1590 x19: .cfa -16 + ^
STACK CFI 15c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 15d0 1c .cfa: sp 0 + .ra: x30
STACK CFI 15d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 15f0 20 .cfa: sp 0 + .ra: x30
STACK CFI 15f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1604 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1610 6c .cfa: sp 0 + .ra: x30
STACK CFI 1618 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1620 x19: .cfa -16 + ^
STACK CFI 1660 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1668 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1674 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1680 50 .cfa: sp 0 + .ra: x30
STACK CFI 1688 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1690 x19: .cfa -16 + ^
STACK CFI 16c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 16d0 74 .cfa: sp 0 + .ra: x30
STACK CFI 16d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16e4 x19: .cfa -16 + ^
STACK CFI 1728 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1730 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 173c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1744 a8 .cfa: sp 0 + .ra: x30
STACK CFI 174c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1754 x21: .cfa -16 + ^
STACK CFI 175c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 17e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 17f0 50 .cfa: sp 0 + .ra: x30
STACK CFI 17f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1800 x19: .cfa -16 + ^
STACK CFI 1838 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1840 58 .cfa: sp 0 + .ra: x30
STACK CFI 1848 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1870 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 188c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1890 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18a0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 18a8 .cfa: sp 48 +
STACK CFI 18b4 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 194c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1954 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1960 268 .cfa: sp 0 + .ra: x30
STACK CFI 1968 .cfa: sp 80 +
STACK CFI 1978 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1980 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 19ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 19bc x21: x21 x22: x22
STACK CFI 19e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 19f0 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1ab4 x21: x21 x22: x22
STACK CFI 1ab8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1b10 x21: x21 x22: x22
STACK CFI 1b14 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1b58 x21: x21 x22: x22
STACK CFI 1b5c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1bac x21: x21 x22: x22
STACK CFI 1bb0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 1bd0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 1bd8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1be0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1be8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1bf4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1c70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1c78 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1c80 124 .cfa: sp 0 + .ra: x30
STACK CFI 1c88 .cfa: sp 96 +
STACK CFI 1c94 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1c9c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1ca8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1cb4 x23: .cfa -16 + ^
STACK CFI 1d90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1d98 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1da4 278 .cfa: sp 0 + .ra: x30
STACK CFI 1dac .cfa: sp 112 +
STACK CFI 1db0 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1db8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1dcc .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 1dd4 .cfa: sp 112 + .ra: .cfa -88 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1dd8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1de4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1dec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1df8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1e54 x19: x19 x20: x20
STACK CFI 1e58 x21: x21 x22: x22
STACK CFI 1e5c x23: x23 x24: x24
STACK CFI 1e60 x25: x25 x26: x26
STACK CFI 1e68 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 1e70 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1eb8 x19: x19 x20: x20
STACK CFI 1ebc x21: x21 x22: x22
STACK CFI 1ec0 x23: x23 x24: x24
STACK CFI 1ec4 x25: x25 x26: x26
STACK CFI 1ecc .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 1ed4 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1efc x19: x19 x20: x20
STACK CFI 1f04 x21: x21 x22: x22
STACK CFI 1f0c x23: x23 x24: x24
STACK CFI 1f14 x25: x25 x26: x26
STACK CFI 1f24 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 1f3c .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1fc4 x19: x19 x20: x20
STACK CFI 1fcc x21: x21 x22: x22
STACK CFI 1fd4 x23: x23 x24: x24
STACK CFI 1fdc x25: x25 x26: x26
STACK CFI 1fec .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 2004 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2020 c4 .cfa: sp 0 + .ra: x30
STACK CFI 2028 .cfa: sp 96 +
STACK CFI 2034 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 203c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 204c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2054 x23: .cfa -16 + ^
STACK CFI 20d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 20d8 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 20e4 8a0 .cfa: sp 0 + .ra: x30
STACK CFI 20ec .cfa: sp 208 +
STACK CFI 20f8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2100 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 210c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2134 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2188 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2240 x25: x25 x26: x26
STACK CFI 2248 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2270 x25: x25 x26: x26
STACK CFI 22b8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 25e0 x25: x25 x26: x26
STACK CFI 2624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 262c .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 26c0 x25: x25 x26: x26
STACK CFI 26c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2750 x25: x25 x26: x26
STACK CFI 2754 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 27ec x25: x25 x26: x26
STACK CFI 27f0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2960 x25: x25 x26: x26
STACK CFI 2964 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 297c x25: x25 x26: x26
STACK CFI 2980 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 2984 f4 .cfa: sp 0 + .ra: x30
STACK CFI 298c .cfa: sp 48 +
STACK CFI 299c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 29fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2a04 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2a80 148 .cfa: sp 0 + .ra: x30
STACK CFI 2a88 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a90 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ac4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2acc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2b30 x21: .cfa -16 + ^
STACK CFI 2b60 x21: x21
STACK CFI 2b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2bd0 234 .cfa: sp 0 + .ra: x30
STACK CFI 2bd8 .cfa: sp 80 +
STACK CFI 2be4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2bec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2bf4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2d88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2d90 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
