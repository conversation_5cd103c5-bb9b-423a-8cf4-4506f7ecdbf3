MODULE Linux arm64 8D4AB243DA9213FB7638A7DDB86D436D0 libboost_timer.so.1.77.0
INFO CODE_ID 43B24A8D92DAFB137638A7DDB86D436D
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 37e0 24 0 init_have_lse_atomics
37e0 4 45 0
37e4 4 46 0
37e8 4 45 0
37ec 4 46 0
37f0 4 47 0
37f4 4 47 0
37f8 4 48 0
37fc 4 47 0
3800 4 48 0
PUBLIC 32f0 0 _init
PUBLIC 3640 0 _GLOBAL__sub_I_auto_timers_construction.cpp
PUBLIC 3710 0 _GLOBAL__sub_I_cpu_timer.cpp
PUBLIC 3804 0 call_weak_fn
PUBLIC 3820 0 deregister_tm_clones
PUBLIC 3850 0 register_tm_clones
PUBLIC 3890 0 __do_global_dtors_aux
PUBLIC 38e0 0 frame_dummy
PUBLIC 38f0 0 boost::timer::auto_cpu_timer::auto_cpu_timer(short)
PUBLIC 39e0 0 boost::timer::auto_cpu_timer::auto_cpu_timer(short, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 3ad0 0 boost::timer::auto_cpu_timer::auto_cpu_timer(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 3bc0 0 (anonymous namespace)::show_time(boost::timer::cpu_times const&, std::ostream&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, short) [clone .isra.0]
PUBLIC 3ea0 0 boost::timer::cpu_timer::start()
PUBLIC 3fa0 0 boost::timer::cpu_timer::stop()
PUBLIC 40f0 0 boost::timer::cpu_timer::elapsed() const
PUBLIC 4260 0 boost::timer::cpu_timer::resume()
PUBLIC 42c0 0 boost::timer::auto_cpu_timer::auto_cpu_timer(std::ostream&, short)
PUBLIC 43b0 0 boost::timer::auto_cpu_timer::report()
PUBLIC 4420 0 boost::timer::auto_cpu_timer::~auto_cpu_timer()
PUBLIC 44a0 0 boost::timer::format(boost::timer::cpu_times const&, short, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 4830 0 boost::timer::format[abi:cxx11](boost::timer::cpu_times const&, short)
PUBLIC 48a0 0 boost::system::error_category::failed(int) const
PUBLIC 48b0 0 boost::system::detail::generic_error_category::name() const
PUBLIC 48c0 0 boost::system::detail::system_error_category::name() const
PUBLIC 48d0 0 boost::system::detail::system_error_category::default_error_condition(int) const
PUBLIC 48f0 0 boost::system::detail::interop_error_category::name() const
PUBLIC 4900 0 boost::system::error_category::equivalent(boost::system::error_code const&, int) const
PUBLIC 49e0 0 boost::system::detail::std_category::name() const
PUBLIC 4a00 0 boost::system::detail::std_category::message[abi:cxx11](int) const
PUBLIC 4a70 0 boost::system::detail::system_error_category::message(int, char*, unsigned long) const
PUBLIC 4a80 0 boost::system::detail::generic_error_category::message(int, char*, unsigned long) const
PUBLIC 4a90 0 boost::system::detail::std_category::~std_category()
PUBLIC 4ab0 0 boost::system::detail::std_category::~std_category()
PUBLIC 4af0 0 boost::system::error_category::equivalent(int, boost::system::error_condition const&) const
PUBLIC 4b80 0 boost::system::error_category::default_error_condition(int) const
PUBLIC 4c20 0 boost::system::detail::generic_error_category::message[abi:cxx11](int) const
PUBLIC 4d40 0 boost::system::detail::system_error_category::message[abi:cxx11](int) const
PUBLIC 4e60 0 boost::system::detail::std_category::default_error_condition(int) const
PUBLIC 5020 0 boost::system::detail::std_category::equivalent(std::error_code const&, int) const
PUBLIC 5550 0 boost::system::detail::std_category::equivalent(int, std::error_condition const&) const
PUBLIC 5b30 0 boost::system::detail::snprintf(char*, unsigned long, char const*, ...)
PUBLIC 5be0 0 boost::system::detail::interop_error_category::message(int, char*, unsigned long) const
PUBLIC 5c20 0 boost::system::error_category::message(int, char*, unsigned long) const
PUBLIC 5d20 0 boost::system::detail::interop_error_category::message[abi:cxx11](int) const
PUBLIC 5e80 0 __aarch64_cas8_acq_rel
PUBLIC 5eb4 0 _fini
STACK CFI INIT 3820 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3850 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3890 48 .cfa: sp 0 + .ra: x30
STACK CFI 3894 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 389c x19: .cfa -16 + ^
STACK CFI 38d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 38e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38f0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 38f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3904 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 390c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3998 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 39e0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 39e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 39f4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 39fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3a84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3a88 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3ad0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 3ad4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3ae4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3aec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3b78 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3640 c4 .cfa: sp 0 + .ra: x30
STACK CFI 3644 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 365c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 36f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3700 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 48a0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 48b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 48c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 48d0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 48f0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4900 d4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 49e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a00 64 .cfa: sp 0 + .ra: x30
STACK CFI 4a04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a10 x19: .cfa -32 + ^
STACK CFI 4a5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4a60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4a70 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a80 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4a90 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4ab0 38 .cfa: sp 0 + .ra: x30
STACK CFI 4ab4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ac4 x19: .cfa -16 + ^
STACK CFI 4ae4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4af0 88 .cfa: sp 0 + .ra: x30
STACK CFI 4af4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4afc x19: .cfa -16 + ^
STACK CFI 4b24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4b28 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4b5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4b60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4b74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3bc0 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 3bc4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 3bdc x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 3be4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 3bec x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 3bf4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 3c00 v8: .cfa -48 + ^ v9: .cfa -40 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 3d3c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3d40 .cfa: sp 144 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 4b80 94 .cfa: sp 0 + .ra: x30
STACK CFI 4b84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b98 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4bf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4bf8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4c20 114 .cfa: sp 0 + .ra: x30
STACK CFI 4c24 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 4c38 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4c40 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4cc4 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 4d40 114 .cfa: sp 0 + .ra: x30
STACK CFI 4d44 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 4d58 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 4d60 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4de4 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 4e60 1bc .cfa: sp 0 + .ra: x30
STACK CFI 4e64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4e7c x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4ee4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4ee8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4f30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4f34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4f5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4f60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5020 530 .cfa: sp 0 + .ra: x30
STACK CFI 5024 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 502c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5044 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5064 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5190 x25: x25 x26: x26
STACK CFI 5198 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 51ec x25: x25 x26: x26
STACK CFI 51f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 51f8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 5270 x25: x25 x26: x26
STACK CFI 52a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 52ac .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 52c0 x25: x25 x26: x26
STACK CFI 52c4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5350 x25: x25 x26: x26
STACK CFI 53fc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5410 x25: x25 x26: x26
STACK CFI 5424 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 54d8 x25: x25 x26: x26
STACK CFI 54ec x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5500 x25: x25 x26: x26
STACK CFI 551c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5530 x25: x25 x26: x26
STACK CFI 5534 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 5550 5d4 .cfa: sp 0 + .ra: x30
STACK CFI 5554 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 555c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5574 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5838 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 58f8 x25: .cfa -48 + ^
STACK CFI 592c x25: x25
STACK CFI 5aec x25: .cfa -48 + ^
STACK CFI 5b08 x25: x25
STACK CFI INIT 5b30 a4 .cfa: sp 0 + .ra: x30
STACK CFI 5b34 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 5bcc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5bd0 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI INIT 5be0 3c .cfa: sp 0 + .ra: x30
STACK CFI 5be4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5bfc x19: .cfa -16 + ^
STACK CFI 5c18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5c20 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5c24 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5c34 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5c64 x21: .cfa -64 + ^
STACK CFI 5ca8 x21: x21
STACK CFI 5cd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5cd4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI 5cd8 x21: x21
STACK CFI 5ce8 x21: .cfa -64 + ^
STACK CFI 5d10 x21: x21
STACK CFI INIT 5d20 158 .cfa: sp 0 + .ra: x30
STACK CFI 5d24 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5d38 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5d40 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5de0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3ea0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 3ea4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3eac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3f0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f10 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3fa0 150 .cfa: sp 0 + .ra: x30
STACK CFI 3fa4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3fac x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3ff0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ff4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI 3ff8 v8: .cfa -64 + ^
STACK CFI 401c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 402c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 4054 x23: x23 x24: x24
STACK CFI 405c x21: x21 x22: x22
STACK CFI 4068 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 406c x21: x21 x22: x22
STACK CFI 4070 x23: x23 x24: x24
STACK CFI 4098 v8: v8
STACK CFI 409c v8: .cfa -64 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 40d8 v8: v8 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 40dc x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 40e0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 40e4 v8: .cfa -64 + ^
STACK CFI INIT 40f0 16c .cfa: sp 0 + .ra: x30
STACK CFI 40f4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 40fc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 411c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 415c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4160 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI 4164 v8: .cfa -56 + ^
STACK CFI 41a4 v8: v8
STACK CFI 41a8 v8: .cfa -56 + ^
STACK CFI 41b0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 41bc x25: .cfa -64 + ^
STACK CFI 41e8 x23: x23 x24: x24
STACK CFI 41f0 x25: x25
STACK CFI 41fc x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^
STACK CFI 4200 x23: x23 x24: x24
STACK CFI 4204 x25: x25
STACK CFI 4208 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^
STACK CFI 4244 v8: v8 x23: x23 x24: x24 x25: x25
STACK CFI 4248 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 424c x25: .cfa -64 + ^
STACK CFI 4250 v8: .cfa -56 + ^
STACK CFI INIT 4260 54 .cfa: sp 0 + .ra: x30
STACK CFI 4270 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 427c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 42b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 42c0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 42c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 42d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 42e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4368 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 43b0 6c .cfa: sp 0 + .ra: x30
STACK CFI 43b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 43c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4414 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4418 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4420 78 .cfa: sp 0 + .ra: x30
STACK CFI 4424 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4430 x19: .cfa -16 + ^
STACK CFI 445c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4460 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4488 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 448c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 44a0 390 .cfa: sp 0 + .ra: x30
STACK CFI 44a4 .cfa: sp 576 +
STACK CFI 44b0 .ra: .cfa -568 + ^ x29: .cfa -576 + ^
STACK CFI 44b8 x19: .cfa -560 + ^ x20: .cfa -552 + ^
STACK CFI 44c0 x21: .cfa -544 + ^ x22: .cfa -536 + ^
STACK CFI 44cc x23: .cfa -528 + ^ x24: .cfa -520 + ^ x25: .cfa -512 + ^ x26: .cfa -504 + ^
STACK CFI 44d4 x27: .cfa -496 + ^ x28: .cfa -488 + ^
STACK CFI 4734 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4738 .cfa: sp 576 + .ra: .cfa -568 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^ x23: .cfa -528 + ^ x24: .cfa -520 + ^ x25: .cfa -512 + ^ x26: .cfa -504 + ^ x27: .cfa -496 + ^ x28: .cfa -488 + ^ x29: .cfa -576 + ^
STACK CFI INIT 4830 64 .cfa: sp 0 + .ra: x30
STACK CFI 4834 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 484c x19: .cfa -32 + ^
STACK CFI 488c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4890 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3710 c4 .cfa: sp 0 + .ra: x30
STACK CFI 3714 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3720 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 37c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 37d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5e80 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 37e0 24 .cfa: sp 0 + .ra: x30
STACK CFI 37e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 37fc .cfa: sp 0 + .ra: .ra x29: x29
