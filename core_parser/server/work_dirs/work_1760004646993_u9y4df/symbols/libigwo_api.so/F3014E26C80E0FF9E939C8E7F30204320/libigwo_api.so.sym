MODULE Linux arm64 F3014E26C80E0FF9E939C8E7F30204320 libigwo_api.so
INFO CODE_ID 264E01F30EC8F90FE939C8E7F3020432
FILE 0 /home/<USER>/agent/workspace/MAX/app/liodometry/code/src/core/data_buffer/data_buffer.h
FILE 1 /home/<USER>/agent/workspace/MAX/app/liodometry/code/src/core/igwo/sensor_data.h
FILE 2 /home/<USER>/agent/workspace/MAX/app/liodometry/code/src/core/igwo_api_impl.cpp
FILE 3 /opt/aarch64--glibc--bleeding-edge-2024.02-1/aarch64-buildroot-linux-gnu/include/c++/13.2.0/aarch64-buildroot-linux-gnu/bits/gthr-default.h
FILE 4 /opt/aarch64--glibc--bleeding-edge-2024.02-1/aarch64-buildroot-linux-gnu/include/c++/13.2.0/bits/std_mutex.h
FILE 5 /opt/aarch64--glibc--bleeding-edge-2024.02-1/aarch64-buildroot-linux-gnu/include/c++/13.2.0/bits/unique_lock.h
FILE 6 /root/.conan/data/eigen/3.4.0/_/_/package/5ab84d6acfe1f23c4fae0ab88f26e3a396351ac9/include/eigen3/Eigen/src/Core/CommaInitializer.h
FUNC 1770 4 0 _GLOBAL__sub_I_igwo_api_impl.cpp
1770 4 190 2
FUNC 1860 2c 0 std::unique_lock<std::mutex>::unlock()
1860 c 194 5
186c 4 194 5
1870 4 198 5
1874 4 198 5
1878 4 779 3
187c 4 201 5
1880 4 203 5
1884 8 203 5
FUNC 1890 84 0 odometry_create_handle
1890 14 9 2
18a4 4 9 2
18a8 4 10 2
18ac 4 10 2
18b0 4 10 2
18b4 4 10 2
18b8 4 11 2
18bc 4 12 2
18c0 4 20 0
18c4 c 12 2
18d0 4 14 2
18d4 8 12 2
18dc 4 14 2
18e0 8 12 2
18e8 4 12 2
18ec 4 20 0
18f0 c 14 2
18fc 8 10 2
1904 10 10 2
FUNC 1920 8 0 odometry_get_state
1920 4 20 2
1924 4 20 2
FUNC 1930 8 0 odometry_get_trigger_state
1930 4 26 2
1934 4 26 2
FUNC 1940 18 0 odometry_process
1940 8 28 2
1948 4 30 2
194c c 33 2
FUNC 1960 110 0 odometry_add_imu_data
1960 c 35 2
196c 4 39 2
1970 c 35 2
197c 8 69 5
1984 c 35 2
1990 8 40 2
1998 4 38 2
199c 4 41 2
19a0 4 40 2
19a4 4 37 2
19a8 4 41 2
19ac 4 40 2
19b0 4 69 5
19b4 4 41 2
19b8 4 41 2
19bc 4 40 2
19c0 4 39 2
19c4 4 37 2
19c8 4 38 2
19cc 4 749 3
19d0 4 116 4
19d4 4 142 5
19d8 4 142 5
19dc 4 44 2
19e0 8 44 2
19e8 c 779 3
19f4 28 45 2
1a1c 20 117 4
1a3c 8 106 5
1a44 4 106 5
1a48 1c 106 5
1a64 c 45 2
FUNC 1a70 160 0 odometry_add_ins_data
1a70 10 47 2
1a80 4 49 2
1a84 4 47 2
1a88 4 51 2
1a8c c 47 2
1a98 4 52 2
1a9c 4 49 2
1aa0 8 51 2
1aa8 4 50 2
1aac 8 52 2
1ab4 4 53 2
1ab8 4 51 2
1abc 4 54 2
1ac0 4 52 2
1ac4 8 53 2
1acc 8 54 2
1ad4 4 55 2
1ad8 4 50 2
1adc 4 56 2
1ae0 4 53 2
1ae4 4 55 2
1ae8 4 54 2
1aec 4 55 2
1af0 4 57 2
1af4 4 56 2
1af8 4 55 2
1afc 4 58 2
1b00 4 59 2
1b04 4 56 2
1b08 4 63 2
1b0c 8 69 5
1b14 4 69 5
1b18 4 56 2
1b1c 4 57 2
1b20 4 58 2
1b24 4 59 2
1b28 4 63 2
1b2c 4 749 3
1b30 4 116 4
1b34 4 142 5
1b38 4 142 5
1b3c 4 66 2
1b40 8 66 2
1b48 c 779 3
1b54 28 67 2
1b7c 20 117 4
1b9c 8 106 5
1ba4 4 106 5
1ba8 1c 106 5
1bc4 c 67 2
FUNC 1bd0 194 0 odometry_add_odo_data
1bd0 10 69 2
1be0 8 69 2
1be8 4 71 2
1bec 4 77 2
1bf0 4 79 2
1bf4 4 81 2
1bf8 4 83 2
1bfc 4 73 2
1c00 4 84 2
1c04 4 69 2
1c08 c 69 2
1c14 4 72 2
1c18 4 74 2
1c1c 4 72 2
1c20 8 74 2
1c28 4 75 2
1c2c 4 74 2
1c30 c 75 2
1c3c 4 71 2
1c40 4 76 2
1c44 4 77 2
1c48 4 78 2
1c4c 4 79 2
1c50 4 80 2
1c54 4 81 2
1c58 8 85 2
1c60 4 86 2
1c64 4 83 2
1c68 4 87 2
1c6c 4 84 2
1c70 4 87 2
1c74 4 86 2
1c78 4 91 2
1c7c 4 85 2
1c80 4 87 2
1c84 4 86 2
1c88 4 87 2
1c8c 4 92 2
1c90 4 85 2
1c94 c 69 5
1ca0 4 94 2
1ca4 4 86 2
1ca8 4 82 2
1cac 4 91 2
1cb0 4 92 2
1cb4 4 93 2
1cb8 4 94 2
1cbc 4 749 3
1cc0 4 116 4
1cc4 4 142 5
1cc8 4 142 5
1ccc 4 97 2
1cd0 8 97 2
1cd8 c 779 3
1ce4 2c 98 2
1d10 20 117 4
1d30 8 106 5
1d38 4 106 5
1d3c 1c 106 5
1d58 c 98 2
FUNC 1d70 10c 0 odometry_add_gps_data
1d70 8 100 2
1d78 4 38 6
1d7c 8 100 2
1d84 4 100 2
1d88 8 102 2
1d90 4 100 2
1d94 8 102 2
1d9c c 100 2
1da8 8 102 2
1db0 4 102 2
1db4 4 78 6
1db8 c 69 5
1dc4 4 78 6
1dc8 4 38 6
1dcc 4 78 6
1dd0 4 123 1
1dd4 4 126 1
1dd8 4 749 3
1ddc 4 116 4
1de0 4 142 5
1de4 4 142 5
1de8 4 104 2
1dec 8 104 2
1df4 c 779 3
1e00 28 105 2
1e28 20 117 4
1e48 8 106 5
1e50 4 106 5
1e54 1c 106 5
1e70 c 105 2
FUNC 1e80 1b4 0 odometry_add_vehicle_data
1e80 14 107 2
1e94 4 113 2
1e98 4 111 2
1e9c 4 117 2
1ea0 4 118 2
1ea4 4 122 2
1ea8 4 110 2
1eac 4 116 2
1eb0 4 114 2
1eb4 4 121 2
1eb8 4 124 2
1ebc 4 120 2
1ec0 4 125 2
1ec4 4 112 2
1ec8 4 107 2
1ecc c 107 2
1ed8 4 112 2
1edc 8 109 2
1ee4 4 110 2
1ee8 4 116 2
1eec 4 114 2
1ef0 4 121 2
1ef4 4 120 2
1ef8 4 126 2
1efc 4 113 2
1f00 4 111 2
1f04 4 117 2
1f08 4 118 2
1f0c 8 69 5
1f14 4 122 2
1f18 4 127 2
1f1c 4 132 2
1f20 4 137 2
1f24 4 142 2
1f28 4 129 2
1f2c 4 131 2
1f30 4 134 2
1f34 4 130 2
1f38 4 135 2
1f3c 4 124 2
1f40 4 136 2
1f44 4 125 2
1f48 4 139 2
1f4c 4 126 2
1f50 4 140 2
1f54 4 141 2
1f58 4 69 5
1f5c 4 127 2
1f60 4 129 2
1f64 4 130 2
1f68 4 131 2
1f6c 4 132 2
1f70 4 134 2
1f74 4 135 2
1f78 4 136 2
1f7c 4 137 2
1f80 4 139 2
1f84 4 140 2
1f88 4 141 2
1f8c 4 142 2
1f90 4 749 3
1f94 4 116 4
1f98 4 142 5
1f9c 4 142 5
1fa0 4 145 2
1fa4 8 145 2
1fac c 779 3
1fb8 28 146 2
1fe0 20 117 4
2000 8 106 5
2008 4 106 5
200c 1c 106 5
2028 c 146 2
FUNC 2040 30 0 odometry_destroy_handle
2040 4 150 2
2044 10 148 2
2054 4 150 2
2058 8 150 2
2060 4 151 2
2064 4 151 2
2068 4 150 2
206c 4 150 2
FUNC 2070 f4 0 odometry_output3d
2070 18 153 2
2088 4 153 2
208c 4 156 2
2090 c 153 2
209c 4 156 2
20a0 4 165 2
20a4 4 158 2
20a8 8 163 2
20b0 4 161 2
20b4 4 164 2
20b8 4 158 2
20bc 4 163 2
20c0 8 165 2
20c8 4 162 2
20cc 4 157 2
20d0 4 163 2
20d4 8 164 2
20dc 4 160 2
20e0 4 159 2
20e4 4 157 2
20e8 4 168 2
20ec 4 164 2
20f0 4 168 2
20f4 4 160 2
20f8 4 168 2
20fc 4 159 2
2100 4 161 2
2104 4 162 2
2108 4 166 2
210c 4 165 2
2110 4 166 2
2114 8 167 2
211c 8 166 2
2124 8 167 2
212c 4 168 2
2130 28 170 2
2158 8 170 2
2160 4 170 2
FUNC 2170 f8 0 odometry_output2d
2170 18 172 2
2188 4 172 2
218c 4 175 2
2190 c 172 2
219c 4 175 2
21a0 4 177 2
21a4 4 185 2
21a8 4 183 2
21ac 4 185 2
21b0 4 178 2
21b4 4 188 2
21b8 4 176 2
21bc 4 181 2
21c0 4 182 2
21c4 4 188 2
21c8 4 185 2
21cc 4 178 2
21d0 8 183 2
21d8 4 180 2
21dc 4 184 2
21e0 4 183 2
21e4 8 184 2
21ec 4 179 2
21f0 4 184 2
21f4 4 188 2
21f8 4 180 2
21fc 4 179 2
2200 4 177 2
2204 4 181 2
2208 4 182 2
220c 4 186 2
2210 4 185 2
2214 4 186 2
2218 8 187 2
2220 8 186 2
2228 8 187 2
2230 4 188 2
2234 28 190 2
225c 8 190 2
2264 4 190 2
PUBLIC 15d8 0 _init
PUBLIC 1774 0 call_weak_fn
PUBLIC 1790 0 deregister_tm_clones
PUBLIC 17c0 0 register_tm_clones
PUBLIC 1800 0 __do_global_dtors_aux
PUBLIC 1850 0 frame_dummy
PUBLIC 2268 0 _fini
STACK CFI INIT 1790 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17c0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1800 48 .cfa: sp 0 + .ra: x30
STACK CFI 1804 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 180c x19: .cfa -16 + ^
STACK CFI 1844 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1850 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1860 2c .cfa: sp 0 + .ra: x30
STACK CFI 1864 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 186c x19: .cfa -16 + ^
STACK CFI 1888 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1890 84 .cfa: sp 0 + .ra: x30
STACK CFI 1894 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 189c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 18a4 x21: .cfa -16 + ^
STACK CFI 18f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 18fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1920 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1930 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1940 18 .cfa: sp 0 + .ra: x30
STACK CFI 1944 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1954 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1960 110 .cfa: sp 0 + .ra: x30
STACK CFI 1964 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 197c x19: .cfa -112 + ^
STACK CFI 1a18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1a1c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1a70 160 .cfa: sp 0 + .ra: x30
STACK CFI 1a74 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 1a88 x19: .cfa -240 + ^
STACK CFI 1b78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1b7c .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x29: .cfa -256 + ^
STACK CFI INIT 1bd0 194 .cfa: sp 0 + .ra: x30
STACK CFI 1bd4 .cfa: sp 880 +
STACK CFI 1be4 .ra: .cfa -872 + ^ x29: .cfa -880 + ^
STACK CFI 1c08 x19: .cfa -864 + ^
STACK CFI 1d0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1d10 .cfa: sp 880 + .ra: .cfa -872 + ^ x19: .cfa -864 + ^ x29: .cfa -880 + ^
STACK CFI INIT 1d70 10c .cfa: sp 0 + .ra: x30
STACK CFI 1d78 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1d94 x19: .cfa -112 + ^
STACK CFI 1e24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1e28 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1e80 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 1e84 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 1ecc x19: .cfa -240 + ^
STACK CFI 1fdc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1fe0 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x29: .cfa -256 + ^
STACK CFI INIT 2040 30 .cfa: sp 0 + .ra: x30
STACK CFI 2048 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2050 x19: .cfa -16 + ^
STACK CFI 2068 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2070 f4 .cfa: sp 0 + .ra: x30
STACK CFI 2074 .cfa: sp 864 +
STACK CFI 2080 .ra: .cfa -856 + ^ x29: .cfa -864 + ^
STACK CFI 2088 x19: .cfa -848 + ^
STACK CFI 215c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2160 .cfa: sp 864 + .ra: .cfa -856 + ^ x19: .cfa -848 + ^ x29: .cfa -864 + ^
STACK CFI INIT 2170 f8 .cfa: sp 0 + .ra: x30
STACK CFI 2174 .cfa: sp 864 +
STACK CFI 2180 .ra: .cfa -856 + ^ x29: .cfa -864 + ^
STACK CFI 2188 x19: .cfa -848 + ^
STACK CFI 2260 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2264 .cfa: sp 864 + .ra: .cfa -856 + ^ x19: .cfa -848 + ^ x29: .cfa -864 + ^
STACK CFI INIT 1770 4 .cfa: sp 0 + .ra: x30
