MODULE Linux arm64 3857BDA658124CEF9AB7E1F2CA84EC0A0 libpipewire-module-zeroconf-discover.so
INFO CODE_ID A6BD57381258EF4C9AB7E1F2CA84EC0A4708F82D
PUBLIC 4c40 0 pipewire__module_init
STACK CFI INIT 2fa0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2fd0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3010 48 .cfa: sp 0 + .ra: x30
STACK CFI 3014 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 301c x19: .cfa -16 + ^
STACK CFI 3054 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3060 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3070 50 .cfa: sp 0 + .ra: x30
STACK CFI 3078 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3080 x19: .cfa -16 + ^
STACK CFI 30b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 30c0 1c .cfa: sp 0 + .ra: x30
STACK CFI 30c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30e0 20 .cfa: sp 0 + .ra: x30
STACK CFI 30e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 30f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3100 58 .cfa: sp 0 + .ra: x30
STACK CFI 3108 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3130 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 314c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3150 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3160 b8 .cfa: sp 0 + .ra: x30
STACK CFI 3168 .cfa: sp 48 +
STACK CFI 3174 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 320c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3214 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3220 fc .cfa: sp 0 + .ra: x30
STACK CFI 3228 .cfa: sp 352 +
STACK CFI 3238 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 3248 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 3308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3310 .cfa: sp 352 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x29: .cfa -208 + ^
STACK CFI INIT 3320 6c .cfa: sp 0 + .ra: x30
STACK CFI 3328 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3330 x19: .cfa -16 + ^
STACK CFI 3370 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3378 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3384 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3390 50 .cfa: sp 0 + .ra: x30
STACK CFI 3398 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33a0 x19: .cfa -16 + ^
STACK CFI 33d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 33e0 74 .cfa: sp 0 + .ra: x30
STACK CFI 33e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33f4 x19: .cfa -16 + ^
STACK CFI 3438 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3440 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 344c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3454 b4 .cfa: sp 0 + .ra: x30
STACK CFI 345c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3464 x21: .cfa -16 + ^
STACK CFI 346c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3510 50 .cfa: sp 0 + .ra: x30
STACK CFI 3518 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3520 x19: .cfa -16 + ^
STACK CFI 3558 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3560 80c .cfa: sp 0 + .ra: x30
STACK CFI 3568 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3574 .cfa: x29 96 +
STACK CFI 358c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 36b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 36c0 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3d70 b0 .cfa: sp 0 + .ra: x30
STACK CFI 3d78 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3d80 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3d88 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3d94 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3e10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3e18 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3e20 124 .cfa: sp 0 + .ra: x30
STACK CFI 3e28 .cfa: sp 96 +
STACK CFI 3e34 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3e3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3e48 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3e54 x23: .cfa -16 + ^
STACK CFI 3f30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3f38 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3f44 7c4 .cfa: sp 0 + .ra: x30
STACK CFI 3f4c .cfa: sp 192 +
STACK CFI 3f58 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3f60 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3f6c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3f78 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 441c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4424 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4710 274 .cfa: sp 0 + .ra: x30
STACK CFI 4718 .cfa: sp 112 +
STACK CFI 471c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4724 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4738 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 4740 .cfa: sp 112 + .ra: .cfa -88 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4744 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4750 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4758 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4764 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 47bc x19: x19 x20: x20
STACK CFI 47c0 x21: x21 x22: x22
STACK CFI 47c4 x23: x23 x24: x24
STACK CFI 47c8 x25: x25 x26: x26
STACK CFI 47d0 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 47d8 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4820 x19: x19 x20: x20
STACK CFI 4824 x21: x21 x22: x22
STACK CFI 4828 x23: x23 x24: x24
STACK CFI 482c x25: x25 x26: x26
STACK CFI 4834 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 483c .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4864 x19: x19 x20: x20
STACK CFI 486c x21: x21 x22: x22
STACK CFI 4874 x23: x23 x24: x24
STACK CFI 487c x25: x25 x26: x26
STACK CFI 488c .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 48a4 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 492c x19: x19 x20: x20
STACK CFI 4934 x21: x21 x22: x22
STACK CFI 493c x23: x23 x24: x24
STACK CFI 4944 x25: x25 x26: x26
STACK CFI 4954 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 496c .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4984 e8 .cfa: sp 0 + .ra: x30
STACK CFI 498c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4998 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 49a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 49e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 49e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4a64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4a70 f4 .cfa: sp 0 + .ra: x30
STACK CFI 4a78 .cfa: sp 48 +
STACK CFI 4a88 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a98 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4ae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4af0 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4b64 d8 .cfa: sp 0 + .ra: x30
STACK CFI 4b6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b74 x19: .cfa -16 + ^
STACK CFI 4bb8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4bc0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4c00 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4c08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4c40 234 .cfa: sp 0 + .ra: x30
STACK CFI 4c48 .cfa: sp 80 +
STACK CFI 4c54 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4c64 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4df8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4e00 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
