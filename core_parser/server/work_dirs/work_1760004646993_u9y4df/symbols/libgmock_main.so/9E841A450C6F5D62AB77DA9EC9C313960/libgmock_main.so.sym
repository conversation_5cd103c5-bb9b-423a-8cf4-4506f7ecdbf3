MODULE Linux arm64 9E841A450C6F5D62AB77DA9EC9C313960 libgmock_main.so
INFO CODE_ID 451A849E6F0C625DAB77DA9EC9C31396
PUBLIC 750 0 _init
PUBLIC 7f0 0 main
PUBLIC 83c 0 call_weak_fn
PUBLIC 850 0 deregister_tm_clones
PUBLIC 880 0 register_tm_clones
PUBLIC 8c0 0 __do_global_dtors_aux
PUBLIC 910 0 frame_dummy
PUBLIC 914 0 _fini
STACK CFI INIT 850 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 880 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c0 48 .cfa: sp 0 + .ra: x30
STACK CFI 8c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8cc x19: .cfa -16 + ^
STACK CFI 904 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 910 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7f0 4c .cfa: sp 0 + .ra: x30
STACK CFI 7f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 80c x19: .cfa -32 + ^
STACK CFI 838 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
