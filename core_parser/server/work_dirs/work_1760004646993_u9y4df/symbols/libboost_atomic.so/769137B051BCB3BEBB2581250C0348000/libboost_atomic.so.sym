MODULE Linux arm64 769137B051BCB3BEBB2581250C0348000 libboost_atomic.so.1.77.0
INFO CODE_ID B0379176BC51BEB3BB2581250C034800
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC de0 24 0 init_have_lse_atomics
de0 4 45 0
de4 4 46 0
de8 4 45 0
dec 4 46 0
df0 4 47 0
df4 4 47 0
df8 4 48 0
dfc 4 47 0
e00 4 48 0
PUBLIC cb8 0 _init
PUBLIC e04 0 call_weak_fn
PUBLIC e20 0 deregister_tm_clones
PUBLIC e50 0 register_tm_clones
PUBLIC e90 0 __do_global_dtors_aux
PUBLIC ee0 0 frame_dummy
PUBLIC ef0 0 boost::atomics::detail::lock_pool::(anonymous namespace)::wait_state_list::allocate_buffer(unsigned long, boost::atomics::detail::lock_pool::(anonymous namespace)::wait_state_list::header*)
PUBLIC 1070 0 boost::atomics::detail::lock_pool::(anonymous namespace)::cleanup_lock_pool()
PUBLIC 1220 0 boost::atomics::detail::find_address_generic(void const volatile*, void const volatile* const*, unsigned long)
PUBLIC 1260 0 boost::atomics::detail::lock_pool::short_lock(unsigned long)
PUBLIC 1330 0 boost::atomics::detail::lock_pool::long_lock(unsigned long)
PUBLIC 1400 0 boost::atomics::detail::lock_pool::unlock(void*)
PUBLIC 14a0 0 boost::atomics::detail::lock_pool::allocate_wait_state(void*, void const volatile*)
PUBLIC 15f0 0 boost::atomics::detail::lock_pool::free_wait_state(void*, void*)
PUBLIC 16f0 0 boost::atomics::detail::lock_pool::wait(void*, void*)
PUBLIC 19c0 0 boost::atomics::detail::lock_pool::notify_one(void*, void const volatile*)
PUBLIC 1a70 0 boost::atomics::detail::lock_pool::notify_all(void*, void const volatile*)
PUBLIC 1b20 0 boost::atomics::detail::lock_pool::thread_fence()
PUBLIC 1b30 0 boost::atomics::detail::lock_pool::signal_fence()
PUBLIC 1b40 0 __aarch64_cas4_relax
PUBLIC 1b80 0 __aarch64_cas4_acq
PUBLIC 1bc0 0 __aarch64_cas4_rel
PUBLIC 1c00 0 __aarch64_swp1_relax
PUBLIC 1c30 0 atexit
PUBLIC 1c40 0 _fini
STACK CFI INIT e20 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT e50 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT e90 48 .cfa: sp 0 + .ra: x30
STACK CFI e94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e9c x19: .cfa -16 + ^
STACK CFI ed4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ee0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ef0 17c .cfa: sp 0 + .ra: x30
STACK CFI ef4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI f04 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI f0c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI f54 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI fcc x23: x23 x24: x24
STACK CFI ffc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1000 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 1028 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1040 x23: x23 x24: x24
STACK CFI 1048 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 1070 1ac .cfa: sp 0 + .ra: x30
STACK CFI 1074 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 107c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1088 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1094 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 115c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1220 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1260 c4 .cfa: sp 0 + .ra: x30
STACK CFI 1264 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1274 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1280 x21: .cfa -16 + ^
STACK CFI 12b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 12b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1330 c4 .cfa: sp 0 + .ra: x30
STACK CFI 1334 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1344 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1350 x21: .cfa -16 + ^
STACK CFI 1384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1388 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1400 94 .cfa: sp 0 + .ra: x30
STACK CFI 1404 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 140c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1414 x21: .cfa -16 + ^
STACK CFI 144c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1450 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 14a0 150 .cfa: sp 0 + .ra: x30
STACK CFI 14a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 14ac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 14b8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 151c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1520 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 153c x23: .cfa -32 + ^
STACK CFI 1564 x23: x23
STACK CFI 1588 x23: .cfa -32 + ^
STACK CFI 15bc x23: x23
STACK CFI 15dc x23: .cfa -32 + ^
STACK CFI INIT 15f0 fc .cfa: sp 0 + .ra: x30
STACK CFI 160c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1618 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 167c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1680 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 168c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 16d8 x21: x21 x22: x22
STACK CFI 16dc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 16e4 x21: x21 x22: x22
STACK CFI INIT 16f0 2cc .cfa: sp 0 + .ra: x30
STACK CFI 16f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1704 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1718 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1728 x23: .cfa -48 + ^
STACK CFI 17b8 x23: x23
STACK CFI 17e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 17ec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x29: .cfa -96 + ^
STACK CFI 1858 x23: x23
STACK CFI 1928 x23: .cfa -48 + ^
STACK CFI 196c x23: x23
STACK CFI 19b8 x23: .cfa -48 + ^
STACK CFI INIT 19c0 ac .cfa: sp 0 + .ra: x30
STACK CFI 19c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19cc x19: .cfa -16 + ^
STACK CFI 1a68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1a70 ac .cfa: sp 0 + .ra: x30
STACK CFI 1a74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a7c x19: .cfa -16 + ^
STACK CFI 1b18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1b20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b40 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b80 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1bc0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c00 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT de0 24 .cfa: sp 0 + .ra: x30
STACK CFI de4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI dfc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1c30 10 .cfa: sp 0 + .ra: x30
