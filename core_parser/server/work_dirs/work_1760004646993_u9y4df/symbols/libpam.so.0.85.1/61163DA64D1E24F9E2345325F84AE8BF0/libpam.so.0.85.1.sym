MODULE Linux arm64 61163DA64D1E24F9E2345325F84AE8BF0 libpam.so.0
INFO CODE_ID A63D16611E4DF924E2345325F84AE8BF718F0A2B
PUBLIC 32b0 0 pam_get_data
PUBLIC 3370 0 pam_fail_delay
PUBLIC 33d4 0 pam_strerror
PUBLIC 36a0 0 pam_vsyslog
PUBLIC 3890 0 pam_syslog
PUBLIC 3944 0 pam_getenv
PUBLIC 3ab0 0 pam_get_item
PUBLIC 3cf0 0 pam_set_data
PUBLIC 3e34 0 pam_putenv
PUBLIC 4204 0 pam_getenvlist
PUBLIC 6090 0 pam_set_item
PUBLIC 65c0 0 pam_get_user
PUBLIC 6d10 0 pam_start_confdir
PUBLIC 6d30 0 pam_start
PUBLIC 6d50 0 pam_vprompt
PUBLIC 6f94 0 pam_prompt
PUBLIC 76a4 0 pam_get_authtok
PUBLIC 76c0 0 pam_get_authtok_noverify
PUBLIC 76f0 0 pam_get_authtok_verify
PUBLIC 8540 0 pam_acct_mgmt
PUBLIC 8594 0 pam_authenticate
PUBLIC 86c0 0 pam_setcred
PUBLIC 8720 0 pam_chauthtok
PUBLIC 88b0 0 pam_open_session
PUBLIC 8904 0 pam_close_session
PUBLIC 8960 0 pam_end
PUBLIC 8f60 0 pam_modutil_audit_write
PUBLIC 9010 0 pam_modutil_check_user_in_passwd
PUBLIC 91f0 0 pam_modutil_getpwnam
PUBLIC 93e0 0 pam_modutil_read
PUBLIC 94d0 0 pam_modutil_write
PUBLIC 95c0 0 pam_modutil_getgrgid
PUBLIC 97e4 0 pam_modutil_getpwuid
PUBLIC 9a10 0 pam_modutil_getgrnam
PUBLIC 9c00 0 pam_modutil_getspnam
PUBLIC 9df0 0 pam_modutil_getlogin
PUBLIC 9ec0 0 pam_modutil_user_in_group_nam_nam
PUBLIC 9f04 0 pam_modutil_user_in_group_nam_gid
PUBLIC 9f50 0 pam_modutil_user_in_group_uid_nam
PUBLIC 9f94 0 pam_modutil_user_in_group_uid_gid
PUBLIC 9fe0 0 pam_modutil_drop_priv
PUBLIC a2c0 0 pam_modutil_regain_priv
PUBLIC a420 0 pam_modutil_sanitize_helper_fds
PUBLIC a570 0 pam_modutil_search_key
STACK CFI INIT 2970 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29a0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 29e0 48 .cfa: sp 0 + .ra: x30
STACK CFI 29e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29ec x19: .cfa -16 + ^
STACK CFI 2a24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2a30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a40 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 2a48 .cfa: sp 288 +
STACK CFI 2a54 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b40 .cfa: sp 288 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2be4 2c .cfa: sp 0 + .ra: x30
STACK CFI 2bec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2bf8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2c10 160 .cfa: sp 0 + .ra: x30
STACK CFI 2c18 .cfa: sp 64 +
STACK CFI 2c28 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c38 x19: .cfa -16 + ^
STACK CFI 2d18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2d20 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2d70 e8 .cfa: sp 0 + .ra: x30
STACK CFI 2d7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2d88 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2dac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2e10 x19: x19 x20: x20
STACK CFI 2e3c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 2e60 7c .cfa: sp 0 + .ra: x30
STACK CFI 2e68 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2e70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2e78 x21: .cfa -16 + ^
STACK CFI 2ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2ee0 120 .cfa: sp 0 + .ra: x30
STACK CFI 2ee8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2ef0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ef8 x23: .cfa -16 + ^
STACK CFI 2f10 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2f60 x21: x21 x22: x22
STACK CFI 2f68 x23: x23
STACK CFI 2f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2f7c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 2f8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2f94 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2fa0 x21: x21 x22: x22
STACK CFI 2fac x23: x23
STACK CFI 2fb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2fb8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2fc4 x21: x21 x22: x22
STACK CFI 2fd0 x23: x23
STACK CFI 2fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2fdc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2fec x23: x23
STACK CFI 2ff0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ff8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2ffc x23: x23
STACK CFI INIT 3000 10c .cfa: sp 0 + .ra: x30
STACK CFI 3008 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3014 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 30e4 x19: x19 x20: x20
STACK CFI 30e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 30f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3110 19c .cfa: sp 0 + .ra: x30
STACK CFI 3118 .cfa: sp 96 +
STACK CFI 3128 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3144 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 314c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3188 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3198 x25: .cfa -16 + ^
STACK CFI 3200 x23: x23 x24: x24
STACK CFI 3204 x25: x25
STACK CFI 320c x19: x19 x20: x20
STACK CFI 3210 x21: x21 x22: x22
STACK CFI 3234 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 323c .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 326c x19: x19 x20: x20
STACK CFI 3270 x21: x21 x22: x22
STACK CFI 3274 x23: x23 x24: x24
STACK CFI 3278 x25: x25
STACK CFI 3280 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 3284 x19: x19 x20: x20
STACK CFI 328c x21: x21 x22: x22
STACK CFI 3290 x23: x23 x24: x24
STACK CFI 3294 x25: x25
STACK CFI 329c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 32a0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 32a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 32a8 x25: .cfa -16 + ^
STACK CFI INIT 32b0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 32b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 32c8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 32e0 x21: .cfa -16 + ^
STACK CFI 330c x19: x19 x20: x20
STACK CFI 3314 x21: x21
STACK CFI 3318 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3320 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3324 x19: x19 x20: x20
STACK CFI 332c x21: x21
STACK CFI 3330 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3338 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 333c x19: x19 x20: x20
STACK CFI 3344 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 334c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3370 64 .cfa: sp 0 + .ra: x30
STACK CFI 33ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 33c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 33d4 2cc .cfa: sp 0 + .ra: x30
STACK CFI 33dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 33f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3408 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 36a0 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 36a8 .cfa: sp 160 +
STACK CFI 36b4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 36bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 36c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 36d0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 37a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 37a8 .cfa: sp 160 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3890 b4 .cfa: sp 0 + .ra: x30
STACK CFI 3898 .cfa: sp 272 +
STACK CFI 38a8 .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 3938 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3940 .cfa: sp 272 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI INIT 3944 164 .cfa: sp 0 + .ra: x30
STACK CFI 394c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3958 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3964 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3974 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 39a8 x25: .cfa -16 + ^
STACK CFI 39e4 x19: x19 x20: x20
STACK CFI 39ec x21: x21 x22: x22
STACK CFI 39f0 x23: x23 x24: x24
STACK CFI 39f4 x25: x25
STACK CFI 39f8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 3a04 x19: x19 x20: x20
STACK CFI 3a0c x21: x21 x22: x22
STACK CFI 3a10 x23: x23 x24: x24
STACK CFI 3a14 x25: x25
STACK CFI 3a18 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3a20 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 3a40 x19: x19 x20: x20
STACK CFI 3a48 x21: x21 x22: x22
STACK CFI 3a4c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3a50 x23: x23 x24: x24
STACK CFI 3a5c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3a60 x19: x19 x20: x20
STACK CFI 3a68 x21: x21 x22: x22
STACK CFI 3a6c x23: x23 x24: x24
STACK CFI 3a8c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3aa0 x21: x21 x22: x22
STACK CFI INIT 3ab0 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 3ab8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ae0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3ae8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3c60 90 .cfa: sp 0 + .ra: x30
STACK CFI 3c68 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c7c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3cb0 x21: x21 x22: x22
STACK CFI 3cbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3cc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3cd8 x21: x21 x22: x22
STACK CFI 3ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3cf0 144 .cfa: sp 0 + .ra: x30
STACK CFI 3cf8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3d04 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3d10 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3d28 x23: .cfa -16 + ^
STACK CFI 3d74 x19: x19 x20: x20
STACK CFI 3d78 x21: x21 x22: x22
STACK CFI 3d7c x23: x23
STACK CFI 3d80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3d88 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3d8c x19: x19 x20: x20
STACK CFI 3d94 x21: x21 x22: x22
STACK CFI 3d98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3da0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3dd0 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 3dec x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 3e0c x19: x19 x20: x20
STACK CFI 3e14 x21: x21 x22: x22
STACK CFI 3e18 x23: x23
STACK CFI 3e1c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 3e34 3d0 .cfa: sp 0 + .ra: x30
STACK CFI 3e3c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3e44 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3e4c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3e58 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3e74 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3ea0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3f24 x21: x21 x22: x22
STACK CFI 3f28 x23: x23 x24: x24
STACK CFI 3f2c x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3fb0 x21: x21 x22: x22
STACK CFI 3fb8 x23: x23 x24: x24
STACK CFI 3fbc x25: x25 x26: x26
STACK CFI 3fc0 x27: x27 x28: x28
STACK CFI 3fc4 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4090 x21: x21 x22: x22
STACK CFI 4094 x23: x23 x24: x24
STACK CFI 4098 x25: x25 x26: x26
STACK CFI 409c x27: x27 x28: x28
STACK CFI 40a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 40b0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 40c4 x25: x25 x26: x26
STACK CFI 40cc x27: x27 x28: x28
STACK CFI 40d0 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 40f4 x21: x21 x22: x22
STACK CFI 40f8 x25: x25 x26: x26
STACK CFI 40fc x27: x27 x28: x28
STACK CFI 4108 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4110 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 4114 x23: x23 x24: x24
STACK CFI 4120 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 413c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4154 x27: x27 x28: x28
STACK CFI 4158 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 41ec x21: x21 x22: x22
STACK CFI 41f0 x23: x23 x24: x24
STACK CFI 41f4 x25: x25 x26: x26
STACK CFI 41f8 x27: x27 x28: x28
STACK CFI 41fc x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 4204 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 420c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4214 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4228 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 427c x23: .cfa -16 + ^
STACK CFI 42b0 x19: x19 x20: x20
STACK CFI 42bc x23: x23
STACK CFI 42c0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 42c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 42e0 x19: x19 x20: x20
STACK CFI 42f0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 42f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4348 x19: x19 x20: x20
STACK CFI 434c x23: x23
STACK CFI 4370 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4384 x19: x19 x20: x20
STACK CFI 4388 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 438c x19: x19 x20: x20
STACK CFI 43b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 43b4 x19: x19 x20: x20
STACK CFI INIT 43c0 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 43c8 .cfa: sp 112 +
STACK CFI 43d4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 43dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 43f0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4428 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4458 x23: x23 x24: x24
STACK CFI 44a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 44b0 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 44b8 x25: .cfa -16 + ^
STACK CFI 44fc x23: x23 x24: x24
STACK CFI 4500 x25: x25
STACK CFI 4530 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4534 x25: .cfa -16 + ^
STACK CFI 453c x25: x25
STACK CFI 4554 x23: x23 x24: x24
STACK CFI 4560 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4564 x25: .cfa -16 + ^
STACK CFI 4568 x23: x23 x24: x24 x25: x25
STACK CFI 4578 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 4598 x23: x23 x24: x24
STACK CFI 45a0 x25: x25
STACK CFI INIT 45a4 8c .cfa: sp 0 + .ra: x30
STACK CFI 45ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45b8 x19: .cfa -16 + ^
STACK CFI 4600 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4608 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4628 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4630 7e8 .cfa: sp 0 + .ra: x30
STACK CFI 4638 .cfa: sp 160 +
STACK CFI 4644 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4660 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 466c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4678 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4684 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4908 x19: x19 x20: x20
STACK CFI 490c x21: x21 x22: x22
STACK CFI 4910 x23: x23 x24: x24
STACK CFI 4940 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4948 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4974 x19: x19 x20: x20
STACK CFI 497c x21: x21 x22: x22
STACK CFI 4980 x23: x23 x24: x24
STACK CFI 4984 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4ae0 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 4afc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4d88 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 4d8c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4d90 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4d94 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 4e20 cd8 .cfa: sp 0 + .ra: x30
STACK CFI 4e28 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4e44 .cfa: sp 1536 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4efc .cfa: sp 96 +
STACK CFI 4f14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4f1c .cfa: sp 1536 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5b00 178 .cfa: sp 0 + .ra: x30
STACK CFI 5b08 .cfa: sp 96 +
STACK CFI 5b14 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5b1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5b3c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5b54 x23: .cfa -16 + ^
STACK CFI 5ba4 x21: x21 x22: x22
STACK CFI 5ba8 x23: x23
STACK CFI 5bd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5bdc .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5bfc x21: x21 x22: x22
STACK CFI 5c00 x23: x23
STACK CFI 5c04 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 5c34 x21: x21 x22: x22 x23: x23
STACK CFI 5c4c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5c68 x21: x21 x22: x22
STACK CFI 5c70 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5c74 x23: .cfa -16 + ^
STACK CFI INIT 5c80 410 .cfa: sp 0 + .ra: x30
STACK CFI 5c88 .cfa: sp 224 +
STACK CFI 5c94 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5cb0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5cbc x19: x19 x20: x20
STACK CFI 5ce4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5cec .cfa: sp 224 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 5d0c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5d10 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5d90 x21: x21 x22: x22
STACK CFI 5d94 x23: x23 x24: x24
STACK CFI 5da0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5e18 x21: x21 x22: x22
STACK CFI 5e1c x23: x23 x24: x24
STACK CFI 5ed0 x19: x19 x20: x20
STACK CFI 5ed8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5ef8 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5f4c x21: x21 x22: x22
STACK CFI 5f50 x23: x23 x24: x24
STACK CFI 5f54 x19: x19 x20: x20
STACK CFI 5f70 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5f98 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 5fa0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5fa4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5ffc x21: x21 x22: x22
STACK CFI 6000 x23: x23 x24: x24
STACK CFI 6008 x19: x19 x20: x20
STACK CFI 6010 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 601c x21: x21 x22: x22
STACK CFI 6020 x23: x23 x24: x24
STACK CFI 6024 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6040 x21: x21 x22: x22
STACK CFI 6044 x23: x23 x24: x24
STACK CFI 6048 x19: x19 x20: x20
STACK CFI 604c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6050 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6054 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6058 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 6070 x19: x19 x20: x20
STACK CFI 6078 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT 6090 52c .cfa: sp 0 + .ra: x30
STACK CFI 6098 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 60a8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 60bc x19: x19 x20: x20
STACK CFI 60c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 60c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 60d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6174 x23: .cfa -16 + ^
STACK CFI 61a4 x23: x23
STACK CFI 61f0 x19: x19 x20: x20
STACK CFI 61f8 x21: x21 x22: x22
STACK CFI 61fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6204 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6290 x19: x19 x20: x20
STACK CFI 6294 x21: x21 x22: x22
STACK CFI 6298 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6488 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 64a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6514 x19: x19 x20: x20
STACK CFI 651c x21: x21 x22: x22
STACK CFI 6520 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6528 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6540 x19: x19 x20: x20
STACK CFI 6548 x21: x21 x22: x22
STACK CFI 654c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 65a0 x23: x23
STACK CFI INIT 65c0 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 65c8 .cfa: sp 112 +
STACK CFI 65d4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 65e8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 65f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 661c x21: x21 x22: x22
STACK CFI 6648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6650 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 666c x23: .cfa -16 + ^
STACK CFI 6718 x21: x21 x22: x22
STACK CFI 671c x23: x23
STACK CFI 6720 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 673c x23: .cfa -16 + ^
STACK CFI 676c x23: x23
STACK CFI 6798 x21: x21 x22: x22
STACK CFI 679c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 67a4 x21: x21 x22: x22 x23: x23
STACK CFI 67c0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 67d8 x21: x21 x22: x22
STACK CFI 67dc x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 67fc x23: x23
STACK CFI 6814 x21: x21 x22: x22
STACK CFI 6818 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 6850 x23: x23
STACK CFI 6868 x21: x21 x22: x22
STACK CFI 6870 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 6890 x23: x23
STACK CFI 68a8 x21: x21 x22: x22
STACK CFI 68ac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 68b0 x23: .cfa -16 + ^
STACK CFI INIT 68b4 458 .cfa: sp 0 + .ra: x30
STACK CFI 68bc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 68c8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 68d4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 68e8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6910 x25: .cfa -16 + ^
STACK CFI 6994 v8: .cfa -8 + ^
STACK CFI 6a5c x19: x19 x20: x20
STACK CFI 6a60 x21: x21 x22: x22
STACK CFI 6a64 x23: x23 x24: x24
STACK CFI 6a68 x25: x25
STACK CFI 6a6c v8: v8
STACK CFI 6a70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6a78 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 6a84 v8: .cfa -8 + ^
STACK CFI 6a8c v8: v8 x23: x23 x24: x24 x25: x25
STACK CFI 6aa4 x19: x19 x20: x20
STACK CFI 6aac x21: x21 x22: x22
STACK CFI 6ab0 v8: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 6b34 v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 6b50 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6b64 x19: x19 x20: x20
STACK CFI 6b6c x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 6b90 x25: x25
STACK CFI 6b98 x19: x19 x20: x20
STACK CFI 6ba0 x21: x21 x22: x22
STACK CFI 6ba4 x23: x23 x24: x24
STACK CFI 6ba8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 6c28 x25: x25
STACK CFI 6c2c v8: .cfa -8 + ^ x25: .cfa -16 + ^
STACK CFI 6c8c x25: x25
STACK CFI 6c90 v8: v8
STACK CFI 6c98 v8: .cfa -8 + ^ x25: .cfa -16 + ^
STACK CFI 6cd0 v8: v8 x25: x25
STACK CFI 6ce4 v8: .cfa -8 + ^ x25: .cfa -16 + ^
STACK CFI INIT 6d10 18 .cfa: sp 0 + .ra: x30
STACK CFI 6d18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6d20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6d30 20 .cfa: sp 0 + .ra: x30
STACK CFI 6d38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6d44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6d50 244 .cfa: sp 0 + .ra: x30
STACK CFI 6d58 .cfa: sp 176 +
STACK CFI 6d64 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6d6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6d74 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6d80 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6ec0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6ec8 .cfa: sp 176 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6f94 b0 .cfa: sp 0 + .ra: x30
STACK CFI 6f9c .cfa: sp 256 +
STACK CFI 6fac .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 7038 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7040 .cfa: sp 256 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI INIT 7044 660 .cfa: sp 0 + .ra: x30
STACK CFI 704c .cfa: sp 128 +
STACK CFI 7058 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7060 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7084 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7090 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 70e0 x21: x21 x22: x22
STACK CFI 70e4 x23: x23 x24: x24
STACK CFI 7110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7118 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7124 x23: x23 x24: x24
STACK CFI 712c x21: x21 x22: x22
STACK CFI 7130 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7184 x21: x21 x22: x22
STACK CFI 718c x23: x23 x24: x24
STACK CFI 7190 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 71b4 x21: x21 x22: x22
STACK CFI 71bc x23: x23 x24: x24
STACK CFI 71c8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7268 x21: x21 x22: x22
STACK CFI 7270 x23: x23 x24: x24
STACK CFI 7274 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 72a0 x25: .cfa -16 + ^
STACK CFI 72e0 x25: x25
STACK CFI 7344 x25: .cfa -16 + ^
STACK CFI 7374 x25: x25
STACK CFI 737c x25: .cfa -16 + ^
STACK CFI 73cc x25: x25
STACK CFI 73d8 x25: .cfa -16 + ^
STACK CFI 7470 x25: x25
STACK CFI 74a4 x25: .cfa -16 + ^
STACK CFI 752c x25: x25
STACK CFI 7530 x25: .cfa -16 + ^
STACK CFI 7548 x25: x25
STACK CFI 754c x25: .cfa -16 + ^
STACK CFI 75d8 x25: x25
STACK CFI 75dc x25: .cfa -16 + ^
STACK CFI 75e0 x25: x25
STACK CFI 75e4 x25: .cfa -16 + ^
STACK CFI 75e8 x25: x25
STACK CFI 75ec x25: .cfa -16 + ^
STACK CFI 7678 x21: x21 x22: x22
STACK CFI 7680 x23: x23 x24: x24
STACK CFI 7684 x25: x25
STACK CFI 7688 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 768c x25: x25
STACK CFI 7694 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 7698 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 769c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 76a0 x25: .cfa -16 + ^
STACK CFI INIT 76a4 1c .cfa: sp 0 + .ra: x30
STACK CFI 76ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 76b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 76c0 28 .cfa: sp 0 + .ra: x30
STACK CFI 76c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 76d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 76f0 2c8 .cfa: sp 0 + .ra: x30
STACK CFI 76f8 .cfa: sp 96 +
STACK CFI 7704 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7710 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7730 x23: .cfa -16 + ^
STACK CFI 77f0 x23: x23
STACK CFI 7820 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7828 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7844 x23: x23
STACK CFI 784c x23: .cfa -16 + ^
STACK CFI 7858 x23: x23
STACK CFI 7860 x23: .cfa -16 + ^
STACK CFI 78f4 x23: x23
STACK CFI 78fc x23: .cfa -16 + ^
STACK CFI 793c x23: x23
STACK CFI 7940 x23: .cfa -16 + ^
STACK CFI 79a8 x23: x23
STACK CFI 79b4 x23: .cfa -16 + ^
STACK CFI INIT 79c0 194 .cfa: sp 0 + .ra: x30
STACK CFI 79c8 .cfa: sp 80 +
STACK CFI 79d4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 79dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 79e8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7ad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7ad8 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7b54 74 .cfa: sp 0 + .ra: x30
STACK CFI 7b5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7b64 x19: .cfa -16 + ^
STACK CFI 7b78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7b80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7bc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7bd0 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 7bd8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7be0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7bf0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7bfc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7c8c x27: .cfa -16 + ^
STACK CFI 7cd0 x27: x27
STACK CFI 7d24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7d2c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 7d90 x27: .cfa -16 + ^
STACK CFI 7dac x27: x27
STACK CFI 7de4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7dec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7e80 6b8 .cfa: sp 0 + .ra: x30
STACK CFI 7e88 .cfa: sp 128 +
STACK CFI 7e94 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7eac x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7eb8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7ec4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7ec8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7f18 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 80bc x19: x19 x20: x20
STACK CFI 80ec x25: x25 x26: x26
STACK CFI 80fc x23: x23 x24: x24
STACK CFI 8104 x21: x21 x22: x22
STACK CFI 8108 x27: x27 x28: x28
STACK CFI 810c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8114 .cfa: sp 128 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 81dc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 828c x19: x19 x20: x20
STACK CFI 829c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8314 x19: x19 x20: x20
STACK CFI 8350 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8390 x19: x19 x20: x20
STACK CFI 839c x21: x21 x22: x22
STACK CFI 83a0 x23: x23 x24: x24
STACK CFI 83a4 x25: x25 x26: x26
STACK CFI 83a8 x27: x27 x28: x28
STACK CFI 83ac x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 83c8 x19: x19 x20: x20
STACK CFI 83d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8408 x19: x19 x20: x20
STACK CFI 8454 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8460 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 849c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 84a4 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 84a8 x19: x19 x20: x20
STACK CFI 84e4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 84e8 x19: x19 x20: x20
STACK CFI 851c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8520 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8524 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8528 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 852c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8530 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8534 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 8540 54 .cfa: sp 0 + .ra: x30
STACK CFI 856c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8588 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8594 12c .cfa: sp 0 + .ra: x30
STACK CFI 859c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 85a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 85ac x21: .cfa -16 + ^
STACK CFI 85e8 x21: x21
STACK CFI 85f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 85fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 863c x21: x21
STACK CFI 8648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8650 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 868c x21: x21
STACK CFI 869c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 86a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 86c0 5c .cfa: sp 0 + .ra: x30
STACK CFI 86f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8710 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8720 18c .cfa: sp 0 + .ra: x30
STACK CFI 8728 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8730 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8738 x21: .cfa -16 + ^
STACK CFI 87cc x21: x21
STACK CFI 87d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 87e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 8844 x21: x21
STACK CFI 8850 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8858 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 885c x21: x21
STACK CFI 886c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8874 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 888c x21: x21
STACK CFI INIT 88b0 54 .cfa: sp 0 + .ra: x30
STACK CFI 88dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 88f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8904 54 .cfa: sp 0 + .ra: x30
STACK CFI 8930 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 894c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8960 3e8 .cfa: sp 0 + .ra: x30
STACK CFI 8968 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8970 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8978 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 89ec x21: x21 x22: x22
STACK CFI 89f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8a00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8cdc x21: x21 x22: x22
STACK CFI 8ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8cf0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8d0c x21: x21 x22: x22
STACK CFI 8d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8d24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8d2c x21: x21 x22: x22
STACK CFI INIT 8d50 ec .cfa: sp 0 + .ra: x30
STACK CFI 8d58 .cfa: sp 64 +
STACK CFI 8d64 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8d6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8d78 x21: .cfa -16 + ^
STACK CFI 8df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8dfc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8e40 118 .cfa: sp 0 + .ra: x30
STACK CFI 8e48 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8e50 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8e60 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8e70 x23: .cfa -16 + ^
STACK CFI 8eb4 x23: x23
STACK CFI 8ecc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8ed4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 8eec x23: .cfa -16 + ^
STACK CFI 8f14 x23: x23
STACK CFI 8f18 x23: .cfa -16 + ^
STACK CFI 8f28 x23: x23
STACK CFI 8f2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8f34 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 8f54 x23: x23
STACK CFI INIT 8f60 a8 .cfa: sp 0 + .ra: x30
STACK CFI 8f68 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8f70 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8f78 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8f84 x23: .cfa -16 + ^
STACK CFI 8fe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8fec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 9000 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 9010 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 9018 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9030 .cfa: sp 8288 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 90b8 x25: .cfa -16 + ^
STACK CFI 9150 x25: x25
STACK CFI 9174 .cfa: sp 80 +
STACK CFI 9188 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9190 .cfa: sp 8288 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 91ec x25: .cfa -16 + ^
STACK CFI INIT 91f0 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 91f8 .cfa: sp 128 +
STACK CFI 9204 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 920c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9214 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 921c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9228 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 92f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 92f8 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 9330 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 93a4 x27: x27 x28: x28
STACK CFI 93a8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 93bc x27: x27 x28: x28
STACK CFI 93dc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 93e0 ec .cfa: sp 0 + .ra: x30
STACK CFI 93e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 93f4 x25: .cfa -16 + ^
STACK CFI 93fc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9408 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9414 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9484 x19: x19 x20: x20
STACK CFI 9488 x21: x21 x22: x22
STACK CFI 948c x23: x23 x24: x24
STACK CFI 9498 .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI 94a0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 94a8 x19: x19 x20: x20
STACK CFI 94b0 x21: x21 x22: x22
STACK CFI 94b4 x23: x23 x24: x24
STACK CFI 94bc .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI 94c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 94d0 ec .cfa: sp 0 + .ra: x30
STACK CFI 94d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 94e4 x25: .cfa -16 + ^
STACK CFI 94ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 94f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9504 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9574 x19: x19 x20: x20
STACK CFI 9578 x21: x21 x22: x22
STACK CFI 957c x23: x23 x24: x24
STACK CFI 9588 .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI 9590 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 9598 x19: x19 x20: x20
STACK CFI 95a0 x21: x21 x22: x22
STACK CFI 95a4 x23: x23 x24: x24
STACK CFI 95ac .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI 95b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 95c0 224 .cfa: sp 0 + .ra: x30
STACK CFI 95c8 .cfa: sp 128 +
STACK CFI 95d4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 95dc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 95e4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 95ec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 95f8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 96c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 96c8 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 972c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 97a0 x27: x27 x28: x28
STACK CFI 97a4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 97b8 x27: x27 x28: x28
STACK CFI 97e0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 97e4 228 .cfa: sp 0 + .ra: x30
STACK CFI 97ec .cfa: sp 128 +
STACK CFI 97f8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9800 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9808 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9810 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 981c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 98e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 98ec .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 9954 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 99c8 x27: x27 x28: x28
STACK CFI 99cc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 99e0 x27: x27 x28: x28
STACK CFI 9a08 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 9a10 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 9a18 .cfa: sp 128 +
STACK CFI 9a24 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9a2c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9a34 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9a3c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9a48 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9b18 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 9b50 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9bc4 x27: x27 x28: x28
STACK CFI 9bc8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9bdc x27: x27 x28: x28
STACK CFI 9bfc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 9c00 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 9c08 .cfa: sp 128 +
STACK CFI 9c14 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9c1c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9c24 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9c2c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9c38 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9d00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9d08 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 9d40 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9db4 x27: x27 x28: x28
STACK CFI 9db8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9dcc x27: x27 x28: x28
STACK CFI 9dec x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 9df0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 9df8 .cfa: sp 64 +
STACK CFI 9e04 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9e0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9e18 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9e6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9e74 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9ec0 44 .cfa: sp 0 + .ra: x30
STACK CFI 9ec8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9ed0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9efc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9f04 44 .cfa: sp 0 + .ra: x30
STACK CFI 9f0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9f14 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9f50 44 .cfa: sp 0 + .ra: x30
STACK CFI 9f58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9f60 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9f8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9f94 44 .cfa: sp 0 + .ra: x30
STACK CFI 9f9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9fa4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9fd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9fe0 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 9fe8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9ff0 x23: .cfa -16 + ^
STACK CFI 9ffc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a004 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a0bc x21: x21 x22: x22
STACK CFI a0cc x19: x19 x20: x20
STACK CFI a0d4 .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI a0dc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a0e4 x21: x21 x22: x22
STACK CFI a0f4 x19: x19 x20: x20
STACK CFI a0fc .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI a104 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a1a8 x21: x21 x22: x22
STACK CFI a1b8 x19: x19 x20: x20
STACK CFI a1c4 .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI a1cc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI a238 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI a250 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a26c x19: x19 x20: x20
STACK CFI a270 x21: x21 x22: x22
STACK CFI a274 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a290 x19: x19 x20: x20
STACK CFI a294 x21: x21 x22: x22
STACK CFI a298 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT a2c0 160 .cfa: sp 0 + .ra: x30
STACK CFI a2c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a2d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a2fc x21: .cfa -16 + ^
STACK CFI a358 x21: x21
STACK CFI a384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a38c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a398 x21: .cfa -16 + ^
STACK CFI a3b8 x21: x21
STACK CFI a3c4 x21: .cfa -16 + ^
STACK CFI INIT a420 150 .cfa: sp 0 + .ra: x30
STACK CFI a428 .cfa: sp 80 +
STACK CFI a434 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a43c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a448 x21: .cfa -16 + ^
STACK CFI a52c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a534 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT a570 1d8 .cfa: sp 0 + .ra: x30
STACK CFI a578 .cfa: sp 112 +
STACK CFI a588 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a594 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a5c8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a5d4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI a6f8 x21: x21 x22: x22
STACK CFI a6fc x25: x25 x26: x26
STACK CFI a72c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI a734 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI a738 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a73c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI a740 x21: x21 x22: x22 x25: x25 x26: x26
