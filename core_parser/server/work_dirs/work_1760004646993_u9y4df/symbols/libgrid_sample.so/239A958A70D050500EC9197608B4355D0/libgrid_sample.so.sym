MODULE Linux arm64 239A958A70D050500EC9197608B4355D0 libgrid_sample.so
INFO CODE_ID 8A959A23D07050500EC9197608B4355D
PUBLIC 35a0 0 _init
PUBLIC 3730 0 _GLOBAL__sub_I_trt_grid_sampler.cpp
PUBLIC 3788 0 call_weak_fn
PUBLIC 37a0 0 deregister_tm_clones
PUBLIC 37d0 0 register_tm_clones
PUBLIC 3810 0 __do_global_dtors_aux
PUBLIC 3860 0 frame_dummy
PUBLIC 3870 0 mmdeploy::TRTGridSampler::getOutputDimensions(int, nvinfer1::DimsExprs const*, int, nvinfer1::IExprBuilder&)
PUBLIC 38b0 0 mmdeploy::TRTGridSampler::configurePlugin(nvinfer1::DynamicPluginTensorDesc const*, int, nvinfer1::DynamicPluginTensorDesc const*, int)
PUBLIC 38c0 0 mmdeploy::TRTGridSampler::getWorkspaceSize(nvinfer1::PluginTensorDesc const*, int, nvinfer1::PluginTensorDesc const*, int) const
PUBLIC 38d0 0 mmdeploy::TRTGridSampler::getOutputDataType(int, nvinfer1::DataType const*, int) const
PUBLIC 38e0 0 mmdeploy::TRTGridSampler::getPluginType() const
PUBLIC 38f0 0 mmdeploy::TRTGridSamplerCreator::getPluginVersion() const
PUBLIC 3900 0 mmdeploy::TRTGridSampler::getNbOutputs() const
PUBLIC 3910 0 mmdeploy::TRTGridSampler::getSerializationSize() const
PUBLIC 3920 0 mmdeploy::TRTGridSampler::serialize(void*) const
PUBLIC 3940 0 mmdeploy::TRTGridSampler::enqueue(nvinfer1::PluginTensorDesc const*, nvinfer1::PluginTensorDesc const*, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 3b50 0 mmdeploy::TRTGridSampler::supportsFormatCombination(int, nvinfer1::PluginTensorDesc const*, int, int)
PUBLIC 3b80 0 mmdeploy::TRTGridSampler::TRTGridSampler(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int, int, bool)
PUBLIC 3ca0 0 mmdeploy::TRTGridSampler::clone() const
PUBLIC 3d30 0 mmdeploy::TRTGridSamplerCreator::createPlugin(char const*, nvinfer1::PluginFieldCollection const*)
PUBLIC 4250 0 mmdeploy::TRTGridSampler::TRTGridSampler(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, void const*, unsigned long)
PUBLIC 4370 0 mmdeploy::TRTGridSamplerCreator::deserializePlugin(char const*, void const*, unsigned long)
PUBLIC 4550 0 mmdeploy::TRTGridSamplerCreator::TRTGridSamplerCreator()
PUBLIC 46e0 0 nvinfer1::IVersionedInterface::getAPILanguage() const
PUBLIC 46f0 0 nvinfer1::IPluginV2Ext::configureWithFormat(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType, nvinfer1::TensorFormat, int)
PUBLIC 4700 0 nvinfer1::v_1_0::IPluginCreator::getInterfaceInfo() const
PUBLIC 4710 0 nvinfer1::IPluginV2DynamicExt::getTensorRTVersion() const
PUBLIC 4720 0 nvinfer1::IPluginV2DynamicExt::configurePlugin(nvinfer1::Dims64 const*, int, nvinfer1::Dims64 const*, int, nvinfer1::DataType const*, nvinfer1::DataType const*, bool const*, bool const*, nvinfer1::TensorFormat, int)
PUBLIC 4730 0 nvinfer1::IPluginV2DynamicExt::supportsFormat(nvinfer1::DataType, nvinfer1::TensorFormat) const
PUBLIC 4740 0 nvinfer1::IPluginV2DynamicExt::getOutputDimensions(int, nvinfer1::Dims64 const*, int)
PUBLIC 4760 0 nvinfer1::IPluginV2DynamicExt::isOutputBroadcastAcrossBatch(int, bool const*, int) const
PUBLIC 4770 0 nvinfer1::IPluginV2DynamicExt::canBroadcastInputAcrossBatch(int) const
PUBLIC 4780 0 nvinfer1::IPluginV2DynamicExt::getWorkspaceSize(int) const
PUBLIC 4790 0 nvinfer1::IPluginV2DynamicExt::enqueue(int, void const* const*, void* const*, void*, CUstream_st*)
PUBLIC 47a0 0 mmdeploy::TRTPluginBase::getPluginVersion() const
PUBLIC 47b0 0 mmdeploy::TRTPluginBase::initialize()
PUBLIC 47c0 0 mmdeploy::TRTPluginBase::terminate()
PUBLIC 47d0 0 mmdeploy::TRTPluginBase::getPluginNamespace() const
PUBLIC 47e0 0 mmdeploy::TRTPluginBase::configurePlugin(nvinfer1::DynamicPluginTensorDesc const*, int, nvinfer1::DynamicPluginTensorDesc const*, int)
PUBLIC 47f0 0 mmdeploy::TRTPluginBase::getWorkspaceSize(nvinfer1::PluginTensorDesc const*, int, nvinfer1::PluginTensorDesc const*, int) const
PUBLIC 4800 0 mmdeploy::TRTPluginBase::attachToContext(cudnnContext*, cublasContext*, nvinfer1::v_1_0::IGpuAllocator*)
PUBLIC 4810 0 mmdeploy::TRTPluginBase::detachFromContext()
PUBLIC 4820 0 mmdeploy::TRTPluginCreatorBase::getPluginVersion() const
PUBLIC 4830 0 mmdeploy::TRTPluginCreatorBase::getFieldNames()
PUBLIC 4840 0 mmdeploy::TRTPluginCreatorBase::getPluginNamespace() const
PUBLIC 4850 0 mmdeploy::TRTPluginBase::setPluginNamespace(char const*)
PUBLIC 48a0 0 mmdeploy::TRTPluginCreatorBase::setPluginNamespace(char const*)
PUBLIC 48f0 0 mmdeploy::TRTGridSampler::~TRTGridSampler()
PUBLIC 4970 0 nvinfer1::PluginRegistrar<mmdeploy::TRTGridSamplerCreator>::~PluginRegistrar()
PUBLIC 49e0 0 mmdeploy::TRTGridSamplerCreator::~TRTGridSamplerCreator()
PUBLIC 4a50 0 mmdeploy::TRTGridSamplerCreator::~TRTGridSamplerCreator()
PUBLIC 4ac0 0 mmdeploy::TRTGridSampler::~TRTGridSampler()
PUBLIC 4b30 0 mmdeploy::TRTPluginBase::destroy()
PUBLIC 4bc0 0 _fini
STACK CFI INIT 37a0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 37d0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3810 48 .cfa: sp 0 + .ra: x30
STACK CFI 3814 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 381c x19: .cfa -16 + ^
STACK CFI 3854 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3860 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 46f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4700 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4710 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4720 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4730 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4740 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4760 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4770 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4780 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4790 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47a0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 47b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4800 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4810 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4820 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4830 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4840 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3870 40 .cfa: sp 0 + .ra: x30
STACK CFI 3890 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 38a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 38b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 38e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 38f0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3900 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3910 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3920 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3940 204 .cfa: sp 0 + .ra: x30
STACK CFI 3944 .cfa: sp 400 +
STACK CFI 3958 .ra: .cfa -360 + ^ x29: .cfa -368 + ^
STACK CFI 3988 x19: .cfa -352 + ^
STACK CFI 3af0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3af4 .cfa: sp 400 + .ra: .cfa -360 + ^ x19: .cfa -352 + ^ x29: .cfa -368 + ^
STACK CFI INIT 3b50 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4850 44 .cfa: sp 0 + .ra: x30
STACK CFI 4854 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 485c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 48a0 44 .cfa: sp 0 + .ra: x30
STACK CFI 48a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 48ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 48e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 48f0 74 .cfa: sp 0 + .ra: x30
STACK CFI 48f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 490c x19: .cfa -16 + ^
STACK CFI 4954 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4958 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4960 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4970 6c .cfa: sp 0 + .ra: x30
STACK CFI 4974 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 498c x19: .cfa -16 + ^
STACK CFI 49cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 49d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 49d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 49e0 6c .cfa: sp 0 + .ra: x30
STACK CFI 49e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 49fc x19: .cfa -16 + ^
STACK CFI 4a3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4a40 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4a48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4a50 68 .cfa: sp 0 + .ra: x30
STACK CFI 4a54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a6c x19: .cfa -16 + ^
STACK CFI 4ab4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4ac0 70 .cfa: sp 0 + .ra: x30
STACK CFI 4ac4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4adc x19: .cfa -16 + ^
STACK CFI 4b2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4b30 90 .cfa: sp 0 + .ra: x30
STACK CFI 4b4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b60 x19: .cfa -16 + ^
STACK CFI 4bb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3b80 11c .cfa: sp 0 + .ra: x30
STACK CFI 3b84 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3b94 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3ba4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3bb8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 3c58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 3c5c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3ca0 8c .cfa: sp 0 + .ra: x30
STACK CFI 3ca4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3cac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3d18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3d30 520 .cfa: sp 0 + .ra: x30
STACK CFI 3d34 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 3d44 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 3d58 x21: .cfa -176 + ^ x22: .cfa -168 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 3d70 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 3d78 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 3ea8 x23: x23 x24: x24
STACK CFI 3fb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3fbc .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 4134 x23: x23 x24: x24
STACK CFI 4140 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 4180 x23: x23 x24: x24
STACK CFI 41b4 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 41dc x23: x23 x24: x24
STACK CFI 4218 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 421c x23: x23 x24: x24
STACK CFI 4238 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 4248 x23: x23 x24: x24
STACK CFI 424c x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI INIT 4250 118 .cfa: sp 0 + .ra: x30
STACK CFI 4254 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4264 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4274 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4280 x23: .cfa -32 + ^
STACK CFI 4324 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4328 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4370 1dc .cfa: sp 0 + .ra: x30
STACK CFI 4374 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4388 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 4394 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 43a0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 44b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 44b8 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4550 18c .cfa: sp 0 + .ra: x30
STACK CFI 4554 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4578 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 466c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4670 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI 4674 x21: .cfa -96 + ^
STACK CFI 4678 x21: x21
STACK CFI 4684 x21: .cfa -96 + ^
STACK CFI INIT 3730 58 .cfa: sp 0 + .ra: x30
STACK CFI 3734 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 373c x19: .cfa -16 + ^
STACK CFI 377c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
