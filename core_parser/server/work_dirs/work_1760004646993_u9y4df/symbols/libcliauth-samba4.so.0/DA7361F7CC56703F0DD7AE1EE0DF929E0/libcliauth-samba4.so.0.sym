MODULE Linux arm64 DA7361F7CC56703F0DD7AE1EE0DF929E0 libcliauth-samba4.so.0
INFO CODE_ID F76173DA56CC3F700DD7AE1EE0DF929EA4F1D5CD
PUBLIC 5584 0 pam_to_nt_status
PUBLIC 5600 0 nt_status_to_pam
PUBLIC 5664 0 auth_blob_2_auth_info
PUBLIC 56f4 0 auth_info_2_trustauth_inout
PUBLIC 5910 0 auth_info_2_auth_blob
PUBLIC 5990 0 hash_password_check
PUBLIC 5c10 0 log_escape
PUBLIC 5e30 0 netlogon_creds_is_random_challenge
PUBLIC 5e90 0 netlogon_creds_random_challenge
PUBLIC 5ee4 0 netlogon_creds_arcfour_crypt
PUBLIC 5fe0 0 netlogon_creds_aes_encrypt
PUBLIC 6150 0 netlogon_creds_aes_decrypt
PUBLIC 62c0 0 netlogon_creds_client_init_session_key
PUBLIC 6304 0 netlogon_creds_client_check
PUBLIC 6390 0 netlogon_creds_shallow_copy_logon
PUBLIC 6534 0 netlogon_creds_copy
PUBLIC 6640 0 E_md4hash
PUBLIC 6720 0 ntv2_owf_gen
PUBLIC 6a30 0 SMBOWFencrypt_ntv2
PUBLIC 6b64 0 SMBsesskeygen_ntv2
PUBLIC 7140 0 SMBsesskeygen_ntv1
PUBLIC 7170 0 NTLMv2_RESPONSE_verify_netlogon_creds
PUBLIC 7664 0 encode_pw_buffer
PUBLIC 7760 0 encode_pwd_buffer514_from_str
PUBLIC 7834 0 extract_pwd_blob_from_buffer514
PUBLIC 78b0 0 decode_pwd_string_from_buffer514
PUBLIC 7980 0 encode_rc4_passwd_buffer
PUBLIC 7ae0 0 decode_rc4_passwd_buffer
PUBLIC 7bc0 0 set_pw_in_buffer
PUBLIC 7c50 0 extract_pw_from_buffer
PUBLIC 7cd0 0 decode_pw_buffer
PUBLIC 7e10 0 encode_wkssvc_join_password_buffer
PUBLIC 7fc4 0 decode_wkssvc_join_password_buffer
PUBLIC 81b0 0 des_crypt56_gnutls
PUBLIC 8340 0 netlogon_creds_des_encrypt_LMKey
PUBLIC 83e4 0 netlogon_creds_des_decrypt_LMKey
PUBLIC 8630 0 netlogon_creds_decrypt_samlogon_validation
PUBLIC 8650 0 netlogon_creds_encrypt_samlogon_validation
PUBLIC 8670 0 sess_crypt_blob
PUBLIC 87b4 0 sess_encrypt_string
PUBLIC 8970 0 sess_decrypt_string
PUBLIC 8b60 0 sess_encrypt_blob
PUBLIC 8d24 0 sess_decrypt_blob
PUBLIC 8fa0 0 SMBsesskeygen_lm_sess_key
PUBLIC 90a0 0 E_P16
PUBLIC 9150 0 E_deshash
PUBLIC 92b0 0 nt_lm_owf_gen
PUBLIC 92f0 0 E_P24
PUBLIC 9374 0 SMBOWFencrypt
PUBLIC 9660 0 ntlm_password_check
PUBLIC a420 0 SMBencrypt_hash
PUBLIC a490 0 SMBencrypt
PUBLIC a524 0 SMBNTencrypt_hash
PUBLIC a594 0 SMBNTencrypt
PUBLIC a620 0 E_old_pw_hash
PUBLIC a690 0 des_crypt128
PUBLIC a894 0 des_crypt112
PUBLIC aa30 0 netlogon_creds_client_init
PUBLIC acb0 0 netlogon_creds_server_init
PUBLIC b330 0 netlogon_creds_client_authenticator
PUBLIC b3a0 0 netlogon_creds_server_step_check
PUBLIC b4a0 0 des_crypt112_16
PUBLIC b504 0 netlogon_creds_des_encrypt
PUBLIC b5b0 0 netlogon_creds_des_decrypt
PUBLIC b850 0 netlogon_creds_decrypt_samlogon_logon
PUBLIC b870 0 netlogon_creds_encrypt_samlogon_logon
PUBLIC b890 0 sam_rid_crypt
PUBLIC b964 0 msrpc_gen
PUBLIC c090 0 NTLMv2_generate_names_blob
PUBLIC c184 0 SMBNTLMv2encrypt_hash
PUBLIC c4f4 0 SMBNTLMv2encrypt
PUBLIC c5d0 0 msrpc_parse
PUBLIC cbc0 0 spnego_read_data
PUBLIC d184 0 spnego_write_data
PUBLIC d544 0 spnego_free_data
PUBLIC d620 0 spnego_write_mech_types
PUBLIC d700 0 open_schannel_session_store
PUBLIC d810 0 schannel_get_creds_state
PUBLIC d930 0 schannel_save_creds_state
PUBLIC d9d4 0 schannel_get_challenge
PUBLIC dd20 0 schannel_delete_challenge
PUBLIC de30 0 schannel_save_challenge
PUBLIC e0a0 0 schannel_check_creds_state
STACK CFI INIT 4580 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 45b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 45f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 45f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45fc x19: .cfa -16 + ^
STACK CFI 4634 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4640 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4650 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 4658 .cfa: sp 80 +
STACK CFI 4664 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4670 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 467c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4808 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4820 124 .cfa: sp 0 + .ra: x30
STACK CFI 4828 .cfa: sp 128 +
STACK CFI 4834 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 483c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4844 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4850 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 48e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 48e8 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4944 16c .cfa: sp 0 + .ra: x30
STACK CFI 494c .cfa: sp 64 +
STACK CFI 4958 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4970 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 497c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 49e4 x19: x19 x20: x20
STACK CFI 49e8 x21: x21 x22: x22
STACK CFI 49f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4a30 x19: x19 x20: x20
STACK CFI 4a38 x21: x21 x22: x22
STACK CFI 4a60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4a68 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4aa4 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 4aa8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4aac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 4ab0 264 .cfa: sp 0 + .ra: x30
STACK CFI 4ab8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4ac4 .cfa: x29 80 +
STACK CFI 4ac8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4ad0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4adc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4ae8 x25: .cfa -16 + ^
STACK CFI 4c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4c40 .cfa: x29 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4d14 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 4d1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4d28 .cfa: x29 64 +
STACK CFI 4d2c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4d34 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4d44 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4e60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4e68 .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4f00 d0 .cfa: sp 0 + .ra: x30
STACK CFI 4f08 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4f14 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4f1c x21: .cfa -16 + ^
STACK CFI 4f44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4f4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4f94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4f9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4fd0 258 .cfa: sp 0 + .ra: x30
STACK CFI 4fd8 .cfa: sp 96 +
STACK CFI 4fdc .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4fe4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4ff8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5050 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5058 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5088 x23: .cfa -16 + ^
STACK CFI 5114 x23: x23
STACK CFI 511c x23: .cfa -16 + ^
STACK CFI 5120 x23: x23
STACK CFI 512c x23: .cfa -16 + ^
STACK CFI 5148 x23: x23
STACK CFI 514c x23: .cfa -16 + ^
STACK CFI 519c x23: x23
STACK CFI 51a4 x23: .cfa -16 + ^
STACK CFI 5220 x23: x23
STACK CFI 5224 x23: .cfa -16 + ^
STACK CFI INIT 5230 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 5238 .cfa: sp 112 +
STACK CFI 523c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5244 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5254 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 525c x23: .cfa -16 + ^
STACK CFI 5394 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 539c .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 54e4 a0 .cfa: sp 0 + .ra: x30
STACK CFI 54ec .cfa: sp 64 +
STACK CFI 54f8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5500 x19: .cfa -16 + ^
STACK CFI 5578 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5580 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5584 78 .cfa: sp 0 + .ra: x30
STACK CFI 558c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 55d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 55dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 55e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 55ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 55f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5600 64 .cfa: sp 0 + .ra: x30
STACK CFI 5608 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5618 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5620 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5664 90 .cfa: sp 0 + .ra: x30
STACK CFI 566c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5674 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 56ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 56b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 56f4 154 .cfa: sp 0 + .ra: x30
STACK CFI 56fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5704 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5710 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5724 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5734 x25: .cfa -16 + ^
STACK CFI 57bc x25: x25
STACK CFI 57d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 57dc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 57e8 x25: x25
STACK CFI 5804 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 580c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 5828 x25: x25
STACK CFI 582c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5834 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 5838 x25: x25
STACK CFI 583c x25: .cfa -16 + ^
STACK CFI 5840 x25: x25
STACK CFI INIT 5850 bc .cfa: sp 0 + .ra: x30
STACK CFI 5858 .cfa: sp 64 +
STACK CFI 5864 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 586c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5874 x21: .cfa -16 + ^
STACK CFI 58f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 58fc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5910 80 .cfa: sp 0 + .ra: x30
STACK CFI 5918 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5924 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5930 x21: .cfa -16 + ^
STACK CFI 595c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5964 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5990 278 .cfa: sp 0 + .ra: x30
STACK CFI 5998 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 59a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 59b0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 59e8 x19: x19 x20: x20
STACK CFI 59f0 x21: x21 x22: x22
STACK CFI 59f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 59fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5a14 x19: x19 x20: x20
STACK CFI 5a1c x21: x21 x22: x22
STACK CFI 5a20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5a28 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5a40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5a4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5a64 x19: x19 x20: x20
STACK CFI 5a6c x21: x21 x22: x22
STACK CFI 5a70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5a78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5bac x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 5bb0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5bdc x19: x19 x20: x20
STACK CFI 5be0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5c04 x19: x19 x20: x20
STACK CFI INIT 5c10 21c .cfa: sp 0 + .ra: x30
STACK CFI 5c18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5c20 x21: .cfa -16 + ^
STACK CFI 5c28 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5d18 x19: x19 x20: x20
STACK CFI 5d24 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 5d2c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5dd0 x19: x19 x20: x20
STACK CFI 5dd8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5e28 x19: x19 x20: x20
STACK CFI INIT 5e30 60 .cfa: sp 0 + .ra: x30
STACK CFI 5e40 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5e5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5e64 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5e90 54 .cfa: sp 0 + .ra: x30
STACK CFI 5e98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ea0 x19: .cfa -16 + ^
STACK CFI 5edc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5ee4 fc .cfa: sp 0 + .ra: x30
STACK CFI 5eec .cfa: sp 64 +
STACK CFI 5efc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5f08 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5f88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f90 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5fe0 16c .cfa: sp 0 + .ra: x30
STACK CFI 5fe8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ff0 .cfa: x29 32 +
STACK CFI 5ffc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 60ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 60f4 .cfa: x29 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6150 16c .cfa: sp 0 + .ra: x30
STACK CFI 6158 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6160 .cfa: x29 32 +
STACK CFI 616c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 625c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6264 .cfa: x29 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 62c0 44 .cfa: sp 0 + .ra: x30
STACK CFI 62c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 62d8 x19: .cfa -16 + ^
STACK CFI 62fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6304 8c .cfa: sp 0 + .ra: x30
STACK CFI 630c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6334 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6340 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6390 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 6398 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 63a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 63b4 x21: .cfa -16 + ^
STACK CFI 646c x21: x21
STACK CFI 6478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6480 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 64a8 x21: x21
STACK CFI 64cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 64d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 64d8 x21: x21
STACK CFI 64e0 x21: .cfa -16 + ^
STACK CFI 64f8 x21: x21
STACK CFI 64fc x21: .cfa -16 + ^
STACK CFI 6514 x21: x21
STACK CFI 6518 x21: .cfa -16 + ^
STACK CFI 6530 x21: x21
STACK CFI INIT 6534 104 .cfa: sp 0 + .ra: x30
STACK CFI 653c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 654c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 65e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 65f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6640 dc .cfa: sp 0 + .ra: x30
STACK CFI 6648 .cfa: sp 80 +
STACK CFI 6658 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6660 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 666c x21: .cfa -16 + ^
STACK CFI 6710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6718 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6720 308 .cfa: sp 0 + .ra: x30
STACK CFI 6728 .cfa: sp 112 +
STACK CFI 6734 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 673c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6748 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6754 x23: .cfa -16 + ^
STACK CFI 68ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 68b4 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6a30 134 .cfa: sp 0 + .ra: x30
STACK CFI 6a38 .cfa: sp 64 +
STACK CFI 6a44 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6a4c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6a58 x21: .cfa -16 + ^
STACK CFI 6ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6aec .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6b64 60 .cfa: sp 0 + .ra: x30
STACK CFI 6b6c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6b94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6b9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6ba0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6bc4 2c0 .cfa: sp 0 + .ra: x30
STACK CFI 6bcc .cfa: sp 144 +
STACK CFI 6bd0 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6bd8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6c00 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6c30 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6c3c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6c70 x21: x21 x22: x22
STACK CFI 6c74 x23: x23 x24: x24
STACK CFI 6c78 x25: x25 x26: x26
STACK CFI 6ca4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6cac .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 6cbc x21: x21 x22: x22
STACK CFI 6d1c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6d9c x21: x21 x22: x22
STACK CFI 6da4 x23: x23 x24: x24
STACK CFI 6da8 x25: x25 x26: x26
STACK CFI 6dac x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6db0 x21: x21 x22: x22
STACK CFI 6db4 x23: x23 x24: x24
STACK CFI 6db8 x25: x25 x26: x26
STACK CFI 6dbc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6e0c x21: x21 x22: x22
STACK CFI 6e10 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6e68 x21: x21 x22: x22
STACK CFI 6e6c x23: x23 x24: x24
STACK CFI 6e70 x25: x25 x26: x26
STACK CFI 6e78 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6e7c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6e80 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 6e84 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 6e8c .cfa: sp 144 +
STACK CFI 6e90 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6e98 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6ec0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6ef0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6efc x25: .cfa -16 + ^
STACK CFI 6f30 x21: x21 x22: x22
STACK CFI 6f34 x23: x23 x24: x24
STACK CFI 6f38 x25: x25
STACK CFI 6f60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6f68 .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 6f78 x21: x21 x22: x22
STACK CFI 6ff8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7000 .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 7050 x21: x21 x22: x22
STACK CFI 7054 x23: x23 x24: x24
STACK CFI 7058 x25: x25
STACK CFI 705c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 70c0 x21: x21 x22: x22
STACK CFI 70c8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 7120 x21: x21 x22: x22
STACK CFI 7124 x23: x23 x24: x24
STACK CFI 7128 x25: x25
STACK CFI 7130 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7134 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7138 x25: .cfa -16 + ^
STACK CFI INIT 7140 28 .cfa: sp 0 + .ra: x30
STACK CFI 7148 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7154 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7170 4f4 .cfa: sp 0 + .ra: x30
STACK CFI 7178 .cfa: sp 240 +
STACK CFI 717c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 71a4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 71ac x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 71c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 71f0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7250 x21: x21 x22: x22
STACK CFI 7254 x23: x23 x24: x24
STACK CFI 7258 x25: x25 x26: x26
STACK CFI 7288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7290 .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 72d0 x21: x21 x22: x22
STACK CFI 72d4 x23: x23 x24: x24
STACK CFI 72d8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 72dc x21: x21 x22: x22
STACK CFI 72e0 x23: x23 x24: x24
STACK CFI 72e4 x25: x25 x26: x26
STACK CFI 72e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 739c x21: x21 x22: x22
STACK CFI 73a4 x23: x23 x24: x24
STACK CFI 73a8 x25: x25 x26: x26
STACK CFI 73ac x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 73fc x21: x21 x22: x22
STACK CFI 7400 x23: x23 x24: x24
STACK CFI 7404 x25: x25 x26: x26
STACK CFI 7408 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 740c x21: x21 x22: x22
STACK CFI 7410 x23: x23 x24: x24
STACK CFI 7414 x25: x25 x26: x26
STACK CFI 7418 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 748c x25: x25 x26: x26
STACK CFI 74e4 x21: x21 x22: x22
STACK CFI 74e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 75c0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7620 x27: x27 x28: x28
STACK CFI 7640 x21: x21 x22: x22
STACK CFI 7648 x23: x23 x24: x24
STACK CFI 764c x25: x25 x26: x26
STACK CFI 7654 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7658 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 765c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7660 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 7664 f4 .cfa: sp 0 + .ra: x30
STACK CFI 766c .cfa: sp 64 +
STACK CFI 767c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7684 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 76c0 x21: .cfa -16 + ^
STACK CFI 76e4 x21: x21
STACK CFI 772c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7734 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7754 x21: .cfa -16 + ^
STACK CFI INIT 7760 d4 .cfa: sp 0 + .ra: x30
STACK CFI 7768 .cfa: sp 48 +
STACK CFI 7778 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7780 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7808 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7834 74 .cfa: sp 0 + .ra: x30
STACK CFI 783c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7844 x19: .cfa -16 + ^
STACK CFI 7868 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7870 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 78a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 78b0 cc .cfa: sp 0 + .ra: x30
STACK CFI 78b8 .cfa: sp 80 +
STACK CFI 78c4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 78cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 78d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7938 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7980 160 .cfa: sp 0 + .ra: x30
STACK CFI 7988 .cfa: sp 128 +
STACK CFI 7994 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 799c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 79a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7a38 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 7a40 x23: .cfa -16 + ^
STACK CFI 7a88 x23: x23
STACK CFI 7a90 x23: .cfa -16 + ^
STACK CFI 7ad4 x23: x23
STACK CFI 7adc x23: .cfa -16 + ^
STACK CFI INIT 7ae0 dc .cfa: sp 0 + .ra: x30
STACK CFI 7ae8 .cfa: sp 80 +
STACK CFI 7af4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7afc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7b88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7b90 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7bc0 8c .cfa: sp 0 + .ra: x30
STACK CFI 7bd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7be0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7bf0 x21: .cfa -16 + ^
STACK CFI 7c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7c50 78 .cfa: sp 0 + .ra: x30
STACK CFI 7c58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7c60 x19: .cfa -16 + ^
STACK CFI 7c7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7c84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7cc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7cd0 138 .cfa: sp 0 + .ra: x30
STACK CFI 7cd8 .cfa: sp 96 +
STACK CFI 7cdc .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7ce4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7cf8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7d00 x23: .cfa -16 + ^
STACK CFI 7da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7da8 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7e10 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 7e18 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7e28 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7e30 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7e40 .cfa: sp 640 + x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7f58 .cfa: sp 64 +
STACK CFI 7f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7f70 .cfa: sp 640 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7fc4 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 7fcc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7fdc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7fec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7ff8 .cfa: sp 640 + x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8110 .cfa: sp 64 +
STACK CFI 8120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8128 .cfa: sp 640 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 81b0 188 .cfa: sp 0 + .ra: x30
STACK CFI 81b8 .cfa: sp 112 +
STACK CFI 81cc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 81d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 81e4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 82c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 82d0 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8340 a4 .cfa: sp 0 + .ra: x30
STACK CFI 8348 .cfa: sp 48 +
STACK CFI 8358 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8360 x19: .cfa -16 + ^
STACK CFI 83b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 83bc .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 83e4 a4 .cfa: sp 0 + .ra: x30
STACK CFI 83ec .cfa: sp 48 +
STACK CFI 83fc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8404 x19: .cfa -16 + ^
STACK CFI 8458 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8460 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8490 198 .cfa: sp 0 + .ra: x30
STACK CFI 849c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 84a8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 84b0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8538 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 85e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 85ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8630 1c .cfa: sp 0 + .ra: x30
STACK CFI 8638 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8644 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8650 1c .cfa: sp 0 + .ra: x30
STACK CFI 8658 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8664 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8670 144 .cfa: sp 0 + .ra: x30
STACK CFI 8678 .cfa: sp 128 +
STACK CFI 867c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8684 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 86a4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 86bc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 86c8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 86d4 x27: .cfa -16 + ^
STACK CFI 874c x21: x21 x22: x22
STACK CFI 8750 x23: x23 x24: x24
STACK CFI 8754 x27: x27
STACK CFI 8758 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^
STACK CFI 875c x21: x21 x22: x22
STACK CFI 8760 x23: x23 x24: x24
STACK CFI 8764 x27: x27
STACK CFI 8794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 879c .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 87a8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 87ac x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 87b0 x27: .cfa -16 + ^
STACK CFI INIT 87b4 1bc .cfa: sp 0 + .ra: x30
STACK CFI 87bc .cfa: sp 112 +
STACK CFI 87c8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 87d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 87dc x23: .cfa -16 + ^
STACK CFI 8910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8918 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8970 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 8978 .cfa: sp 80 +
STACK CFI 897c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8984 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 89bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8a38 x21: x21 x22: x22
STACK CFI 8a64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8a6c .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8a84 x21: x21 x22: x22
STACK CFI 8a8c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8a90 x21: x21 x22: x22
STACK CFI 8a98 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8b18 x21: x21 x22: x22
STACK CFI 8b1c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8b58 x21: x21 x22: x22
STACK CFI 8b5c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 8b60 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 8b68 .cfa: sp 112 +
STACK CFI 8b74 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8b7c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8b8c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8b98 x23: .cfa -16 + ^
STACK CFI 8cc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8ccc .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8d24 27c .cfa: sp 0 + .ra: x30
STACK CFI 8d2c .cfa: sp 96 +
STACK CFI 8d30 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8d38 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8d6c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8d8c x23: .cfa -16 + ^
STACK CFI 8e04 x21: x21 x22: x22
STACK CFI 8e0c x23: x23
STACK CFI 8e10 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 8e14 x23: x23
STACK CFI 8e20 x21: x21 x22: x22
STACK CFI 8e4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8e54 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 8e6c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 8e80 x21: x21 x22: x22
STACK CFI 8e88 x23: x23
STACK CFI 8e8c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 8ea4 x21: x21 x22: x22
STACK CFI 8eac x23: x23
STACK CFI 8eb0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 8ee0 x21: x21 x22: x22
STACK CFI 8ee4 x23: x23
STACK CFI 8f20 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 8f94 x21: x21 x22: x22 x23: x23
STACK CFI 8f98 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8f9c x23: .cfa -16 + ^
STACK CFI INIT 8fa0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 8fac .cfa: sp 80 +
STACK CFI 8fb8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8fc0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9044 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 904c .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 90a0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 90a8 .cfa: sp 64 +
STACK CFI 90c0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 90d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 9144 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 914c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9150 15c .cfa: sp 0 + .ra: x30
STACK CFI 9158 .cfa: sp 96 +
STACK CFI 9164 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 916c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9174 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 917c x23: .cfa -16 + ^
STACK CFI 9264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 926c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 92b0 38 .cfa: sp 0 + .ra: x30
STACK CFI 92b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 92c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 92e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 92f0 84 .cfa: sp 0 + .ra: x30
STACK CFI 92f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9304 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9318 x21: .cfa -16 + ^
STACK CFI 9330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9338 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 936c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9374 9c .cfa: sp 0 + .ra: x30
STACK CFI 937c .cfa: sp 80 +
STACK CFI 9388 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9390 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 939c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9404 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 940c .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9410 248 .cfa: sp 0 + .ra: x30
STACK CFI 9418 .cfa: sp 96 +
STACK CFI 941c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9424 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 944c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 946c x23: x23 x24: x24
STACK CFI 949c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 94a4 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 94b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 94d0 x21: x21 x22: x22
STACK CFI 94d4 x23: x23 x24: x24
STACK CFI 9534 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9584 x21: x21 x22: x22
STACK CFI 9588 x23: x23 x24: x24
STACK CFI 958c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 95dc x23: x23 x24: x24
STACK CFI 95e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9638 x21: x21 x22: x22
STACK CFI 963c x23: x23 x24: x24
STACK CFI 9640 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9644 x21: x21 x22: x22
STACK CFI 9648 x23: x23 x24: x24
STACK CFI 9650 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9654 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 9660 db8 .cfa: sp 0 + .ra: x30
STACK CFI 9668 .cfa: sp 224 +
STACK CFI 9678 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9680 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 968c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 96b8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 96c4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 978c x19: x19 x20: x20
STACK CFI 9794 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 98cc x19: x19 x20: x20
STACK CFI 9918 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9920 .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 99a8 x19: x19 x20: x20
STACK CFI 99b0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9b10 x19: x19 x20: x20
STACK CFI 9b18 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9c48 x19: x19 x20: x20
STACK CFI 9c4c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9db8 x19: x19 x20: x20
STACK CFI 9dc0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9e40 x19: x19 x20: x20
STACK CFI 9e44 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9e70 x19: x19 x20: x20
STACK CFI 9e74 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9e98 x19: x19 x20: x20
STACK CFI 9e9c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9f48 x19: x19 x20: x20
STACK CFI 9f58 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a410 x19: x19 x20: x20
STACK CFI a414 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI INIT a420 70 .cfa: sp 0 + .ra: x30
STACK CFI a428 .cfa: sp 48 +
STACK CFI a43c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a484 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a48c .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT a490 94 .cfa: sp 0 + .ra: x30
STACK CFI a498 .cfa: sp 80 +
STACK CFI a4a4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a4ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a4b4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a518 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a520 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT a524 70 .cfa: sp 0 + .ra: x30
STACK CFI a52c .cfa: sp 48 +
STACK CFI a540 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a588 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a590 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT a594 88 .cfa: sp 0 + .ra: x30
STACK CFI a59c .cfa: sp 80 +
STACK CFI a5a8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a5b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a5bc x21: .cfa -16 + ^
STACK CFI a610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a618 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT a620 6c .cfa: sp 0 + .ra: x30
STACK CFI a628 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a634 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a648 x21: .cfa -16 + ^
STACK CFI a660 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a668 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a684 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a690 94 .cfa: sp 0 + .ra: x30
STACK CFI a698 .cfa: sp 64 +
STACK CFI a6a4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a6ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a6b8 x21: .cfa -16 + ^
STACK CFI a718 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a720 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT a724 170 .cfa: sp 0 + .ra: x30
STACK CFI a72c .cfa: sp 48 +
STACK CFI a740 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a7c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a860 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a868 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT a894 c4 .cfa: sp 0 + .ra: x30
STACK CFI a89c .cfa: sp 64 +
STACK CFI a8a8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a8b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a8bc x21: .cfa -16 + ^
STACK CFI a908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a910 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT a960 7c .cfa: sp 0 + .ra: x30
STACK CFI a968 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a998 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a9a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a9b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a9e0 50 .cfa: sp 0 + .ra: x30
STACK CFI a9e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a9f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI aa28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT aa30 27c .cfa: sp 0 + .ra: x30
STACK CFI aa38 .cfa: sp 176 +
STACK CFI aa3c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI aa44 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI aa4c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI aa58 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI aa64 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI aa7c x27: .cfa -16 + ^
STACK CFI ab88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI ab90 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT acb0 38c .cfa: sp 0 + .ra: x30
STACK CFI acb8 .cfa: sp 176 +
STACK CFI acbc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI acc4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI accc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI acd8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI acec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI acfc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ae58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ae60 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT b040 2ec .cfa: sp 0 + .ra: x30
STACK CFI b048 .cfa: sp 64 +
STACK CFI b054 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b05c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b068 x21: .cfa -16 + ^
STACK CFI b208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b210 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT b330 70 .cfa: sp 0 + .ra: x30
STACK CFI b338 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b340 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b398 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b3a0 100 .cfa: sp 0 + .ra: x30
STACK CFI b3a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b3b8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b3c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b3fc x19: x19 x20: x20
STACK CFI b40c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI b414 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b424 x19: x19 x20: x20
STACK CFI b42c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI b434 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b448 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI b450 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b470 x19: x19 x20: x20
STACK CFI b47c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI b484 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b49c x19: x19 x20: x20
STACK CFI INIT b4a0 64 .cfa: sp 0 + .ra: x30
STACK CFI b4a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b4b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b4bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b4d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b4e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b4fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT b504 a4 .cfa: sp 0 + .ra: x30
STACK CFI b50c .cfa: sp 64 +
STACK CFI b51c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b524 x19: .cfa -16 + ^
STACK CFI b578 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b580 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b5b0 a4 .cfa: sp 0 + .ra: x30
STACK CFI b5b8 .cfa: sp 64 +
STACK CFI b5c8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b5d0 x19: .cfa -16 + ^
STACK CFI b624 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b62c .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT b654 1f4 .cfa: sp 0 + .ra: x30
STACK CFI b668 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b674 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b680 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b6d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b6d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI b7b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b7c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b850 1c .cfa: sp 0 + .ra: x30
STACK CFI b858 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b864 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b870 1c .cfa: sp 0 + .ra: x30
STACK CFI b878 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b884 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b890 d4 .cfa: sp 0 + .ra: x30
STACK CFI b8a4 .cfa: sp 80 +
STACK CFI b8b8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b8cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b8d8 x21: .cfa -16 + ^
STACK CFI b958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b960 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT b964 72c .cfa: sp 0 + .ra: x30
STACK CFI b96c .cfa: sp 208 +
STACK CFI b978 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI b980 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI b988 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI b994 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI ba24 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI ba30 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI bb28 x19: x19 x20: x20
STACK CFI bb2c x27: x27 x28: x28
STACK CFI bb68 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI bb70 .cfa: sp 208 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI bb80 x19: x19 x20: x20
STACK CFI bb88 x27: x27 x28: x28
STACK CFI bb8c x19: .cfa -128 + ^ x20: .cfa -120 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI bf10 x19: x19 x20: x20
STACK CFI bf18 x27: x27 x28: x28
STACK CFI bf1c x19: .cfa -128 + ^ x20: .cfa -120 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI bf20 x19: x19 x20: x20
STACK CFI bf24 x27: x27 x28: x28
STACK CFI bf30 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI bfac x19: x19 x20: x20
STACK CFI bfb0 x27: x27 x28: x28
STACK CFI bfb4 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI c07c x19: x19 x20: x20 x27: x27 x28: x28
STACK CFI c080 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI c084 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT c090 f4 .cfa: sp 0 + .ra: x30
STACK CFI c098 .cfa: sp 96 +
STACK CFI c0a4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c0ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c0b8 x21: .cfa -16 + ^
STACK CFI c14c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c154 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT c184 370 .cfa: sp 0 + .ra: x30
STACK CFI c18c .cfa: sp 224 +
STACK CFI c198 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c1a4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c1b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c1bc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c1cc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c3c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c3cc .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT c4f4 d8 .cfa: sp 0 + .ra: x30
STACK CFI c4fc .cfa: sp 160 +
STACK CFI c508 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c510 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c51c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c52c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c538 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c540 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c5c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c5c8 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT c5d0 5f0 .cfa: sp 0 + .ra: x30
STACK CFI c5d8 .cfa: sp 176 +
STACK CFI c5dc .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI c624 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI c630 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI c63c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI c650 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI c760 x19: x19 x20: x20
STACK CFI c764 x21: x21 x22: x22
STACK CFI c768 x23: x23 x24: x24
STACK CFI c76c x25: x25 x26: x26
STACK CFI c794 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c79c .cfa: sp 176 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI c874 x19: x19 x20: x20
STACK CFI c87c x21: x21 x22: x22
STACK CFI c880 x23: x23 x24: x24
STACK CFI c884 x25: x25 x26: x26
STACK CFI c888 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI cba4 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI cba8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI cbac x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI cbb0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI cbb4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI INIT cbc0 5c4 .cfa: sp 0 + .ra: x30
STACK CFI cbc8 .cfa: sp 128 +
STACK CFI cbd4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI cbdc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI cbe4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI cbf0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI cc80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI cc88 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI cd14 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI cd94 x25: x25 x26: x26
STACK CFI cdc4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ce7c x27: x27 x28: x28
STACK CFI ce84 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ceb4 x25: x25 x26: x26
STACK CFI ceb8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI cee0 x25: x25 x26: x26
STACK CFI cee4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI cf38 x25: x25 x26: x26
STACK CFI cf44 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI cf74 x25: x25 x26: x26
STACK CFI cf80 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d03c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d0d8 x25: x25 x26: x26
STACK CFI d104 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d120 x25: x25 x26: x26
STACK CFI d124 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d128 x25: x25 x26: x26
STACK CFI d178 x27: x27 x28: x28
STACK CFI d17c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d180 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT d184 3c0 .cfa: sp 0 + .ra: x30
STACK CFI d18c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d194 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d19c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d200 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d208 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI d3b0 x23: .cfa -16 + ^
STACK CFI d3d8 x23: x23
STACK CFI d3e0 x23: .cfa -16 + ^
STACK CFI d3e4 x23: x23
STACK CFI INIT d544 dc .cfa: sp 0 + .ra: x30
STACK CFI d54c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d554 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI d590 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d598 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI d618 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d620 e0 .cfa: sp 0 + .ra: x30
STACK CFI d628 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d630 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d63c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI d6a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d6b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI d6f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT d700 108 .cfa: sp 0 + .ra: x30
STACK CFI d708 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d718 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d720 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI d798 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI d7a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT d810 118 .cfa: sp 0 + .ra: x30
STACK CFI d818 .cfa: sp 80 +
STACK CFI d824 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d82c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d834 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d840 x23: .cfa -16 + ^
STACK CFI d8f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI d8fc .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT d930 a4 .cfa: sp 0 + .ra: x30
STACK CFI d938 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d940 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d94c x21: .cfa -16 + ^
STACK CFI d9a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI d9a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI d9cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT d9d4 344 .cfa: sp 0 + .ra: x30
STACK CFI d9dc .cfa: sp 160 +
STACK CFI d9e8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d9f0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d9f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI da04 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI da7c x25: .cfa -16 + ^
STACK CFI dae0 x25: x25
STACK CFI db30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI db38 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI db64 x25: .cfa -16 + ^
STACK CFI db7c x25: x25
STACK CFI db8c x25: .cfa -16 + ^
STACK CFI dbdc x25: x25
STACK CFI dbe0 x25: .cfa -16 + ^
STACK CFI dc2c x25: x25
STACK CFI dc8c x25: .cfa -16 + ^
STACK CFI dd10 x25: x25
STACK CFI dd14 x25: .cfa -16 + ^
STACK CFI INIT dd20 110 .cfa: sp 0 + .ra: x30
STACK CFI dd28 .cfa: sp 80 +
STACK CFI dd34 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI dd3c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI dd44 x21: .cfa -16 + ^
STACK CFI dde4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ddec .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT de30 270 .cfa: sp 0 + .ra: x30
STACK CFI de38 .cfa: sp 144 +
STACK CFI de44 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI de4c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI de54 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI de60 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI def4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI defc .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI df18 x25: .cfa -16 + ^
STACK CFI df58 x25: x25
STACK CFI df7c x25: .cfa -16 + ^
STACK CFI dfec x25: x25
STACK CFI dffc x25: .cfa -16 + ^
STACK CFI e054 x25: x25
STACK CFI e058 x25: .cfa -16 + ^
STACK CFI e098 x25: x25
STACK CFI e09c x25: .cfa -16 + ^
STACK CFI INIT e0a0 1dc .cfa: sp 0 + .ra: x30
STACK CFI e0a8 .cfa: sp 112 +
STACK CFI e0b4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e0bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI e0c4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e0d0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e0dc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI e140 x27: .cfa -16 + ^
STACK CFI e1e0 x27: x27
STACK CFI e238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI e240 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI e24c x27: .cfa -16 + ^
STACK CFI e254 x27: x27
STACK CFI e25c x27: .cfa -16 + ^
STACK CFI e264 x27: x27
STACK CFI e26c x27: .cfa -16 + ^
STACK CFI e270 x27: x27
STACK CFI e278 x27: .cfa -16 + ^
