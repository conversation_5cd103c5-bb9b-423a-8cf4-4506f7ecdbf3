MODULE Linux arm64 23FCAFCB08AB33FE00DF8631186D75970 libpixbufloader-webp.so
INFO CODE_ID CBAFFC23AB08FE3300DF8631186D75970E917510
PUBLIC 2900 0 add_icc_data
PUBLIC 3000 0 fill_vtable
PUBLIC 3060 0 fill_info
PUBLIC 30c0 0 gdk_webp_animation_get_type
PUBLIC 3130 0 gdk_webp_animation_new_from_bytes
PUBLIC 3574 0 gdk_webp_animation_iter_get_type
PUBLIC 35e4 0 gdk_webp_animation_new_from_buffer_and_time
STACK CFI INIT 1ee0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f10 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f50 48 .cfa: sp 0 + .ra: x30
STACK CFI 1f54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f5c x19: .cfa -16 + ^
STACK CFI 1f94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1fa0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1fb0 28 .cfa: sp 0 + .ra: x30
STACK CFI 1fbc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1fc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1fe0 28 .cfa: sp 0 + .ra: x30
STACK CFI 1fec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ff8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2010 40 .cfa: sp 0 + .ra: x30
STACK CFI 2020 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2048 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2050 2c .cfa: sp 0 + .ra: x30
STACK CFI 205c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2074 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2080 28 .cfa: sp 0 + .ra: x30
STACK CFI 208c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2098 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 20b0 38 .cfa: sp 0 + .ra: x30
STACK CFI 20bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 20c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 20f0 38 .cfa: sp 0 + .ra: x30
STACK CFI 20fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2108 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2130 1c .cfa: sp 0 + .ra: x30
STACK CFI 2138 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2144 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2150 134 .cfa: sp 0 + .ra: x30
STACK CFI 2160 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2238 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2244 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2248 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2254 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2284 58 .cfa: sp 0 + .ra: x30
STACK CFI 228c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2294 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 22a0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 22d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 22e0 34 .cfa: sp 0 + .ra: x30
STACK CFI 22f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2308 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2314 194 .cfa: sp 0 + .ra: x30
STACK CFI 231c .cfa: sp 128 +
STACK CFI 2328 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2330 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 233c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 239c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 23a4 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 23a8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 23b4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 242c x23: x23 x24: x24
STACK CFI 2430 x25: x25 x26: x26
STACK CFI 243c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2440 x23: x23 x24: x24
STACK CFI 2448 x25: x25 x26: x26
STACK CFI 244c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 246c x23: x23 x24: x24
STACK CFI 2470 x25: x25 x26: x26
STACK CFI 2474 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2494 x23: x23 x24: x24
STACK CFI 2498 x25: x25 x26: x26
STACK CFI 24a0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 24a4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 24b0 70 .cfa: sp 0 + .ra: x30
STACK CFI 24b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 24c0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 24cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2520 1c .cfa: sp 0 + .ra: x30
STACK CFI 2528 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2530 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2540 3c .cfa: sp 0 + .ra: x30
STACK CFI 2548 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2554 x19: .cfa -16 + ^
STACK CFI 2574 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2580 a0 .cfa: sp 0 + .ra: x30
STACK CFI 2588 .cfa: sp 80 +
STACK CFI 259c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 25b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2614 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 261c .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2620 88 .cfa: sp 0 + .ra: x30
STACK CFI 2628 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2630 x19: .cfa -16 + ^
STACK CFI 2690 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2698 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 26b0 88 .cfa: sp 0 + .ra: x30
STACK CFI 26b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 26c0 x19: .cfa -16 + ^
STACK CFI 2720 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2728 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2740 78 .cfa: sp 0 + .ra: x30
STACK CFI 2748 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2750 x19: .cfa -16 + ^
STACK CFI 27b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 27c0 64 .cfa: sp 0 + .ra: x30
STACK CFI 27c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 27d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 27dc x21: .cfa -16 + ^
STACK CFI 2814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2824 78 .cfa: sp 0 + .ra: x30
STACK CFI 282c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2834 x19: .cfa -16 + ^
STACK CFI 2894 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 28a0 5c .cfa: sp 0 + .ra: x30
STACK CFI 28ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 28c0 x19: .cfa -16 + ^
STACK CFI 28f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2900 138 .cfa: sp 0 + .ra: x30
STACK CFI 2908 .cfa: sp 80 +
STACK CFI 2914 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 291c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2924 x21: .cfa -16 + ^
STACK CFI 29d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 29e0 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2a40 544 .cfa: sp 0 + .ra: x30
STACK CFI 2a48 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2a58 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2a64 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2a74 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2a80 .cfa: sp 656 + x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2abc x28: .cfa -8 + ^
STACK CFI 2ad0 x27: .cfa -16 + ^
STACK CFI 2b8c x27: x27
STACK CFI 2b90 x28: x28
STACK CFI 2bb0 .cfa: sp 96 +
STACK CFI 2bc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2bd0 .cfa: sp 656 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2bd4 x27: x27
STACK CFI 2bd8 x28: x28
STACK CFI 2bfc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2c20 x27: x27
STACK CFI 2c24 x28: x28
STACK CFI 2c28 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2da0 x27: x27
STACK CFI 2da4 x28: x28
STACK CFI 2da8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2eb4 x27: x27
STACK CFI 2eb8 x28: x28
STACK CFI 2ebc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2ef8 x27: x27
STACK CFI 2efc x28: x28
STACK CFI 2f00 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2f04 x27: x27
STACK CFI 2f0c x28: x28
STACK CFI 2f10 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2f6c x27: x27
STACK CFI 2f74 x28: x28
STACK CFI 2f7c x27: .cfa -16 + ^
STACK CFI 2f80 x28: .cfa -8 + ^
STACK CFI INIT 2f84 34 .cfa: sp 0 + .ra: x30
STACK CFI 2f8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2fc0 38 .cfa: sp 0 + .ra: x30
STACK CFI 2fc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2fd4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3000 58 .cfa: sp 0 + .ra: x30
STACK CFI 3008 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 301c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3060 58 .cfa: sp 0 + .ra: x30
STACK CFI 3068 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 307c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 30c0 70 .cfa: sp 0 + .ra: x30
STACK CFI 30c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 30f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 30fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3130 e0 .cfa: sp 0 + .ra: x30
STACK CFI 3138 .cfa: sp 80 +
STACK CFI 3148 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3150 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 31e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 31e8 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3210 364 .cfa: sp 0 + .ra: x30
STACK CFI 3218 .cfa: sp 368 +
STACK CFI 3224 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 322c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3250 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3278 x25: .cfa -16 + ^
STACK CFI 32e8 x21: x21 x22: x22
STACK CFI 32ec x25: x25
STACK CFI 32f0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI 3314 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 33d0 x21: x21 x22: x22
STACK CFI 33d8 x23: x23 x24: x24
STACK CFI 33dc x25: x25
STACK CFI 3428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3430 .cfa: sp 368 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 3434 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3488 x21: x21 x22: x22
STACK CFI 348c x23: x23 x24: x24
STACK CFI 3490 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3494 x21: x21 x22: x22
STACK CFI 3498 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 349c x21: x21 x22: x22
STACK CFI 34a0 x23: x23 x24: x24
STACK CFI 34a4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 34c8 x21: x21 x22: x22
STACK CFI 34cc x23: x23 x24: x24
STACK CFI 34d0 x25: x25
STACK CFI 34d4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI 34e4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3530 x25: x25
STACK CFI 3554 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 3558 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 355c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3560 x25: .cfa -16 + ^
STACK CFI 3568 x23: x23 x24: x24
STACK CFI 3570 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 3574 70 .cfa: sp 0 + .ra: x30
STACK CFI 357c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3584 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 35a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 35b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 35dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 35e4 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 35ec .cfa: sp 272 +
STACK CFI 35f8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3604 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3610 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 366c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3670 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3674 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3794 x23: x23 x24: x24
STACK CFI 3798 x25: x25 x26: x26
STACK CFI 379c x27: x27 x28: x28
STACK CFI 37cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 37d4 .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 37fc x23: x23 x24: x24
STACK CFI 3804 x25: x25 x26: x26
STACK CFI 3808 x27: x27 x28: x28
STACK CFI 385c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3868 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3890 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3894 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3898 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 38a0 9c .cfa: sp 0 + .ra: x30
STACK CFI 38a8 .cfa: sp 32 +
STACK CFI 38bc .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3908 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3910 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3940 70 .cfa: sp 0 + .ra: x30
STACK CFI 3950 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 395c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 397c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 39a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
