MODULE Linux arm64 C798E9778EF0771E8CBC6F4E9854A78D0 libopencv_aruco.so.4.3
INFO CODE_ID 77E998C7F08E1E778CBC6F4E9854A78D0EA97B4E
PUBLIC 7478 0 _init
PUBLIC 7ea0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.30]
PUBLIC 7f40 0 call_weak_fn
PUBLIC 7f58 0 deregister_tm_clones
PUBLIC 7f90 0 register_tm_clones
PUBLIC 7fd0 0 __do_global_dtors_aux
PUBLIC 8018 0 frame_dummy
PUBLIC 8050 0 cv::aruco::err_compare_descending(void const*, void const*)
PUBLIC 8068 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.76]
PUBLIC 8148 0 cv::aruco::fit_line(cv::aruco::line_fit_pt*, int, int, int, double*, double*, double*) [clone .constprop.133]
PUBLIC 8460 0 cv::aruco::ptsort_(cv::aruco::pt*, int)
PUBLIC 93a8 0 cv::Mat::~Mat()
PUBLIC 9438 0 cv::aruco::fit_line(cv::aruco::line_fit_pt*, int, int, int, double*, double*, double*)
PUBLIC 9770 0 cv::aruco::quad_segment_maxima(cv::Ptr<cv::aruco::DetectorParameters> const&, int, cv::aruco::line_fit_pt*, int*)
PUBLIC a260 0 cv::aruco::fit_quad(cv::Ptr<cv::aruco::DetectorParameters> const&, cv::Mat, cv::aruco::zarray*, cv::aruco::sQuad*)
PUBLIC b200 0 cv::aruco::threshold(cv::Mat, cv::Ptr<cv::aruco::DetectorParameters> const&, cv::Mat&)
PUBLIC c0c0 0 void std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >::_M_emplace_back_aux<cv::Point_<int> const&>(cv::Point_<int> const&)
PUBLIC c1c0 0 void std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >::_M_emplace_back_aux<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > const&>(std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > const&)
PUBLIC c400 0 cv::aruco::apriltag_quad_thresh(cv::Ptr<cv::aruco::DetectorParameters> const&, cv::Mat const&, std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >&)
PUBLIC de80 0 std::_Sp_counted_ptr_inplace<cv::aruco::GridBoard, std::allocator<cv::aruco::GridBoard>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC de88 0 std::_Sp_counted_ptr_inplace<cv::aruco::Dictionary, std::allocator<cv::aruco::Dictionary>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC de90 0 std::_Sp_counted_ptr_inplace<cv::aruco::Board, std::allocator<cv::aruco::Board>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC de98 0 std::_Sp_counted_ptr_inplace<cv::aruco::DetectorParameters, std::allocator<cv::aruco::DetectorParameters>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC dea0 0 std::_Sp_counted_ptr_inplace<cv::aruco::DetectorParameters, std::allocator<cv::aruco::DetectorParameters>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC dea8 0 std::_Sp_counted_ptr_inplace<cv::aruco::DetectorParameters, std::allocator<cv::aruco::DetectorParameters>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC def8 0 std::_Sp_counted_ptr_inplace<cv::aruco::Board, std::allocator<cv::aruco::Board>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC df48 0 std::_Sp_counted_ptr_inplace<cv::aruco::Dictionary, std::allocator<cv::aruco::Dictionary>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC df98 0 std::_Sp_counted_ptr_inplace<cv::aruco::GridBoard, std::allocator<cv::aruco::GridBoard>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC dfe8 0 std::_Sp_counted_ptr_inplace<cv::aruco::DetectorParameters, std::allocator<cv::aruco::DetectorParameters>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC dff0 0 std::_Sp_counted_ptr_inplace<cv::aruco::Board, std::allocator<cv::aruco::Board>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC dff8 0 std::_Sp_counted_ptr_inplace<cv::aruco::Dictionary, std::allocator<cv::aruco::Dictionary>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC e000 0 std::_Sp_counted_ptr_inplace<cv::aruco::GridBoard, std::allocator<cv::aruco::GridBoard>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC e008 0 std::_Sp_counted_ptr_inplace<cv::aruco::GridBoard, std::allocator<cv::aruco::GridBoard>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC e010 0 std::_Sp_counted_ptr_inplace<cv::aruco::Dictionary, std::allocator<cv::aruco::Dictionary>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC e018 0 std::_Sp_counted_ptr_inplace<cv::aruco::Board, std::allocator<cv::aruco::Board>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC e020 0 std::_Sp_counted_ptr_inplace<cv::aruco::DetectorParameters, std::allocator<cv::aruco::DetectorParameters>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC e028 0 cv::ParallelLoopBodyLambdaWrapper::~ParallelLoopBodyLambdaWrapper()
PUBLIC e060 0 cv::ParallelLoopBodyLambdaWrapper::operator()(cv::Range const&) const
PUBLIC e080 0 std::_Function_base::_Base_manager<cv::aruco::_identifyCandidates(cv::_InputArray const&, std::vector<std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >, std::allocator<std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > > > >&, std::vector<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >, std::allocator<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > > > >&, cv::Ptr<cv::aruco::Dictionary> const&, std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >&, std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >&, std::vector<int, std::allocator<int> >&, cv::Ptr<cv::aruco::DetectorParameters> const&, cv::_OutputArray const&)::{lambda(cv::Range const&)#1}>::_M_manager(std::_Any_data&, std::_Function_base::_Base_manager<cv::aruco::_identifyCandidates(cv::_InputArray const&, std::vector<std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >, std::allocator<std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > > > >&, std::vector<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >, std::allocator<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > > > >&, cv::Ptr<cv::aruco::Dictionary> const&, std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >&, std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >&, std::vector<int, std::allocator<int> >&, cv::Ptr<cv::aruco::DetectorParameters> const&, cv::_OutputArray const&)::{lambda(cv::Range const&)#1}> const&, std::_Manager_operation)
PUBLIC e128 0 std::_Function_base::_Base_manager<cv::aruco::estimatePoseSingleMarkers(cv::_InputArray const&, float, cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)::{lambda(cv::Range const&)#1}>::_M_manager(std::_Any_data&, std::_Function_base::_Base_manager<cv::aruco::estimatePoseSingleMarkers(cv::_InputArray const&, float, cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)::{lambda(cv::Range const&)#1}> const&, std::_Manager_operation)
PUBLIC e1c8 0 std::_Function_base::_Base_manager<cv::aruco::_detectInitialCandidates(cv::Mat const&, std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >&, std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >&, cv::Ptr<cv::aruco::DetectorParameters> const&)::{lambda(cv::Range const&)#1}>::_M_manager(std::_Any_data&, std::_Function_base::_Base_manager<cv::aruco::_detectInitialCandidates(cv::Mat const&, std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >&, std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >&, cv::Ptr<cv::aruco::DetectorParameters> const&)::{lambda(cv::Range const&)#1}> const&, std::_Manager_operation)
PUBLIC e260 0 std::_Function_base::_Base_manager<cv::aruco::detectMarkers(cv::_InputArray const&, cv::Ptr<cv::aruco::Dictionary> const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::Ptr<cv::aruco::DetectorParameters> const&, cv::_OutputArray const&, cv::_InputArray const&, cv::_InputArray const&)::{lambda(cv::Range const&)#1}>::_M_manager(std::_Any_data&, std::_Function_base::_Base_manager<cv::aruco::detectMarkers(cv::_InputArray const&, cv::Ptr<cv::aruco::Dictionary> const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::Ptr<cv::aruco::DetectorParameters> const&, cv::_OutputArray const&, cv::_InputArray const&, cv::_InputArray const&)::{lambda(cv::Range const&)#1}> const&, std::_Manager_operation)
PUBLIC e2f8 0 std::_Function_base::_Base_manager<cv::aruco::detectMarkers(cv::_InputArray const&, cv::Ptr<cv::aruco::Dictionary> const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::Ptr<cv::aruco::DetectorParameters> const&, cv::_OutputArray const&, cv::_InputArray const&, cv::_InputArray const&)::{lambda(cv::Range const&)#2}>::_M_manager(std::_Any_data&, std::_Function_base::_Base_manager<cv::aruco::detectMarkers(cv::_InputArray const&, cv::Ptr<cv::aruco::Dictionary> const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::Ptr<cv::aruco::DetectorParameters> const&, cv::_OutputArray const&, cv::_InputArray const&, cv::_InputArray const&)::{lambda(cv::Range const&)#2}> const&, std::_Manager_operation)
PUBLIC e390 0 cv::aruco::_getCrossPoint(cv::Point3_<float>, cv::Point3_<float>)
PUBLIC e400 0 void std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::_M_construct<char const*>(char const*, char const*, std::forward_iterator_tag) [clone .isra.285]
PUBLIC e4c8 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.287]
PUBLIC e508 0 cv::aruco::_getBorderErrors(cv::Mat const&, int, int)
PUBLIC e6a0 0 cv::Mat::Mat<int>(std::vector<int, std::allocator<int> > const&, bool) [clone .constprop.514]
PUBLIC e710 0 float cv::normL2Sqr<float, float>(float const*, int) [clone .constprop.527]
PUBLIC e728 0 cv::aruco::_convertToGrey(cv::_InputArray const&, cv::_OutputArray const&) [clone .constprop.530]
PUBLIC e800 0 cv::ParallelLoopBodyLambdaWrapper::~ParallelLoopBodyLambdaWrapper()
PUBLIC e840 0 std::_Sp_counted_ptr_inplace<cv::aruco::Board, std::allocator<cv::aruco::Board>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC e950 0 std::_Sp_counted_ptr_inplace<cv::aruco::GridBoard, std::allocator<cv::aruco::GridBoard>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC ea60 0 cv::Mat::Mat(cv::Mat const&)
PUBLIC eae0 0 cv::_InputArray::getMat(int) const
PUBLIC eb48 0 cv::_InputArray::getMat(int) const [clone .constprop.532]
PUBLIC eba8 0 std::_Function_handler<void (cv::Range const&), cv::aruco::estimatePoseSingleMarkers(cv::_InputArray const&, float, cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)::{lambda(cv::Range const&)#1}>::_M_invoke(std::_Any_data const&, cv::Range const&)
PUBLIC eed8 0 std::_Function_handler<void (cv::Range const&), cv::aruco::detectMarkers(cv::_InputArray const&, cv::Ptr<cv::aruco::Dictionary> const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::Ptr<cv::aruco::DetectorParameters> const&, cv::_OutputArray const&, cv::_InputArray const&, cv::_InputArray const&)::{lambda(cv::Range const&)#1}>::_M_invoke(std::_Any_data const&, cv::Range const&)
PUBLIC f100 0 std::_Sp_counted_ptr_inplace<cv::aruco::Dictionary, std::allocator<cv::aruco::Dictionary>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC f110 0 cv::aruco::_extractBits(cv::_InputArray const&, cv::_InputArray const&, int, int, int, double, double) [clone .constprop.520]
PUBLIC f840 0 cv::Mat::operator=(cv::Mat&&)
PUBLIC f970 0 cv::aruco::DetectorParameters::DetectorParameters()
PUBLIC fa48 0 cv::aruco::DetectorParameters::create()
PUBLIC fac0 0 cv::MatExpr::~MatExpr()
PUBLIC faf0 0 std::_Function_handler<void (cv::Range const&), cv::aruco::detectMarkers(cv::_InputArray const&, cv::Ptr<cv::aruco::Dictionary> const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::Ptr<cv::aruco::DetectorParameters> const&, cv::_OutputArray const&, cv::_InputArray const&, cv::_InputArray const&)::{lambda(cv::Range const&)#2}>::_M_invoke(std::_Any_data const&, cv::Range const&)
PUBLIC 110a0 0 cv::aruco::_copyVector2Output(std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >&, cv::_OutputArray const&)
PUBLIC 11760 0 cv::aruco::_identifyOneCandidate(cv::Ptr<cv::aruco::Dictionary> const&, cv::_InputArray const&, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >&, int&, cv::Ptr<cv::aruco::DetectorParameters> const&, int&) [clone .constprop.519]
PUBLIC 11be0 0 std::_Function_handler<void (cv::Range const&), cv::aruco::_identifyCandidates(cv::_InputArray const&, std::vector<std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >, std::allocator<std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > > > >&, std::vector<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >, std::allocator<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > > > >&, cv::Ptr<cv::aruco::Dictionary> const&, std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >&, std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >&, std::vector<int, std::allocator<int> >&, cv::Ptr<cv::aruco::DetectorParameters> const&, cv::_OutputArray const&)::{lambda(cv::Range const&)#1}>::_M_invoke(std::_Any_data const&, cv::Range const&)
PUBLIC 11d10 0 cv::aruco::getBoardObjectAndImagePoints(cv::Ptr<cv::aruco::Board> const&, cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC 12460 0 cv::aruco::estimatePoseBoard(cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::aruco::Board> const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&, bool)
PUBLIC 12790 0 cv::aruco::drawDetectedMarkers(cv::_InputOutputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Scalar_<double>)
PUBLIC 131c8 0 cv::aruco::drawAxis(cv::_InputOutputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, float)
PUBLIC 131d0 0 cv::aruco::drawMarker(cv::Ptr<cv::aruco::Dictionary> const&, int, int, cv::_OutputArray const&, int)
PUBLIC 131e0 0 cv::aruco::_drawPlanarBoardImpl(cv::aruco::Board*, cv::Size_<int>, cv::_OutputArray const&, int, int)
PUBLIC 13860 0 cv::aruco::GridBoard::draw(cv::Size_<int>, cv::_OutputArray const&, int, int)
PUBLIC 13880 0 cv::aruco::drawPlanarBoard(cv::Ptr<cv::aruco::Board> const&, cv::Size_<int>, cv::_OutputArray const&, int, int)
PUBLIC 138a0 0 std::function<void (cv::Range const&)>::function(std::function<void (cv::Range const&)> const&)
PUBLIC 13900 0 cv::parallel_for_(cv::Range const&, std::function<void (cv::Range const&)>, double)
PUBLIC 139f0 0 cv::aruco::estimatePoseSingleMarkers(cv::_InputArray const&, float, cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC 13cf0 0 std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >::~vector()
PUBLIC 13d50 0 std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >::~vector()
PUBLIC 13db0 0 std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >::vector(std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > const&)
PUBLIC 13e50 0 std::vector<std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >, std::allocator<std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > > > >::~vector()
PUBLIC 13ee0 0 std::vector<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >, std::allocator<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > > > >::~vector()
PUBLIC 13f70 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::~vector()
PUBLIC 13fc8 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 14080 0 void std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >::_M_emplace_back_aux<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > const&>(std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > const&)
PUBLIC 14240 0 __gnu_cxx::__normal_iterator<cv::Point_<float>*, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > std::_V2::__rotate<__gnu_cxx::__normal_iterator<cv::Point_<float>*, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >(__gnu_cxx::__normal_iterator<cv::Point_<float>*, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > >, __gnu_cxx::__normal_iterator<cv::Point_<float>*, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > >, __gnu_cxx::__normal_iterator<cv::Point_<float>*, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > >, std::random_access_iterator_tag)
PUBLIC 14398 0 void std::vector<unsigned int, std::allocator<unsigned int> >::_M_emplace_back_aux<unsigned int const&>(unsigned int const&)
PUBLIC 14480 0 void std::vector<std::vector<unsigned int, std::allocator<unsigned int> >, std::allocator<std::vector<unsigned int, std::allocator<unsigned int> > > >::_M_emplace_back_aux<std::vector<unsigned int, std::allocator<unsigned int> > const&>(std::vector<unsigned int, std::allocator<unsigned int> > const&)
PUBLIC 146a8 0 void std::vector<int, std::allocator<int> >::_M_emplace_back_aux<int const&>(int const&)
PUBLIC 14790 0 std::vector<int, std::allocator<int> >::push_back(int const&)
PUBLIC 147c0 0 void std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >::_M_emplace_back_aux<cv::Point_<float> const&>(cv::Point_<float> const&)
PUBLIC 148c0 0 std::vector<int, std::allocator<int> >::_M_default_append(unsigned long)
PUBLIC 14a10 0 std::vector<int, std::allocator<int> >::_M_fill_insert(__gnu_cxx::__normal_iterator<int*, std::vector<int, std::allocator<int> > >, unsigned long, int const&)
PUBLIC 15080 0 void std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >::_M_emplace_back_aux<cv::Point_<float> >(cv::Point_<float>&&)
PUBLIC 15180 0 void std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >::emplace_back<cv::Point_<float> >(cv::Point_<float>&&)
PUBLIC 151b8 0 std::_Bvector_base<std::allocator<bool> >::_M_deallocate()
PUBLIC 151d0 0 std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >::vector(std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > const&)
PUBLIC 15270 0 std::_Function_handler<void (cv::Range const&), cv::aruco::_detectInitialCandidates(cv::Mat const&, std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >&, std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >&, cv::Ptr<cv::aruco::DetectorParameters> const&)::{lambda(cv::Range const&)#1}>::_M_invoke(std::_Any_data const&, cv::Range const&)
PUBLIC 162e8 0 std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > >::vector(std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > > const&)
PUBLIC 163c8 0 void std::vector<std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > >, std::allocator<std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > > > >::_M_emplace_back_aux<std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > > const&>(std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > > const&)
PUBLIC 16588 0 cv::aruco::GridBoard::create(int, int, float, float, cv::Ptr<cv::aruco::Dictionary> const&, int)
PUBLIC 16b10 0 std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >* std::__uninitialized_copy<false>::__uninit_copy<__gnu_cxx::__normal_iterator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > const*, std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > > >, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >*>(__gnu_cxx::__normal_iterator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > const*, std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > > >, __gnu_cxx::__normal_iterator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > const*, std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > > >, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >*)
PUBLIC 16c28 0 void std::vector<std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >, std::allocator<std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > > > >::_M_emplace_back_aux<std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > > const&>(std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > > const&)
PUBLIC 16e50 0 std::vector<std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >, std::allocator<std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > > > >::push_back(std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > > const&)
PUBLIC 17000 0 std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >* std::__uninitialized_copy<false>::__uninit_copy<__gnu_cxx::__normal_iterator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > const*, std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > > >, std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >*>(__gnu_cxx::__normal_iterator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > const*, std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > > >, __gnu_cxx::__normal_iterator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > const*, std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > > >, std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >*)
PUBLIC 17118 0 void std::vector<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >, std::allocator<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > > > >::_M_emplace_back_aux<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > > const&>(std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > > const&)
PUBLIC 17340 0 std::vector<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >, std::allocator<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > > > >::push_back(std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > > const&)
PUBLIC 174f0 0 cv::Mat* std::__uninitialized_copy<false>::__uninit_copy<cv::Mat const*, cv::Mat*>(cv::Mat const*, cv::Mat const*, cv::Mat*)
PUBLIC 17628 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_default_append(unsigned long)
PUBLIC 17840 0 void std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_emplace_back_aux<cv::Mat const&>(cv::Mat const&)
PUBLIC 17990 0 void std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_emplace_back_aux<cv::Mat>(cv::Mat&&)
PUBLIC 17b60 0 cv::aruco::calibrateCameraAruco(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::aruco::Board> const&, cv::Size_<int>, cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, int, cv::TermCriteria)
PUBLIC 183e0 0 cv::aruco::calibrateCameraAruco(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::aruco::Board> const&, cv::Size_<int>, cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, int, cv::TermCriteria)
PUBLIC 184a0 0 std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >* std::__uninitialized_copy<false>::__uninit_copy<std::move_iterator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >*>, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >*>(std::move_iterator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >*>, std::move_iterator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >*>, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >*)
PUBLIC 18538 0 void std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >::emplace_back<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > >(std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >&&)
PUBLIC 186a8 0 std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >::_M_default_append(unsigned long)
PUBLIC 18828 0 std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >::operator=(std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > const&)
PUBLIC 18a38 0 std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > >::operator=(std::vector<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >, std::allocator<std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > > > > const&)
PUBLIC 18ce0 0 cv::aruco::refineDetectedMarkers(cv::_InputArray const&, cv::Ptr<cv::aruco::Board> const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_InputArray const&, cv::_InputArray const&, float, float, bool, cv::_OutputArray const&, cv::Ptr<cv::aruco::DetectorParameters> const&)
PUBLIC 1a9b8 0 std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >::operator=(std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > const&)
PUBLIC 1abc8 0 std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >::operator=(std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > > const&)
PUBLIC 1ae70 0 cv::aruco::detectMarkers(cv::_InputArray const&, cv::Ptr<cv::aruco::Dictionary> const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::Ptr<cv::aruco::DetectorParameters> const&, cv::_OutputArray const&, cv::_InputArray const&, cv::_InputArray const&)
PUBLIC 1d510 0 std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > >::operator=(std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > > const&)
PUBLIC 1d850 0 std::vector<std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > >, std::allocator<std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > > > >::operator=(std::vector<std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > >, std::allocator<std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > > > > const&)
PUBLIC 1db90 0 cv::aruco::Board::create(cv::_InputArray const&, cv::Ptr<cv::aruco::Dictionary> const&, cv::_InputArray const&)
PUBLIC 1e2f8 0 std::_Sp_counted_ptr_inplace<cv::aruco::CharucoBoard, std::allocator<cv::aruco::CharucoBoard>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 1e300 0 std::_Sp_counted_ptr_inplace<cv::aruco::CharucoBoard, std::allocator<cv::aruco::CharucoBoard>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 1e308 0 std::_Sp_counted_ptr_inplace<cv::aruco::CharucoBoard, std::allocator<cv::aruco::CharucoBoard>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 1e310 0 std::_Sp_counted_ptr_inplace<cv::aruco::CharucoBoard, std::allocator<cv::aruco::CharucoBoard>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 1e360 0 std::_Function_base::_Base_manager<cv::aruco::_selectAndRefineChessboardCorners(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, std::vector<cv::Size_<int>, std::allocator<cv::Size_<int> > > const&)::{lambda(cv::Range const&)#1}>::_M_manager(std::_Any_data&, std::_Function_base::_Base_manager<cv::aruco::_selectAndRefineChessboardCorners(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, std::vector<cv::Size_<int>, std::allocator<cv::Size_<int> > > const&)::{lambda(cv::Range const&)#1}> const&, std::_Manager_operation)
PUBLIC 1e3f8 0 cv::_InputArray::getMat(int) const [clone .constprop.307]
PUBLIC 1e4e8 0 std::_Sp_counted_ptr_inplace<cv::aruco::CharucoBoard, std::allocator<cv::aruco::CharucoBoard>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 1e678 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.162]
PUBLIC 1e760 0 cv::aruco::CharucoBoard::draw(cv::Size_<int>, cv::_OutputArray const&, int, int)
PUBLIC 1f0e0 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC 1f128 0 cv::aruco::drawDetectedCornersCharuco(cv::_InputOutputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Scalar_<double>)
PUBLIC 1fe68 0 cv::aruco::drawDetectedDiamonds(cv::_InputOutputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Scalar_<double>)
PUBLIC 20e00 0 std::__cxx11::basic_stringbuf<char, std::char_traits<char>, std::allocator<char> >::~basic_stringbuf()
PUBLIC 20e50 0 std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > >::reserve(unsigned long)
PUBLIC 20f50 0 std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > >::_M_default_append(unsigned long)
PUBLIC 210e0 0 void std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > >::_M_emplace_back_aux<cv::Point3_<float> const&>(cv::Point3_<float> const&)
PUBLIC 21220 0 std::vector<std::vector<int, std::allocator<int> >, std::allocator<std::vector<int, std::allocator<int> > > >::_M_default_append(unsigned long)
PUBLIC 213e0 0 std::_Function_handler<void (cv::Range const&), cv::aruco::_selectAndRefineChessboardCorners(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, std::vector<cv::Size_<int>, std::allocator<cv::Size_<int> > > const&)::{lambda(cv::Range const&)#1}>::_M_invoke(std::_Any_data const&, cv::Range const&)
PUBLIC 21548 0 cv::aruco::_filterCornersWithoutMinMarkers(cv::Ptr<cv::aruco::CharucoBoard> const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, int, cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC 22350 0 void std::vector<cv::Size_<int>, std::allocator<cv::Size_<int> > >::_M_emplace_back_aux<cv::Size_<int> const&>(cv::Size_<int> const&)
PUBLIC 22450 0 std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >::_M_default_append(unsigned long)
PUBLIC 225a0 0 std::vector<std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > >, std::allocator<std::vector<cv::Point3_<float>, std::allocator<cv::Point3_<float> > > > >::_M_default_append(unsigned long)
PUBLIC 22760 0 cv::aruco::calibrateCameraCharuco(cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::aruco::CharucoBoard> const&, cv::Size_<int>, cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, int, cv::TermCriteria)
PUBLIC 23030 0 cv::aruco::calibrateCameraCharuco(cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::aruco::CharucoBoard> const&, cv::Size_<int>, cv::_InputOutputArray const&, cv::_InputOutputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, int, cv::TermCriteria)
PUBLIC 230e8 0 void std::vector<cv::Vec<int, 4>, std::allocator<cv::Vec<int, 4> > >::_M_emplace_back_aux<cv::Vec<int, 4> const&>(cv::Vec<int, 4> const&)
PUBLIC 231f8 0 void std::vector<int, std::allocator<int> >::_M_emplace_back_aux<int>(int&&)
PUBLIC 232e0 0 cv::aruco::CharucoBoard::_getNearestMarkerCorners()
PUBLIC 238f8 0 cv::aruco::CharucoBoard::create(int, int, float, float, cv::Ptr<cv::aruco::Dictionary> const&)
PUBLIC 23ec8 0 cv::aruco::drawCharucoDiamond(cv::Ptr<cv::aruco::Dictionary> const&, cv::Vec<int, 4>, int, int, cv::_OutputArray const&, int, int)
PUBLIC 240c0 0 cv::aruco::_selectAndRefineChessboardCorners(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, std::vector<cv::Size_<int>, std::allocator<cv::Size_<int> > > const&) [clone .constprop.301]
PUBLIC 24c40 0 std::vector<cv::Size_<int>, std::allocator<cv::Size_<int> > >::_M_fill_insert(__gnu_cxx::__normal_iterator<cv::Size_<int>*, std::vector<cv::Size_<int>, std::allocator<cv::Size_<int> > > >, unsigned long, cv::Size_<int> const&)
PUBLIC 24ff0 0 cv::aruco::_getMaximumSubPixWindowSizes(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::aruco::CharucoBoard> const&, std::vector<cv::Size_<int>, std::allocator<cv::Size_<int> > >&) [clone .constprop.300]
PUBLIC 25b90 0 cv::aruco::_interpolateCornersCharucoLocalHom(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::aruco::CharucoBoard> const&, cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC 26dc0 0 cv::aruco::_interpolateCornersCharucoApproxCalib(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::aruco::CharucoBoard> const&, cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC 27510 0 cv::aruco::interpolateCornersCharuco(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::aruco::CharucoBoard> const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_InputArray const&, cv::_InputArray const&, int)
PUBLIC 275d0 0 void std::vector<double, std::allocator<double> >::_M_emplace_back_aux<double>(double&&)
PUBLIC 276b8 0 cv::aruco::estimatePoseCharucoBoard(cv::_InputArray const&, cv::_InputArray const&, cv::Ptr<cv::aruco::CharucoBoard> const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputOutputArray const&, cv::_InputOutputArray const&, bool)
PUBLIC 28290 0 cv::aruco::detectCharucoDiamond(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, float, cv::_OutputArray const&, cv::_OutputArray const&, cv::_InputArray const&, cv::_InputArray const&)
PUBLIC 29de0 0 std::__shared_ptr<cv::aruco::Dictionary, (__gnu_cxx::_Lock_policy)2>::__shared_ptr<std::allocator<cv::aruco::Dictionary>, cv::aruco::Dictionary const&>(std::_Sp_make_shared_tag, std::allocator<cv::aruco::Dictionary> const&, cv::aruco::Dictionary const&) [clone .constprop.53]
PUBLIC 29f00 0 cv::aruco::Dictionary::~Dictionary()
PUBLIC 29f90 0 cv::aruco::Dictionary::Dictionary(cv::Ptr<cv::aruco::Dictionary> const&)
PUBLIC 2a200 0 cv::aruco::Dictionary::Dictionary(cv::Mat const&, int, int)
PUBLIC 2a390 0 cv::aruco::Dictionary::getByteListFromBits(cv::Mat const&)
PUBLIC 2a5a0 0 cv::aruco::Dictionary::identify(cv::Mat const&, int&, int&, double) const
PUBLIC 2a7c0 0 cv::aruco::Dictionary::getDistanceToId(cv::_InputArray const&, int, bool) const
PUBLIC 2aab0 0 cv::aruco::Dictionary::getBitsFromByteList(cv::Mat const&, int)
PUBLIC 2adb0 0 cv::aruco::Dictionary::drawMarker(int, int, cv::_OutputArray const&, int) const
PUBLIC 2b980 0 cv::aruco::getPredefinedDictionary(cv::aruco::PREDEFINED_DICTIONARY_NAME)
PUBLIC 2ded0 0 cv::aruco::getPredefinedDictionary(int)
PUBLIC 2dee8 0 cv::aruco::Dictionary::get(int)
PUBLIC 2df00 0 cv::aruco::generateCustomDictionary(int, int, cv::Ptr<cv::aruco::Dictionary> const&, int)
PUBLIC 2ece8 0 cv::aruco::Dictionary::create(int, int, cv::Ptr<cv::aruco::Dictionary> const&, int)
PUBLIC 2ed00 0 cv::aruco::Dictionary::create(int, int, int)
PUBLIC 2ef40 0 cv::aruco::generateCustomDictionary(int, int, int)
PUBLIC 2f180 0 _fini
STACK CFI INIT 8050 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8068 dc .cfa: sp 0 + .ra: x30
STACK CFI 806c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8070 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8078 .ra: .cfa -32 + ^
STACK CFI 80c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 80c8 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 810c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 8110 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8130 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 8138 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 8148 314 .cfa: sp 0 + .ra: x30
STACK CFI 814c .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 815c .ra: .cfa -88 + ^ v10: .cfa -64 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x21: .cfa -96 + ^
STACK CFI 82f0 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21
STACK CFI 82f8 .cfa: sp 112 + .ra: .cfa -88 + ^ v10: .cfa -64 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^
STACK CFI INIT 8460 f44 .cfa: sp 0 + .ra: x30
STACK CFI 8468 .cfa: sp 12384 +
STACK CFI 8474 x23: .cfa -12352 + ^ x24: .cfa -12344 + ^
STACK CFI 8484 x19: .cfa -12384 + ^ x20: .cfa -12376 + ^ x21: .cfa -12368 + ^ x22: .cfa -12360 + ^
STACK CFI 8494 .ra: .cfa -12320 + ^ x25: .cfa -12336 + ^ x26: .cfa -12328 + ^
STACK CFI 89ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 89b0 .cfa: sp 12384 + .ra: .cfa -12320 + ^ x19: .cfa -12384 + ^ x20: .cfa -12376 + ^ x21: .cfa -12368 + ^ x22: .cfa -12360 + ^ x23: .cfa -12352 + ^ x24: .cfa -12344 + ^ x25: .cfa -12336 + ^ x26: .cfa -12328 + ^
STACK CFI INIT 93a8 90 .cfa: sp 0 + .ra: x30
STACK CFI 93ac .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 9420 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 9428 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 9434 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 9438 334 .cfa: sp 0 + .ra: x30
STACK CFI 943c .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9454 .ra: .cfa -96 + ^ v10: .cfa -64 + ^ v11: .cfa -56 + ^ v12: .cfa -88 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 9604 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 9608 .cfa: sp 128 + .ra: .cfa -96 + ^ v10: .cfa -64 + ^ v11: .cfa -56 + ^ v12: .cfa -88 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI INIT 9770 ad8 .cfa: sp 0 + .ra: x30
STACK CFI 9774 .cfa: sp 544 +
STACK CFI 97a0 .ra: .cfa -464 + ^ v10: .cfa -432 + ^ v11: .cfa -424 + ^ v12: .cfa -416 + ^ v13: .cfa -408 + ^ v14: .cfa -400 + ^ v15: .cfa -392 + ^ v8: .cfa -448 + ^ v9: .cfa -440 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 97f4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 97f8 .cfa: sp 544 + .ra: .cfa -464 + ^ v10: .cfa -432 + ^ v11: .cfa -424 + ^ v12: .cfa -416 + ^ v13: .cfa -408 + ^ v14: .cfa -400 + ^ v15: .cfa -392 + ^ v8: .cfa -448 + ^ v9: .cfa -440 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI INIT a260 f4c .cfa: sp 0 + .ra: x30
STACK CFI a264 .cfa: sp 3376 +
STACK CFI a284 .ra: .cfa -3304 + ^ v10: .cfa -3280 + ^ v11: .cfa -3272 + ^ v12: .cfa -3264 + ^ v13: .cfa -3256 + ^ v8: .cfa -3296 + ^ v9: .cfa -3288 + ^ x19: .cfa -3376 + ^ x20: .cfa -3368 + ^ x21: .cfa -3360 + ^ x22: .cfa -3352 + ^ x23: .cfa -3344 + ^ x24: .cfa -3336 + ^ x25: .cfa -3328 + ^ x26: .cfa -3320 + ^ x27: .cfa -3312 + ^
STACK CFI a414 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI a418 .cfa: sp 3376 + .ra: .cfa -3304 + ^ v10: .cfa -3280 + ^ v11: .cfa -3272 + ^ v12: .cfa -3264 + ^ v13: .cfa -3256 + ^ v8: .cfa -3296 + ^ v9: .cfa -3288 + ^ x19: .cfa -3376 + ^ x20: .cfa -3368 + ^ x21: .cfa -3360 + ^ x22: .cfa -3352 + ^ x23: .cfa -3344 + ^ x24: .cfa -3336 + ^ x25: .cfa -3328 + ^ x26: .cfa -3320 + ^ x27: .cfa -3312 + ^
STACK CFI INIT b200 eac .cfa: sp 0 + .ra: x30
STACK CFI b204 .cfa: sp 304 + x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI b210 x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI b224 .ra: .cfa -224 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI bc5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI bc60 .cfa: sp 304 + .ra: .cfa -224 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI bfa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI bfa4 .cfa: sp 304 + .ra: .cfa -224 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI INIT c0c0 100 .cfa: sp 0 + .ra: x30
STACK CFI c0c4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c0cc .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI c0d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c188 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI c190 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT c1c0 23c .cfa: sp 0 + .ra: x30
STACK CFI c1c4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c1d0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c1d8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI c1e0 .ra: .cfa -16 + ^
STACK CFI c37c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI c380 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT c400 1a6c .cfa: sp 0 + .ra: x30
STACK CFI c408 .cfa: sp 592 +
STACK CFI c41c x19: .cfa -592 + ^ x20: .cfa -584 + ^ x27: .cfa -528 + ^ x28: .cfa -520 + ^
STACK CFI c434 .ra: .cfa -512 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^
STACK CFI dd08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI dd10 .cfa: sp 592 + .ra: .cfa -512 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^ x27: .cfa -528 + ^ x28: .cfa -520 + ^
STACK CFI INIT de80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT de88 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT de90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT de98 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT dea0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT dea8 50 .cfa: sp 0 + .ra: x30
STACK CFI deac .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI deb8 .ra: .cfa -16 + ^
STACK CFI def4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT def8 50 .cfa: sp 0 + .ra: x30
STACK CFI defc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI df08 .ra: .cfa -16 + ^
STACK CFI df44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT df48 50 .cfa: sp 0 + .ra: x30
STACK CFI df4c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI df58 .ra: .cfa -16 + ^
STACK CFI df94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT df98 50 .cfa: sp 0 + .ra: x30
STACK CFI df9c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI dfa8 .ra: .cfa -16 + ^
STACK CFI dfe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT dfe8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT dff0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT dff8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e000 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e008 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e010 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e018 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e020 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT e028 38 .cfa: sp 0 + .ra: x30
STACK CFI e02c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI e05c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT e060 1c .cfa: sp 0 + .ra: x30
STACK CFI e078 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI INIT e080 a8 .cfa: sp 0 + .ra: x30
STACK CFI e090 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e09c .ra: .cfa -16 + ^
STACK CFI e0bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI e0c0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e0fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT e128 a0 .cfa: sp 0 + .ra: x30
STACK CFI e138 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e144 .ra: .cfa -16 + ^
STACK CFI e164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI e168 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e19c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT e1c8 98 .cfa: sp 0 + .ra: x30
STACK CFI e1d8 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e1e4 .ra: .cfa -16 + ^
STACK CFI e204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI e208 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT e260 98 .cfa: sp 0 + .ra: x30
STACK CFI e270 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e27c .ra: .cfa -16 + ^
STACK CFI e29c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI e2a0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e2cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT e2f8 98 .cfa: sp 0 + .ra: x30
STACK CFI e308 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e314 .ra: .cfa -16 + ^
STACK CFI e334 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI e338 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT e390 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT e400 c4 .cfa: sp 0 + .ra: x30
STACK CFI e404 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e410 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI e458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI e460 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI e49c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI e4a0 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI e4c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT e4c8 40 .cfa: sp 0 + .ra: x30
STACK CFI e4cc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e4dc .ra: .cfa -16 + ^
STACK CFI e504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT e508 194 .cfa: sp 0 + .ra: x30
STACK CFI e640 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e650 .ra: .cfa -48 + ^
STACK CFI INIT e6a0 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT e710 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT e728 d4 .cfa: sp 0 + .ra: x30
STACK CFI e72c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e73c .ra: .cfa -48 + ^
STACK CFI e76c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI e770 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e7d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI e7d8 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT e800 40 .cfa: sp 0 + .ra: x30
STACK CFI e804 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI e83c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT e840 110 .cfa: sp 0 + .ra: x30
STACK CFI e844 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e848 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI e8c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI e8c8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI e908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI e910 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT e950 110 .cfa: sp 0 + .ra: x30
STACK CFI e954 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e958 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI e9d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI e9d8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI ea18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI ea20 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT ea60 80 .cfa: sp 0 + .ra: x30
STACK CFI INIT eae0 64 .cfa: sp 0 + .ra: x30
STACK CFI eae4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI eaf0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI eb20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI eb28 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI eb40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT eb48 5c .cfa: sp 0 + .ra: x30
STACK CFI eb4c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI eb58 .ra: .cfa -16 + ^
STACK CFI eb80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI eb88 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI eba0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT eba8 328 .cfa: sp 0 + .ra: x30
STACK CFI ebac .cfa: sp 352 + x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI ebc4 .ra: .cfa -272 + ^ v8: .cfa -256 + ^ v9: .cfa -248 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI ede4 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI ede8 .cfa: sp 352 + .ra: .cfa -272 + ^ v8: .cfa -256 + ^ v9: .cfa -248 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI INIT eed8 224 .cfa: sp 0 + .ra: x30
STACK CFI eedc .cfa: sp 272 + x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI eef4 .ra: .cfa -192 + ^ v8: .cfa -184 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI f058 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f060 .cfa: sp 272 + .ra: .cfa -192 + ^ v8: .cfa -184 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI INIT f100 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT f110 6f8 .cfa: sp 0 + .ra: x30
STACK CFI f114 .cfa: sp 928 +
STACK CFI f118 v8: .cfa -832 + ^ v9: .cfa -824 + ^
STACK CFI f124 x19: .cfa -928 + ^ x20: .cfa -920 + ^
STACK CFI f134 x21: .cfa -912 + ^ x22: .cfa -904 + ^ x23: .cfa -896 + ^ x24: .cfa -888 + ^
STACK CFI f14c .ra: .cfa -848 + ^ x25: .cfa -880 + ^ x26: .cfa -872 + ^ x27: .cfa -864 + ^ x28: .cfa -856 + ^
STACK CFI f534 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f538 .cfa: sp 928 + .ra: .cfa -848 + ^ v8: .cfa -832 + ^ v9: .cfa -824 + ^ x19: .cfa -928 + ^ x20: .cfa -920 + ^ x21: .cfa -912 + ^ x22: .cfa -904 + ^ x23: .cfa -896 + ^ x24: .cfa -888 + ^ x25: .cfa -880 + ^ x26: .cfa -872 + ^ x27: .cfa -864 + ^ x28: .cfa -856 + ^
STACK CFI INIT f840 120 .cfa: sp 0 + .ra: x30
STACK CFI f844 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f850 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI f93c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI f940 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT f970 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT fa48 74 .cfa: sp 0 + .ra: x30
STACK CFI fa4c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI fa58 .ra: .cfa -16 + ^
STACK CFI faa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI faa8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT fac0 24 .cfa: sp 0 + .ra: x30
STACK CFI fac4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI fae0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT faf0 158c .cfa: sp 0 + .ra: x30
STACK CFI faf4 .cfa: sp 1424 +
STACK CFI fb14 .ra: .cfa -1344 + ^ v10: .cfa -1312 + ^ v11: .cfa -1304 + ^ v8: .cfa -1328 + ^ v9: .cfa -1320 + ^ x19: .cfa -1424 + ^ x20: .cfa -1416 + ^ x21: .cfa -1408 + ^ x22: .cfa -1400 + ^ x23: .cfa -1392 + ^ x24: .cfa -1384 + ^ x25: .cfa -1376 + ^ x26: .cfa -1368 + ^ x27: .cfa -1360 + ^ x28: .cfa -1352 + ^
STACK CFI 10b00 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 10b08 .cfa: sp 1424 + .ra: .cfa -1344 + ^ v10: .cfa -1312 + ^ v11: .cfa -1304 + ^ v8: .cfa -1328 + ^ v9: .cfa -1320 + ^ x19: .cfa -1424 + ^ x20: .cfa -1416 + ^ x21: .cfa -1408 + ^ x22: .cfa -1400 + ^ x23: .cfa -1392 + ^ x24: .cfa -1384 + ^ x25: .cfa -1376 + ^ x26: .cfa -1368 + ^ x27: .cfa -1360 + ^ x28: .cfa -1352 + ^
STACK CFI INIT 110a0 6ac .cfa: sp 0 + .ra: x30
STACK CFI 110a4 .cfa: sp 816 +
STACK CFI 110bc x19: .cfa -816 + ^ x20: .cfa -808 + ^
STACK CFI 110c4 x23: .cfa -784 + ^ x24: .cfa -776 + ^
STACK CFI 110e0 .ra: .cfa -736 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^ x28: .cfa -744 + ^
STACK CFI 114c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 114c8 .cfa: sp 816 + .ra: .cfa -736 + ^ x19: .cfa -816 + ^ x20: .cfa -808 + ^ x21: .cfa -800 + ^ x22: .cfa -792 + ^ x23: .cfa -784 + ^ x24: .cfa -776 + ^ x25: .cfa -768 + ^ x26: .cfa -760 + ^ x27: .cfa -752 + ^ x28: .cfa -744 + ^
STACK CFI INIT 11760 460 .cfa: sp 0 + .ra: x30
STACK CFI 11764 .cfa: sp 1056 +
STACK CFI 11768 x21: .cfa -1040 + ^ x22: .cfa -1032 + ^
STACK CFI 11770 x23: .cfa -1024 + ^ x24: .cfa -1016 + ^
STACK CFI 11784 .ra: .cfa -976 + ^ x19: .cfa -1056 + ^ x20: .cfa -1048 + ^ x25: .cfa -1008 + ^ x26: .cfa -1000 + ^ x27: .cfa -992 + ^ x28: .cfa -984 + ^
STACK CFI 11948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 11950 .cfa: sp 1056 + .ra: .cfa -976 + ^ x19: .cfa -1056 + ^ x20: .cfa -1048 + ^ x21: .cfa -1040 + ^ x22: .cfa -1032 + ^ x23: .cfa -1024 + ^ x24: .cfa -1016 + ^ x25: .cfa -1008 + ^ x26: .cfa -1000 + ^ x27: .cfa -992 + ^ x28: .cfa -984 + ^
STACK CFI INIT 11be0 12c .cfa: sp 0 + .ra: x30
STACK CFI 11be4 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 11bfc .ra: .cfa -48 + ^ v8: .cfa -40 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 11cf8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 11d00 .cfa: sp 128 + .ra: .cfa -48 + ^ v8: .cfa -40 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT 11d10 74c .cfa: sp 0 + .ra: x30
STACK CFI 11d14 .cfa: sp 288 + x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI 11d20 x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 11d34 .ra: .cfa -208 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^
STACK CFI 11eec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 11ef0 .cfa: sp 288 + .ra: .cfa -208 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI INIT 12460 320 .cfa: sp 0 + .ra: x30
STACK CFI 12464 .cfa: sp 352 + x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 12468 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 12478 x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 1248c x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 12498 .ra: .cfa -272 + ^
STACK CFI 12630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 12638 .cfa: sp 352 + .ra: .cfa -272 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI INIT 12790 a38 .cfa: sp 0 + .ra: x30
STACK CFI 12794 .cfa: sp 960 +
STACK CFI 127a0 x21: .cfa -944 + ^ x22: .cfa -936 + ^
STACK CFI 127a8 x23: .cfa -928 + ^ x24: .cfa -920 + ^
STACK CFI 127cc .ra: .cfa -880 + ^ v10: .cfa -848 + ^ v11: .cfa -840 + ^ v12: .cfa -832 + ^ v13: .cfa -824 + ^ v14: .cfa -872 + ^ v8: .cfa -864 + ^ v9: .cfa -856 + ^ x19: .cfa -960 + ^ x20: .cfa -952 + ^ x25: .cfa -912 + ^ x26: .cfa -904 + ^ x27: .cfa -896 + ^ x28: .cfa -888 + ^
STACK CFI 12f00 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 12f08 .cfa: sp 960 + .ra: .cfa -880 + ^ v10: .cfa -848 + ^ v11: .cfa -840 + ^ v12: .cfa -832 + ^ v13: .cfa -824 + ^ v14: .cfa -872 + ^ v8: .cfa -864 + ^ v9: .cfa -856 + ^ x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^ x25: .cfa -912 + ^ x26: .cfa -904 + ^ x27: .cfa -896 + ^ x28: .cfa -888 + ^
STACK CFI INIT 131c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 131d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 131e0 654 .cfa: sp 0 + .ra: x30
STACK CFI 131e4 .cfa: sp 624 +
STACK CFI 131e8 x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI 1320c .ra: .cfa -544 + ^ v10: .cfa -512 + ^ v11: .cfa -504 + ^ v12: .cfa -496 + ^ v13: .cfa -488 + ^ v8: .cfa -528 + ^ v9: .cfa -520 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI 136e8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 136f0 .cfa: sp 624 + .ra: .cfa -544 + ^ v10: .cfa -512 + ^ v11: .cfa -504 + ^ v12: .cfa -496 + ^ v13: .cfa -488 + ^ v8: .cfa -528 + ^ v9: .cfa -520 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI INIT 13860 1c .cfa: sp 0 + .ra: x30
STACK CFI 13864 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 13878 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 13880 20 .cfa: sp 0 + .ra: x30
STACK CFI 13884 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 1389c .cfa: sp 0 + .ra: .ra
STACK CFI INIT 138a0 60 .cfa: sp 0 + .ra: x30
STACK CFI 138b0 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 138c0 .ra: .cfa -16 + ^
STACK CFI 138d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 138dc .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 13900 e8 .cfa: sp 0 + .ra: x30
STACK CFI 13904 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 1390c v8: .cfa -88 + ^
STACK CFI 13918 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 13924 .ra: .cfa -96 + ^
STACK CFI 139a0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 139a4 .cfa: sp 128 + .ra: .cfa -96 + ^ v8: .cfa -88 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI INIT 139f0 2e8 .cfa: sp 0 + .ra: x30
STACK CFI 139f8 .cfa: sp 496 + x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 13a10 .ra: .cfa -416 + ^ v8: .cfa -408 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 13c08 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 13c0c .cfa: sp 496 + .ra: .cfa -416 + ^ v8: .cfa -408 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI INIT 13cf0 5c .cfa: sp 0 + .ra: x30
STACK CFI 13cf4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13cf8 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 13d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 13d40 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 13d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 13d50 5c .cfa: sp 0 + .ra: x30
STACK CFI 13d54 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13d58 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 13d98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 13da0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 13da8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 13db0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 13db4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13dbc .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 13e48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 13e4c .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 13e50 8c .cfa: sp 0 + .ra: x30
STACK CFI 13e54 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 13e5c .ra: .cfa -8 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 13ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 13ecc .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 13ed8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 13ee0 8c .cfa: sp 0 + .ra: x30
STACK CFI 13ee4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 13eec .ra: .cfa -8 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 13f58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 13f5c .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 13f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 13f70 54 .cfa: sp 0 + .ra: x30
STACK CFI 13f74 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13f78 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 13fb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 13fb8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 13fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 13fc8 b4 .cfa: sp 0 + .ra: x30
STACK CFI 13fd0 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13fdc .ra: .cfa -16 + ^
STACK CFI 14004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 14008 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1404c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 14058 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 14080 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 14084 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 14094 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 140a0 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 141c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 141c8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 14240 154 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14398 e8 .cfa: sp 0 + .ra: x30
STACK CFI 1439c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 143a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 143b0 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14430 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 14438 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 14480 228 .cfa: sp 0 + .ra: x30
STACK CFI 14484 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 14490 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14498 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 144a0 .ra: .cfa -16 + ^
STACK CFI 1461c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 14620 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 146a8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 146ac .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 146b4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 146c0 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14740 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 14748 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 14790 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 147c0 100 .cfa: sp 0 + .ra: x30
STACK CFI 147c4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 147cc .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 147d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14888 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 14890 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 148c0 14c .cfa: sp 0 + .ra: x30
STACK CFI 148c8 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 148e0 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 14920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 14930 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 149cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 149d0 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 14a10 670 .cfa: sp 0 + .ra: x30
STACK CFI 14a18 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 14a24 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 14a2c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 14a3c .ra: .cfa -16 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 14ba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 14bb0 .cfa: sp 96 + .ra: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 14f88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 14f90 .cfa: sp 96 + .ra: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 15080 100 .cfa: sp 0 + .ra: x30
STACK CFI 15084 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1508c .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 15094 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 15148 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 15150 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 15180 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 151b8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 151d0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 151d4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 151dc .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 15268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1526c .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 15270 1060 .cfa: sp 0 + .ra: x30
STACK CFI 15274 .cfa: sp 864 +
STACK CFI 15298 .ra: .cfa -784 + ^ v10: .cfa -752 + ^ v11: .cfa -744 + ^ v12: .cfa -776 + ^ v8: .cfa -768 + ^ v9: .cfa -760 + ^ x19: .cfa -864 + ^ x20: .cfa -856 + ^ x21: .cfa -848 + ^ x22: .cfa -840 + ^ x23: .cfa -832 + ^ x24: .cfa -824 + ^ x25: .cfa -816 + ^ x26: .cfa -808 + ^ x27: .cfa -800 + ^ x28: .cfa -792 + ^
STACK CFI 15a68 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 15a70 .cfa: sp 864 + .ra: .cfa -784 + ^ v10: .cfa -752 + ^ v11: .cfa -744 + ^ v12: .cfa -776 + ^ v8: .cfa -768 + ^ v9: .cfa -760 + ^ x19: .cfa -864 + ^ x20: .cfa -856 + ^ x21: .cfa -848 + ^ x22: .cfa -840 + ^ x23: .cfa -832 + ^ x24: .cfa -824 + ^ x25: .cfa -816 + ^ x26: .cfa -808 + ^ x27: .cfa -800 + ^ x28: .cfa -792 + ^
STACK CFI INIT 162e8 dc .cfa: sp 0 + .ra: x30
STACK CFI 162ec .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 162f4 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 163bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 163c0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 163c8 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 163cc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 163dc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 163e8 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 16508 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 16510 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 16588 588 .cfa: sp 0 + .ra: x30
STACK CFI 1658c .cfa: sp 176 + x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 16598 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI 165b8 .ra: .cfa -96 + ^ v10: .cfa -88 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 16968 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 16970 .cfa: sp 176 + .ra: .cfa -96 + ^ v10: .cfa -88 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI INIT 16b10 118 .cfa: sp 0 + .ra: x30
STACK CFI 16b14 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 16b1c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 16b28 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 16be0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 16be4 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 16c28 228 .cfa: sp 0 + .ra: x30
STACK CFI 16c2c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 16c38 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 16c48 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 16db8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 16dc0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 16e50 1ac .cfa: sp 0 + .ra: x30
STACK CFI 16e54 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 16e64 .ra: .cfa -16 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 16f94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 16f98 .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 16fac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 16fb0 .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 17000 118 .cfa: sp 0 + .ra: x30
STACK CFI 17004 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1700c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 17018 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 170d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 170d4 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 17118 228 .cfa: sp 0 + .ra: x30
STACK CFI 1711c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 17128 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 17138 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 172a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 172b0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 17340 1ac .cfa: sp 0 + .ra: x30
STACK CFI 17344 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 17354 .ra: .cfa -16 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 17484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 17488 .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1749c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 174a0 .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 174f0 138 .cfa: sp 0 + .ra: x30
STACK CFI 174f4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 174fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 17504 .ra: .cfa -16 + ^
STACK CFI 175d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 175d8 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 17628 214 .cfa: sp 0 + .ra: x30
STACK CFI 17630 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 17644 .ra: .cfa -8 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 17760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 17768 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 177c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 177d0 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 17840 14c .cfa: sp 0 + .ra: x30
STACK CFI 17844 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 17854 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 17860 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 17910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 17918 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 17990 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 17994 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 179a0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 179b0 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 17ac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 17ac8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 17b60 868 .cfa: sp 0 + .ra: x30
STACK CFI 17b64 .cfa: sp 672 +
STACK CFI 17b68 x23: .cfa -592 + ^ x24: .cfa -584 + ^
STACK CFI 17b80 x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI 17b9c .ra: .cfa -544 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI 18160 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 18168 .cfa: sp 672 + .ra: .cfa -544 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI INIT 183e0 bc .cfa: sp 0 + .ra: x30
STACK CFI 183e4 .cfa: sp 176 +
STACK CFI 183ec .ra: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 183fc x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 1840c x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 18498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 184a0 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT 18538 16c .cfa: sp 0 + .ra: x30
STACK CFI 1858c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 185a8 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 18668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 18670 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 186a8 17c .cfa: sp 0 + .ra: x30
STACK CFI 1870c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1871c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 18728 .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 187fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 18800 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 18828 210 .cfa: sp 0 + .ra: x30
STACK CFI 1882c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 18834 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1883c .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 188ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 188f0 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 18a38 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 18a3c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 18a44 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 18a54 .ra: .cfa -8 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 18b1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 18b20 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 18ce0 1cbc .cfa: sp 0 + .ra: x30
STACK CFI 18ce4 .cfa: sp 1760 +
STACK CFI 18d28 .ra: .cfa -1680 + ^ v10: .cfa -1648 + ^ v11: .cfa -1640 + ^ v12: .cfa -1632 + ^ v13: .cfa -1624 + ^ v14: .cfa -1672 + ^ v8: .cfa -1664 + ^ v9: .cfa -1656 + ^ x19: .cfa -1760 + ^ x20: .cfa -1752 + ^ x21: .cfa -1744 + ^ x22: .cfa -1736 + ^ x23: .cfa -1728 + ^ x24: .cfa -1720 + ^ x25: .cfa -1712 + ^ x26: .cfa -1704 + ^ x27: .cfa -1696 + ^ x28: .cfa -1688 + ^
STACK CFI 18d78 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 18d7c .cfa: sp 1760 + .ra: .cfa -1680 + ^ v10: .cfa -1648 + ^ v11: .cfa -1640 + ^ v12: .cfa -1632 + ^ v13: .cfa -1624 + ^ v14: .cfa -1672 + ^ v8: .cfa -1664 + ^ v9: .cfa -1656 + ^ x19: .cfa -1760 + ^ x20: .cfa -1752 + ^ x21: .cfa -1744 + ^ x22: .cfa -1736 + ^ x23: .cfa -1728 + ^ x24: .cfa -1720 + ^ x25: .cfa -1712 + ^ x26: .cfa -1704 + ^ x27: .cfa -1696 + ^ x28: .cfa -1688 + ^
STACK CFI INIT 1a9b8 210 .cfa: sp 0 + .ra: x30
STACK CFI 1a9bc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1a9c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1a9cc .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 1aa7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 1aa80 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 1abc8 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 1abcc .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1abd4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1abe4 .ra: .cfa -8 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 1acac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 1acb0 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 1ae70 2684 .cfa: sp 0 + .ra: x30
STACK CFI 1ae74 .cfa: sp 1440 +
STACK CFI 1ae78 x19: .cfa -1440 + ^ x20: .cfa -1432 + ^
STACK CFI 1ae98 .ra: .cfa -1360 + ^ v10: .cfa -1328 + ^ v11: .cfa -1320 + ^ v8: .cfa -1344 + ^ v9: .cfa -1336 + ^ x21: .cfa -1424 + ^ x22: .cfa -1416 + ^ x23: .cfa -1408 + ^ x24: .cfa -1400 + ^ x25: .cfa -1392 + ^ x26: .cfa -1384 + ^ x27: .cfa -1376 + ^ x28: .cfa -1368 + ^
STACK CFI 1c60c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1c610 .cfa: sp 1440 + .ra: .cfa -1360 + ^ v10: .cfa -1328 + ^ v11: .cfa -1320 + ^ v8: .cfa -1344 + ^ v9: .cfa -1336 + ^ x19: .cfa -1440 + ^ x20: .cfa -1432 + ^ x21: .cfa -1424 + ^ x22: .cfa -1416 + ^ x23: .cfa -1408 + ^ x24: .cfa -1400 + ^ x25: .cfa -1392 + ^ x26: .cfa -1384 + ^ x27: .cfa -1376 + ^ x28: .cfa -1368 + ^
STACK CFI INIT 1d510 33c .cfa: sp 0 + .ra: x30
STACK CFI 1d514 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1d51c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1d524 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 1d650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 1d658 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 1d850 340 .cfa: sp 0 + .ra: x30
STACK CFI 1d854 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1d85c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1d870 .ra: .cfa -16 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1d93c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1d940 .cfa: sp 96 + .ra: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT 1db90 768 .cfa: sp 0 + .ra: x30
STACK CFI 1db94 .cfa: sp 384 + x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI 1dba4 x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI 1dbbc .ra: .cfa -304 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI 1e158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1e15c .cfa: sp 384 + .ra: .cfa -304 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI INIT 1e2f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e300 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e308 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e310 50 .cfa: sp 0 + .ra: x30
STACK CFI 1e314 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e320 .ra: .cfa -16 + ^
STACK CFI 1e35c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 1e360 98 .cfa: sp 0 + .ra: x30
STACK CFI 1e370 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e37c .ra: .cfa -16 + ^
STACK CFI 1e39c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1e3a0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e3cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 1e3f8 ec .cfa: sp 0 + .ra: x30
STACK CFI 1e3fc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e408 .ra: .cfa -16 + ^
STACK CFI 1e430 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1e438 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e4c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1e4c8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e4e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 1e4e8 190 .cfa: sp 0 + .ra: x30
STACK CFI 1e4ec .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e4f0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 1e5e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1e5f0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 1e630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1e638 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 1e678 dc .cfa: sp 0 + .ra: x30
STACK CFI 1e67c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1e680 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1e688 .ra: .cfa -32 + ^
STACK CFI 1e6d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1e6d8 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1e71c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1e720 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1e740 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1e748 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 1e760 95c .cfa: sp 0 + .ra: x30
STACK CFI 1e764 .cfa: sp 832 +
STACK CFI 1e768 x25: .cfa -784 + ^ x26: .cfa -776 + ^
STACK CFI 1e788 .ra: .cfa -752 + ^ v10: .cfa -744 + ^ v8: .cfa -736 + ^ v9: .cfa -728 + ^ x19: .cfa -832 + ^ x20: .cfa -824 + ^ x21: .cfa -816 + ^ x22: .cfa -808 + ^ x23: .cfa -800 + ^ x24: .cfa -792 + ^ x27: .cfa -768 + ^ x28: .cfa -760 + ^
STACK CFI 1eea8 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1eeac .cfa: sp 832 + .ra: .cfa -752 + ^ v10: .cfa -744 + ^ v8: .cfa -736 + ^ v9: .cfa -728 + ^ x19: .cfa -832 + ^ x20: .cfa -824 + ^ x21: .cfa -816 + ^ x22: .cfa -808 + ^ x23: .cfa -800 + ^ x24: .cfa -792 + ^ x25: .cfa -784 + ^ x26: .cfa -776 + ^ x27: .cfa -768 + ^ x28: .cfa -760 + ^
STACK CFI INIT 1f0e0 48 .cfa: sp 0 + .ra: x30
STACK CFI 1f0e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 1f124 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 1f128 d3c .cfa: sp 0 + .ra: x30
STACK CFI 1f12c .cfa: sp 832 +
STACK CFI 1f138 x25: .cfa -784 + ^ x26: .cfa -776 + ^
STACK CFI 1f148 x21: .cfa -816 + ^ x22: .cfa -808 + ^ x23: .cfa -800 + ^ x24: .cfa -792 + ^
STACK CFI 1f164 .ra: .cfa -752 + ^ v10: .cfa -744 + ^ v8: .cfa -736 + ^ v9: .cfa -728 + ^ x19: .cfa -832 + ^ x20: .cfa -824 + ^ x27: .cfa -768 + ^ x28: .cfa -760 + ^
STACK CFI 1f4ac .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1f4b0 .cfa: sp 832 + .ra: .cfa -752 + ^ v10: .cfa -744 + ^ v8: .cfa -736 + ^ v9: .cfa -728 + ^ x19: .cfa -832 + ^ x20: .cfa -824 + ^ x21: .cfa -816 + ^ x22: .cfa -808 + ^ x23: .cfa -800 + ^ x24: .cfa -792 + ^ x25: .cfa -784 + ^ x26: .cfa -776 + ^ x27: .cfa -768 + ^ x28: .cfa -760 + ^
STACK CFI INIT 1fe68 f98 .cfa: sp 0 + .ra: x30
STACK CFI 1fe6c .cfa: sp 960 +
STACK CFI 1fe78 x23: .cfa -928 + ^ x24: .cfa -920 + ^
STACK CFI 1fe80 x27: .cfa -896 + ^ x28: .cfa -888 + ^
STACK CFI 1fea4 .ra: .cfa -880 + ^ v10: .cfa -848 + ^ v11: .cfa -840 + ^ v12: .cfa -832 + ^ v13: .cfa -824 + ^ v14: .cfa -872 + ^ v8: .cfa -864 + ^ v9: .cfa -856 + ^ x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x25: .cfa -912 + ^ x26: .cfa -904 + ^
STACK CFI 208d0 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 208d8 .cfa: sp 960 + .ra: .cfa -880 + ^ v10: .cfa -848 + ^ v11: .cfa -840 + ^ v12: .cfa -832 + ^ v13: .cfa -824 + ^ v14: .cfa -872 + ^ v8: .cfa -864 + ^ v9: .cfa -856 + ^ x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^ x25: .cfa -912 + ^ x26: .cfa -904 + ^ x27: .cfa -896 + ^ x28: .cfa -888 + ^
STACK CFI INIT 20e00 50 .cfa: sp 0 + .ra: x30
STACK CFI 20e08 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 20e4c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 20e50 100 .cfa: sp 0 + .ra: x30
STACK CFI 20e54 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 20e70 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 20eac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 20eb0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 20f34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 20f38 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 20f50 18c .cfa: sp 0 + .ra: x30
STACK CFI 20f58 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 20f5c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 20f6c .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 20fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 20fd0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 210b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 210b8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 210e0 140 .cfa: sp 0 + .ra: x30
STACK CFI 210e4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 210f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 210f8 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 211e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 211e8 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 21220 1bc .cfa: sp 0 + .ra: x30
STACK CFI 21284 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 21288 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 21298 .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 213b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 213b8 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 213e0 164 .cfa: sp 0 + .ra: x30
STACK CFI 213e4 .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 213f8 .ra: .cfa -112 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 21524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 21528 .cfa: sp 192 + .ra: .cfa -112 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT 21548 e08 .cfa: sp 0 + .ra: x30
STACK CFI 2154c .cfa: sp 592 +
STACK CFI 21568 .ra: .cfa -512 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^ x27: .cfa -528 + ^ x28: .cfa -520 + ^
STACK CFI 220f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 220f4 .cfa: sp 592 + .ra: .cfa -512 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^ x27: .cfa -528 + ^ x28: .cfa -520 + ^
STACK CFI INIT 22350 100 .cfa: sp 0 + .ra: x30
STACK CFI 22354 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2235c .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 22364 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 22418 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 22420 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 22450 150 .cfa: sp 0 + .ra: x30
STACK CFI 2249c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 224a8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 224b4 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 22578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 22580 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 225a0 1bc .cfa: sp 0 + .ra: x30
STACK CFI 22604 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 22608 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 22618 .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 22734 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 22738 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 22760 8cc .cfa: sp 0 + .ra: x30
STACK CFI 22764 .cfa: sp 448 +
STACK CFI 22768 x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI 22778 x19: .cfa -400 + ^ x20: .cfa -392 + ^ x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI 2278c .ra: .cfa -320 + ^ v8: .cfa -312 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^ x26: .cfa -344 + ^
STACK CFI 22ff8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 22ffc .cfa: sp 448 + .ra: .cfa -320 + ^ v8: .cfa -312 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^ x26: .cfa -344 + ^ x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI INIT 23030 b4 .cfa: sp 0 + .ra: x30
STACK CFI 23034 .cfa: sp 160 +
STACK CFI 2303c .ra: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2304c x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 2305c x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 230e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 230e8 110 .cfa: sp 0 + .ra: x30
STACK CFI 230ec .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 230f4 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 230fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 231c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 231c8 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 231f8 e8 .cfa: sp 0 + .ra: x30
STACK CFI 231fc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 23204 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 23210 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 23290 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 23298 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 232e0 610 .cfa: sp 0 + .ra: x30
STACK CFI 232e4 .cfa: sp 224 + x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 232f0 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 23314 .ra: .cfa -144 + ^ v10: .cfa -112 + ^ v11: .cfa -104 + ^ v12: .cfa -96 + ^ v13: .cfa -88 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 23878 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 23880 .cfa: sp 224 + .ra: .cfa -144 + ^ v10: .cfa -112 + ^ v11: .cfa -104 + ^ v12: .cfa -96 + ^ v13: .cfa -88 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT 238f8 5d0 .cfa: sp 0 + .ra: x30
STACK CFI 238fc .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 23920 .ra: .cfa -80 + ^ v10: .cfa -72 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 23ca8 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 23cb0 .cfa: sp 160 + .ra: .cfa -80 + ^ v10: .cfa -72 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT 23ec8 1ec .cfa: sp 0 + .ra: x30
STACK CFI 23ecc .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 23ee4 .ra: .cfa -56 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 23f8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 23f90 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI INIT 240c0 b70 .cfa: sp 0 + .ra: x30
STACK CFI 240c4 .cfa: sp 672 +
STACK CFI 240c8 x21: .cfa -656 + ^ x22: .cfa -648 + ^
STACK CFI 240d8 x19: .cfa -672 + ^ x20: .cfa -664 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^
STACK CFI 240f0 .ra: .cfa -592 + ^ x25: .cfa -624 + ^ x26: .cfa -616 + ^ x27: .cfa -608 + ^ x28: .cfa -600 + ^
STACK CFI 24828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2482c .cfa: sp 672 + .ra: .cfa -592 + ^ x19: .cfa -672 + ^ x20: .cfa -664 + ^ x21: .cfa -656 + ^ x22: .cfa -648 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^ x25: .cfa -624 + ^ x26: .cfa -616 + ^ x27: .cfa -608 + ^ x28: .cfa -600 + ^
STACK CFI INIT 24c40 3ac .cfa: sp 0 + .ra: x30
STACK CFI 24e5c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 24e64 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 24e6c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 24e74 .ra: .cfa -8 + ^ x25: .cfa -16 + ^
STACK CFI 24f94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 24fc0 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 24ff0 ba0 .cfa: sp 0 + .ra: x30
STACK CFI 24ff4 .cfa: sp 304 + x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 25020 .ra: .cfa -224 + ^ v10: .cfa -192 + ^ v11: .cfa -184 + ^ v12: .cfa -216 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI 25898 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2589c .cfa: sp 304 + .ra: .cfa -224 + ^ v10: .cfa -192 + ^ v11: .cfa -184 + ^ v12: .cfa -216 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI INIT 25b90 1214 .cfa: sp 0 + .ra: x30
STACK CFI 25b94 .cfa: sp 608 +
STACK CFI 25b98 x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 25ba8 x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 25bc4 .ra: .cfa -528 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x28: .cfa -536 + ^
STACK CFI 26c04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 26c08 .cfa: sp 608 + .ra: .cfa -528 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x28: .cfa -536 + ^
STACK CFI INIT 26dc0 740 .cfa: sp 0 + .ra: x30
STACK CFI 26dc4 .cfa: sp 464 + x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 26dd0 x21: .cfa -448 + ^ x22: .cfa -440 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI 26ddc x23: .cfa -432 + ^ x24: .cfa -424 + ^
STACK CFI 26df8 .ra: .cfa -384 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^
STACK CFI 27160 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 27168 .cfa: sp 464 + .ra: .cfa -384 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^ x26: .cfa -408 + ^ x27: .cfa -400 + ^ x28: .cfa -392 + ^
STACK CFI INIT 27510 c0 .cfa: sp 0 + .ra: x30
STACK CFI 27514 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 27524 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 27534 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2753c .ra: .cfa -8 + ^ x27: .cfa -16 + ^
STACK CFI 275a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 275a8 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 275d0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 275d4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 275dc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 275e8 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 27668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 27670 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 276b8 bd0 .cfa: sp 0 + .ra: x30
STACK CFI 276bc .cfa: sp 432 + x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 276c8 x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 276dc .ra: .cfa -352 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 2791c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 27920 .cfa: sp 432 + .ra: .cfa -352 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 28054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 28058 .cfa: sp 432 + .ra: .cfa -352 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI INIT 28290 1b1c .cfa: sp 0 + .ra: x30
STACK CFI 28294 .cfa: sp 1152 +
STACK CFI 28298 v8: .cfa -1040 + ^ v9: .cfa -1032 + ^
STACK CFI 282a0 x19: .cfa -1136 + ^ x20: .cfa -1128 + ^
STACK CFI 282b0 x23: .cfa -1104 + ^ x24: .cfa -1096 + ^ x25: .cfa -1088 + ^ x26: .cfa -1080 + ^
STACK CFI 282c0 x21: .cfa -1120 + ^ x22: .cfa -1112 + ^ x27: .cfa -1072 + ^ x28: .cfa -1064 + ^
STACK CFI 282c8 .ra: .cfa -1056 + ^
STACK CFI 29a44 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 29a48 .cfa: sp 1152 + .ra: .cfa -1056 + ^ v8: .cfa -1040 + ^ v9: .cfa -1032 + ^ x19: .cfa -1136 + ^ x20: .cfa -1128 + ^ x21: .cfa -1120 + ^ x22: .cfa -1112 + ^ x23: .cfa -1104 + ^ x24: .cfa -1096 + ^ x25: .cfa -1088 + ^ x26: .cfa -1080 + ^ x27: .cfa -1072 + ^ x28: .cfa -1064 + ^
STACK CFI INIT 7ea0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 7ea4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7eb0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 7f30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 7f34 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 29de0 11c .cfa: sp 0 + .ra: x30
STACK CFI 29de4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 29dec .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 29ed0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 29ed8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 29f00 90 .cfa: sp 0 + .ra: x30
STACK CFI 29f04 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 29f78 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 29f80 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 29f8c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 29f90 258 .cfa: sp 0 + .ra: x30
STACK CFI 29f98 .cfa: sp 176 + x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 29fa4 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 29fac .ra: .cfa -136 + ^ x23: .cfa -144 + ^
STACK CFI 2a11c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2a120 .cfa: sp 176 + .ra: .cfa -136 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^
STACK CFI INIT 2a200 178 .cfa: sp 0 + .ra: x30
STACK CFI 2a238 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a244 .ra: .cfa -16 + ^
STACK CFI 2a304 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 2a308 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 2a364 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 2a390 200 .cfa: sp 0 + .ra: x30
STACK CFI 2a394 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2a3a4 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI 2a58c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 2a5a0 21c .cfa: sp 0 + .ra: x30
STACK CFI 2a5a4 .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 2a5a8 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 2a5b8 x23: .cfa -160 + ^ x24: .cfa -152 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 2a5c4 .ra: .cfa -112 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 2a72c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2a730 .cfa: sp 192 + .ra: .cfa -112 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT 2a7c0 2ec .cfa: sp 0 + .ra: x30
STACK CFI 2a7c4 .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 2a7c8 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 2a7d4 .ra: .cfa -208 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 2a984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2a988 .cfa: sp 256 + .ra: .cfa -208 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI INIT 2aab0 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 2aab4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2aac4 .ra: .cfa -56 + ^ x21: .cfa -64 + ^
STACK CFI 2ace4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2ace8 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^
STACK CFI INIT 2adb0 ba8 .cfa: sp 0 + .ra: x30
STACK CFI 2adb4 .cfa: sp 976 +
STACK CFI 2adb8 x19: .cfa -976 + ^ x20: .cfa -968 + ^
STACK CFI 2adc0 x21: .cfa -960 + ^ x22: .cfa -952 + ^
STACK CFI 2add8 .ra: .cfa -896 + ^ v8: .cfa -888 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x27: .cfa -912 + ^ x28: .cfa -904 + ^
STACK CFI 2b564 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2b568 .cfa: sp 976 + .ra: .cfa -896 + ^ v8: .cfa -888 + ^ x19: .cfa -976 + ^ x20: .cfa -968 + ^ x21: .cfa -960 + ^ x22: .cfa -952 + ^ x23: .cfa -944 + ^ x24: .cfa -936 + ^ x25: .cfa -928 + ^ x26: .cfa -920 + ^ x27: .cfa -912 + ^ x28: .cfa -904 + ^
STACK CFI INIT 2b980 23c0 .cfa: sp 0 + .ra: x30
STACK CFI 2b984 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 2b98c x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 2b998 .ra: .cfa -120 + ^ x23: .cfa -128 + ^
STACK CFI 2bc78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2bc80 .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^
STACK CFI INIT 2ded0 18 .cfa: sp 0 + .ra: x30
STACK CFI 2ded4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2dee4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2dee8 18 .cfa: sp 0 + .ra: x30
STACK CFI 2deec .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2defc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2df00 dc4 .cfa: sp 0 + .ra: x30
STACK CFI 2df04 .cfa: sp 528 +
STACK CFI 2df08 x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 2df28 x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^
STACK CFI 2df30 x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI 2df48 .ra: .cfa -448 + ^ v8: .cfa -432 + ^ v9: .cfa -424 + ^
STACK CFI 2e804 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2e808 .cfa: sp 528 + .ra: .cfa -448 + ^ v8: .cfa -432 + ^ v9: .cfa -424 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^ x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI INIT 2ece8 18 .cfa: sp 0 + .ra: x30
STACK CFI 2ecec .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2ecfc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2ed00 230 .cfa: sp 0 + .ra: x30
STACK CFI 2ed04 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 2ed08 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 2ed14 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 2ed24 .ra: .cfa -112 + ^
STACK CFI 2ee68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2ee70 .cfa: sp 160 + .ra: .cfa -112 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI INIT 2ef40 230 .cfa: sp 0 + .ra: x30
STACK CFI 2ef44 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 2ef48 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 2ef54 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI 2ef64 .ra: .cfa -112 + ^
STACK CFI 2f0a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2f0b0 .cfa: sp 160 + .ra: .cfa -112 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^
