MODULE Linux arm64 B38F54F2B9784D621CCE8A401F3451D90 libavc1394.so.0
INFO CODE_ID F2548FB378B9624D1CCE8A401F3451D9A1CE6277
PUBLIC 1760 0 avc_fcp_handler
PUBLIC 1810 0 avc1394_transaction_block_close
PUBLIC 1850 0 avc1394_init_target
PUBLIC 18a4 0 avc1394_close_target
PUBLIC 18c0 0 avc1394_vcr_decode_status
PUBLIC 1a10 0 htonl_block
PUBLIC 1a50 0 ntohl_block
PUBLIC 1a90 0 decode_response
PUBLIC 1b80 0 decode_ctype
PUBLIC 1c30 0 init_avc_response_handler
PUBLIC 1c70 0 stop_avc_response_handler
PUBLIC 1c90 0 cooked1394_read
PUBLIC 1d80 0 cooked1394_write
PUBLIC 1e70 0 avc1394_send_command
PUBLIC 1ef0 0 avc1394_transaction
PUBLIC 20d0 0 avc1394_vcr_is_playing
PUBLIC 2110 0 avc1394_vcr_is_recording
PUBLIC 2150 0 avc1394_vcr_status
PUBLIC 2174 0 avc1394_vcr_play
PUBLIC 21c0 0 avc1394_vcr_reverse
PUBLIC 2210 0 avc1394_vcr_trick_play
PUBLIC 22f4 0 avc1394_vcr_stop
PUBLIC 2314 0 avc1394_vcr_rewind
PUBLIC 2360 0 avc1394_vcr_pause
PUBLIC 23f0 0 avc1394_vcr_forward
PUBLIC 2440 0 avc1394_vcr_next
PUBLIC 2494 0 avc1394_vcr_previous
PUBLIC 24f0 0 avc1394_vcr_eject
PUBLIC 2510 0 avc1394_vcr_record
PUBLIC 2530 0 avc1394_send_command_block
PUBLIC 2610 0 avc1394_transaction_block2
PUBLIC 2850 0 avc1394_transaction_block
PUBLIC 28b0 0 avc1394_open_descriptor
PUBLIC 29c0 0 avc1394_close_descriptor
PUBLIC 2ab0 0 avc1394_read_descriptor
PUBLIC 2b90 0 avc1394_subunit_info
PUBLIC 2c80 0 avc1394_check_subunit_type
PUBLIC 2d50 0 avc1394_unit_info
PUBLIC 2dc0 0 avc1394_vcr_get_timecode
PUBLIC 2ea4 0 avc1394_vcr_get_timecode2
PUBLIC 2f80 0 avc1394_vcr_next_index
PUBLIC 3014 0 avc1394_vcr_previous_index
PUBLIC 30b0 0 avc1394_vcr_seek_timecode
PUBLIC 3190 0 target_fcp_handler
STACK CFI INIT 1690 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16c0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1700 48 .cfa: sp 0 + .ra: x30
STACK CFI 1704 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 170c x19: .cfa -16 + ^
STACK CFI 1744 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1750 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1760 ac .cfa: sp 0 + .ra: x30
STACK CFI 1780 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1788 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1794 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 17f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 17f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1810 38 .cfa: sp 0 + .ra: x30
STACK CFI 1818 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1820 x19: .cfa -16 + ^
STACK CFI 1840 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1850 54 .cfa: sp 0 + .ra: x30
STACK CFI 1864 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 187c x19: .cfa -16 + ^
STACK CFI 1894 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 18a4 18 .cfa: sp 0 + .ra: x30
STACK CFI 18ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 18b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18c0 14c .cfa: sp 0 + .ra: x30
STACK CFI 18c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1948 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1950 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1968 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1974 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1978 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1988 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 198c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 199c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1a10 38 .cfa: sp 0 + .ra: x30
STACK CFI 1a18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a50 38 .cfa: sp 0 + .ra: x30
STACK CFI 1a58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a90 e8 .cfa: sp 0 + .ra: x30
STACK CFI 1a98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1b10 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1b24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1b38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1b4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1b60 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1b80 a8 .cfa: sp 0 + .ra: x30
STACK CFI 1b88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1bd8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1be0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1be4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1bf4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1bf8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1c0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1c30 3c .cfa: sp 0 + .ra: x30
STACK CFI 1c38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c40 x19: .cfa -16 + ^
STACK CFI 1c64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1c70 18 .cfa: sp 0 + .ra: x30
STACK CFI 1c78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1c80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1c90 ec .cfa: sp 0 + .ra: x30
STACK CFI 1c98 .cfa: sp 112 +
STACK CFI 1ca0 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1cac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1cb8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1cc4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1cd4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1d78 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1d80 ec .cfa: sp 0 + .ra: x30
STACK CFI 1d88 .cfa: sp 112 +
STACK CFI 1d90 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1d9c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1da8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1db4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1dc4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1e60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1e68 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1e70 78 .cfa: sp 0 + .ra: x30
STACK CFI 1e78 .cfa: sp 32 +
STACK CFI 1e90 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1edc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1ee4 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1ef0 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 1ef8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1f08 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1f10 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1f18 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1f20 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1f30 .cfa: sp 656 + x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1fd4 .cfa: sp 96 +
STACK CFI 1ff0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1ff8 .cfa: sp 656 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 20d0 3c .cfa: sp 0 + .ra: x30
STACK CFI 20dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2100 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2110 3c .cfa: sp 0 + .ra: x30
STACK CFI 211c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2140 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2150 24 .cfa: sp 0 + .ra: x30
STACK CFI 2158 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2164 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2174 4c .cfa: sp 0 + .ra: x30
STACK CFI 217c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2184 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 21b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 21c0 4c .cfa: sp 0 + .ra: x30
STACK CFI 21c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 21d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2210 e4 .cfa: sp 0 + .ra: x30
STACK CFI 2218 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2220 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2230 x21: .cfa -16 + ^
STACK CFI 2278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2280 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 228c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2294 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 22b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 22b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 22e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 22f4 20 .cfa: sp 0 + .ra: x30
STACK CFI 22fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2308 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2314 48 .cfa: sp 0 + .ra: x30
STACK CFI 231c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2324 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2360 88 .cfa: sp 0 + .ra: x30
STACK CFI 2368 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2370 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 23a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 23b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 23dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 23f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 23f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2400 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2430 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2440 54 .cfa: sp 0 + .ra: x30
STACK CFI 2448 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2450 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 246c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2474 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2494 54 .cfa: sp 0 + .ra: x30
STACK CFI 249c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 24a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 24c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 24c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 24dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 24f0 20 .cfa: sp 0 + .ra: x30
STACK CFI 24f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2504 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2510 20 .cfa: sp 0 + .ra: x30
STACK CFI 2518 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2524 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2530 e0 .cfa: sp 0 + .ra: x30
STACK CFI 2548 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2554 .cfa: x29 16 +
STACK CFI 25fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2604 .cfa: x29 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2610 240 .cfa: sp 0 + .ra: x30
STACK CFI 2618 .cfa: sp 144 +
STACK CFI 2624 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 262c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2634 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2640 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 264c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2690 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2794 x27: x27 x28: x28
STACK CFI 27cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 27d4 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2814 x27: x27 x28: x28
STACK CFI 281c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 283c x27: x27 x28: x28
STACK CFI 2840 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 2850 60 .cfa: sp 0 + .ra: x30
STACK CFI 2858 .cfa: sp 32 +
STACK CFI 2868 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 28a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 28ac .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 28b0 110 .cfa: sp 0 + .ra: x30
STACK CFI 28b8 .cfa: sp 80 +
STACK CFI 28c8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 28d0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 28dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 28e8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 29a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 29ac .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 29c0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 29c8 .cfa: sp 80 +
STACK CFI 29d4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 29dc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 29e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 29f4 x23: .cfa -16 + ^
STACK CFI 2a94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2a9c .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2ab0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 2ab8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2ac8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ad8 .cfa: sp 576 + x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2b78 .cfa: sp 48 +
STACK CFI 2b84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2b8c .cfa: sp 576 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2b90 ec .cfa: sp 0 + .ra: x30
STACK CFI 2b98 .cfa: sp 96 +
STACK CFI 2ba4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2bac x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2bb8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2bc4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2bd0 x25: .cfa -16 + ^
STACK CFI 2c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 2c68 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2c80 c8 .cfa: sp 0 + .ra: x30
STACK CFI 2c88 .cfa: sp 80 +
STACK CFI 2c94 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d3c .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2d50 70 .cfa: sp 0 + .ra: x30
STACK CFI 2d58 .cfa: sp 32 +
STACK CFI 2d68 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2db4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2dbc .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2dc0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 2dc8 .cfa: sp 64 +
STACK CFI 2ddc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2dec x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2e88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2e90 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2ea4 d4 .cfa: sp 0 + .ra: x30
STACK CFI 2eac .cfa: sp 48 +
STACK CFI 2ebc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ec8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2f5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2f64 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2f80 94 .cfa: sp 0 + .ra: x30
STACK CFI 2f88 .cfa: sp 48 +
STACK CFI 2f94 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2fe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2fec .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3014 94 .cfa: sp 0 + .ra: x30
STACK CFI 301c .cfa: sp 48 +
STACK CFI 3028 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3030 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3078 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3080 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 30b0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 30b8 .cfa: sp 64 +
STACK CFI 30c8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30d8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 317c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3184 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3190 ec .cfa: sp 0 + .ra: x30
STACK CFI 3198 .cfa: sp 96 +
STACK CFI 31a4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 31ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 31b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 31c4 x23: .cfa -16 + ^
STACK CFI 325c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3264 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
