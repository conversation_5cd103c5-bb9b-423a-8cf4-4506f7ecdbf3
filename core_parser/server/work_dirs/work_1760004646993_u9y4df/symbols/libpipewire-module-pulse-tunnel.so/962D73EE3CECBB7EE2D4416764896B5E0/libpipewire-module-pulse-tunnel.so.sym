MODULE Linux arm64 962D73EE3CECBB7EE2D4416764896B5E0 libpipewire-module-pulse-tunnel.so
INFO CODE_ID EE732D96EC3C7EBBE2D4416764896B5E370548CB
PUBLIC a3c0 0 pipewire__module_init
STACK CFI INIT 4180 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 41f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 41f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41fc x19: .cfa -16 + ^
STACK CFI 4234 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4240 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4250 340 .cfa: sp 0 + .ra: x30
STACK CFI 4258 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4344 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4350 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4488 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4494 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 44e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 44ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4564 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 456c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4590 50 .cfa: sp 0 + .ra: x30
STACK CFI 4598 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45a0 x19: .cfa -16 + ^
STACK CFI 45d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 45e0 24 .cfa: sp 0 + .ra: x30
STACK CFI 45e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 45fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4604 2c88 .cfa: sp 0 + .ra: x30
STACK CFI 460c .cfa: sp 480 +
STACK CFI 4620 .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 4638 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 4640 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 4658 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 4e00 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4e08 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 7290 60 .cfa: sp 0 + .ra: x30
STACK CFI 7298 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 72bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 72e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 72e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 72f0 ac .cfa: sp 0 + .ra: x30
STACK CFI 72f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7368 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7390 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7394 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 73a0 ac .cfa: sp 0 + .ra: x30
STACK CFI 73a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7418 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7440 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7444 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7450 13c .cfa: sp 0 + .ra: x30
STACK CFI 7458 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7460 x19: .cfa -16 + ^
STACK CFI 7470 v8: .cfa -8 + ^
STACK CFI 752c v8: v8
STACK CFI 7534 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 753c .cfa: sp 32 + .ra: .cfa -24 + ^ v8: .cfa -8 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7590 10c .cfa: sp 0 + .ra: x30
STACK CFI 7598 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 75a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 760c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7614 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 76a0 6c .cfa: sp 0 + .ra: x30
STACK CFI 76a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 76d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 76d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 76e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 76f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 76f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 76fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7700 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7710 34 .cfa: sp 0 + .ra: x30
STACK CFI 7724 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7730 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7744 68 .cfa: sp 0 + .ra: x30
STACK CFI 774c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7754 x19: .cfa -16 + ^
STACK CFI 7794 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 779c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 77a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 77b0 4c0 .cfa: sp 0 + .ra: x30
STACK CFI 77b8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 77c4 .cfa: sp 1568 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 780c .cfa: sp 96 +
STACK CFI 7814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 781c .cfa: sp 1568 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 7824 x21: .cfa -64 + ^
STACK CFI 7838 x22: .cfa -56 + ^
STACK CFI 783c x23: .cfa -48 + ^
STACK CFI 7844 x24: .cfa -40 + ^
STACK CFI 7890 x21: x21
STACK CFI 7894 x22: x22
STACK CFI 7898 x23: x23
STACK CFI 789c x24: x24
STACK CFI 78a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 78b0 x25: .cfa -32 + ^
STACK CFI 78b8 x26: .cfa -24 + ^
STACK CFI 79d8 x25: x25
STACK CFI 79dc x26: x26
STACK CFI 79e0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 79f8 x27: .cfa -16 + ^
STACK CFI 7a00 x28: .cfa -8 + ^
STACK CFI 7a60 x28: x28
STACK CFI 7a68 x27: x27
STACK CFI 7af4 x28: .cfa -8 + ^
STACK CFI 7afc x27: .cfa -16 + ^
STACK CFI 7ba0 x27: x27
STACK CFI 7ba4 x28: x28
STACK CFI 7bcc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7c08 x27: x27
STACK CFI 7c0c x28: x28
STACK CFI 7c1c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7c24 x27: x27 x28: x28
STACK CFI 7c2c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7c4c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7c50 x21: .cfa -64 + ^
STACK CFI 7c54 x22: .cfa -56 + ^
STACK CFI 7c58 x23: .cfa -48 + ^
STACK CFI 7c5c x24: .cfa -40 + ^
STACK CFI 7c60 x25: .cfa -32 + ^
STACK CFI 7c64 x26: .cfa -24 + ^
STACK CFI 7c68 x27: .cfa -16 + ^
STACK CFI 7c6c x28: .cfa -8 + ^
STACK CFI INIT 7c70 2bc .cfa: sp 0 + .ra: x30
STACK CFI 7c78 .cfa: sp 128 +
STACK CFI 7c7c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7c84 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7c98 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7c9c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7ca4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7ca8 x27: .cfa -16 + ^
STACK CFI 7d80 x21: x21 x22: x22
STACK CFI 7d84 x23: x23 x24: x24
STACK CFI 7d88 x25: x25 x26: x26
STACK CFI 7d94 x27: x27
STACK CFI 7d98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7da0 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 7e3c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 7e68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e70 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 7e88 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 7ef0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 7f08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7f30 1ec .cfa: sp 0 + .ra: x30
STACK CFI 7f38 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7f40 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7f54 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7f5c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7f60 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7ff0 x21: x21 x22: x22
STACK CFI 7ff4 x23: x23 x24: x24
STACK CFI 7ff8 x25: x25 x26: x26
STACK CFI 8004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 800c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 8010 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8078 x27: x27 x28: x28
STACK CFI 8090 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 80b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 80bc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 80dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8108 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8120 67c .cfa: sp 0 + .ra: x30
STACK CFI 8128 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8134 .cfa: sp 1392 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8170 x21: .cfa -64 + ^
STACK CFI 8178 x22: .cfa -56 + ^
STACK CFI 817c x23: .cfa -48 + ^
STACK CFI 8180 x24: .cfa -40 + ^
STACK CFI 8184 x25: .cfa -32 + ^
STACK CFI 8188 x26: .cfa -24 + ^
STACK CFI 818c x27: .cfa -16 + ^
STACK CFI 8190 x28: .cfa -8 + ^
STACK CFI 8494 x21: x21
STACK CFI 849c x22: x22
STACK CFI 84a4 x23: x23
STACK CFI 84a8 x24: x24
STACK CFI 84ac x25: x25
STACK CFI 84b0 x26: x26
STACK CFI 84b4 x27: x27
STACK CFI 84b8 x28: x28
STACK CFI 84d8 .cfa: sp 96 +
STACK CFI 84e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 84e8 .cfa: sp 1392 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8768 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 877c x21: .cfa -64 + ^
STACK CFI 8780 x22: .cfa -56 + ^
STACK CFI 8784 x23: .cfa -48 + ^
STACK CFI 8788 x24: .cfa -40 + ^
STACK CFI 878c x25: .cfa -32 + ^
STACK CFI 8790 x26: .cfa -24 + ^
STACK CFI 8794 x27: .cfa -16 + ^
STACK CFI 8798 x28: .cfa -8 + ^
STACK CFI INIT 87a0 7c .cfa: sp 0 + .ra: x30
STACK CFI 87a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 87b0 x19: .cfa -16 + ^
STACK CFI 87e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 87f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 87fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8804 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8814 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8820 9c .cfa: sp 0 + .ra: x30
STACK CFI 8828 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8830 x19: .cfa -16 + ^
STACK CFI 8878 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8880 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 888c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8894 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 88b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 88c0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 88c8 .cfa: sp 64 +
STACK CFI 88d8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 88e0 x19: .cfa -16 + ^
STACK CFI 8948 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8950 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 89a0 3e8 .cfa: sp 0 + .ra: x30
STACK CFI 89a8 .cfa: sp 192 +
STACK CFI 89b4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 89bc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 89c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 89e4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8a30 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8a40 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8af8 x23: x23 x24: x24
STACK CFI 8afc x27: x27 x28: x28
STACK CFI 8b68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 8b70 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8c14 x23: x23 x24: x24
STACK CFI 8c18 x27: x27 x28: x28
STACK CFI 8c80 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8c98 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 8d30 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8d74 x23: x23 x24: x24
STACK CFI 8d78 x27: x27 x28: x28
STACK CFI 8d80 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8d84 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 8d90 324 .cfa: sp 0 + .ra: x30
STACK CFI 8d98 .cfa: sp 176 +
STACK CFI 8da4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8dac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8dc4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9038 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 90b4 150 .cfa: sp 0 + .ra: x30
STACK CFI 90bc .cfa: sp 64 +
STACK CFI 90c8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 90d0 x19: .cfa -16 + ^
STACK CFI 916c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9174 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9204 150 .cfa: sp 0 + .ra: x30
STACK CFI 920c .cfa: sp 64 +
STACK CFI 9218 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9220 x19: .cfa -16 + ^
STACK CFI 92bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 92c4 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9354 3a0 .cfa: sp 0 + .ra: x30
STACK CFI 935c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9374 .cfa: sp 1680 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 93b8 v8: .cfa -16 + ^
STACK CFI 93f0 v8: v8
STACK CFI 9614 .cfa: sp 96 +
STACK CFI 962c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9634 .cfa: sp 1680 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 96f0 v8: .cfa -16 + ^
STACK CFI INIT 96f4 5c .cfa: sp 0 + .ra: x30
STACK CFI 96fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9720 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9730 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9734 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 973c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9740 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9750 57c .cfa: sp 0 + .ra: x30
STACK CFI 9758 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9764 .cfa: sp 1280 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9790 x21: .cfa -32 + ^
STACK CFI 9798 x22: .cfa -24 + ^
STACK CFI 98a4 x23: .cfa -16 + ^
STACK CFI 9afc x21: x21
STACK CFI 9b04 x22: x22
STACK CFI 9b08 x23: x23
STACK CFI 9b0c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9b10 x21: x21
STACK CFI 9b14 x22: x22
STACK CFI 9b4c x21: .cfa -32 + ^
STACK CFI 9b54 x22: .cfa -24 + ^
STACK CFI 9ba8 x21: x21
STACK CFI 9bac x22: x22
STACK CFI 9bd0 .cfa: sp 64 +
STACK CFI 9bd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9be0 .cfa: sp 1280 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 9bf0 x23: x23
STACK CFI 9c58 x23: .cfa -16 + ^
STACK CFI 9c68 x23: x23
STACK CFI 9c70 x23: .cfa -16 + ^
STACK CFI 9c78 x23: x23
STACK CFI 9c7c x21: x21
STACK CFI 9c84 x22: x22
STACK CFI 9c88 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 9cbc x21: x21 x22: x22 x23: x23
STACK CFI 9cc0 x21: .cfa -32 + ^
STACK CFI 9cc4 x22: .cfa -24 + ^
STACK CFI 9cc8 x23: .cfa -16 + ^
STACK CFI INIT 9cd0 fc .cfa: sp 0 + .ra: x30
STACK CFI 9cd8 .cfa: sp 96 +
STACK CFI 9cdc .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9ce4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9cf0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9d3c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 9d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9d54 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9dd0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 9dd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9de0 x19: .cfa -16 + ^
STACK CFI 9e98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9ea0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9eb0 54 .cfa: sp 0 + .ra: x30
STACK CFI 9eb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9ec0 x19: .cfa -16 + ^
STACK CFI 9efc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9f04 4b4 .cfa: sp 0 + .ra: x30
STACK CFI 9f0c .cfa: sp 448 +
STACK CFI 9f1c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9f30 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9f38 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9f48 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a0b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a0b8 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT a3c0 bf4 .cfa: sp 0 + .ra: x30
STACK CFI a3c8 .cfa: sp 160 +
STACK CFI a3d4 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI a3dc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI a3f4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI a444 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI a52c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI a530 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI a91c v8: .cfa -16 + ^
STACK CFI aa40 x23: x23 x24: x24
STACK CFI aa44 x25: x25 x26: x26
STACK CFI aa48 x27: x27 x28: x28
STACK CFI aa4c v8: v8
STACK CFI aa50 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI aad0 x25: x25 x26: x26
STACK CFI ab00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ab08 .cfa: sp 160 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI ab68 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI ab6c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI ab74 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI ab94 v8: .cfa -16 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI ab98 x23: x23 x24: x24
STACK CFI ab9c x27: x27 x28: x28
STACK CFI aba0 v8: v8
STACK CFI aba4 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI ac04 x23: x23 x24: x24
STACK CFI ac08 x27: x27 x28: x28
STACK CFI ac10 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI ac80 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI acbc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI acc0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI ad14 v8: .cfa -16 + ^
STACK CFI ad94 x23: x23 x24: x24
STACK CFI ad9c x27: x27 x28: x28
STACK CFI ada0 v8: v8
STACK CFI ada4 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI adcc v8: .cfa -16 + ^
STACK CFI addc v8: v8 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI ae48 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI af58 x23: x23 x24: x24
STACK CFI af5c x27: x27 x28: x28
STACK CFI af60 v8: .cfa -16 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI af90 v8: v8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI af94 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI af98 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI af9c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI afa0 v8: .cfa -16 + ^
STACK CFI afa4 v8: v8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
