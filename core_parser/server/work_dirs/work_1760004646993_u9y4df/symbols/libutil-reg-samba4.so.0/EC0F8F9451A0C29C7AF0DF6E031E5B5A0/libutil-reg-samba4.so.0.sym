MODULE Linux arm64 EC0F8F9451A0C29C7AF0DF6E031E5B5A0 libutil-reg-samba4.so.0
INFO CODE_ID 948F0FECA0519CC27AF0DF6E031E5B5AEB13126B
PUBLIC b00 0 str_regtype
PUBLIC b60 0 regtype_by_string
PUBLIC be4 0 push_reg_sz
PUBLIC c70 0 push_reg_multi_sz
PUBLIC cf4 0 pull_reg_sz
PUBLIC d90 0 pull_reg_multi_sz
STACK CFI INIT a30 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT a60 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT aa0 48 .cfa: sp 0 + .ra: x30
STACK CFI aa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aac x19: .cfa -16 + ^
STACK CFI ae4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT af0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b00 5c .cfa: sp 0 + .ra: x30
STACK CFI b08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b60 84 .cfa: sp 0 + .ra: x30
STACK CFI b68 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b7c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI bcc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI bdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT be4 84 .cfa: sp 0 + .ra: x30
STACK CFI bec .cfa: sp 48 +
STACK CFI c10 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c64 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT c70 84 .cfa: sp 0 + .ra: x30
STACK CFI c78 .cfa: sp 48 +
STACK CFI c9c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ce8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI cf0 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT cf4 98 .cfa: sp 0 + .ra: x30
STACK CFI cfc .cfa: sp 64 +
STACK CFI d0c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d20 x19: .cfa -16 + ^
STACK CFI d80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI d88 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT d90 98 .cfa: sp 0 + .ra: x30
STACK CFI d98 .cfa: sp 64 +
STACK CFI da8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI dbc x19: .cfa -16 + ^
STACK CFI e1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e24 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
