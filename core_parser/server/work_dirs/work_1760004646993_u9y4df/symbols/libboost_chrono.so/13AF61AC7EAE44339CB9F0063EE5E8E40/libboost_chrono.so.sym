MODULE Linux arm64 13AF61AC7EAE44339CB9F0063EE5E8E40 libboost_chrono.so.1.77.0
INFO CODE_ID AC61AF13AE7E33449CB9F0063EE5E8E4
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 3970 24 0 init_have_lse_atomics
3970 4 45 0
3974 4 46 0
3978 4 45 0
397c 4 46 0
3980 4 47 0
3984 4 47 0
3988 4 48 0
398c 4 47 0
3990 4 48 0
PUBLIC 34e8 0 _init
PUBLIC 37a0 0 void boost::throw_exception<boost::system::system_error>(boost::system::system_error const&)
PUBLIC 3914 0 boost::wrapexcept<boost::system::system_error>::rethrow() const
PUBLIC 3994 0 call_weak_fn
PUBLIC 39b0 0 deregister_tm_clones
PUBLIC 39e0 0 register_tm_clones
PUBLIC 3a20 0 __do_global_dtors_aux
PUBLIC 3a70 0 frame_dummy
PUBLIC 3a80 0 boost::chrono::system_clock::now()
PUBLIC 3ae0 0 boost::chrono::system_clock::to_time_t(boost::chrono::time_point<boost::chrono::system_clock, boost::chrono::duration<long, boost::ratio<1l, 1000000000l> > > const&)
PUBLIC 3b10 0 boost::chrono::system_clock::from_time_t(long)
PUBLIC 3b20 0 boost::chrono::steady_clock::now()
PUBLIC 3b80 0 boost::chrono::system_clock::now(boost::system::error_code&)
PUBLIC 3d90 0 boost::chrono::steady_clock::now(boost::system::error_code&)
PUBLIC 3fa0 0 boost::system::error_category::failed(int) const
PUBLIC 3fb0 0 boost::system::detail::generic_error_category::name() const
PUBLIC 3fc0 0 boost::system::detail::system_error_category::name() const
PUBLIC 3fd0 0 boost::system::detail::system_error_category::default_error_condition(int) const
PUBLIC 3ff0 0 boost::system::detail::interop_error_category::name() const
PUBLIC 4000 0 boost::system::error_category::equivalent(boost::system::error_code const&, int) const
PUBLIC 40e0 0 boost::system::detail::std_category::name() const
PUBLIC 4100 0 boost::system::detail::std_category::message[abi:cxx11](int) const
PUBLIC 4170 0 boost::system::detail::system_error_category::message(int, char*, unsigned long) const
PUBLIC 4180 0 boost::system::detail::generic_error_category::message(int, char*, unsigned long) const
PUBLIC 4190 0 boost::system::detail::std_category::~std_category()
PUBLIC 41b0 0 boost::system::detail::std_category::~std_category()
PUBLIC 41f0 0 boost::system::error_category::equivalent(int, boost::system::error_condition const&) const
PUBLIC 4280 0 boost::system::detail::system_error_category::message[abi:cxx11](int) const
PUBLIC 43a0 0 boost::system::detail::generic_error_category::message[abi:cxx11](int) const
PUBLIC 44c0 0 boost::system::error_category::default_error_condition(int) const
PUBLIC 4560 0 boost::system::system_error::~system_error()
PUBLIC 45b0 0 boost::system::system_error::~system_error()
PUBLIC 4610 0 non-virtual thunk to boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 46a0 0 non-virtual thunk to boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 4730 0 boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 47c0 0 boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 4850 0 non-virtual thunk to boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 48f0 0 non-virtual thunk to boost::wrapexcept<boost::system::system_error>::~wrapexcept()
PUBLIC 4990 0 boost::wrapexcept<boost::system::system_error>::clone() const
PUBLIC 4cb0 0 boost::system::detail::std_category::default_error_condition(int) const
PUBLIC 4e70 0 boost::system::detail::std_category::equivalent(std::error_code const&, int) const
PUBLIC 53a0 0 boost::system::detail::std_category::equivalent(int, std::error_condition const&) const
PUBLIC 5980 0 boost::system::detail::snprintf(char*, unsigned long, char const*, ...)
PUBLIC 5a30 0 boost::system::detail::interop_error_category::message(int, char*, unsigned long) const
PUBLIC 5a70 0 boost::system::error_category::message(int, char*, unsigned long) const
PUBLIC 5b70 0 boost::system::detail::interop_error_category::message[abi:cxx11](int) const
PUBLIC 5cd0 0 boost::wrapexcept<boost::system::system_error>::wrapexcept(boost::wrapexcept<boost::system::system_error> const&)
PUBLIC 5e90 0 boost::system::system_error::what() const
PUBLIC 60c0 0 boost::chrono::thread_clock::now()
PUBLIC 6120 0 boost::chrono::thread_clock::now(boost::system::error_code&)
PUBLIC 6330 0 boost::system::error_code::error_code(int, boost::system::error_category const&) [clone .constprop.0]
PUBLIC 6360 0 boost::chrono::process_real_cpu_clock::now()
PUBLIC 6420 0 boost::chrono::process_user_cpu_clock::now()
PUBLIC 64f0 0 boost::chrono::process_system_cpu_clock::now()
PUBLIC 65c0 0 boost::chrono::process_cpu_clock::now()
PUBLIC 6680 0 boost::chrono::process_real_cpu_clock::now(boost::system::error_code&)
PUBLIC 68a0 0 boost::chrono::process_user_cpu_clock::now(boost::system::error_code&)
PUBLIC 6ac0 0 boost::chrono::process_system_cpu_clock::now(boost::system::error_code&)
PUBLIC 6ce0 0 boost::chrono::process_cpu_clock::now(boost::system::error_code&)
PUBLIC 6f60 0 __aarch64_cas8_acq_rel
PUBLIC 6f94 0 _fini
STACK CFI INIT 39b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 39e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a20 48 .cfa: sp 0 + .ra: x30
STACK CFI 3a24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a2c x19: .cfa -16 + ^
STACK CFI 3a64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3a70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3fa0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3fb0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3fc0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3fd0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ff0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4000 d4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 40e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4100 64 .cfa: sp 0 + .ra: x30
STACK CFI 4104 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4110 x19: .cfa -32 + ^
STACK CFI 415c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4160 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4170 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4180 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4190 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 41b0 38 .cfa: sp 0 + .ra: x30
STACK CFI 41b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41c4 x19: .cfa -16 + ^
STACK CFI 41e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 41f0 88 .cfa: sp 0 + .ra: x30
STACK CFI 41f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41fc x19: .cfa -16 + ^
STACK CFI 4224 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4228 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 425c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4260 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4274 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4280 114 .cfa: sp 0 + .ra: x30
STACK CFI 4284 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 4298 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 42a0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4320 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4324 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 43a0 114 .cfa: sp 0 + .ra: x30
STACK CFI 43a4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 43b8 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 43c0 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 4440 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4444 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 44c0 94 .cfa: sp 0 + .ra: x30
STACK CFI 44c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44d8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4538 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4560 50 .cfa: sp 0 + .ra: x30
STACK CFI 4564 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 457c x19: .cfa -16 + ^
STACK CFI 45ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 45b0 5c .cfa: sp 0 + .ra: x30
STACK CFI 45b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45cc x19: .cfa -16 + ^
STACK CFI 4608 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4610 84 .cfa: sp 0 + .ra: x30
STACK CFI 4614 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4624 x19: .cfa -16 + ^
STACK CFI 4690 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 46a0 88 .cfa: sp 0 + .ra: x30
STACK CFI 46a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 46b4 x19: .cfa -16 + ^
STACK CFI 4724 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4730 84 .cfa: sp 0 + .ra: x30
STACK CFI 4734 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4744 x19: .cfa -16 + ^
STACK CFI 47b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 47c0 90 .cfa: sp 0 + .ra: x30
STACK CFI 47c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47d4 x19: .cfa -16 + ^
STACK CFI 484c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4850 98 .cfa: sp 0 + .ra: x30
STACK CFI 4854 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4864 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 48e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 48f0 9c .cfa: sp 0 + .ra: x30
STACK CFI 48f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4904 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4988 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4990 318 .cfa: sp 0 + .ra: x30
STACK CFI 4994 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 49a4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 49b8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 4b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4b5c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4cb0 1bc .cfa: sp 0 + .ra: x30
STACK CFI 4cb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4ccc x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4d38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4d84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4dac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4db0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4e70 530 .cfa: sp 0 + .ra: x30
STACK CFI 4e74 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4e7c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4e94 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4eb4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4fe0 x25: x25 x26: x26
STACK CFI 4fe8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 503c x25: x25 x26: x26
STACK CFI 5044 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5048 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 50c0 x25: x25 x26: x26
STACK CFI 50f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 50fc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 5110 x25: x25 x26: x26
STACK CFI 5114 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 51a0 x25: x25 x26: x26
STACK CFI 524c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5260 x25: x25 x26: x26
STACK CFI 5274 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5328 x25: x25 x26: x26
STACK CFI 533c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5350 x25: x25 x26: x26
STACK CFI 536c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5380 x25: x25 x26: x26
STACK CFI 5384 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 53a0 5d4 .cfa: sp 0 + .ra: x30
STACK CFI 53a4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 53ac x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 53c4 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5684 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5688 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 5748 x25: .cfa -48 + ^
STACK CFI 577c x25: x25
STACK CFI 593c x25: .cfa -48 + ^
STACK CFI 5958 x25: x25
STACK CFI INIT 5980 a4 .cfa: sp 0 + .ra: x30
STACK CFI 5984 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 5a1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5a20 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI INIT 5a30 3c .cfa: sp 0 + .ra: x30
STACK CFI 5a34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a4c x19: .cfa -16 + ^
STACK CFI 5a68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5a70 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5a74 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5a84 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5ab4 x21: .cfa -64 + ^
STACK CFI 5af8 x21: x21
STACK CFI 5b20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5b24 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI 5b28 x21: x21
STACK CFI 5b38 x21: .cfa -64 + ^
STACK CFI 5b60 x21: x21
STACK CFI INIT 5b70 158 .cfa: sp 0 + .ra: x30
STACK CFI 5b74 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5b88 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5b90 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5c2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5c30 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 3a80 60 .cfa: sp 0 + .ra: x30
STACK CFI 3a8c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3ad8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3adc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3ae0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b10 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3b20 60 .cfa: sp 0 + .ra: x30
STACK CFI 3b2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3b78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3b7c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 37a0 174 .cfa: sp 0 + .ra: x30
STACK CFI 37a4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 37b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 37c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 3b80 20c .cfa: sp 0 + .ra: x30
STACK CFI 3b84 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 3b98 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3bb4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3c24 x21: x21 x22: x22
STACK CFI 3c70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c74 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x29: .cfa -160 + ^
STACK CFI 3ca4 x23: .cfa -112 + ^
STACK CFI 3d34 x21: x21 x22: x22 x23: x23
STACK CFI 3d38 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3d3c x23: .cfa -112 + ^
STACK CFI INIT 3d90 210 .cfa: sp 0 + .ra: x30
STACK CFI 3d94 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 3da4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 3dc8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3e38 x21: x21 x22: x22
STACK CFI 3e84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e88 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x29: .cfa -160 + ^
STACK CFI 3eb8 x23: .cfa -112 + ^
STACK CFI 3f48 x21: x21 x22: x22 x23: x23
STACK CFI 3f4c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 3f50 x23: .cfa -112 + ^
STACK CFI INIT 5cd0 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 5cd4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5ce4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5cf0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5d04 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5d0c x25: .cfa -32 + ^
STACK CFI 5df8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5dfc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3914 54 .cfa: sp 0 + .ra: x30
STACK CFI 3918 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3920 x19: .cfa -16 + ^
STACK CFI INIT 5e90 224 .cfa: sp 0 + .ra: x30
STACK CFI 5e94 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5e9c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5eac x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5ef4 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 60c0 60 .cfa: sp 0 + .ra: x30
STACK CFI 60cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6118 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 611c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6120 210 .cfa: sp 0 + .ra: x30
STACK CFI 6124 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6134 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 6158 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 61c8 x21: x21 x22: x22
STACK CFI 6214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6218 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x29: .cfa -160 + ^
STACK CFI 6248 x23: .cfa -112 + ^
STACK CFI 62d8 x21: x21 x22: x22 x23: x23
STACK CFI 62dc x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 62e0 x23: .cfa -112 + ^
STACK CFI INIT 6330 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6360 bc .cfa: sp 0 + .ra: x30
STACK CFI 636c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 63b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 63b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 63b8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6400 x19: x19 x20: x20
STACK CFI 6404 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6408 x19: x19 x20: x20
STACK CFI 640c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6414 x19: x19 x20: x20
STACK CFI 6418 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 6420 c4 .cfa: sp 0 + .ra: x30
STACK CFI 642c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6470 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6474 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6488 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 64c8 x19: x19 x20: x20
STACK CFI 64cc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 64d0 x19: x19 x20: x20
STACK CFI 64d4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 64dc x19: x19 x20: x20
STACK CFI 64e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 64f0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 64fc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6540 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6544 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6558 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6598 x19: x19 x20: x20
STACK CFI 659c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 65a0 x19: x19 x20: x20
STACK CFI 65a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 65ac x19: x19 x20: x20
STACK CFI 65b0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 65c0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 65c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 65d4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6660 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6664 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6680 218 .cfa: sp 0 + .ra: x30
STACK CFI 6684 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6694 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 670c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6710 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x29: .cfa -160 + ^
STACK CFI 6790 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 67e8 x21: x21 x22: x22
STACK CFI 67f8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 67fc x21: x21 x22: x22
STACK CFI 6808 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI INIT 68a0 220 .cfa: sp 0 + .ra: x30
STACK CFI 68a4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 68b4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 692c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6930 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x29: .cfa -160 + ^
STACK CFI 69b8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6a10 x21: x21 x22: x22
STACK CFI 6a20 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6a24 x21: x21 x22: x22
STACK CFI 6a30 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI INIT 6ac0 220 .cfa: sp 0 + .ra: x30
STACK CFI 6ac4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6ad4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 6b4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6b50 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x29: .cfa -160 + ^
STACK CFI 6bd8 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6c30 x21: x21 x22: x22
STACK CFI 6c40 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6c44 x21: x21 x22: x22
STACK CFI 6c50 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI INIT 6ce0 27c .cfa: sp 0 + .ra: x30
STACK CFI 6ce4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 6cf4 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 6d00 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 6d7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6d80 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x29: .cfa -160 + ^
STACK CFI INIT 6f60 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3970 24 .cfa: sp 0 + .ra: x30
STACK CFI 3974 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 398c .cfa: sp 0 + .ra: .ra x29: x29
