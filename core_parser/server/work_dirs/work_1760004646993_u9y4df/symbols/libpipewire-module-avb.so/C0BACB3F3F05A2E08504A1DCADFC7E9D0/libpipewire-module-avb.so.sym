MODULE Linux arm64 C0BACB3F3F05A2E08504A1DCADFC7E9D0 libpipewire-module-avb.so
INFO CODE_ID 3FCBBAC0053FE0A28504A1DCADFC7E9D93879A56
PUBLIC f970 0 pipewire__module_init
STACK CFI INIT 2860 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2890 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 28d0 48 .cfa: sp 0 + .ra: x30
STACK CFI 28d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 28dc x19: .cfa -16 + ^
STACK CFI 2914 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2920 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2930 340 .cfa: sp 0 + .ra: x30
STACK CFI 2938 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2a24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2a30 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2bc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2bcc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2c70 1c .cfa: sp 0 + .ra: x30
STACK CFI 2c78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2c90 1c .cfa: sp 0 + .ra: x30
STACK CFI 2c98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2ca4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2cb0 40 .cfa: sp 0 + .ra: x30
STACK CFI 2cb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2ce8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2cf0 2c .cfa: sp 0 + .ra: x30
STACK CFI 2cf8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2d20 60 .cfa: sp 0 + .ra: x30
STACK CFI 2d28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2d80 64 .cfa: sp 0 + .ra: x30
STACK CFI 2d88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2ddc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2de4 58 .cfa: sp 0 + .ra: x30
STACK CFI 2dec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2e34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2e40 48 .cfa: sp 0 + .ra: x30
STACK CFI 2e48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2e80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2e90 30 .cfa: sp 0 + .ra: x30
STACK CFI 2e98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2eb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2ec0 3c .cfa: sp 0 + .ra: x30
STACK CFI 2ec8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2edc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2f00 38 .cfa: sp 0 + .ra: x30
STACK CFI 2f08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2f40 50 .cfa: sp 0 + .ra: x30
STACK CFI 2f48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2f90 30 .cfa: sp 0 + .ra: x30
STACK CFI 2f98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2fb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2fc0 3c .cfa: sp 0 + .ra: x30
STACK CFI 2fc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2fdc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3000 50 .cfa: sp 0 + .ra: x30
STACK CFI 3008 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3010 x19: .cfa -16 + ^
STACK CFI 3048 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3050 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 3058 .cfa: sp 192 +
STACK CFI 3068 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3074 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 30a4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 30ac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 30b4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 30b8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 32b0 x19: x19 x20: x20
STACK CFI 32b4 x21: x21 x22: x22
STACK CFI 32b8 x23: x23 x24: x24
STACK CFI 32bc x25: x25 x26: x26
STACK CFI 32e4 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 32ec .cfa: sp 192 + .ra: .cfa -88 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 32f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 32f4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 32f8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 32fc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 3300 50 .cfa: sp 0 + .ra: x30
STACK CFI 3308 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3310 x19: .cfa -16 + ^
STACK CFI 3348 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3350 50 .cfa: sp 0 + .ra: x30
STACK CFI 3358 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3360 x19: .cfa -16 + ^
STACK CFI 3398 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 33a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 33a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 33b0 x19: .cfa -16 + ^
STACK CFI 33e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 33f0 50 .cfa: sp 0 + .ra: x30
STACK CFI 33f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3400 x19: .cfa -16 + ^
STACK CFI 3438 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3440 7c .cfa: sp 0 + .ra: x30
STACK CFI 3448 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3450 x19: .cfa -16 + ^
STACK CFI 34b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 34c0 50 .cfa: sp 0 + .ra: x30
STACK CFI 34c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 34d0 x19: .cfa -16 + ^
STACK CFI 3508 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3510 7c .cfa: sp 0 + .ra: x30
STACK CFI 3518 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3520 x19: .cfa -16 + ^
STACK CFI 3584 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3590 18 .cfa: sp 0 + .ra: x30
STACK CFI 3598 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 35b0 830 .cfa: sp 0 + .ra: x30
STACK CFI 35b8 .cfa: sp 176 +
STACK CFI 35c4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 35cc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 35e0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3680 .cfa: sp 176 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3de0 94 .cfa: sp 0 + .ra: x30
STACK CFI 3de8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3df0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3e08 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3e14 x23: .cfa -16 + ^
STACK CFI 3e5c x21: x21 x22: x22
STACK CFI 3e60 x23: x23
STACK CFI 3e6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3e74 158 .cfa: sp 0 + .ra: x30
STACK CFI 3e7c .cfa: sp 128 +
STACK CFI 3e80 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3e88 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3e94 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3e9c x23: .cfa -16 + ^
STACK CFI 3fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3fc8 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3fd0 cc .cfa: sp 0 + .ra: x30
STACK CFI 3fd8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3fe0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3ff8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4004 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4084 x21: x21 x22: x22
STACK CFI 4088 x23: x23 x24: x24
STACK CFI 4094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 40a0 bc .cfa: sp 0 + .ra: x30
STACK CFI 40a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 40b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 40c8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 40d4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4144 x21: x21 x22: x22
STACK CFI 4148 x23: x23 x24: x24
STACK CFI 4154 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4160 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4168 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4170 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4188 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 41a0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 41ec x21: x21 x22: x22
STACK CFI 41f0 x23: x23 x24: x24
STACK CFI 41fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4204 94 .cfa: sp 0 + .ra: x30
STACK CFI 420c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4214 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 422c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4238 x23: .cfa -16 + ^
STACK CFI 4280 x21: x21 x22: x22
STACK CFI 4284 x23: x23
STACK CFI 4290 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 42a0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 42a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 42b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 42c8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 42e0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 432c x21: x21 x22: x22
STACK CFI 4330 x23: x23 x24: x24
STACK CFI 433c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4344 a4 .cfa: sp 0 + .ra: x30
STACK CFI 434c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4354 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 436c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4384 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 43d0 x21: x21 x22: x22
STACK CFI 43d4 x23: x23 x24: x24
STACK CFI 43e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 43f0 90 .cfa: sp 0 + .ra: x30
STACK CFI 43f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4450 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4458 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4480 84 .cfa: sp 0 + .ra: x30
STACK CFI 4488 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4490 x19: .cfa -16 + ^
STACK CFI 44fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4504 128 .cfa: sp 0 + .ra: x30
STACK CFI 450c .cfa: sp 112 +
STACK CFI 451c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4524 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4628 .cfa: sp 112 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4630 ec .cfa: sp 0 + .ra: x30
STACK CFI 4638 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4640 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 464c x21: .cfa -16 + ^
STACK CFI 470c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4714 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4720 240 .cfa: sp 0 + .ra: x30
STACK CFI 4728 .cfa: sp 256 +
STACK CFI 4734 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 473c x25: .cfa -16 + ^
STACK CFI 4748 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4750 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 475c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4860 .cfa: sp 256 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4960 240 .cfa: sp 0 + .ra: x30
STACK CFI 4968 .cfa: sp 256 +
STACK CFI 4974 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 497c x25: .cfa -16 + ^
STACK CFI 4988 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4990 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 499c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4a98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4aa0 .cfa: sp 256 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4ba0 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 4ba8 .cfa: sp 240 +
STACK CFI 4bb8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4bc0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4bd4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4c5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4c64 .cfa: sp 240 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4ea0 88 .cfa: sp 0 + .ra: x30
STACK CFI 4eb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ebc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4f10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4f20 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4f30 88 .cfa: sp 0 + .ra: x30
STACK CFI 4f40 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4fa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4fb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4fc0 33c .cfa: sp 0 + .ra: x30
STACK CFI 4fc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4fd4 x19: .cfa -16 + ^
STACK CFI 501c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5024 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5300 500 .cfa: sp 0 + .ra: x30
STACK CFI 5308 .cfa: sp 240 +
STACK CFI 5314 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 531c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5328 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5330 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5410 .cfa: sp 240 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5800 14c .cfa: sp 0 + .ra: x30
STACK CFI 5808 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5814 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5860 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5868 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 58b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5950 2c0 .cfa: sp 0 + .ra: x30
STACK CFI 5958 .cfa: sp 256 +
STACK CFI 5964 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 596c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5978 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5980 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 598c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5aa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5aa8 .cfa: sp 256 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5c10 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 5c18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5c24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5c30 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5c9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5cf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5d1c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5ef0 1ec .cfa: sp 0 + .ra: x30
STACK CFI 5ef8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5f04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5f10 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5f5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5f64 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5fcc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 60e0 cc .cfa: sp 0 + .ra: x30
STACK CFI 60e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 60f0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 60f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6108 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 61a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 61b0 4b8 .cfa: sp 0 + .ra: x30
STACK CFI 61b8 .cfa: sp 80 +
STACK CFI 61bc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 61c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 61d4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 626c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6274 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 63a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 63c4 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6670 718 .cfa: sp 0 + .ra: x30
STACK CFI 6678 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6688 .cfa: sp 1712 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 6d70 .cfa: sp 48 +
STACK CFI 6d7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6d84 .cfa: sp 1712 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6d90 f4 .cfa: sp 0 + .ra: x30
STACK CFI 6d98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6da8 x19: .cfa -16 + ^
STACK CFI 6df8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6e00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6e7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6e84 568 .cfa: sp 0 + .ra: x30
STACK CFI 6e8c .cfa: sp 272 +
STACK CFI 6e9c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6ea4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6ef8 .cfa: sp 272 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 6efc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6f28 x21: x21 x22: x22
STACK CFI 6f2c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6f38 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6f68 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6fb8 x21: x21 x22: x22
STACK CFI 6fc0 x23: x23 x24: x24
STACK CFI 6fc4 x25: x25 x26: x26
STACK CFI 6fc8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6fdc x21: x21 x22: x22
STACK CFI 6fe0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6fe4 x21: x21 x22: x22
STACK CFI 6fe8 x23: x23 x24: x24
STACK CFI 6fec x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7098 x21: x21 x22: x22
STACK CFI 709c x23: x23 x24: x24
STACK CFI 70a0 x25: x25 x26: x26
STACK CFI 70a4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 70e8 x21: x21 x22: x22
STACK CFI 70ec x23: x23 x24: x24
STACK CFI 70f0 x25: x25 x26: x26
STACK CFI 70f4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7228 x21: x21 x22: x22
STACK CFI 722c x23: x23 x24: x24
STACK CFI 7230 x25: x25 x26: x26
STACK CFI 7234 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 73c0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 73c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 73c8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 73cc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 73dc x21: x21 x22: x22
STACK CFI 73e4 x23: x23 x24: x24
STACK CFI 73e8 x25: x25 x26: x26
STACK CFI INIT 73f0 590 .cfa: sp 0 + .ra: x30
STACK CFI 73f8 .cfa: sp 288 +
STACK CFI 73fc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7404 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7410 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 743c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7460 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 752c x27: x27 x28: x28
STACK CFI 75d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 75dc .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 7714 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 77a0 x27: x27 x28: x28
STACK CFI 7878 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 78dc x27: x27 x28: x28
STACK CFI 78e0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7978 x27: x27 x28: x28
STACK CFI 797c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 7980 a0 .cfa: sp 0 + .ra: x30
STACK CFI 7988 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7990 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 79b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 79bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 79fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7a04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7a18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7a20 a4 .cfa: sp 0 + .ra: x30
STACK CFI 7a28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7a30 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7a54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7a5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7aa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7aa8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7abc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7ac4 24c .cfa: sp 0 + .ra: x30
STACK CFI 7acc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7ad0 .cfa: x29 64 +
STACK CFI 7ad4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7aec x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7b78 .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7d10 1ac .cfa: sp 0 + .ra: x30
STACK CFI 7d18 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7d20 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7d34 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7d48 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7d5c x25: .cfa -16 + ^
STACK CFI 7e20 x23: x23 x24: x24
STACK CFI 7e24 x25: x25
STACK CFI 7e30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7e38 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7ec0 4c .cfa: sp 0 + .ra: x30
STACK CFI 7ec8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ed4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7f04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7f10 e4 .cfa: sp 0 + .ra: x30
STACK CFI 7f18 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7f1c .cfa: x29 32 +
STACK CFI 7f28 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7fe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7fe8 .cfa: x29 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7ff4 194 .cfa: sp 0 + .ra: x30
STACK CFI 8018 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8020 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 802c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8070 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 80d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 80e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8130 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8190 fc .cfa: sp 0 + .ra: x30
STACK CFI 8198 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 819c .cfa: x29 48 +
STACK CFI 81a8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 81b8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8280 .cfa: x29 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8290 a4 .cfa: sp 0 + .ra: x30
STACK CFI 8298 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 82fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 830c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8310 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8320 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8324 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8334 18 .cfa: sp 0 + .ra: x30
STACK CFI 833c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8344 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8350 124 .cfa: sp 0 + .ra: x30
STACK CFI 8358 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8368 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8370 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 840c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8414 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 846c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8474 1fc .cfa: sp 0 + .ra: x30
STACK CFI 847c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8494 .cfa: sp 2144 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 85c4 .cfa: sp 80 +
STACK CFI 85d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 85e0 .cfa: sp 2144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 8640 .cfa: sp 80 +
STACK CFI 8664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 866c .cfa: sp 2144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8670 184 .cfa: sp 0 + .ra: x30
STACK CFI 8678 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8684 .cfa: sp 2096 + x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8780 .cfa: sp 32 +
STACK CFI 8788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8790 .cfa: sp 2096 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 87b8 .cfa: sp 32 +
STACK CFI 87c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 87c8 .cfa: sp 2096 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 87f4 270 .cfa: sp 0 + .ra: x30
STACK CFI 87fc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 880c .cfa: sp 2160 + x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8840 x23: .cfa -48 + ^
STACK CFI 8848 x26: .cfa -24 + ^
STACK CFI 885c x19: .cfa -80 + ^
STACK CFI 8864 x20: .cfa -72 + ^
STACK CFI 886c x24: .cfa -40 + ^
STACK CFI 8878 x25: .cfa -32 + ^
STACK CFI 89b0 x19: x19
STACK CFI 89b4 x20: x20
STACK CFI 89b8 x23: x23
STACK CFI 89bc x24: x24
STACK CFI 89c0 x25: x25
STACK CFI 89c4 x26: x26
STACK CFI 89e4 .cfa: sp 96 +
STACK CFI 89f0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 89f8 .cfa: sp 2160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8a40 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 8a4c x19: .cfa -80 + ^
STACK CFI 8a50 x20: .cfa -72 + ^
STACK CFI 8a54 x23: .cfa -48 + ^
STACK CFI 8a58 x24: .cfa -40 + ^
STACK CFI 8a5c x25: .cfa -32 + ^
STACK CFI 8a60 x26: .cfa -24 + ^
STACK CFI INIT 8a64 270 .cfa: sp 0 + .ra: x30
STACK CFI 8a6c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8a7c .cfa: sp 2160 + x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8ab0 x23: .cfa -48 + ^
STACK CFI 8ab8 x26: .cfa -24 + ^
STACK CFI 8acc x19: .cfa -80 + ^
STACK CFI 8ad4 x20: .cfa -72 + ^
STACK CFI 8adc x24: .cfa -40 + ^
STACK CFI 8ae8 x25: .cfa -32 + ^
STACK CFI 8c20 x19: x19
STACK CFI 8c24 x20: x20
STACK CFI 8c28 x23: x23
STACK CFI 8c2c x24: x24
STACK CFI 8c30 x25: x25
STACK CFI 8c34 x26: x26
STACK CFI 8c54 .cfa: sp 96 +
STACK CFI 8c60 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 8c68 .cfa: sp 2160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8cb0 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 8cbc x19: .cfa -80 + ^
STACK CFI 8cc0 x20: .cfa -72 + ^
STACK CFI 8cc4 x23: .cfa -48 + ^
STACK CFI 8cc8 x24: .cfa -40 + ^
STACK CFI 8ccc x25: .cfa -32 + ^
STACK CFI 8cd0 x26: .cfa -24 + ^
STACK CFI INIT 8cd4 274 .cfa: sp 0 + .ra: x30
STACK CFI 8cdc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8cf4 .cfa: sp 1120 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 8e30 .cfa: sp 80 +
STACK CFI 8e44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8e4c .cfa: sp 1120 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8f50 57c .cfa: sp 0 + .ra: x30
STACK CFI 8f58 .cfa: sp 256 +
STACK CFI 8f64 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8f70 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8f78 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9248 .cfa: sp 256 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 94d0 254 .cfa: sp 0 + .ra: x30
STACK CFI 94d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 94e8 .cfa: sp 2176 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9570 .cfa: sp 48 +
STACK CFI 957c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9584 .cfa: sp 2176 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9724 28 .cfa: sp 0 + .ra: x30
STACK CFI 972c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9738 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9740 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9744 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9750 370 .cfa: sp 0 + .ra: x30
STACK CFI 9758 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9764 .cfa: sp 2144 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 97ec .cfa: sp 64 +
STACK CFI 97f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 97fc .cfa: sp 2144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 9890 x21: .cfa -32 + ^
STACK CFI 9894 x22: .cfa -24 + ^
STACK CFI 9920 v8: .cfa -16 + ^
STACK CFI 99b0 x21: x21
STACK CFI 99b4 x22: x22
STACK CFI 99b8 v8: v8
STACK CFI 99bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 99c0 x21: x21
STACK CFI 99c4 x22: x22
STACK CFI 99fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9a60 x21: x21
STACK CFI 9a64 x22: x22
STACK CFI 9a68 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9a7c v8: .cfa -16 + ^
STACK CFI 9ab0 v8: v8 x21: x21 x22: x22
STACK CFI 9ab4 x21: .cfa -32 + ^
STACK CFI 9ab8 x22: .cfa -24 + ^
STACK CFI 9abc v8: .cfa -16 + ^
STACK CFI INIT 9ac0 28 .cfa: sp 0 + .ra: x30
STACK CFI 9ac8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9ad4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9adc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9ae0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9af0 21c .cfa: sp 0 + .ra: x30
STACK CFI 9af8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9b08 .cfa: sp 2128 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9b98 .cfa: sp 48 +
STACK CFI 9ba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9bac .cfa: sp 2128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9d10 28 .cfa: sp 0 + .ra: x30
STACK CFI 9d18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9d24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9d2c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9d30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9d40 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 9d48 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9d58 .cfa: sp 2128 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 9de4 .cfa: sp 48 +
STACK CFI 9df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9df8 .cfa: sp 2128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9ef4 28 .cfa: sp 0 + .ra: x30
STACK CFI 9efc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9f08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9f10 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9f14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9f20 21c .cfa: sp 0 + .ra: x30
STACK CFI 9f28 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9f38 .cfa: sp 2128 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9fc8 .cfa: sp 48 +
STACK CFI 9fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9fdc .cfa: sp 2128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT a140 28 .cfa: sp 0 + .ra: x30
STACK CFI a148 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a154 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a15c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a160 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a170 188 .cfa: sp 0 + .ra: x30
STACK CFI a178 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a180 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a188 x21: .cfa -16 + ^
STACK CFI a228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a230 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT a300 2c8c .cfa: sp 0 + .ra: x30
STACK CFI a308 .cfa: sp 480 +
STACK CFI a31c .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI a334 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI a33c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI a354 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI ab00 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI ab08 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT cf90 244 .cfa: sp 0 + .ra: x30
STACK CFI cf98 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI cfa0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI cfb4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI cfbc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI cfc0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d05c x21: x21 x22: x22
STACK CFI d060 x23: x23 x24: x24
STACK CFI d06c x25: x25 x26: x26
STACK CFI d070 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d078 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI d0a8 x27: .cfa -16 + ^
STACK CFI d0f4 x27: x27
STACK CFI d110 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI d140 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d148 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI d160 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d18c x27: .cfa -16 + ^
STACK CFI d1a0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI d1ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT d1d4 2dc .cfa: sp 0 + .ra: x30
STACK CFI d1dc .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d1e8 .cfa: sp 2144 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d26c .cfa: sp 80 +
STACK CFI d274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d27c .cfa: sp 2144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI d2a8 x21: .cfa -48 + ^
STACK CFI d2ac x22: .cfa -40 + ^
STACK CFI d310 x21: x21
STACK CFI d314 x22: x22
STACK CFI d3d0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d3f0 x23: .cfa -32 + ^
STACK CFI d3f4 x24: .cfa -24 + ^
STACK CFI d3fc x25: .cfa -16 + ^
STACK CFI d43c x21: x21
STACK CFI d440 x22: x22
STACK CFI d444 x23: x23
STACK CFI d448 x24: x24
STACK CFI d44c x25: x25
STACK CFI d450 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d47c x21: x21
STACK CFI d480 x22: x22
STACK CFI d484 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI d498 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI d49c x21: .cfa -48 + ^
STACK CFI d4a0 x22: .cfa -40 + ^
STACK CFI d4a4 x23: .cfa -32 + ^
STACK CFI d4a8 x24: .cfa -24 + ^
STACK CFI d4ac x25: .cfa -16 + ^
STACK CFI INIT d4b0 28 .cfa: sp 0 + .ra: x30
STACK CFI d4b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI d4c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI d4cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI d4d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d4e0 3e8 .cfa: sp 0 + .ra: x30
STACK CFI d4e8 .cfa: sp 144 +
STACK CFI d4f4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d4fc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d51c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d528 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d530 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d534 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d740 x21: x21 x22: x22
STACK CFI d744 x23: x23 x24: x24
STACK CFI d748 x25: x25 x26: x26
STACK CFI d74c x27: x27 x28: x28
STACK CFI d774 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d77c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI d7e0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI d838 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d85c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI d874 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d8b4 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI d8b8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d8bc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d8c0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d8c4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT d8d0 824 .cfa: sp 0 + .ra: x30
STACK CFI d8d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d8e8 .cfa: sp 1232 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d95c x23: .cfa -48 + ^
STACK CFI d964 x24: .cfa -40 + ^
STACK CFI db30 x25: .cfa -32 + ^
STACK CFI db34 x26: .cfa -24 + ^
STACK CFI de4c x23: x23
STACK CFI de50 x24: x24
STACK CFI de54 x25: x25
STACK CFI de58 x26: x26
STACK CFI de78 .cfa: sp 96 +
STACK CFI de84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI de8c .cfa: sp 1232 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI def0 x25: .cfa -32 + ^
STACK CFI def4 x26: .cfa -24 + ^
STACK CFI df94 x27: .cfa -16 + ^
STACK CFI e000 x27: x27
STACK CFI e084 x25: x25
STACK CFI e088 x26: x26
STACK CFI e0a8 x23: x23
STACK CFI e0b0 x24: x24
STACK CFI e0b4 .cfa: sp 96 +
STACK CFI e0c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e0c8 .cfa: sp 1232 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI e0cc x23: .cfa -48 + ^
STACK CFI e0d0 x24: .cfa -40 + ^
STACK CFI e0d4 x25: .cfa -32 + ^
STACK CFI e0d8 x26: .cfa -24 + ^
STACK CFI e0dc x27: .cfa -16 + ^
STACK CFI e0e0 x25: x25 x26: x26 x27: x27
STACK CFI e0e4 x25: .cfa -32 + ^
STACK CFI e0e8 x26: .cfa -24 + ^
STACK CFI e0ec x27: .cfa -16 + ^
STACK CFI INIT e0f4 c8 .cfa: sp 0 + .ra: x30
STACK CFI e0fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e104 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e110 x21: .cfa -16 + ^
STACK CFI e1ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI e1b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT e1c0 178 .cfa: sp 0 + .ra: x30
STACK CFI e1c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e1cc .cfa: x29 64 +
STACK CFI e1d8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e1f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI e284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI e28c .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT e340 144 .cfa: sp 0 + .ra: x30
STACK CFI e348 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e350 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e358 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI e39c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e3a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI e47c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT e484 828 .cfa: sp 0 + .ra: x30
STACK CFI e48c .cfa: sp 320 +
STACK CFI e490 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e498 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e4bc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e63c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e644 .cfa: sp 320 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI e6d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e75c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI e7cc x25: x25 x26: x26
STACK CFI e7d8 x23: x23 x24: x24
STACK CFI e804 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e80c .cfa: sp 320 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI e88c x23: x23 x24: x24
STACK CFI e890 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e954 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI e9cc x25: x25 x26: x26
STACK CFI ea28 x23: x23 x24: x24
STACK CFI ea2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ea34 .cfa: sp 320 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI eae0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI eafc x25: x25 x26: x26
STACK CFI eb98 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI ebc4 x25: x25 x26: x26
STACK CFI ec14 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI ec2c x25: x25 x26: x26
STACK CFI ec74 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI eca0 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI eca4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI eca8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT ecb0 1a8 .cfa: sp 0 + .ra: x30
STACK CFI ecb8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ecbc .cfa: x29 64 +
STACK CFI ecc8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ecd4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI ece0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI ed74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI ed7c .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT ee60 16c .cfa: sp 0 + .ra: x30
STACK CFI ee68 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ee74 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI eebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI eec4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI efc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT efd0 358 .cfa: sp 0 + .ra: x30
STACK CFI efd8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI efe4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI efec .cfa: sp 832 +
STACK CFI f050 x21: .cfa -64 + ^
STACK CFI f058 x22: .cfa -56 + ^
STACK CFI f09c x21: x21
STACK CFI f0a0 x22: x22
STACK CFI f0c0 .cfa: sp 96 +
STACK CFI f0c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f0d0 .cfa: sp 832 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI f114 x23: .cfa -48 + ^
STACK CFI f11c x24: .cfa -40 + ^
STACK CFI f124 x25: .cfa -32 + ^
STACK CFI f140 x26: .cfa -24 + ^
STACK CFI f148 x27: .cfa -16 + ^
STACK CFI f158 x28: .cfa -8 + ^
STACK CFI f28c x21: x21
STACK CFI f290 x22: x22
STACK CFI f294 x23: x23
STACK CFI f298 x24: x24
STACK CFI f29c x25: x25
STACK CFI f2a0 x26: x26
STACK CFI f2a4 x27: x27
STACK CFI f2a8 x28: x28
STACK CFI f2ac x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f304 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f308 x21: .cfa -64 + ^
STACK CFI f30c x22: .cfa -56 + ^
STACK CFI f310 x23: .cfa -48 + ^
STACK CFI f314 x24: .cfa -40 + ^
STACK CFI f318 x25: .cfa -32 + ^
STACK CFI f31c x26: .cfa -24 + ^
STACK CFI f320 x27: .cfa -16 + ^
STACK CFI f324 x28: .cfa -8 + ^
STACK CFI INIT f330 158 .cfa: sp 0 + .ra: x30
STACK CFI f338 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f34c .cfa: sp 592 + x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI f434 .cfa: sp 32 +
STACK CFI f43c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f444 .cfa: sp 592 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT f490 ec .cfa: sp 0 + .ra: x30
STACK CFI f498 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f4a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f4d8 v8: .cfa -16 + ^
STACK CFI f51c v8: v8
STACK CFI f524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f52c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI f538 v8: .cfa -16 + ^
STACK CFI INIT f580 318 .cfa: sp 0 + .ra: x30
STACK CFI f588 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f598 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f5a8 .cfa: sp 560 + x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f614 x23: .cfa -48 + ^
STACK CFI f61c x27: .cfa -16 + ^
STACK CFI f634 x24: .cfa -40 + ^
STACK CFI f63c x28: .cfa -8 + ^
STACK CFI f75c x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI f780 .cfa: sp 96 +
STACK CFI f790 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI f798 .cfa: sp 560 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI f830 x23: x23
STACK CFI f834 x24: x24
STACK CFI f838 x27: x27
STACK CFI f83c x28: x28
STACK CFI f840 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f884 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI f888 x23: .cfa -48 + ^
STACK CFI f88c x24: .cfa -40 + ^
STACK CFI f890 x27: .cfa -16 + ^
STACK CFI f894 x28: .cfa -8 + ^
STACK CFI INIT f8a0 cc .cfa: sp 0 + .ra: x30
STACK CFI f8b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f8bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f8d0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI f924 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f92c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI f954 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f964 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT f970 1134 .cfa: sp 0 + .ra: x30
STACK CFI f978 .cfa: sp 192 +
STACK CFI f984 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f98c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f99c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI fb48 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10268 x27: x27 x28: x28
STACK CFI 102c4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 102dc x27: x27 x28: x28
STACK CFI 10324 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10380 x27: x27 x28: x28
STACK CFI 103cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 103d4 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 104c0 x27: x27 x28: x28
STACK CFI 104c4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10554 x27: x27 x28: x28
STACK CFI 10558 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10568 x27: x27 x28: x28
STACK CFI 10614 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10620 x27: x27 x28: x28
STACK CFI 10658 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 107f4 x27: x27 x28: x28
STACK CFI 10804 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10890 x27: x27 x28: x28
STACK CFI 1089c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10944 x27: x27 x28: x28
STACK CFI 10974 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10a38 x27: x27 x28: x28
STACK CFI 10a4c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10a80 x27: x27 x28: x28
