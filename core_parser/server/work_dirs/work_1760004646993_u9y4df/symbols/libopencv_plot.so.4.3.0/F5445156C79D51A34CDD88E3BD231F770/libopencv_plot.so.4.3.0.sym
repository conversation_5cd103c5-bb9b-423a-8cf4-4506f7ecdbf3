MODULE Linux arm64 F5445156C79D51A34CDD88E3BD231F770 libopencv_plot.so.4.3
INFO CODE_ID 565144F59DC7A3514CDD88E3BD231F77A14929C2
PUBLIC 2048 0 _init
PUBLIC 2320 0 _GLOBAL__sub_I_plot.cpp
PUBLIC 2350 0 call_weak_fn
PUBLIC 2368 0 deregister_tm_clones
PUBLIC 23a0 0 register_tm_clones
PUBLIC 23e0 0 __do_global_dtors_aux
PUBLIC 2428 0 frame_dummy
PUBLIC 2460 0 cv::Algorithm::clear()
PUBLIC 2468 0 cv::Algorithm::write(cv::FileStorage&) const
PUBLIC 2470 0 cv::Algorithm::read(cv::FileNode const&)
PUBLIC 2478 0 cv::Algorithm::empty() const
PUBLIC 2480 0 cv::plot::Plot2dImpl::setMinX(double)
PUBLIC 2490 0 cv::plot::Plot2dImpl::setMaxX(double)
PUBLIC 24a0 0 cv::plot::Plot2dImpl::setMinY(double)
PUBLIC 24b0 0 cv::plot::Plot2dImpl::setMaxY(double)
PUBLIC 24c0 0 cv::plot::Plot2dImpl::setPlotLineWidth(int)
PUBLIC 24c8 0 cv::plot::Plot2dImpl::setInvertOrientation(bool)
PUBLIC 24d0 0 cv::plot::Plot2dImpl::setNeedPlotLine(bool)
PUBLIC 24d8 0 cv::plot::Plot2dImpl::setPlotLineColor(cv::Scalar_<double>)
PUBLIC 24f0 0 cv::plot::Plot2dImpl::setPlotBackgroundColor(cv::Scalar_<double>)
PUBLIC 2508 0 cv::plot::Plot2dImpl::setPlotAxisColor(cv::Scalar_<double>)
PUBLIC 2520 0 cv::plot::Plot2dImpl::setPlotGridColor(cv::Scalar_<double>)
PUBLIC 2538 0 cv::plot::Plot2dImpl::setPlotTextColor(cv::Scalar_<double>)
PUBLIC 2550 0 cv::plot::Plot2dImpl::setPlotSize(int, int)
PUBLIC 2578 0 cv::plot::Plot2dImpl::setShowGrid(bool)
PUBLIC 2580 0 cv::plot::Plot2dImpl::setShowText(bool)
PUBLIC 2588 0 cv::plot::Plot2dImpl::setGridLinesNumber(int)
PUBLIC 2598 0 cv::plot::Plot2dImpl::setPointIdxToPrint(int)
PUBLIC 25b8 0 std::_Sp_counted_ptr<cv::plot::Plot2dImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 25c0 0 std::_Sp_counted_ptr<cv::plot::Plot2dImpl*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 25c8 0 std::_Sp_counted_ptr<cv::plot::Plot2dImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 25d0 0 std::_Sp_counted_ptr<cv::plot::Plot2dImpl*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 25d8 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.25]
PUBLIC 26b8 0 cv::plot::Plot2dImpl::~Plot2dImpl()
PUBLIC 2990 0 cv::plot::Plot2dImpl::~Plot2dImpl()
PUBLIC 2c60 0 std::_Sp_counted_ptr<cv::plot::Plot2dImpl*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 2f50 0 cv::Mat::~Mat()
PUBLIC 2fe0 0 cv::MatExpr::~MatExpr()
PUBLIC 3190 0 cv::plot::Plot2dImpl::plotHelper(cv::Mat, cv::Mat)
PUBLIC 3980 0 cv::plot::Plot2dImpl::Plot2dImpl(cv::_InputArray const&)
PUBLIC 4650 0 cv::plot::Plot2dImpl::Plot2dImpl(cv::_InputArray const&, cv::_InputArray const&)
PUBLIC 5110 0 cv::plot::Plot2dImpl::drawAxis(int, int, double, double, cv::Scalar_<double>, cv::Scalar_<double>)
PUBLIC 5cc0 0 cv::plot::Plot2dImpl::render(cv::_OutputArray const&)
PUBLIC 7938 0 cv::plot::Plot2d::create(cv::_InputArray const&)
PUBLIC 79d0 0 cv::plot::Plot2d::create(cv::_InputArray const&, cv::_InputArray const&)
PUBLIC 7a74 0 _fini
STACK CFI INIT 2460 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2468 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2470 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2478 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2480 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2490 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 24a0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 24b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 24c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24d8 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24f0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2508 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2520 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2538 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2550 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2578 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2580 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2588 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2598 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 25b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25c8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 25d8 dc .cfa: sp 0 + .ra: x30
STACK CFI 25dc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 25e0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 25e8 .ra: .cfa -32 + ^
STACK CFI 2634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2638 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 267c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2680 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 26a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 26a8 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 26b8 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 26bc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 26cc .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 293c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2940 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 2990 2cc .cfa: sp 0 + .ra: x30
STACK CFI 2994 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 29a4 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 2c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2c10 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 2c60 2ec .cfa: sp 0 + .ra: x30
STACK CFI 2c64 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2c68 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 2ee8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2ef0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2f48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 2f50 90 .cfa: sp 0 + .ra: x30
STACK CFI 2f54 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2fc8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 2fd0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2fdc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2fe0 1ac .cfa: sp 0 + .ra: x30
STACK CFI 2fe4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ff0 .ra: .cfa -16 + ^
STACK CFI 314c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3150 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3188 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 3190 7b4 .cfa: sp 0 + .ra: x30
STACK CFI 3194 .cfa: sp 496 + x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 31a4 x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 31b8 .ra: .cfa -432 + ^ v8: .cfa -424 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI 3844 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3848 .cfa: sp 496 + .ra: .cfa -432 + ^ v8: .cfa -424 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI INIT 3980 cb0 .cfa: sp 0 + .ra: x30
STACK CFI 3984 .cfa: sp 608 +
STACK CFI 3988 x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI 3994 x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI 39a4 .ra: .cfa -560 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI 431c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 4320 .cfa: sp 608 + .ra: .cfa -560 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^
STACK CFI INIT 4650 aa8 .cfa: sp 0 + .ra: x30
STACK CFI 4654 .cfa: sp 704 +
STACK CFI 4658 x21: .cfa -688 + ^ x22: .cfa -680 + ^
STACK CFI 4664 x19: .cfa -704 + ^ x20: .cfa -696 + ^
STACK CFI 4670 .ra: .cfa -656 + ^ x23: .cfa -672 + ^ x24: .cfa -664 + ^
STACK CFI 4df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 4df8 .cfa: sp 704 + .ra: .cfa -656 + ^ x19: .cfa -704 + ^ x20: .cfa -696 + ^ x21: .cfa -688 + ^ x22: .cfa -680 + ^ x23: .cfa -672 + ^ x24: .cfa -664 + ^
STACK CFI INIT 5110 b9c .cfa: sp 0 + .ra: x30
STACK CFI 5114 .cfa: sp 352 + x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 5134 .ra: .cfa -272 + ^ v8: .cfa -256 + ^ v9: .cfa -248 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 534c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5350 .cfa: sp 352 + .ra: .cfa -272 + ^ v8: .cfa -256 + ^ v9: .cfa -248 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI INIT 5cc0 1c5c .cfa: sp 0 + .ra: x30
STACK CFI 5cc4 .cfa: sp 2288 +
STACK CFI 5cd8 x19: .cfa -2288 + ^ x20: .cfa -2280 + ^
STACK CFI 5ce8 .ra: .cfa -2208 + ^ x23: .cfa -2256 + ^ x24: .cfa -2248 + ^
STACK CFI 5d00 v10: .cfa -2200 + ^ v8: .cfa -2192 + ^ v9: .cfa -2184 + ^ x21: .cfa -2272 + ^ x22: .cfa -2264 + ^ x25: .cfa -2240 + ^ x26: .cfa -2232 + ^ x27: .cfa -2224 + ^ x28: .cfa -2216 + ^
STACK CFI 6ed4 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6ed8 .cfa: sp 2288 + .ra: .cfa -2208 + ^ v10: .cfa -2200 + ^ v8: .cfa -2192 + ^ v9: .cfa -2184 + ^ x19: .cfa -2288 + ^ x20: .cfa -2280 + ^ x21: .cfa -2272 + ^ x22: .cfa -2264 + ^ x23: .cfa -2256 + ^ x24: .cfa -2248 + ^ x25: .cfa -2240 + ^ x26: .cfa -2232 + ^ x27: .cfa -2224 + ^ x28: .cfa -2216 + ^
STACK CFI INIT 7938 94 .cfa: sp 0 + .ra: x30
STACK CFI 793c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7944 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 798c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 7990 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 79d0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 79d4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 79dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 79e8 .ra: .cfa -16 + ^
STACK CFI 7a34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 7a38 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 2320 30 .cfa: sp 0 + .ra: x30
STACK CFI 2324 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2340 .cfa: sp 0 + .ra: .ra x19: x19
