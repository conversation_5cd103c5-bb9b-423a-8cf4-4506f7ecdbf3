MODULE Linux arm64 A5D9C4051A7F2D4BDF5087A49F27E0690 libvic_interface.so
INFO CODE_ID 05C4D9A57F1A4B2DDF5087A49F27E069
PUBLIC 1b40 0 _init
PUBLIC 1e10 0 call_weak_fn
PUBLIC 1e30 0 deregister_tm_clones
PUBLIC 1e60 0 register_tm_clones
PUBLIC 1ea0 0 __do_global_dtors_aux
PUBLIC 1ef0 0 frame_dummy
PUBLIC 1f00 0 lios::vic::VicNvMedia::VicNvMedia()
PUBLIC 2140 0 lios::vic::VicNvMedia::~VicNvMedia()
PUBLIC 2180 0 lios::vic::VicNvMedia::transform_surface(NvSciBufObjRefRec*, NvSciBufObjRefRec*, NvMediaRect const*, NvMediaRect const*)
PUBLIC 2430 0 lios::vic::VicNvMedia::register_bufs(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> > const&)
PUBLIC 24c0 0 lios::vic::VicNvMedia::GetVicBufAttrList(linvs::buf::BufAttrList*)
PUBLIC 25bc 0 _fini
STACK CFI INIT 1e30 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e60 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ea0 48 .cfa: sp 0 + .ra: x30
STACK CFI 1ea4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1eac x19: .cfa -16 + ^
STACK CFI 1ee4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ef0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1f00 240 .cfa: sp 0 + .ra: x30
STACK CFI 1f04 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 1f0c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 1f24 x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 1f2c x25: .cfa -160 + ^
STACK CFI 20c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 20cc .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x29: .cfa -224 + ^
STACK CFI INIT 2140 3c .cfa: sp 0 + .ra: x30
STACK CFI 2144 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 214c x19: .cfa -16 + ^
STACK CFI 2178 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2180 2a8 .cfa: sp 0 + .ra: x30
STACK CFI 2184 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2194 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 21a0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 21ac x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 21d8 x25: .cfa -96 + ^
STACK CFI 22ac x25: x25
STACK CFI 22dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 22e0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x29: .cfa -160 + ^
STACK CFI 2304 x25: x25
STACK CFI 2328 x25: .cfa -96 + ^
STACK CFI 234c x25: x25
STACK CFI 2350 x25: .cfa -96 + ^
STACK CFI 2374 x25: x25
STACK CFI 2378 x25: .cfa -96 + ^
STACK CFI 239c x25: x25
STACK CFI 23a0 x25: .cfa -96 + ^
STACK CFI 23c4 x25: x25
STACK CFI 23c8 x25: .cfa -96 + ^
STACK CFI 23ec x25: x25
STACK CFI 23f4 x25: .cfa -96 + ^
STACK CFI INIT 2430 88 .cfa: sp 0 + .ra: x30
STACK CFI 2434 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 243c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2444 x21: .cfa -16 + ^
STACK CFI 24a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 24a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 24b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 24c0 fc .cfa: sp 0 + .ra: x30
STACK CFI 24c4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 24d4 x19: .cfa -144 + ^
STACK CFI 2590 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2594 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
