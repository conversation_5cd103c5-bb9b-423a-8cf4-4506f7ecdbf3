MODULE Linux arm64 D00BDF2555467D8B32C1856221D1AB260 libwfd_interface.so
INFO CODE_ID 25DF0BD046558B7D32C1856221D1AB26
PUBLIC 1ad0 0 _init
PUBLIC 1d90 0 call_weak_fn
PUBLIC 1db0 0 deregister_tm_clones
PUBLIC 1de0 0 register_tm_clones
PUBLIC 1e20 0 __do_global_dtors_aux
PUBLIC 1e70 0 frame_dummy
PUBLIC 1e80 0 lios::wfd::WfdNvMedia::GetWfdBufAttrList(linvs::buf::BufAttrList*)
PUBLIC 1fb0 0 lios::wfd::WfdNvMedia::WfdFlip(NvSciBufObjRefRec*&)
PUBLIC 20e0 0 lios::wfd::WfdNvMedia::WfdClearDisplay()
PUBLIC 2200 0 lios::wfd::WfdNvMedia::~WfdNvMedia()
PUBLIC 2360 0 lios::wfd::WfdNvMedia::Init()
PUBLIC 28b0 0 lios::wfd::WfdNvMedia::GetBuffers(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> >&)
PUBLIC 2930 0 lios::wfd::WfdNvMedia::RegisterBufs(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> > const&)
PUBLIC 2c30 0 void std::vector<int, std::allocator<int> >::_M_realloc_insert<int const&>(__gnu_cxx::__normal_iterator<int*, std::vector<int, std::allocator<int> > >, int const&)
PUBLIC 2db0 0 void std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> >::_M_realloc_insert<NvSciBufObjRefRec* const&>(__gnu_cxx::__normal_iterator<NvSciBufObjRefRec**, std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> > >, NvSciBufObjRefRec* const&)
PUBLIC 2f30 0 void std::vector<std::pair<NvSciBufObjRefRec*, std::pair<unsigned int, unsigned int> >, std::allocator<std::pair<NvSciBufObjRefRec*, std::pair<unsigned int, unsigned int> > > >::_M_realloc_insert<std::pair<NvSciBufObjRefRec*, std::pair<unsigned int, unsigned int> > >(__gnu_cxx::__normal_iterator<std::pair<NvSciBufObjRefRec*, std::pair<unsigned int, unsigned int> >*, std::vector<std::pair<NvSciBufObjRefRec*, std::pair<unsigned int, unsigned int> >, std::allocator<std::pair<NvSciBufObjRefRec*, std::pair<unsigned int, unsigned int> > > > >, std::pair<NvSciBufObjRefRec*, std::pair<unsigned int, unsigned int> >&&)
PUBLIC 3084 0 _fini
STACK CFI INIT 1db0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1de0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e20 48 .cfa: sp 0 + .ra: x30
STACK CFI 1e24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e2c x19: .cfa -16 + ^
STACK CFI 1e64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1e70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e80 130 .cfa: sp 0 + .ra: x30
STACK CFI 1e84 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 1ea0 x19: .cfa -160 + ^
STACK CFI 1f78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1f7c .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x29: .cfa -176 + ^
STACK CFI INIT 1fb0 124 .cfa: sp 0 + .ra: x30
STACK CFI 1fb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1fbc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1fdc x21: .cfa -16 + ^
STACK CFI 205c x21: x21
STACK CFI 2068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 206c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 208c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2090 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 20b0 x21: x21
STACK CFI 20b8 x21: .cfa -16 + ^
STACK CFI INIT 20e0 118 .cfa: sp 0 + .ra: x30
STACK CFI 20e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 20ec x19: .cfa -16 + ^
STACK CFI 2118 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 211c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2200 160 .cfa: sp 0 + .ra: x30
STACK CFI 2204 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2214 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 221c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2228 x23: .cfa -16 + ^
STACK CFI 229c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 22a0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 22b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 22b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2c30 180 .cfa: sp 0 + .ra: x30
STACK CFI 2c34 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2c3c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2c4c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2c58 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2ce4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2360 550 .cfa: sp 0 + .ra: x30
STACK CFI 2364 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2378 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 238c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 241c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2494 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 24a8 x27: .cfa -32 + ^
STACK CFI 2608 x21: x21 x22: x22
STACK CFI 260c x23: x23 x24: x24
STACK CFI 2610 x27: x27
STACK CFI 2658 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 265c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 26ac x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^
STACK CFI 26c8 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27
STACK CFI 26e8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2704 x23: x23 x24: x24
STACK CFI 2708 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2724 x23: x23 x24: x24
STACK CFI 2728 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 273c x23: x23 x24: x24
STACK CFI 2740 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^
STACK CFI 27c8 x21: x21 x22: x22 x27: x27
STACK CFI 27e4 x23: x23 x24: x24
STACK CFI 27e8 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^
STACK CFI 2840 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27
STACK CFI 2844 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2848 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 284c x27: .cfa -32 + ^
STACK CFI 2850 x27: x27
STACK CFI 286c x27: .cfa -32 + ^
STACK CFI INIT 2db0 180 .cfa: sp 0 + .ra: x30
STACK CFI 2db4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2dbc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2dcc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2dd8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2e60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2e64 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 28b0 78 .cfa: sp 0 + .ra: x30
STACK CFI 28b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 28bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 28c4 x21: .cfa -16 + ^
STACK CFI 2924 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2f30 154 .cfa: sp 0 + .ra: x30
STACK CFI 2f34 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2f3c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2f48 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2f54 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3018 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2930 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 2934 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 293c x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 294c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 296c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 29e0 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 29ec x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2b30 x25: x25 x26: x26
STACK CFI 2b34 x27: x27 x28: x28
STACK CFI 2b6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2b70 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 2bb8 x25: x25 x26: x26
STACK CFI 2bc0 x27: x27 x28: x28
STACK CFI 2bc4 x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 2c1c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2c20 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 2c24 x27: .cfa -80 + ^ x28: .cfa -72 + ^
