MODULE Linux arm64 F3D43B2D990419866CF23958F6F3041C0 libavahi-glib.so.1
INFO CODE_ID 2D3BD4F3049986196CF23958F6F3041CB1893AD2
PUBLIC 1870 0 avahi_glib_poll_new
PUBLIC 1950 0 avahi_glib_poll_free
PUBLIC 1a54 0 avahi_glib_poll_get
PUBLIC 1b00 0 avahi_glib_allocator
STACK CFI INIT d10 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT d40 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT d80 48 .cfa: sp 0 + .ra: x30
STACK CFI d84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d8c x19: .cfa -16 + ^
STACK CFI dc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT dd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT de0 ec .cfa: sp 0 + .ra: x30
STACK CFI de8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI df4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT ed0 140 .cfa: sp 0 + .ra: x30
STACK CFI ed8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ee8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI f78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI f80 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1010 e0 .cfa: sp 0 + .ra: x30
STACK CFI 1018 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1020 x19: .cfa -16 + ^
STACK CFI 1064 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 106c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 10a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 10f0 84 .cfa: sp 0 + .ra: x30
STACK CFI 10f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1124 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 112c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1174 7c .cfa: sp 0 + .ra: x30
STACK CFI 117c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 11a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 11f0 78 .cfa: sp 0 + .ra: x30
STACK CFI 11f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1214 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1220 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1270 78 .cfa: sp 0 + .ra: x30
STACK CFI 1278 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1298 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 12a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 12f0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 12f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1300 x19: .cfa -16 + ^
STACK CFI 1330 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1338 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1360 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1368 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 13b0 94 .cfa: sp 0 + .ra: x30
STACK CFI 13b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1418 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1420 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1444 150 .cfa: sp 0 + .ra: x30
STACK CFI 144c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1454 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 14d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1594 a8 .cfa: sp 0 + .ra: x30
STACK CFI 159c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 15a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1608 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1610 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1640 30 .cfa: sp 0 + .ra: x30
STACK CFI 1648 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1670 30 .cfa: sp 0 + .ra: x30
STACK CFI 1678 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 16a0 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 16a8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 16b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI 173c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1744 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI 17f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1800 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1870 d8 .cfa: sp 0 + .ra: x30
STACK CFI 1878 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1880 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1890 x21: .cfa -16 + ^
STACK CFI 1934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 193c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1950 104 .cfa: sp 0 + .ra: x30
STACK CFI 1958 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1960 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1a54 40 .cfa: sp 0 + .ra: x30
STACK CFI 1a6c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1aa0 1c .cfa: sp 0 + .ra: x30
STACK CFI 1aa8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ab4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ac0 18 .cfa: sp 0 + .ra: x30
STACK CFI 1ac8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ad0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ae0 18 .cfa: sp 0 + .ra: x30
STACK CFI 1ae8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1af0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b00 64 .cfa: sp 0 + .ra: x30
STACK CFI 1b10 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b58 .cfa: sp 0 + .ra: .ra x29: x29
