MODULE Linux arm64 78704F27ED2A42ECE0D09A0CE26B52030 libpolkit-agent-1.so.0
INFO CODE_ID 274F70782AEDEC42E0D09A0CE26B5203B8A01E7F
PUBLIC 5850 0 polkit_agent_register_flags_get_type
PUBLIC 58e0 0 polkit_agent_listener_unregister
PUBLIC 5920 0 polkit_agent_listener_get_type
PUBLIC 5990 0 polkit_agent_listener_register_with_options
PUBLIC 5d00 0 polkit_agent_listener_register
PUBLIC 5d24 0 polkit_agent_register_listener
PUBLIC 5e64 0 polkit_agent_listener_initiate_authentication
PUBLIC 6340 0 polkit_agent_listener_initiate_authentication_finish
PUBLIC 6554 0 polkit_agent_session_get_type
PUBLIC 67a0 0 polkit_agent_session_new
PUBLIC 6870 0 polkit_agent_session_response
PUBLIC 6bc0 0 polkit_agent_session_initiate
PUBLIC 7390 0 polkit_agent_session_cancel
PUBLIC 7484 0 polkit_agent_text_listener_get_type
PUBLIC 74f4 0 polkit_agent_text_listener_new
STACK CFI INIT 3fc0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ff0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4030 48 .cfa: sp 0 + .ra: x30
STACK CFI 4034 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 403c x19: .cfa -16 + ^
STACK CFI 4074 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4080 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4090 18 .cfa: sp 0 + .ra: x30
STACK CFI 4098 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 40a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 40b0 20 .cfa: sp 0 + .ra: x30
STACK CFI 40b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 40c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 40d0 28 .cfa: sp 0 + .ra: x30
STACK CFI 40dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 40e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4100 24 .cfa: sp 0 + .ra: x30
STACK CFI 4108 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 411c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4124 a0 .cfa: sp 0 + .ra: x30
STACK CFI 4138 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4140 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 414c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4188 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4190 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 41ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 41c4 158 .cfa: sp 0 + .ra: x30
STACK CFI 41cc .cfa: sp 48 +
STACK CFI 41d0 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41d8 x19: .cfa -16 + ^
STACK CFI 42b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 42bc .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4320 18 .cfa: sp 0 + .ra: x30
STACK CFI 4328 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4330 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4340 cc .cfa: sp 0 + .ra: x30
STACK CFI 4348 .cfa: sp 64 +
STACK CFI 4354 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 435c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4364 x21: .cfa -16 + ^
STACK CFI 43ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 43f4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4410 88 .cfa: sp 0 + .ra: x30
STACK CFI 4418 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4420 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4448 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4450 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 44a0 134 .cfa: sp 0 + .ra: x30
STACK CFI 44a8 .cfa: sp 48 +
STACK CFI 44b4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 44c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4518 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 45d4 b0 .cfa: sp 0 + .ra: x30
STACK CFI 45dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45e4 x19: .cfa -16 + ^
STACK CFI 4650 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4658 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 467c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4684 50 .cfa: sp 0 + .ra: x30
STACK CFI 468c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4694 x19: .cfa -16 + ^
STACK CFI 46b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 46bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 46cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 46d4 48 .cfa: sp 0 + .ra: x30
STACK CFI 46dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 46f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4720 48 .cfa: sp 0 + .ra: x30
STACK CFI 4728 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 473c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4770 d0 .cfa: sp 0 + .ra: x30
STACK CFI 4778 .cfa: sp 64 +
STACK CFI 477c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4784 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 47ac x21: .cfa -16 + ^
STACK CFI 4804 x21: x21
STACK CFI 4808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4810 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4820 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4828 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4838 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4840 d8 .cfa: sp 0 + .ra: x30
STACK CFI 4848 .cfa: sp 64 +
STACK CFI 484c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4854 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 488c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4898 x21: .cfa -16 + ^
STACK CFI 48f0 x21: x21
STACK CFI 48f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 48fc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 4910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4920 110 .cfa: sp 0 + .ra: x30
STACK CFI 4928 .cfa: sp 48 +
STACK CFI 4934 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 493c x19: .cfa -16 + ^
STACK CFI 49e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 49ec .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4a30 6c .cfa: sp 0 + .ra: x30
STACK CFI 4a38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a44 x19: .cfa -16 + ^
STACK CFI 4a84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4a8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4a94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4aa0 90 .cfa: sp 0 + .ra: x30
STACK CFI 4aa8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ab4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4ad4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4adc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4b30 4cc .cfa: sp 0 + .ra: x30
STACK CFI 4b38 .cfa: sp 80 +
STACK CFI 4b3c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4b44 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4b58 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4c08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4c10 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5000 154 .cfa: sp 0 + .ra: x30
STACK CFI 5008 .cfa: sp 48 +
STACK CFI 500c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5014 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 513c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5144 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5154 64 .cfa: sp 0 + .ra: x30
STACK CFI 515c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5164 x19: .cfa -16 + ^
STACK CFI 51a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 51a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 51b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 51c0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 51c8 .cfa: sp 64 +
STACK CFI 51cc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 51d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5210 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5228 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5240 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 524c x21: .cfa -16 + ^
STACK CFI 52a4 x21: x21
STACK CFI 52a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 52b0 fc .cfa: sp 0 + .ra: x30
STACK CFI 52b8 .cfa: sp 64 +
STACK CFI 52bc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 52c4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 52fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5304 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5320 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5334 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 533c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5348 x21: .cfa -16 + ^
STACK CFI 53a0 x21: x21
STACK CFI 53a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 53b0 40 .cfa: sp 0 + .ra: x30
STACK CFI 53b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 53c4 x19: .cfa -16 + ^
STACK CFI 53e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 53f0 40 .cfa: sp 0 + .ra: x30
STACK CFI 53f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5404 x19: .cfa -16 + ^
STACK CFI 5428 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5430 7c .cfa: sp 0 + .ra: x30
STACK CFI 5438 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5444 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 54a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 54b0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 54b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 54c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 54cc x21: .cfa -16 + ^
STACK CFI 5504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 550c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5580 fc .cfa: sp 0 + .ra: x30
STACK CFI 5588 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5594 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5608 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5610 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5680 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 5688 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5694 .cfa: sp 2160 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5718 .cfa: sp 48 +
STACK CFI 5724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 572c .cfa: sp 2160 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5758 x21: .cfa -16 + ^
STACK CFI 577c x21: x21
STACK CFI 57a0 .cfa: sp 48 +
STACK CFI 57a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 57b0 .cfa: sp 2160 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 57f4 x21: .cfa -16 + ^
STACK CFI 581c x21: x21
STACK CFI 5844 x21: .cfa -16 + ^
STACK CFI INIT 5850 88 .cfa: sp 0 + .ra: x30
STACK CFI 5858 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5860 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 588c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 58d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 58e0 40 .cfa: sp 0 + .ra: x30
STACK CFI 58e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 58f0 x19: .cfa -16 + ^
STACK CFI 5918 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5920 70 .cfa: sp 0 + .ra: x30
STACK CFI 5928 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5930 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5954 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 595c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5988 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5990 368 .cfa: sp 0 + .ra: x30
STACK CFI 5998 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 59a0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 59a8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 59b4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 59c0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5bcc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5d00 24 .cfa: sp 0 + .ra: x30
STACK CFI 5d08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5d14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5d24 74 .cfa: sp 0 + .ra: x30
STACK CFI 5d2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5d54 x19: .cfa -16 + ^
STACK CFI 5d78 x19: x19
STACK CFI 5d80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5d88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5d8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5da0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 5da8 .cfa: sp 64 +
STACK CFI 5db4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5dbc x19: .cfa -16 + ^
STACK CFI 5e58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5e60 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5e64 204 .cfa: sp 0 + .ra: x30
STACK CFI 5e6c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5e74 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5e80 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5e8c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5e98 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5ea0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5f88 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 5fb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5fc0 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 6070 2cc .cfa: sp 0 + .ra: x30
STACK CFI 6078 .cfa: sp 304 +
STACK CFI 6088 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6090 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 609c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 60e8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 60f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 60fc x27: .cfa -16 + ^
STACK CFI 6220 x21: x21 x22: x22
STACK CFI 6224 x23: x23 x24: x24
STACK CFI 6228 x27: x27
STACK CFI 62a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 62ac .cfa: sp 304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 62d8 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27
STACK CFI 6300 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6304 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6308 x27: .cfa -16 + ^
STACK CFI 630c x21: x21 x22: x22 x23: x23 x24: x24 x27: x27
STACK CFI 6330 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6334 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6338 x27: .cfa -16 + ^
STACK CFI INIT 6340 148 .cfa: sp 0 + .ra: x30
STACK CFI 6348 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6350 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 635c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 63e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 63ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6418 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6420 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 644c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6454 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6490 c4 .cfa: sp 0 + .ra: x30
STACK CFI 6498 .cfa: sp 48 +
STACK CFI 64a4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 64ac x19: .cfa -16 + ^
STACK CFI 6538 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6540 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6554 70 .cfa: sp 0 + .ra: x30
STACK CFI 655c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6564 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6590 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 65bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 65c4 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 65cc .cfa: sp 96 +
STACK CFI 65d0 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 65d8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 65e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 6784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 678c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 67a0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 67a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 67b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6800 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6818 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6840 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6848 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6870 14c .cfa: sp 0 + .ra: x30
STACK CFI 6878 .cfa: sp 48 +
STACK CFI 6884 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 688c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6940 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 69c0 200 .cfa: sp 0 + .ra: x30
STACK CFI 69c8 .cfa: sp 208 +
STACK CFI 69d4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 69dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 69e8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 69f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6b80 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6bc0 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 6bc8 .cfa: sp 128 +
STACK CFI 6bd4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6bdc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6cc8 x21: .cfa -16 + ^
STACK CFI 6d74 x21: x21
STACK CFI 6dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6dcc .cfa: sp 128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 6e1c x21: .cfa -16 + ^
STACK CFI 6e30 x21: x21
STACK CFI 6e74 x21: .cfa -16 + ^
STACK CFI INIT 6e80 510 .cfa: sp 0 + .ra: x30
STACK CFI 6e88 .cfa: sp 112 +
STACK CFI 6e94 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6e9c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6ea8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6eb4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6ec0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6f44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6f4c .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 6fd0 x27: .cfa -16 + ^
STACK CFI 701c x27: x27
STACK CFI 71ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 71f4 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 7384 x27: .cfa -16 + ^
STACK CFI 7388 x27: x27
STACK CFI 738c x27: .cfa -16 + ^
STACK CFI INIT 7390 ac .cfa: sp 0 + .ra: x30
STACK CFI 7398 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 73a0 x19: .cfa -16 + ^
STACK CFI 73e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 73f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 73fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7418 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7434 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7440 44 .cfa: sp 0 + .ra: x30
STACK CFI 7448 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7454 x19: .cfa -16 + ^
STACK CFI 747c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7484 70 .cfa: sp 0 + .ra: x30
STACK CFI 748c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7494 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 74b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 74c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 74ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 74f4 d0 .cfa: sp 0 + .ra: x30
STACK CFI 74fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7504 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 755c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7564 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 758c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7594 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 75bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
