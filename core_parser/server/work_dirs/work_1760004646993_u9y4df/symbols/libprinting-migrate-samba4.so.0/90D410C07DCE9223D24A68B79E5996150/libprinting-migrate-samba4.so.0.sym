MODULE Linux arm64 90D410C07DCE9223D24A68B79E5996150 libprinting-migrate-samba4.so.0
INFO CODE_ID C010D490CE7D2392D24A68B79E5996151B7987DD
PUBLIC 4620 0 winreg_set_printer_dataex
PUBLIC 4900 0 winreg_get_printer_dataex
PUBLIC 4d30 0 winreg_delete_printer_dataex
PUBLIC 4f90 0 winreg_printer_deleteform1
PUBLIC 5204 0 winreg_printer_setform1
PUBLIC 5610 0 winreg_printer_getform1
PUBLIC 59e0 0 spoolss_map_to_os2_driver
PUBLIC 5ea0 0 dcerpc_winreg_query_dword
PUBLIC 6024 0 winreg_printer_get_changeid
PUBLIC 6284 0 dcerpc_winreg_query_binary
PUBLIC 6410 0 dcerpc_winreg_query_multi_sz
PUBLIC 6590 0 dcerpc_winreg_query_sz
PUBLIC 6710 0 dcerpc_winreg_query_sd
PUBLIC 6840 0 dcerpc_winreg_set_dword
PUBLIC 6910 0 winreg_printer_update_changeid
PUBLIC 6b30 0 dcerpc_winreg_set_sz
PUBLIC 6c60 0 winreg_add_core_driver
PUBLIC 71a0 0 winreg_add_driver_package
PUBLIC 73e0 0 dcerpc_winreg_set_expand_sz
PUBLIC 7510 0 dcerpc_winreg_set_multi_sz
PUBLIC 7620 0 winreg_add_driver
PUBLIC 7d20 0 printing_tdb_migrate_driver
PUBLIC 7fe0 0 dcerpc_winreg_set_binary
PUBLIC 8030 0 dcerpc_winreg_set_sd
PUBLIC 8440 0 winreg_get_printer_secdesc
PUBLIC 84e0 0 winreg_get_printserver_secdesc
PUBLIC 8760 0 winreg_set_printer_secdesc
PUBLIC 8800 0 printing_tdb_migrate_secdesc
PUBLIC 89a4 0 winreg_update_printer
PUBLIC 9180 0 printing_tdb_migrate_printer
PUBLIC 95f0 0 winreg_create_printer
PUBLIC 9e40 0 winreg_set_printserver_secdesc
PUBLIC 9e70 0 dcerpc_winreg_add_multi_sz
PUBLIC 9f90 0 dcerpc_winreg_enum_keys
PUBLIC a330 0 winreg_enum_printer_key
PUBLIC a5d0 0 winreg_get_driver_list
PUBLIC a870 0 dcerpc_winreg_enumvals
PUBLIC ad70 0 winreg_get_printer
PUBLIC b6d0 0 winreg_enum_printer_dataex
PUBLIC ba34 0 winreg_printer_enumforms1
PUBLIC bec0 0 winreg_printer_addform1
PUBLIC c304 0 printing_tdb_migrate_form
PUBLIC c4b4 0 winreg_get_driver
PUBLIC cdc0 0 winreg_get_core_driver
PUBLIC d294 0 winreg_get_driver_package
PUBLIC d690 0 dcerpc_winreg_delete_subkeys_recursive
PUBLIC d9f0 0 winreg_delete_printer_key
PUBLIC dcf0 0 winreg_del_driver
PUBLIC dff0 0 winreg_del_driver_package
STACK CFI INIT 3c70 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ca0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3ce0 48 .cfa: sp 0 + .ra: x30
STACK CFI 3ce4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3cec x19: .cfa -16 + ^
STACK CFI 3d24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3d30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3d40 98 .cfa: sp 0 + .ra: x30
STACK CFI 3d48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d50 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d88 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3da4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3dac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3db8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3dc0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3de0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 3de8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3df0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e04 x21: .cfa -16 + ^
STACK CFI 3e2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3e34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3e68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3e70 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3e88 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3ea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3eb0 dc .cfa: sp 0 + .ra: x30
STACK CFI 3eb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3ec0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3ed4 x21: .cfa -16 + ^
STACK CFI 3efc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3f04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3f44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3f4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3f5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3f64 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3f84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3f90 150 .cfa: sp 0 + .ra: x30
STACK CFI 3f98 .cfa: sp 96 +
STACK CFI 3fa8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3fb8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3fc4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4038 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4040 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 40e0 198 .cfa: sp 0 + .ra: x30
STACK CFI 40e8 .cfa: sp 112 +
STACK CFI 40f4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 40fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4108 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4180 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4280 398 .cfa: sp 0 + .ra: x30
STACK CFI 4288 .cfa: sp 208 +
STACK CFI 4294 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 429c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 42a8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 42b8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 42c0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 42cc x27: .cfa -16 + ^
STACK CFI 4400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 4408 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4620 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 4628 .cfa: sp 208 +
STACK CFI 4634 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 463c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4644 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4650 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 465c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 46ac x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4774 x27: x27 x28: x28
STACK CFI 47ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 47b4 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 47cc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 48f8 x27: x27 x28: x28
STACK CFI 48fc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 4900 430 .cfa: sp 0 + .ra: x30
STACK CFI 4908 .cfa: sp 256 +
STACK CFI 4914 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4920 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4928 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4934 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4940 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4948 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4b34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4b3c .cfa: sp 256 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4d30 258 .cfa: sp 0 + .ra: x30
STACK CFI 4d38 .cfa: sp 160 +
STACK CFI 4d44 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4d4c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4d54 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4d60 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4dac x25: .cfa -16 + ^
STACK CFI 4e4c x25: x25
STACK CFI 4e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4e88 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4ea0 x25: .cfa -16 + ^
STACK CFI 4f80 x25: x25
STACK CFI 4f84 x25: .cfa -16 + ^
STACK CFI INIT 4f90 274 .cfa: sp 0 + .ra: x30
STACK CFI 4f98 .cfa: sp 144 +
STACK CFI 4fa8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4fb0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4fd0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5038 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 5054 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5114 x23: x23 x24: x24
STACK CFI 511c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 51b8 x23: x23 x24: x24
STACK CFI 51bc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 5204 404 .cfa: sp 0 + .ra: x30
STACK CFI 520c .cfa: sp 176 +
STACK CFI 5218 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5220 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 522c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5244 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5300 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5308 .cfa: sp 176 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 5368 x25: .cfa -16 + ^
STACK CFI 5564 x25: x25
STACK CFI 556c x25: .cfa -16 + ^
STACK CFI 55bc x25: x25
STACK CFI 55c0 x25: .cfa -16 + ^
STACK CFI INIT 5610 3d0 .cfa: sp 0 + .ra: x30
STACK CFI 5618 .cfa: sp 240 +
STACK CFI 5624 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 562c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5638 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5644 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 56e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 56e8 .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 5704 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5764 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 57b8 x27: x27 x28: x28
STACK CFI 57e4 x25: x25 x26: x26
STACK CFI 57ec x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5864 x27: x27 x28: x28
STACK CFI 5928 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 592c x27: x27 x28: x28
STACK CFI 5938 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5950 x27: x27 x28: x28
STACK CFI 5958 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 59ac x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 59b0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 59b4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 59e0 4bc .cfa: sp 0 + .ra: x30
STACK CFI 59e8 .cfa: sp 128 +
STACK CFI 59f4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5a00 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5a08 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5a64 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5ab4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5b5c x27: x27 x28: x28
STACK CFI 5b60 x25: x25 x26: x26
STACK CFI 5b94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5b9c .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 5bd8 x25: x25 x26: x26
STACK CFI 5be0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5cf4 x27: x27 x28: x28
STACK CFI 5cfc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5d00 x27: x27 x28: x28
STACK CFI 5d08 x25: x25 x26: x26
STACK CFI 5d0c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5d18 x25: x25 x26: x26
STACK CFI 5d20 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5dc4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5dfc x27: x27 x28: x28
STACK CFI 5e34 x25: x25 x26: x26
STACK CFI 5e38 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5e3c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 5ea0 184 .cfa: sp 0 + .ra: x30
STACK CFI 5ea8 .cfa: sp 176 +
STACK CFI 5eb8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5ec0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5ec8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5ed4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5ee0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5eec x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5f8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5f94 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6024 260 .cfa: sp 0 + .ra: x30
STACK CFI 602c .cfa: sp 144 +
STACK CFI 6038 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6040 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6048 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6098 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6164 x23: x23 x24: x24
STACK CFI 6194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 619c .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 61b4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 627c x23: x23 x24: x24
STACK CFI 6280 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 6284 184 .cfa: sp 0 + .ra: x30
STACK CFI 628c .cfa: sp 176 +
STACK CFI 6298 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 62a0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 62ac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 62b8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 62d0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6388 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6390 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6410 178 .cfa: sp 0 + .ra: x30
STACK CFI 6418 .cfa: sp 176 +
STACK CFI 6428 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6430 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6438 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6444 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6450 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 645c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 64fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6504 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6590 178 .cfa: sp 0 + .ra: x30
STACK CFI 6598 .cfa: sp 176 +
STACK CFI 65a8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 65b0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 65b8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 65c4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 65d0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 65dc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 667c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6684 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6710 128 .cfa: sp 0 + .ra: x30
STACK CFI 6718 .cfa: sp 96 +
STACK CFI 6724 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 672c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6734 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6740 x23: .cfa -16 + ^
STACK CFI 67dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 67e4 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6840 cc .cfa: sp 0 + .ra: x30
STACK CFI 6848 .cfa: sp 112 +
STACK CFI 6854 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 685c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6868 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6874 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6908 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6910 220 .cfa: sp 0 + .ra: x30
STACK CFI 6918 .cfa: sp 144 +
STACK CFI 6924 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 692c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6934 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 697c x23: .cfa -16 + ^
STACK CFI 6a4c x23: x23
STACK CFI 6a7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6a84 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6a9c x23: .cfa -16 + ^
STACK CFI 6b28 x23: x23
STACK CFI 6b2c x23: .cfa -16 + ^
STACK CFI INIT 6b30 12c .cfa: sp 0 + .ra: x30
STACK CFI 6b38 .cfa: sp 112 +
STACK CFI 6b44 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6b4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6b54 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6b60 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6be4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6bec .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6c60 53c .cfa: sp 0 + .ra: x30
STACK CFI 6c68 .cfa: sp 272 +
STACK CFI 6c74 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6c7c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6c84 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6c90 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6ce0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6d14 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6df0 x27: x27 x28: x28
STACK CFI 6e20 x25: x25 x26: x26
STACK CFI 6e54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6e5c .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 6e68 x25: x25 x26: x26
STACK CFI 6e70 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6ec4 x27: x27 x28: x28
STACK CFI 6ec8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7088 x27: x27 x28: x28
STACK CFI 708c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 70d8 x27: x27 x28: x28
STACK CFI 7108 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7190 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7194 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7198 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 71a0 23c .cfa: sp 0 + .ra: x30
STACK CFI 71a8 .cfa: sp 160 +
STACK CFI 71b4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 71bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 71c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 71d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 71dc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 72fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7304 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 73e0 12c .cfa: sp 0 + .ra: x30
STACK CFI 73e8 .cfa: sp 112 +
STACK CFI 73f4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 73fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7404 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7410 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7494 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 749c .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7510 10c .cfa: sp 0 + .ra: x30
STACK CFI 7518 .cfa: sp 112 +
STACK CFI 7524 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 752c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7538 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7544 x23: .cfa -16 + ^
STACK CFI 75bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 75c4 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7620 6fc .cfa: sp 0 + .ra: x30
STACK CFI 7628 .cfa: sp 352 +
STACK CFI 7634 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 763c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7648 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7654 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7660 x25: .cfa -16 + ^
STACK CFI 7724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 772c .cfa: sp 352 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7d20 2bc .cfa: sp 0 + .ra: x30
STACK CFI 7d28 .cfa: sp 320 +
STACK CFI 7d34 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7d3c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7d48 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7d54 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7dc0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7ef4 x25: x25 x26: x26
STACK CFI 7f28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7f30 .cfa: sp 320 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7f4c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7f58 x25: x25 x26: x26
STACK CFI 7f5c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7f94 x25: x25 x26: x26
STACK CFI 7fd8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 7fe0 4c .cfa: sp 0 + .ra: x30
STACK CFI 7fe8 .cfa: sp 32 +
STACK CFI 7ffc .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8024 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8030 10c .cfa: sp 0 + .ra: x30
STACK CFI 8038 .cfa: sp 96 +
STACK CFI 803c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8044 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8058 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8060 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 80e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 80e8 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8140 2fc .cfa: sp 0 + .ra: x30
STACK CFI 8148 .cfa: sp 256 +
STACK CFI 8154 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 815c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8164 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 816c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8178 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 81b4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8228 x27: x27 x28: x28
STACK CFI 8260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8268 .cfa: sp 256 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8360 x27: x27 x28: x28
STACK CFI 836c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8434 x27: x27 x28: x28
STACK CFI 8438 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 8440 a0 .cfa: sp 0 + .ra: x30
STACK CFI 8448 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8450 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8458 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 84c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 84c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 84d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 84e0 2c .cfa: sp 0 + .ra: x30
STACK CFI 84e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 84f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8510 250 .cfa: sp 0 + .ra: x30
STACK CFI 8518 .cfa: sp 160 +
STACK CFI 8524 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 852c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8538 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8540 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8578 x25: .cfa -16 + ^
STACK CFI 85f8 x25: x25
STACK CFI 862c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8634 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 869c x25: x25
STACK CFI 875c x25: .cfa -16 + ^
STACK CFI INIT 8760 9c .cfa: sp 0 + .ra: x30
STACK CFI 8768 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8770 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8780 x21: .cfa -16 + ^
STACK CFI 87dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 87e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 87f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 8800 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 8808 .cfa: sp 112 +
STACK CFI 8814 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 881c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 882c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8838 x23: .cfa -16 + ^
STACK CFI 888c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8894 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 89a4 7d8 .cfa: sp 0 + .ra: x30
STACK CFI 89ac .cfa: sp 208 +
STACK CFI 89b8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 89c4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 89cc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 89d8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 89f0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8a34 x27: .cfa -16 + ^
STACK CFI 8ad0 x27: x27
STACK CFI 8b18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8b20 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 8b7c x27: x27
STACK CFI 8b84 x27: .cfa -16 + ^
STACK CFI 9174 x27: x27
STACK CFI 9178 x27: .cfa -16 + ^
STACK CFI INIT 9180 468 .cfa: sp 0 + .ra: x30
STACK CFI 9188 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9198 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 91a4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 91b4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 91c0 .cfa: sp 608 + x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9204 .cfa: sp 96 +
STACK CFI 921c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9224 .cfa: sp 608 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 93e4 x27: .cfa -16 + ^
STACK CFI 9470 x27: x27
STACK CFI 9498 x27: .cfa -16 + ^
STACK CFI 94b4 x27: x27
STACK CFI 9550 x27: .cfa -16 + ^
STACK CFI 959c x27: x27
STACK CFI 95e4 x27: .cfa -16 + ^
STACK CFI INIT 95f0 84c .cfa: sp 0 + .ra: x30
STACK CFI 95f8 .cfa: sp 336 +
STACK CFI 960c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9618 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9624 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 962c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 966c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9670 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9ae4 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 9b18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 9b20 .cfa: sp 336 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 9b54 x23: x23 x24: x24
STACK CFI 9b58 x27: x27 x28: x28
STACK CFI 9b60 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9c68 x23: x23 x24: x24
STACK CFI 9c70 x27: x27 x28: x28
STACK CFI 9c74 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9e30 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 9e34 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9e38 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 9e40 30 .cfa: sp 0 + .ra: x30
STACK CFI 9e48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9e54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9e70 11c .cfa: sp 0 + .ra: x30
STACK CFI 9e78 .cfa: sp 96 +
STACK CFI 9e84 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9e8c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9e94 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9ea0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9eac x25: .cfa -16 + ^
STACK CFI 9f6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9f74 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9f90 3a0 .cfa: sp 0 + .ra: x30
STACK CFI 9f98 .cfa: sp 288 +
STACK CFI 9fa4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9fb0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9fb8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9fc8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a09c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a0a4 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI a0f4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a1c4 x27: x27 x28: x28
STACK CFI a230 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a250 x27: x27 x28: x28
STACK CFI a258 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a25c x27: x27 x28: x28
STACK CFI a260 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a274 x27: x27 x28: x28
STACK CFI a27c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a290 x27: x27 x28: x28
STACK CFI a298 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a328 x27: x27 x28: x28
STACK CFI a32c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT a330 298 .cfa: sp 0 + .ra: x30
STACK CFI a338 .cfa: sp 176 +
STACK CFI a344 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a34c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a358 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a364 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a370 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a3c0 x27: .cfa -16 + ^
STACK CFI a488 x27: x27
STACK CFI a4c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a4c8 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI a4e0 x27: .cfa -16 + ^
STACK CFI a5c0 x27: x27
STACK CFI a5c4 x27: .cfa -16 + ^
STACK CFI INIT a5d0 298 .cfa: sp 0 + .ra: x30
STACK CFI a5d8 .cfa: sp 176 +
STACK CFI a5dc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a5e4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a5f8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a600 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a610 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a618 x27: .cfa -16 + ^
STACK CFI a758 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI a760 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT a870 4f8 .cfa: sp 0 + .ra: x30
STACK CFI a878 .cfa: sp 352 +
STACK CFI a884 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a894 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a89c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a8a8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a8b0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a998 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI a9a0 .cfa: sp 352 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI aa40 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ab40 x25: x25 x26: x26
STACK CFI abd8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI abe8 x25: x25 x26: x26
STACK CFI abec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ac2c x25: x25 x26: x26
STACK CFI ac30 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ac34 x25: x25 x26: x26
STACK CFI acd4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ace8 x25: x25 x26: x26
STACK CFI acf0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI acf4 x25: x25 x26: x26
STACK CFI ad04 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ad08 x25: x25 x26: x26
STACK CFI ad18 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ad1c x25: x25 x26: x26
STACK CFI ad20 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ad60 x25: x25 x26: x26
STACK CFI ad64 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT ad70 958 .cfa: sp 0 + .ra: x30
STACK CFI ad78 .cfa: sp 304 +
STACK CFI ad84 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ad8c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ad94 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ada0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI adac x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b17c .cfa: sp 304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT b6d0 364 .cfa: sp 0 + .ra: x30
STACK CFI b6d8 .cfa: sp 208 +
STACK CFI b6e4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b6ec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b6f8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b700 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b70c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b764 x27: .cfa -16 + ^
STACK CFI b8bc x27: x27
STACK CFI b8f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b8fc .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI b914 x27: .cfa -16 + ^
STACK CFI ba2c x27: x27
STACK CFI ba30 x27: .cfa -16 + ^
STACK CFI INIT ba34 484 .cfa: sp 0 + .ra: x30
STACK CFI ba3c .cfa: sp 224 +
STACK CFI ba48 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI ba54 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ba5c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ba64 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ba94 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI bab0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI bb48 x25: x25 x26: x26
STACK CFI bb4c x27: x27 x28: x28
STACK CFI bb54 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI bcf8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI bd2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI bd34 .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI beac x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI beb0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI beb4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT bec0 444 .cfa: sp 0 + .ra: x30
STACK CFI bec8 .cfa: sp 176 +
STACK CFI bed4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI bedc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI bee4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI bf10 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI bf2c x25: .cfa -16 + ^
STACK CFI c06c x23: x23 x24: x24
STACK CFI c070 x25: x25
STACK CFI c0a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c0a8 .cfa: sp 176 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI c0b0 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI c2f8 x23: x23 x24: x24 x25: x25
STACK CFI c2fc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI c300 x25: .cfa -16 + ^
STACK CFI INIT c304 1b0 .cfa: sp 0 + .ra: x30
STACK CFI c30c .cfa: sp 144 +
STACK CFI c318 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c320 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c32c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c410 .cfa: sp 144 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT c4b4 90c .cfa: sp 0 + .ra: x30
STACK CFI c4bc .cfa: sp 448 +
STACK CFI c4c8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c4d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c4dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c4e8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c4f8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c554 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c768 x21: x21 x22: x22
STACK CFI c7a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c7a8 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI ccb0 x21: x21 x22: x22
STACK CFI ccb8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI cd88 x21: x21 x22: x22
STACK CFI cd8c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI INIT cdc0 4d4 .cfa: sp 0 + .ra: x30
STACK CFI cdc8 .cfa: sp 240 +
STACK CFI cdd4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI cde0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI cdec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI cdf8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ce00 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI cf04 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d008 x27: x27 x28: x28
STACK CFI d074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI d07c .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI d080 x27: x27 x28: x28
STACK CFI d094 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d110 x27: x27 x28: x28
STACK CFI d164 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d198 x27: x27 x28: x28
STACK CFI d22c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d23c x27: x27 x28: x28
STACK CFI d240 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d288 x27: x27 x28: x28
STACK CFI d290 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT d294 3f4 .cfa: sp 0 + .ra: x30
STACK CFI d29c .cfa: sp 208 +
STACK CFI d2a8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d2b4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d2bc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d2c4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d2cc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d2d8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d4e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI d4f0 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT d690 35c .cfa: sp 0 + .ra: x30
STACK CFI d698 .cfa: sp 192 +
STACK CFI d6a4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d6b0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d6b8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d6c0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d6cc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d6d8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d87c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI d884 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT d9f0 2fc .cfa: sp 0 + .ra: x30
STACK CFI d9f8 .cfa: sp 160 +
STACK CFI da04 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI da0c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI da14 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI da4c x25: .cfa -16 + ^
STACK CFI da64 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI dacc x23: x23 x24: x24
STACK CFI dad0 x25: x25
STACK CFI db08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI db10 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI dc3c x23: x23 x24: x24
STACK CFI dc50 x25: x25
STACK CFI dc58 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI dce0 x23: x23 x24: x24 x25: x25
STACK CFI dce4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI dce8 x25: .cfa -16 + ^
STACK CFI INIT dcf0 2f8 .cfa: sp 0 + .ra: x30
STACK CFI dcf8 .cfa: sp 160 +
STACK CFI dd04 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI dd0c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI dd18 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI dd24 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI dd68 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI de40 x25: x25 x26: x26
STACK CFI de74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI de7c .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI debc x25: x25 x26: x26
STACK CFI dec4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI dfe0 x25: x25 x26: x26
STACK CFI dfe4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT dff0 2f8 .cfa: sp 0 + .ra: x30
STACK CFI dff8 .cfa: sp 160 +
STACK CFI e004 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI e00c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI e014 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI e020 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI e068 x25: .cfa -16 + ^
STACK CFI e0ec x25: x25
STACK CFI e120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI e128 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI e134 x25: x25
STACK CFI e13c x25: .cfa -16 + ^
STACK CFI e2e0 x25: x25
STACK CFI e2e4 x25: .cfa -16 + ^
