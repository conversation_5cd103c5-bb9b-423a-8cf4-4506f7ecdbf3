MODULE Linux arm64 AABC8E98C005B1329739DF080C98A4DD0 slam_node_utest
INFO CODE_ID 988EBCAA05C032B19739DF080C98A4DD
PUBLIC 2bd0 0 _init
PUBLIC 2f80 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&) [clone .constprop.0]
PUBLIC 3090 0 main
PUBLIC 30c0 0 _GLOBAL__sub_I__ZN14Queue_ops_Test10test_info_E
PUBLIC 35c0 0 _start
PUBLIC 35f4 0 call_weak_fn
PUBLIC 3610 0 deregister_tm_clones
PUBLIC 3640 0 register_tm_clones
PUBLIC 3680 0 __do_global_dtors_aux
PUBLIC 36d0 0 frame_dummy
PUBLIC 36e0 0 Queue_ops_Test::TestBody()
PUBLIC 3930 0 testing::Test::Setup()
PUBLIC 3940 0 testing::internal::TestFactoryImpl<Queue_ops_Test>::~TestFactoryImpl()
PUBLIC 3950 0 testing::internal::TestFactoryImpl<Queue_ops_Test>::~TestFactoryImpl()
PUBLIC 3960 0 Queue_ops_Test::~Queue_ops_Test()
PUBLIC 3970 0 Queue_ops_Test::~Queue_ops_Test()
PUBLIC 39b0 0 testing::internal::TestFactoryImpl<Queue_ops_Test>::CreateTest()
PUBLIC 3a00 0 std::_Rb_tree<std::type_index, std::pair<std::type_index const, std::type_index>, std::_Select1st<std::pair<std::type_index const, std::type_index> >, std::less<std::type_index>, std::allocator<std::pair<std::type_index const, std::type_index> > >::_M_erase(std::_Rb_tree_node<std::pair<std::type_index const, std::type_index> >*) [clone .isra.0]
PUBLIC 3b80 0 cereal::detail::PolymorphicCasters::~PolymorphicCasters()
PUBLIC 3ce0 0 std::unique_ptr<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::default_delete<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > >::~unique_ptr()
PUBLIC 3d30 0 cereal::detail::StaticObject<cereal::detail::PolymorphicCasters>::create()
PUBLIC 3de0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > testing::PrintToString<int>(int const&)
PUBLIC 4150 0 testing::AssertionResult testing::internal::CmpHelperOpFailure<int, int>(char const*, char const*, int const&, int const&, char const*)
PUBLIC 4ea4 0 _fini
STACK CFI INIT 35c0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3610 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3640 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3680 48 .cfa: sp 0 + .ra: x30
STACK CFI 3684 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 368c x19: .cfa -16 + ^
STACK CFI 36c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 36d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3930 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3940 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3950 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3960 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3970 34 .cfa: sp 0 + .ra: x30
STACK CFI 3974 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3984 x19: .cfa -16 + ^
STACK CFI 39a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 39b0 50 .cfa: sp 0 + .ra: x30
STACK CFI 39b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 39c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 39e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 39e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2f80 104 .cfa: sp 0 + .ra: x30
STACK CFI 2f84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2f94 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2f9c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 301c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3a00 180 .cfa: sp 0 + .ra: x30
STACK CFI 3a08 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3a10 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3a18 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3a24 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3a48 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3a4c x27: .cfa -16 + ^
STACK CFI 3aa0 x21: x21 x22: x22
STACK CFI 3aa4 x27: x27
STACK CFI 3ac0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 3adc x21: x21 x22: x22 x27: x27
STACK CFI 3af8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 3b14 x21: x21 x22: x22 x27: x27
STACK CFI 3b50 x25: x25 x26: x26
STACK CFI 3b78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 3b80 158 .cfa: sp 0 + .ra: x30
STACK CFI 3b84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3b8c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3b98 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3cc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 3090 24 .cfa: sp 0 + .ra: x30
STACK CFI 3094 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3ce0 50 .cfa: sp 0 + .ra: x30
STACK CFI 3ce4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3cec x19: .cfa -16 + ^
STACK CFI 3d20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3d24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3d2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3d30 a8 .cfa: sp 0 + .ra: x30
STACK CFI 3d34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d3c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3d5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3de0 364 .cfa: sp 0 + .ra: x30
STACK CFI 3de4 .cfa: sp 560 +
STACK CFI 3df0 .ra: .cfa -552 + ^ x29: .cfa -560 + ^
STACK CFI 3df8 x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI 3e00 x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI 3e0c x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^
STACK CFI 3e14 x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI 4048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 404c .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^ x29: .cfa -560 + ^
STACK CFI INIT 4150 d54 .cfa: sp 0 + .ra: x30
STACK CFI 4154 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 4164 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 416c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 4174 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 4180 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 418c x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 4838 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 483c .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI INIT 30c0 4d0 .cfa: sp 0 + .ra: x30
STACK CFI 30c4 .cfa: sp 176 +
STACK CFI 30d8 .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 30e0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 30ec x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 30f8 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 33c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 33c4 .cfa: sp 176 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI INIT 36e0 248 .cfa: sp 0 + .ra: x30
STACK CFI 36e4 .cfa: sp 640 +
STACK CFI 36f0 .ra: .cfa -632 + ^ x29: .cfa -640 + ^
STACK CFI 36f8 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI 3704 x21: .cfa -608 + ^ x22: .cfa -600 + ^
STACK CFI 3884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3888 .cfa: sp 640 + .ra: .cfa -632 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x29: .cfa -640 + ^
