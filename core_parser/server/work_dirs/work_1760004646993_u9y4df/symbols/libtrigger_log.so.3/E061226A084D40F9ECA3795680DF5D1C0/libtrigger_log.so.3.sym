MODULE Linux arm64 E061226A084D40F9ECA3795680DF5D1C0 libtrigger_log.so.3
INFO CODE_ID 6A2261E04D08F940ECA3795680DF5D1C
PUBLIC 870 0 _init
PUBLIC 8f0 0 call_weak_fn
PUBLIC 910 0 deregister_tm_clones
PUBLIC 940 0 register_tm_clones
PUBLIC 980 0 __do_global_dtors_aux
PUBLIC 9d0 0 frame_dummy
PUBLIC 9e0 0 lios::trigger_log::Init()
PUBLIC 9f0 0 lios::trigger_log::Trigger()
PUBLIC 9f4 0 _fini
STACK CFI INIT 910 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 940 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 980 48 .cfa: sp 0 + .ra: x30
STACK CFI 984 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 98c x19: .cfa -16 + ^
STACK CFI 9c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9f0 4 .cfa: sp 0 + .ra: x30
