MODULE Linux arm64 F0DBB807A249148CB7BC56497C63048E0 libpanel.so.6
INFO CODE_ID 07B8DBF049A28C14B7BC56497C63048E06AB4001
PUBLIC 1770 0 ground_panel
PUBLIC 17d4 0 panel_above
PUBLIC 1810 0 ceiling_panel
PUBLIC 1874 0 panel_below
PUBLIC 18d4 0 bottom_panel
PUBLIC 1bc0 0 del_panel
PUBLIC 1e70 0 hide_panel
PUBLIC 2114 0 panel_hidden
PUBLIC 2180 0 move_panel
PUBLIC 23d0 0 replace_panel
PUBLIC 25f0 0 show_panel
PUBLIC 2880 0 new_panel
PUBLIC 2930 0 top_panel
PUBLIC 2950 0 update_panels_sp
PUBLIC 2b30 0 update_panels
PUBLIC 2b54 0 set_panel_userptr
PUBLIC 2b84 0 panel_userptr
PUBLIC 2bb4 0 panel_window
STACK CFI INIT 16a0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16d0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1710 48 .cfa: sp 0 + .ra: x30
STACK CFI 1714 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 171c x19: .cfa -16 + ^
STACK CFI 1754 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1760 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1770 64 .cfa: sp 0 + .ra: x30
STACK CFI 1794 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 17ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 17b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 17b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 17d4 38 .cfa: sp 0 + .ra: x30
STACK CFI 17dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 17ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 17f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1800 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1810 64 .cfa: sp 0 + .ra: x30
STACK CFI 1834 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 184c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1854 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1858 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1874 60 .cfa: sp 0 + .ra: x30
STACK CFI 1884 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 188c x19: .cfa -16 + ^
STACK CFI 18bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 18d4 2ec .cfa: sp 0 + .ra: x30
STACK CFI 18e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 18ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 18f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1a24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1a2c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 1a88 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1aa4 x25: .cfa -16 + ^
STACK CFI 1b50 x23: x23 x24: x24
STACK CFI 1b54 x25: x25
STACK CFI 1b5c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 1b64 x23: x23 x24: x24 x25: x25
STACK CFI 1b74 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 1b80 x23: x23 x24: x24 x25: x25
STACK CFI 1b9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1ba4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1bc0 2ac .cfa: sp 0 + .ra: x30
STACK CFI 1bc8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1bd0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1bdc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1cdc x21: x21 x22: x22
STACK CFI 1ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1cf0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 1d40 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1d5c x25: .cfa -16 + ^
STACK CFI 1e08 x23: x23 x24: x24
STACK CFI 1e0c x25: x25
STACK CFI 1e14 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 1e1c x23: x23 x24: x24 x25: x25
STACK CFI 1e2c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 1e38 x23: x23 x24: x24 x25: x25
STACK CFI 1e5c x21: x21 x22: x22
STACK CFI 1e64 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 1e70 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 1e80 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1e88 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1e94 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1f98 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 1fe8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2004 x25: .cfa -16 + ^
STACK CFI 20b0 x23: x23 x24: x24
STACK CFI 20b4 x25: x25
STACK CFI 20bc x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 20c4 x23: x23 x24: x24 x25: x25
STACK CFI 20d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 20e0 x23: x23 x24: x24 x25: x25
STACK CFI 2104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 210c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2114 68 .cfa: sp 0 + .ra: x30
STACK CFI 2124 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 212c x19: .cfa -16 + ^
STACK CFI 2154 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2164 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2180 24c .cfa: sp 0 + .ra: x30
STACK CFI 2190 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2198 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 21a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 21ac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2290 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 22c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 22c8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 22dc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2390 x25: x25 x26: x26
STACK CFI 2398 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 23c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 23d0 220 .cfa: sp 0 + .ra: x30
STACK CFI 23e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 23e8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 23f4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 24cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 24d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 2500 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 251c x25: .cfa -16 + ^
STACK CFI 25c8 x23: x23 x24: x24
STACK CFI 25cc x25: x25
STACK CFI 25d4 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 25e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 25f0 288 .cfa: sp 0 + .ra: x30
STACK CFI 2600 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2608 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2614 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2728 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2730 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 277c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2798 x25: .cfa -16 + ^
STACK CFI 2840 x23: x23 x24: x24
STACK CFI 2844 x25: x25
STACK CFI 284c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 2854 x23: x23 x24: x24 x25: x25
STACK CFI 2864 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 2870 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 2880 ac .cfa: sp 0 + .ra: x30
STACK CFI 2888 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2890 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 28d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 28dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 28e4 x21: .cfa -16 + ^
STACK CFI 28f8 x21: x21
STACK CFI 2904 x21: .cfa -16 + ^
STACK CFI 2928 x21: x21
STACK CFI INIT 2930 18 .cfa: sp 0 + .ra: x30
STACK CFI 2938 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2940 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2950 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 2960 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2968 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 297c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2980 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2984 x25: .cfa -16 + ^
STACK CFI 2a34 x19: x19 x20: x20
STACK CFI 2a38 x23: x23 x24: x24
STACK CFI 2a3c x25: x25
STACK CFI 2a44 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2a4c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 2b30 24 .cfa: sp 0 + .ra: x30
STACK CFI 2b38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2b54 30 .cfa: sp 0 + .ra: x30
STACK CFI 2b5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2b84 30 .cfa: sp 0 + .ra: x30
STACK CFI 2b8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2ba4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2ba8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2bb4 30 .cfa: sp 0 + .ra: x30
STACK CFI 2bbc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2bcc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2bd4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2bd8 .cfa: sp 0 + .ra: .ra x29: x29
