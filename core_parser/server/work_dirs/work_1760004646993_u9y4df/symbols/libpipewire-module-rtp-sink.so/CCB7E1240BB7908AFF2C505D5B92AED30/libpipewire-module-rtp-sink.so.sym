MODULE Linux arm64 CCB7E1240BB7908AFF2C505D5B92AED30 libpipewire-module-rtp-sink.so
INFO CODE_ID 24E1B7CCB70B8A90FF2C505D5B92AED36FB9DB66
PUBLIC d2b0 0 pipewire__module_init
STACK CFI INIT 2d10 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d40 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d80 48 .cfa: sp 0 + .ra: x30
STACK CFI 2d84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d8c x19: .cfa -16 + ^
STACK CFI 2dc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2dd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2de0 1c .cfa: sp 0 + .ra: x30
STACK CFI 2de8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2df4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2e00 340 .cfa: sp 0 + .ra: x30
STACK CFI 2e08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2ef4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2f00 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3038 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3044 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3094 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 309c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3114 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 311c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3140 50 .cfa: sp 0 + .ra: x30
STACK CFI 3148 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3160 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3168 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3174 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 317c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3188 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3190 50 .cfa: sp 0 + .ra: x30
STACK CFI 3198 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 31a0 x19: .cfa -16 + ^
STACK CFI 31d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 31e0 128 .cfa: sp 0 + .ra: x30
STACK CFI 31e8 .cfa: sp 112 +
STACK CFI 31fc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3204 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3210 x21: .cfa -16 + ^
STACK CFI 32fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3304 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3310 114 .cfa: sp 0 + .ra: x30
STACK CFI 3318 .cfa: sp 96 +
STACK CFI 3328 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3334 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3418 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3420 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3424 e8 .cfa: sp 0 + .ra: x30
STACK CFI 342c .cfa: sp 80 +
STACK CFI 3440 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 34a8 .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3510 90 .cfa: sp 0 + .ra: x30
STACK CFI 3520 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 352c x19: .cfa -16 + ^
STACK CFI 3558 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3560 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 359c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 35a0 54 .cfa: sp 0 + .ra: x30
STACK CFI 35a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35b0 x19: .cfa -16 + ^
STACK CFI 35ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 35f4 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 35fc .cfa: sp 112 +
STACK CFI 3600 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3608 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3618 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 37a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 37ac .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 37c0 50 .cfa: sp 0 + .ra: x30
STACK CFI 37c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37d0 x19: .cfa -16 + ^
STACK CFI 3808 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3810 104 .cfa: sp 0 + .ra: x30
STACK CFI 3818 .cfa: sp 96 +
STACK CFI 381c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3824 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3830 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 386c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3874 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 390c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3914 30c .cfa: sp 0 + .ra: x30
STACK CFI 391c .cfa: sp 96 +
STACK CFI 3928 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3930 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 39b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 39d4 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3a1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a24 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3c20 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 3c28 .cfa: sp 64 +
STACK CFI 3c2c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c48 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3cec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3cf4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3de4 2b28 .cfa: sp 0 + .ra: x30
STACK CFI 3dec .cfa: sp 480 +
STACK CFI 3e00 .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 3e18 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 3e20 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 3e38 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 4590 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4598 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 6910 fe8 .cfa: sp 0 + .ra: x30
STACK CFI 6918 .cfa: sp 128 +
STACK CFI 6924 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 692c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6990 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6994 x23: .cfa -16 + ^
STACK CFI 6aa4 x21: x21 x22: x22 x23: x23
STACK CFI 6acc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6ad0 x23: .cfa -16 + ^
STACK CFI 6bc8 x21: x21 x22: x22
STACK CFI 6bcc x23: x23
STACK CFI 6bd4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6c9c x21: x21 x22: x22
STACK CFI 6ca0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6ca4 x23: .cfa -16 + ^
STACK CFI 6d94 x21: x21 x22: x22 x23: x23
STACK CFI 6db0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6e74 x21: x21 x22: x22
STACK CFI 6e90 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6e94 x23: .cfa -16 + ^
STACK CFI 6f98 x21: x21 x22: x22 x23: x23
STACK CFI 6f9c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7064 x21: x21 x22: x22
STACK CFI 7068 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 712c x21: x21 x22: x22
STACK CFI 7130 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7134 x23: .cfa -16 + ^
STACK CFI 7248 x21: x21 x22: x22 x23: x23
STACK CFI 724c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7314 x21: x21 x22: x22
STACK CFI 7350 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7358 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 735c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7360 x23: .cfa -16 + ^
STACK CFI 745c x21: x21 x22: x22 x23: x23
STACK CFI 7460 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7464 x23: .cfa -16 + ^
STACK CFI 75ac x21: x21 x22: x22 x23: x23
STACK CFI 75b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 76a4 x21: x21 x22: x22
STACK CFI 76a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 76e8 x23: .cfa -16 + ^
STACK CFI 78ec x21: x21 x22: x22 x23: x23
STACK CFI 78f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 78f4 x23: .cfa -16 + ^
STACK CFI INIT 7900 76c .cfa: sp 0 + .ra: x30
STACK CFI 7908 .cfa: sp 272 +
STACK CFI 7914 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 791c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 793c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7944 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 794c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 79f4 x21: x21 x22: x22
STACK CFI 79f8 x23: x23 x24: x24
STACK CFI 79fc x25: x25 x26: x26
STACK CFI 7a08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7a10 .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 7a14 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7ccc x27: x27 x28: x28
STACK CFI 7cd0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7cd4 x27: x27 x28: x28
STACK CFI 7cd8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 7d28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7d30 .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 7d48 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7d54 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7dd4 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7e04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e28 .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8058 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 805c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8060 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8064 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8068 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 8070 510 .cfa: sp 0 + .ra: x30
STACK CFI 8078 .cfa: sp 160 +
STACK CFI 807c .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8084 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 8098 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 80a0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 80a4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 80a8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 816c v8: .cfa -48 + ^
STACK CFI 821c v8: v8
STACK CFI 8290 x21: x21 x22: x22
STACK CFI 8294 x23: x23 x24: x24
STACK CFI 8298 x25: x25 x26: x26
STACK CFI 829c x27: x27 x28: x28
STACK CFI 82a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 82b0 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 83a4 v8: .cfa -48 + ^
STACK CFI 83f8 v8: v8
STACK CFI 8428 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 845c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8464 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 847c v8: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 8480 v8: v8
STACK CFI 84b0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 84c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 84e8 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 8580 510 .cfa: sp 0 + .ra: x30
STACK CFI 8588 .cfa: sp 160 +
STACK CFI 858c .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8594 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 85a8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 85b0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 85b4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 85b8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 867c v8: .cfa -48 + ^
STACK CFI 872c v8: v8
STACK CFI 87a0 x21: x21 x22: x22
STACK CFI 87a4 x23: x23 x24: x24
STACK CFI 87a8 x25: x25 x26: x26
STACK CFI 87ac x27: x27 x28: x28
STACK CFI 87b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 87c0 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 88b4 v8: .cfa -48 + ^
STACK CFI 8908 v8: v8
STACK CFI 8938 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 896c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8974 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 898c v8: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 8990 v8: v8
STACK CFI 89c0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 89d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 89f8 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 8a90 62c .cfa: sp 0 + .ra: x30
STACK CFI 8a98 .cfa: sp 288 +
STACK CFI 8aa4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8aac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8acc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8ad8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8adc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8ae0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8dd8 x21: x21 x22: x22
STACK CFI 8ddc x23: x23 x24: x24
STACK CFI 8de0 x25: x25 x26: x26
STACK CFI 8de4 x27: x27 x28: x28
STACK CFI 8e0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8e14 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8e84 x21: x21 x22: x22
STACK CFI 8e88 x23: x23 x24: x24
STACK CFI 8e8c x25: x25 x26: x26
STACK CFI 8e90 x27: x27 x28: x28
STACK CFI 8e94 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8fc4 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 901c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9040 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 9058 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 90a8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 90ac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 90b0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 90b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 90b8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 90c0 75c .cfa: sp 0 + .ra: x30
STACK CFI 90c8 .cfa: sp 304 +
STACK CFI 90d4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 90dc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 90fc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9108 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 910c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9110 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 91e0 x21: x21 x22: x22
STACK CFI 91e4 x23: x23 x24: x24
STACK CFI 91e8 x25: x25 x26: x26
STACK CFI 91ec x27: x27 x28: x28
STACK CFI 9214 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 921c .cfa: sp 304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 9240 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9298 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 92bc .cfa: sp 304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 92d4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9808 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 980c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9810 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9814 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9818 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 9820 360 .cfa: sp 0 + .ra: x30
STACK CFI 9828 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 983c .cfa: sp 1568 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 98a4 .cfa: sp 96 +
STACK CFI 98b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 98bc .cfa: sp 1568 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 98c0 x25: .cfa -32 + ^
STACK CFI 98c8 x26: .cfa -24 + ^
STACK CFI 98d0 x27: .cfa -16 + ^
STACK CFI 98d8 x28: .cfa -8 + ^
STACK CFI 9ac0 x25: x25
STACK CFI 9ac4 x26: x26
STACK CFI 9ac8 x27: x27
STACK CFI 9acc x28: x28
STACK CFI 9ad0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9b6c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9b70 x25: .cfa -32 + ^
STACK CFI 9b74 x26: .cfa -24 + ^
STACK CFI 9b78 x27: .cfa -16 + ^
STACK CFI 9b7c x28: .cfa -8 + ^
STACK CFI INIT 9b80 3c0 .cfa: sp 0 + .ra: x30
STACK CFI 9b88 .cfa: sp 128 +
STACK CFI 9b8c .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9b94 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 9ba8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 9bb4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 9bbc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 9bc0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 9ce0 x21: x21 x22: x22
STACK CFI 9ce4 x23: x23 x24: x24
STACK CFI 9ce8 x25: x25 x26: x26
STACK CFI 9cec x27: x27 x28: x28
STACK CFI 9cf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9cf8 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 9e10 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9e44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9e4c .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 9e64 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 9f08 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9f1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9f40 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 9f48 .cfa: sp 448 +
STACK CFI 9f58 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9f6c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9f74 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9f84 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a0f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a0f8 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT a404 20c .cfa: sp 0 + .ra: x30
STACK CFI a40c .cfa: sp 96 +
STACK CFI a418 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a424 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a42c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a438 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI a5a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a5a8 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI a5f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI a5fc .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT a610 fec .cfa: sp 0 + .ra: x30
STACK CFI a618 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI a628 .cfa: sp 1360 + x19: .cfa -112 + ^ x20: .cfa -104 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI a664 x21: .cfa -96 + ^
STACK CFI a66c x22: .cfa -88 + ^
STACK CFI a670 x23: .cfa -80 + ^
STACK CFI a678 x24: .cfa -72 + ^
STACK CFI a79c x27: .cfa -48 + ^
STACK CFI a7a4 x28: .cfa -40 + ^
STACK CFI a7a8 v8: .cfa -32 + ^
STACK CFI a7ac v9: .cfa -24 + ^
STACK CFI a7b0 v10: .cfa -16 + ^
STACK CFI a7b4 v11: .cfa -8 + ^
STACK CFI adc0 x21: x21
STACK CFI adc4 x22: x22
STACK CFI adc8 x23: x23
STACK CFI adcc x24: x24
STACK CFI add0 x27: x27
STACK CFI add4 x28: x28
STACK CFI add8 v8: v8
STACK CFI addc v9: v9
STACK CFI ade0 v10: v10
STACK CFI ade4 v11: v11
STACK CFI ae04 .cfa: sp 128 +
STACK CFI ae14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI ae1c .cfa: sp 1360 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI ae30 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI ae58 v10: v10 v11: v11 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI af0c x27: .cfa -48 + ^
STACK CFI af14 x28: .cfa -40 + ^
STACK CFI af18 v8: .cfa -32 + ^
STACK CFI af1c v9: .cfa -24 + ^
STACK CFI af20 v10: .cfa -16 + ^
STACK CFI af24 v11: .cfa -8 + ^
STACK CFI af3c v10: v10 v11: v11 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI afb8 x27: .cfa -48 + ^
STACK CFI afc0 x28: .cfa -40 + ^
STACK CFI afc8 v8: .cfa -32 + ^
STACK CFI afd0 v9: .cfa -24 + ^
STACK CFI afd8 v10: .cfa -16 + ^
STACK CFI afe0 v11: .cfa -8 + ^
STACK CFI b13c x21: x21
STACK CFI b140 x22: x22
STACK CFI b144 x23: x23
STACK CFI b148 x24: x24
STACK CFI b14c x27: x27
STACK CFI b150 x28: x28
STACK CFI b154 v8: v8
STACK CFI b158 v9: v9
STACK CFI b15c v10: v10
STACK CFI b160 v11: v11
STACK CFI b178 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b3d4 v10: v10 v11: v11 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI b3dc x21: x21
STACK CFI b3e4 x22: x22
STACK CFI b3e8 x23: x23
STACK CFI b3ec x24: x24
STACK CFI b3f0 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b440 v10: v10 v11: v11 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI b554 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b57c v10: v10 v11: v11 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI b5c4 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI b5c8 x21: .cfa -96 + ^
STACK CFI b5cc x22: .cfa -88 + ^
STACK CFI b5d0 x23: .cfa -80 + ^
STACK CFI b5d4 x24: .cfa -72 + ^
STACK CFI b5d8 x27: .cfa -48 + ^
STACK CFI b5dc x28: .cfa -40 + ^
STACK CFI b5e0 v8: .cfa -32 + ^
STACK CFI b5e4 v9: .cfa -24 + ^
STACK CFI b5e8 v10: .cfa -16 + ^
STACK CFI b5ec v11: .cfa -8 + ^
STACK CFI b5f0 v10: v10 v11: v11 v8: v8 v9: v9 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI INIT b600 178 .cfa: sp 0 + .ra: x30
STACK CFI b608 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b618 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b624 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b62c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b63c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b648 .cfa: sp 624 + x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b704 .cfa: sp 96 +
STACK CFI b71c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b724 .cfa: sp 624 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT b780 6f0 .cfa: sp 0 + .ra: x30
STACK CFI b788 .cfa: sp 160 +
STACK CFI b78c .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI b794 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI b7a4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI b7ac x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI b7b4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI b7f4 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI b88c v8: .cfa -32 + ^
STACK CFI b954 v8: v8
STACK CFI b994 x19: x19 x20: x20
STACK CFI b99c x21: x21 x22: x22
STACK CFI b9a0 x23: x23 x24: x24
STACK CFI b9a4 x27: x27 x28: x28
STACK CFI b9b0 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI b9b8 .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI ba04 v8: .cfa -32 + ^
STACK CFI ba1c v8: v8
STACK CFI bb7c x27: x27 x28: x28
STACK CFI bba8 x19: x19 x20: x20
STACK CFI bbac x21: x21 x22: x22
STACK CFI bbb0 x23: x23 x24: x24
STACK CFI bbb8 v8: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI bc1c v8: v8
STACK CFI bca4 x27: x27 x28: x28
STACK CFI bcdc x19: x19 x20: x20
STACK CFI bce4 x21: x21 x22: x22
STACK CFI bce8 x23: x23 x24: x24
STACK CFI bcec x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI bd04 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI bd5c x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI bdb0 x19: x19 x20: x20
STACK CFI bdb8 x21: x21 x22: x22
STACK CFI bdbc x23: x23 x24: x24
STACK CFI bdd8 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI be60 x19: x19 x20: x20
STACK CFI be68 x21: x21 x22: x22
STACK CFI be6c x23: x23 x24: x24
STACK CFI INIT be70 cec .cfa: sp 0 + .ra: x30
STACK CFI be78 .cfa: sp 320 +
STACK CFI be84 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI be8c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI bea8 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI beb0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI becc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI bef8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI c230 x21: x21 x22: x22
STACK CFI c234 x23: x23 x24: x24
STACK CFI c238 x25: x25 x26: x26
STACK CFI c23c x27: x27 x28: x28
STACK CFI c264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c26c .cfa: sp 320 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI c42c v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI c430 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI c438 v12: .cfa -16 + ^ v13: .cfa -8 + ^
STACK CFI c55c v10: v10 v11: v11
STACK CFI c564 v8: v8 v9: v9
STACK CFI c568 v12: v12 v13: v13
STACK CFI c570 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI c614 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9
STACK CFI c6d4 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI c70c v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI c738 x21: x21 x22: x22
STACK CFI c73c x23: x23 x24: x24
STACK CFI c740 x25: x25 x26: x26
STACK CFI c748 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI c788 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9
STACK CFI c80c x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI c844 x23: x23 x24: x24
STACK CFI c84c x25: x25 x26: x26
STACK CFI c850 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI c8b0 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI c8c8 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI c938 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI c960 x21: x21 x22: x22
STACK CFI c968 x23: x23 x24: x24
STACK CFI c96c x25: x25 x26: x26
STACK CFI c970 x27: x27 x28: x28
STACK CFI c974 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI c9a0 x21: x21 x22: x22
STACK CFI c9a8 x23: x23 x24: x24
STACK CFI c9ac x25: x25 x26: x26
STACK CFI c9b0 x27: x27 x28: x28
STACK CFI c9b4 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI c9cc x27: x27 x28: x28
STACK CFI c9e4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI ca2c x27: x27 x28: x28
STACK CFI ca80 x21: x21 x22: x22
STACK CFI ca88 x23: x23 x24: x24
STACK CFI ca8c x25: x25 x26: x26
STACK CFI ca90 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI caa8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI caac x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI cab0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI cab4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI cab8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI cabc v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI cac0 v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI cac4 v12: .cfa -16 + ^ v13: .cfa -8 + ^
STACK CFI cac8 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI caf4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI cb20 x21: x21 x22: x22
STACK CFI cb28 x23: x23 x24: x24
STACK CFI cb2c x25: x25 x26: x26
STACK CFI cb30 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT cb60 74c .cfa: sp 0 + .ra: x30
STACK CFI cb68 .cfa: sp 176 +
STACK CFI cb6c .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI cb78 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI cb88 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI cb90 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI cb98 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI cb9c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI cc64 v8: .cfa -48 + ^
STACK CFI cd34 v8: v8
STACK CFI cd7c x19: x19 x20: x20
STACK CFI cd84 x21: x21 x22: x22
STACK CFI cd88 x23: x23 x24: x24
STACK CFI cd8c x25: x25 x26: x26
STACK CFI cd98 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI cda0 .cfa: sp 176 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI cdec v8: .cfa -48 + ^
STACK CFI ce04 v8: v8
STACK CFI d010 x19: x19 x20: x20
STACK CFI d014 x21: x21 x22: x22
STACK CFI d018 x23: x23 x24: x24
STACK CFI d01c x25: x25 x26: x26
STACK CFI d024 v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI d094 v8: v8
STACK CFI d10c x19: x19 x20: x20
STACK CFI d114 x21: x21 x22: x22
STACK CFI d118 x23: x23 x24: x24
STACK CFI d11c x25: x25 x26: x26
STACK CFI d120 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI d138 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI d190 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI d1e4 x19: x19 x20: x20
STACK CFI d1ec x21: x21 x22: x22
STACK CFI d1f0 x23: x23 x24: x24
STACK CFI d1f4 x25: x25 x26: x26
STACK CFI d210 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI d298 x19: x19 x20: x20
STACK CFI d2a0 x21: x21 x22: x22
STACK CFI d2a4 x23: x23 x24: x24
STACK CFI d2a8 x25: x25 x26: x26
STACK CFI INIT d2b0 13ac .cfa: sp 0 + .ra: x30
STACK CFI d2b8 .cfa: sp 208 +
STACK CFI d2c4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d2cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d2d4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d2ec x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d360 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI db60 x27: x27 x28: x28
STACK CFI dc4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI dc54 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI dc84 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI dcf8 x27: x27 x28: x28
STACK CFI dd00 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI de80 x27: x27 x28: x28
STACK CFI de84 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI dec0 x27: x27 x28: x28
STACK CFI dec4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e080 x27: x27 x28: x28
STACK CFI e084 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e270 x27: x27 x28: x28
STACK CFI e2a0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e384 x27: x27 x28: x28
STACK CFI e388 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e644 x27: x27 x28: x28
STACK CFI e658 x27: .cfa -16 + ^ x28: .cfa -8 + ^
