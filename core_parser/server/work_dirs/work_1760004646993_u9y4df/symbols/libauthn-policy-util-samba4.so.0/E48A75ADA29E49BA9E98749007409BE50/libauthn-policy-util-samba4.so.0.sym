MODULE Linux arm64 E48A75ADA29E49BA9E98749007409BE50 libauthn-policy-util-samba4.so.0
INFO CODE_ID AD758AE49EA2BA499E98749007409BE5FE6291E5
PUBLIC 2080 0 authn_policy_silos_and_policies_in_effect
PUBLIC 20a4 0 authn_policy_allowed_ntlm_network_auth_in_effect
PUBLIC 20d0 0 authn_policy_get_assigned_silo
PUBLIC 24f0 0 authn_policy_kerberos_client
PUBLIC 26f0 0 authn_policy_device_restrictions_present
PUBLIC 2720 0 authn_policy_authenticate_from_device
PUBLIC 2780 0 authn_policy_ntlm_client
PUBLIC 2994 0 authn_policy_ntlm_apply_device_restriction
PUBLIC 2a90 0 authn_policy_server
PUBLIC 2c80 0 authn_policy_restrictions_present
PUBLIC 2cb0 0 _authn_kerberos_client_policy_audit_info
PUBLIC 2d10 0 _authn_ntlm_client_policy_audit_info
PUBLIC 2d50 0 _authn_server_policy_audit_info
PUBLIC 2d90 0 authn_policy_authenticate_to_service
STACK CFI INIT 1a40 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a70 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1ab0 48 .cfa: sp 0 + .ra: x30
STACK CFI 1ab4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1abc x19: .cfa -16 + ^
STACK CFI 1af4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1b00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b10 160 .cfa: sp 0 + .ra: x30
STACK CFI 1b18 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1b34 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1b40 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1b54 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1bb8 x19: x19 x20: x20
STACK CFI 1bc0 x21: x21 x22: x22
STACK CFI 1bc8 x23: x23 x24: x24
STACK CFI 1bcc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1bd4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 1bd8 x19: x19 x20: x20
STACK CFI 1be0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1bec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1bf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1bfc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1c08 x19: x19 x20: x20
STACK CFI 1c10 x21: x21 x22: x22
STACK CFI 1c14 x23: x23 x24: x24
STACK CFI 1c18 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c20 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1c2c x19: x19 x20: x20
STACK CFI 1c34 x21: x21 x22: x22
STACK CFI 1c3c x23: x23 x24: x24
STACK CFI 1c40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c48 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1c54 x19: x19 x20: x20
STACK CFI 1c5c x21: x21 x22: x22
STACK CFI 1c64 x23: x23 x24: x24
STACK CFI 1c68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1c70 178 .cfa: sp 0 + .ra: x30
STACK CFI 1c78 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1c80 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1c90 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1c9c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1ca8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1cbc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1d64 x21: x21 x22: x22
STACK CFI 1d68 x23: x23 x24: x24
STACK CFI 1d6c x27: x27 x28: x28
STACK CFI 1d74 x19: x19 x20: x20
STACK CFI 1d80 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 1d88 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1da0 x19: x19 x20: x20
STACK CFI 1da8 x21: x21 x22: x22
STACK CFI 1dac x23: x23 x24: x24
STACK CFI 1db4 x27: x27 x28: x28
STACK CFI 1db8 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 1dc0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1df0 288 .cfa: sp 0 + .ra: x30
STACK CFI 1df8 .cfa: sp 176 +
STACK CFI 1e04 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1e0c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1e14 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1e20 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1e2c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1e34 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1fb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1fc0 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2080 24 .cfa: sp 0 + .ra: x30
STACK CFI 2088 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2098 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 20a4 24 .cfa: sp 0 + .ra: x30
STACK CFI 20ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 20bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 20d0 1ec .cfa: sp 0 + .ra: x30
STACK CFI 20d8 .cfa: sp 128 +
STACK CFI 20dc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 20e4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2100 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2110 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2170 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2178 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 21c8 x27: .cfa -16 + ^
STACK CFI 21cc x27: x27
STACK CFI 21f4 x27: .cfa -16 + ^
STACK CFI 222c x27: x27
STACK CFI 2230 x27: .cfa -16 + ^
STACK CFI 22a4 x27: x27
STACK CFI 22ac x27: .cfa -16 + ^
STACK CFI 22b0 x27: x27
STACK CFI 22b8 x27: .cfa -16 + ^
STACK CFI INIT 22c0 22c .cfa: sp 0 + .ra: x30
STACK CFI 22c8 .cfa: sp 144 +
STACK CFI 22cc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 22d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 22f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 22fc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2308 x27: .cfa -16 + ^
STACK CFI 2494 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 249c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 24f0 1fc .cfa: sp 0 + .ra: x30
STACK CFI 24f8 .cfa: sp 144 +
STACK CFI 24fc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2504 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2518 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2520 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2528 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2580 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2588 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 2590 x27: .cfa -16 + ^
STACK CFI 26cc x27: x27
STACK CFI 26d0 x27: .cfa -16 + ^
STACK CFI 26e4 x27: x27
STACK CFI 26e8 x27: .cfa -16 + ^
STACK CFI INIT 26f0 30 .cfa: sp 0 + .ra: x30
STACK CFI 26f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2710 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2718 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2720 60 .cfa: sp 0 + .ra: x30
STACK CFI 2740 .cfa: sp 48 +
STACK CFI 274c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2770 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2780 214 .cfa: sp 0 + .ra: x30
STACK CFI 2788 .cfa: sp 144 +
STACK CFI 278c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2794 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 27a8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 27b0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 27b8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2818 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 2820 x27: .cfa -16 + ^
STACK CFI 2974 x27: x27
STACK CFI 2978 x27: .cfa -16 + ^
STACK CFI 298c x27: x27
STACK CFI 2990 x27: .cfa -16 + ^
STACK CFI INIT 2994 f8 .cfa: sp 0 + .ra: x30
STACK CFI 299c .cfa: sp 48 +
STACK CFI 29a0 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29a8 x19: .cfa -16 + ^
STACK CFI 2a20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2a28 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2a60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2a68 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2a78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2a80 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2a90 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 2a98 .cfa: sp 144 +
STACK CFI 2a9c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2aa4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2ab8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2ac4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2b18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2b20 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 2b28 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2b70 x27: .cfa -16 + ^
STACK CFI 2c38 x27: x27
STACK CFI 2c54 x25: x25 x26: x26
STACK CFI 2c58 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2c5c x27: x27
STACK CFI 2c60 x27: .cfa -16 + ^
STACK CFI 2c64 x27: x27
STACK CFI 2c6c x25: x25 x26: x26
STACK CFI 2c70 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2c74 x27: .cfa -16 + ^
STACK CFI INIT 2c80 30 .cfa: sp 0 + .ra: x30
STACK CFI 2c88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2ca0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2ca8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2cb0 5c .cfa: sp 0 + .ra: x30
STACK CFI 2cb8 .cfa: sp 32 +
STACK CFI 2ccc .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2d10 40 .cfa: sp 0 + .ra: x30
STACK CFI 2d18 .cfa: sp 32 +
STACK CFI 2d1c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2d50 40 .cfa: sp 0 + .ra: x30
STACK CFI 2d58 .cfa: sp 32 +
STACK CFI 2d5c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2d90 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2dc4 .cfa: sp 48 +
STACK CFI 2dd4 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2df4 .cfa: sp 0 + .ra: .ra x29: x29
