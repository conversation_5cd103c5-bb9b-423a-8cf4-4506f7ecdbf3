MODULE Linux arm64 582B304BF13169809CB6DAEA8CE4C20C0 libprocess-model-samba4.so.0
INFO CODE_ID 4B302B5831F180699CB6DAEA8CE4C20CBEDF273D
PUBLIC 1380 0 process_model_startup
PUBLIC 1470 0 register_process_model
PUBLIC 1630 0 process_model_single_init
PUBLIC 1650 0 process_model_init
PUBLIC 1714 0 process_model_version
STACK CFI INIT f10 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT f40 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT f80 48 .cfa: sp 0 + .ra: x30
STACK CFI f84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f8c x19: .cfa -16 + ^
STACK CFI fc4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT fd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT fe0 18 .cfa: sp 0 + .ra: x30
STACK CFI fe8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ff0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1000 18 .cfa: sp 0 + .ra: x30
STACK CFI 1008 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1010 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1020 18 .cfa: sp 0 + .ra: x30
STACK CFI 1028 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1030 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1040 88 .cfa: sp 0 + .ra: x30
STACK CFI 1048 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1054 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 106c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1074 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 10c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10d0 178 .cfa: sp 0 + .ra: x30
STACK CFI 10d8 .cfa: sp 160 +
STACK CFI 10e4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 10ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1104 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1110 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 11c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 11d0 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1250 130 .cfa: sp 0 + .ra: x30
STACK CFI 1258 .cfa: sp 144 +
STACK CFI 1264 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 126c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1278 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1284 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 133c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1344 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1374 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 137c .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1380 ec .cfa: sp 0 + .ra: x30
STACK CFI 1388 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1394 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 13a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1408 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1470 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 1478 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1480 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1488 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1498 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 14a0 x25: .cfa -16 + ^
STACK CFI 1500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1508 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 1578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1580 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1630 20 .cfa: sp 0 + .ra: x30
STACK CFI 1638 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1640 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1650 c4 .cfa: sp 0 + .ra: x30
STACK CFI 1658 .cfa: sp 64 +
STACK CFI 166c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1698 x19: .cfa -16 + ^
STACK CFI 16dc x19: x19
STACK CFI 1704 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 170c .cfa: sp 64 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1710 x19: .cfa -16 + ^
STACK CFI INIT 1714 24 .cfa: sp 0 + .ra: x30
STACK CFI 171c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1730 .cfa: sp 0 + .ra: .ra x29: x29
