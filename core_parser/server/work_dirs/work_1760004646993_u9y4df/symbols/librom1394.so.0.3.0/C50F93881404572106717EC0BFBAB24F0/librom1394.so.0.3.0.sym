MODULE Linux arm64 C50F93881404572106717EC0BFBAB24F0 librom1394.so.0
INFO CODE_ID 88930FC50414215706717EC0BFBAB24FCE8DDD62
PUBLIC f20 0 rom1394_get_node_type
PUBLIC f84 0 rom1394_free_directory
PUBLIC 1004 0 make_crc
PUBLIC 1080 0 set_unit_directory
PUBLIC 1150 0 set_textual_leaf
PUBLIC 1210 0 rom1394_set_directory
PUBLIC 1390 0 add_textual_leaf
PUBLIC 1430 0 get_leaf_size
PUBLIC 1460 0 get_unit_size
PUBLIC 1504 0 rom1394_get_size
PUBLIC 15d0 0 rom1394_add_unit
PUBLIC 17c0 0 cooked1394_read
PUBLIC 18b0 0 rom1394_get_bus_info_block_length
PUBLIC 1a10 0 rom1394_get_bus_id
PUBLIC 1b80 0 rom1394_get_bus_options
PUBLIC 1ce0 0 rom1394_get_guid
PUBLIC 1e64 0 read_textual_leaf
PUBLIC 22f0 0 proc_directory
PUBLIC 25d0 0 rom1394_get_directory
PUBLIC 27d0 0 cooked1394_write
STACK CFI INIT e50 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT e80 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT ec0 48 .cfa: sp 0 + .ra: x30
STACK CFI ec4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ecc x19: .cfa -16 + ^
STACK CFI f04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f20 64 .cfa: sp 0 + .ra: x30
STACK CFI f28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI f58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI f64 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI f6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f84 80 .cfa: sp 0 + .ra: x30
STACK CFI f8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f98 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI fec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI ffc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1004 7c .cfa: sp 0 + .ra: x30
STACK CFI 100c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1064 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1070 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1074 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1080 cc .cfa: sp 0 + .ra: x30
STACK CFI 1088 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1094 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1144 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1150 bc .cfa: sp 0 + .ra: x30
STACK CFI 1158 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1160 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 116c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1178 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 1210 180 .cfa: sp 0 + .ra: x30
STACK CFI 1218 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1220 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1228 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 123c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1254 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 12f4 x19: x19 x20: x20
STACK CFI 12f8 x21: x21 x22: x22
STACK CFI 1328 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1330 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1390 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1398 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 13a0 x23: .cfa -16 + ^
STACK CFI 13a8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 13b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1430 28 .cfa: sp 0 + .ra: x30
STACK CFI 1438 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1444 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1460 a4 .cfa: sp 0 + .ra: x30
STACK CFI 1468 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1470 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1478 x21: .cfa -16 + ^
STACK CFI 14e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 14e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 14fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1504 c8 .cfa: sp 0 + .ra: x30
STACK CFI 150c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1520 x21: .cfa -16 + ^
STACK CFI 1530 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 158c x19: x19 x20: x20
STACK CFI 1598 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 15a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 15b4 x19: x19 x20: x20
STACK CFI 15c4 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 15d0 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 15d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 15e0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 15f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 15fc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1604 x27: .cfa -16 + ^
STACK CFI 17b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI INIT 17c0 ec .cfa: sp 0 + .ra: x30
STACK CFI 17c8 .cfa: sp 112 +
STACK CFI 17d0 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 17dc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 17e8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 17f4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1804 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 18a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 18a8 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 18b0 15c .cfa: sp 0 + .ra: x30
STACK CFI 18b8 .cfa: sp 48 +
STACK CFI 18c4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 18cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1960 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1a10 170 .cfa: sp 0 + .ra: x30
STACK CFI 1a18 .cfa: sp 48 +
STACK CFI 1a24 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a2c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1ab4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1abc .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b80 160 .cfa: sp 0 + .ra: x30
STACK CFI 1b88 .cfa: sp 64 +
STACK CFI 1b94 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1b9c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1ba4 x21: .cfa -16 + ^
STACK CFI 1c6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1c74 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1ce0 184 .cfa: sp 0 + .ra: x30
STACK CFI 1ce8 .cfa: sp 64 +
STACK CFI 1cf4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1cfc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d28 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1d80 x21: x21 x22: x22
STACK CFI 1db4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1dbc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1dec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1e5c x21: x21 x22: x22
STACK CFI 1e60 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 1e64 488 .cfa: sp 0 + .ra: x30
STACK CFI 1e6c .cfa: sp 144 +
STACK CFI 1e78 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1e84 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1e98 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1ea8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1ef4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 206c x25: x25 x26: x26
STACK CFI 20ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 20b4 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2200 x25: x25 x26: x26
STACK CFI 2234 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2278 x25: x25 x26: x26
STACK CFI 22b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 22e4 x25: x25 x26: x26
STACK CFI INIT 22f0 2dc .cfa: sp 0 + .ra: x30
STACK CFI 22f8 .cfa: sp 128 +
STACK CFI 2304 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 230c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2314 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2324 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 232c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2390 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 244c x25: x25 x26: x26
STACK CFI 2484 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 248c .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 253c x25: x25 x26: x26
STACK CFI 2544 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2574 x25: x25 x26: x26
STACK CFI 25a8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 25c4 x25: x25 x26: x26
STACK CFI 25c8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 25d0 200 .cfa: sp 0 + .ra: x30
STACK CFI 25d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 25e0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 25f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2638 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2660 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 266c x27: .cfa -16 + ^
STACK CFI 273c x21: x21 x22: x22
STACK CFI 2748 x23: x23 x24: x24
STACK CFI 274c x25: x25 x26: x26
STACK CFI 2750 x27: x27
STACK CFI 2754 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 275c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 2760 x21: x21 x22: x22
STACK CFI 2764 x23: x23 x24: x24
STACK CFI 2770 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2778 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 277c x23: x23 x24: x24
STACK CFI 2784 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2790 x21: x21 x22: x22
STACK CFI 2798 x23: x23 x24: x24
STACK CFI 279c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 27a0 x23: x23 x24: x24
STACK CFI INIT 27d0 ec .cfa: sp 0 + .ra: x30
STACK CFI 27d8 .cfa: sp 112 +
STACK CFI 27e0 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 27ec x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 27f8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2804 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2814 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 28b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 28b8 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
