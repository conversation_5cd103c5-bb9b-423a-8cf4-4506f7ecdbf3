MODULE Linux arm64 6F665E23F935BE5F5FB5F97DF7317C950 libc_malloc_debug.so.0
INFO CODE_ID 235E666F35F95FBE5FB5F97DF7317C956FCC43A9
PUBLIC 3aa0 0 mcheck_check_all
PUBLIC 3ab4 0 mprobe
PUBLIC 3ac4 0 mtrace
PUBLIC 3b80 0 muntrace
PUBLIC 61e0 0 malloc
PUBLIC 6894 0 free
PUBLIC 6ac0 0 mcheck
PUBLIC 6b50 0 mcheck_pedantic
PUBLIC 6be4 0 realloc
PUBLIC 76e0 0 memalign
PUBLIC 7700 0 aligned_alloc
PUBLIC 7730 0 pvalloc
PUBLIC 77c4 0 valloc
PUBLIC 7840 0 posix_memalign
PUBLIC 78b0 0 calloc
PUBLIC 7a20 0 malloc_usable_size
PUBLIC 7bd0 0 malloc_info
PUBLIC 7c60 0 mallopt
PUBLIC 7cf0 0 malloc_stats
PUBLIC 7d54 0 mallinfo2
PUBLIC 7e00 0 mallinfo
PUBLIC 7eb0 0 malloc_trim
PUBLIC 7f24 0 malloc_get_state
PUBLIC 7f50 0 malloc_set_state
STACK CFI INIT 1d80 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1db0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1df0 48 .cfa: sp 0 + .ra: x30
STACK CFI 1df4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1dfc x19: .cfa -16 + ^
STACK CFI 1e34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1e40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e50 6c .cfa: sp 0 + .ra: x30
STACK CFI 1e54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ea8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1eac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1eb4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ec0 24 .cfa: sp 0 + .ra: x30
STACK CFI 1ec4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ee0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ee4 24 .cfa: sp 0 + .ra: x30
STACK CFI 1ee8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1f04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1f10 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1f14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1fc4 6c .cfa: sp 0 + .ra: x30
STACK CFI 2020 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2030 120 .cfa: sp 0 + .ra: x30
STACK CFI 2034 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 20f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 20f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2150 254 .cfa: sp 0 + .ra: x30
STACK CFI 2154 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 22dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 22e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 23a4 134 .cfa: sp 0 + .ra: x30
STACK CFI 23f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2408 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 24e0 4c .cfa: sp 0 + .ra: x30
STACK CFI 24e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 24f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 24fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2528 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2530 2c .cfa: sp 0 + .ra: x30
STACK CFI 2534 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2558 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2560 2c .cfa: sp 0 + .ra: x30
STACK CFI 2564 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2588 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2590 2c .cfa: sp 0 + .ra: x30
STACK CFI 2594 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 25b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 25c0 2c .cfa: sp 0 + .ra: x30
STACK CFI 25c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 25e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 25f0 24 .cfa: sp 0 + .ra: x30
STACK CFI 25f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2610 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2614 bc .cfa: sp 0 + .ra: x30
STACK CFI 2618 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2630 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 26a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 26a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 26d0 2c .cfa: sp 0 + .ra: x30
STACK CFI 26d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 26e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 26ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 26f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2700 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 2704 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2710 .cfa: x29 48 +
STACK CFI 2714 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2720 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 282c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2830 .cfa: x29 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 28a0 24 .cfa: sp 0 + .ra: x30
STACK CFI 28a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 28b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 28bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 28c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 28c4 5c .cfa: sp 0 + .ra: x30
STACK CFI 28f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 290c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2910 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2920 2c .cfa: sp 0 + .ra: x30
STACK CFI 2924 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2948 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2950 508 .cfa: sp 0 + .ra: x30
STACK CFI 2954 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2974 .cfa: sp 4656 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2e34 .cfa: sp 96 +
STACK CFI 2e50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2e54 .cfa: sp 4656 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2e60 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 2e64 .cfa: sp 64 +
STACK CFI 2e74 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2e7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2e88 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 300c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3040 e8 .cfa: sp 0 + .ra: x30
STACK CFI 3044 .cfa: sp 48 +
STACK CFI 3050 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3060 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3114 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3130 cc .cfa: sp 0 + .ra: x30
STACK CFI 3134 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 31b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 31b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3200 190 .cfa: sp 0 + .ra: x30
STACK CFI 3204 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 320c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3220 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3254 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3258 .cfa: sp 112 + .ra: .cfa -104 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 325c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3264 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 334c x19: x19 x20: x20
STACK CFI 3350 x21: x21 x22: x22
STACK CFI 3354 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI INIT 3390 cc .cfa: sp 0 + .ra: x30
STACK CFI 3394 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 339c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 33ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 341c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3420 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 344c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3450 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3460 dc .cfa: sp 0 + .ra: x30
STACK CFI 3464 .cfa: sp 112 +
STACK CFI 3470 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3478 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3480 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3488 x23: .cfa -16 + ^
STACK CFI 3528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 352c .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3540 c8 .cfa: sp 0 + .ra: x30
STACK CFI 354c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 355c x19: .cfa -16 + ^
STACK CFI 35e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 35e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3604 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3610 10c .cfa: sp 0 + .ra: x30
STACK CFI 3614 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3620 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3644 x23: .cfa -16 + ^
STACK CFI 36c0 x23: x23
STACK CFI 36dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 36e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 3700 x23: x23
STACK CFI 3710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3714 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3720 84 .cfa: sp 0 + .ra: x30
STACK CFI 3728 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3794 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 37a4 13c .cfa: sp 0 + .ra: x30
STACK CFI 37a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 37b0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 37bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 37cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 388c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3890 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 38e0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 38e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 38ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3904 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 391c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3920 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 395c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3960 x25: .cfa -16 + ^
STACK CFI 3a24 x23: x23 x24: x24
STACK CFI 3a28 x25: x25
STACK CFI 3a2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3a30 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3aa0 14 .cfa: sp 0 + .ra: x30
STACK CFI 3aa4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ab0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3ab4 10 .cfa: sp 0 + .ra: x30
STACK CFI 3ab8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ac0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3ac4 bc .cfa: sp 0 + .ra: x30
STACK CFI 3ac8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3ad0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3ae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3aec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3b5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3b80 60 .cfa: sp 0 + .ra: x30
STACK CFI 3b84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b98 x19: .cfa -16 + ^
STACK CFI 3bd0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3bd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3bdc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3be0 3c .cfa: sp 0 + .ra: x30
STACK CFI 3bf0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3c08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3c20 d0 .cfa: sp 0 + .ra: x30
STACK CFI 3c24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3c2c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3c38 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3c88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3c8c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3cf0 494 .cfa: sp 0 + .ra: x30
STACK CFI 3cf4 .cfa: sp 128 +
STACK CFI 3d04 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3d0c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3d40 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3d48 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3d4c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3d50 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3f0c x21: x21 x22: x22
STACK CFI 3f10 x23: x23 x24: x24
STACK CFI 3f14 x25: x25 x26: x26
STACK CFI 3f18 x27: x27 x28: x28
STACK CFI 3f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f44 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 3fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3fa8 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 3fec x21: x21 x22: x22
STACK CFI 3ff4 x23: x23 x24: x24
STACK CFI 3ffc x27: x27 x28: x28
STACK CFI 4008 x25: x25 x26: x26
STACK CFI 400c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4170 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4174 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4178 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 417c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4180 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 4184 140 .cfa: sp 0 + .ra: x30
STACK CFI 4188 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4190 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 419c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 41a4 x23: .cfa -16 + ^
STACK CFI 4248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 424c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4264 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 42c4 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 42c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 42d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4314 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4324 x23: .cfa -16 + ^
STACK CFI 43bc x21: x21 x22: x22 x23: x23
STACK CFI 43cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 43d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 43fc x21: x21 x22: x22
STACK CFI 4400 x23: x23
STACK CFI 4404 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4408 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 4414 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4418 x23: .cfa -16 + ^
STACK CFI 441c x21: x21 x22: x22 x23: x23
STACK CFI 4428 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 442c x23: .cfa -16 + ^
STACK CFI 4430 x21: x21 x22: x22 x23: x23
STACK CFI 443c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4440 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4470 150 .cfa: sp 0 + .ra: x30
STACK CFI 4478 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4480 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4488 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 449c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 44ac x25: .cfa -16 + ^
STACK CFI 4530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4534 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 456c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4570 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 4590 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 4594 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 45c0 fb8 .cfa: sp 0 + .ra: x30
STACK CFI 45c4 .cfa: sp 128 +
STACK CFI 45d0 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 45d8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 45ec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 45fc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4604 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 46ac x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 46b0 x27: x27 x28: x28
STACK CFI 4924 x19: x19 x20: x20
STACK CFI 4928 x23: x23 x24: x24
STACK CFI 4958 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 495c .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 49cc x19: x19 x20: x20
STACK CFI 49d0 x23: x23 x24: x24
STACK CFI 49d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4a14 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 4a30 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4a3c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4a40 x27: x27 x28: x28
STACK CFI 4a4c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4a50 x27: x27 x28: x28
STACK CFI 4a7c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4a80 x27: x27 x28: x28
STACK CFI 4af8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4afc x27: x27 x28: x28
STACK CFI 4b54 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4b58 x27: x27 x28: x28
STACK CFI 4b64 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4b68 x27: x27 x28: x28
STACK CFI 4bc8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4bcc x27: x27 x28: x28
STACK CFI 4c6c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4c70 x27: x27 x28: x28
STACK CFI 4c7c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4c80 x27: x27 x28: x28
STACK CFI 4dd0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4dd4 x27: x27 x28: x28
STACK CFI 4df4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4df8 x27: x27 x28: x28
STACK CFI 4e40 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4e44 x19: x19 x20: x20 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 4e48 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4e4c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4e50 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4e54 x27: x27 x28: x28
STACK CFI 4f14 x19: x19 x20: x20
STACK CFI 4f18 x23: x23 x24: x24
STACK CFI 4f1c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4f28 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4f80 x19: x19 x20: x20
STACK CFI 4f88 x23: x23 x24: x24
STACK CFI 4f8c x27: x27 x28: x28
STACK CFI 4f90 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4fd0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4fd4 x27: x27 x28: x28
STACK CFI 5024 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5260 x19: x19 x20: x20
STACK CFI 5264 x23: x23 x24: x24
STACK CFI 5268 x27: x27 x28: x28
STACK CFI 526c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5374 x19: x19 x20: x20
STACK CFI 537c x23: x23 x24: x24
STACK CFI 5384 x27: x27 x28: x28
STACK CFI 538c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 53b0 x27: x27 x28: x28
STACK CFI 53bc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 54a8 x27: x27 x28: x28
STACK CFI 54c8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 550c x27: x27 x28: x28
STACK CFI 552c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5530 x27: x27 x28: x28
STACK CFI 5550 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5554 x27: x27 x28: x28
STACK CFI 5568 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 5580 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5584 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 558c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5598 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 55f8 x19: x19 x20: x20
STACK CFI 5600 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 5604 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5650 x19: x19 x20: x20
STACK CFI 5670 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 5674 8c .cfa: sp 0 + .ra: x30
STACK CFI 5688 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5698 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 56e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 56e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 56f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 56f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5700 9c .cfa: sp 0 + .ra: x30
STACK CFI 5704 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 570c x21: .cfa -16 + ^
STACK CFI 5720 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5770 x19: x19 x20: x20
STACK CFI 5778 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 577c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 578c x19: x19 x20: x20
STACK CFI 5798 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 57a0 9c .cfa: sp 0 + .ra: x30
STACK CFI 57ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5838 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5840 dc .cfa: sp 0 + .ra: x30
STACK CFI 5850 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5858 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 586c x21: .cfa -16 + ^
STACK CFI 58bc x21: x21
STACK CFI 58c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 58ec x21: x21
STACK CFI 58f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5920 274 .cfa: sp 0 + .ra: x30
STACK CFI 5924 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5948 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5ae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5aec .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5b94 108 .cfa: sp 0 + .ra: x30
STACK CFI 5b98 .cfa: sp 144 +
STACK CFI 5ba8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5bb8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5c74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5c78 .cfa: sp 144 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5ca0 80 .cfa: sp 0 + .ra: x30
STACK CFI 5ca4 .cfa: sp 128 +
STACK CFI 5cb0 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5cb8 x19: .cfa -16 + ^
STACK CFI 5d18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5d1c .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5d20 230 .cfa: sp 0 + .ra: x30
STACK CFI 5d24 .cfa: sp 224 +
STACK CFI 5d3c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5d58 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5f24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5f28 .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5f50 230 .cfa: sp 0 + .ra: x30
STACK CFI 5f54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5f5c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5f68 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5f78 x23: .cfa -16 + ^
STACK CFI 6094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6098 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6150 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6154 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6180 58 .cfa: sp 0 + .ra: x30
STACK CFI 6188 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6198 x19: .cfa -16 + ^
STACK CFI 61c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 61c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 61d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 61e0 100 .cfa: sp 0 + .ra: x30
STACK CFI 61ec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 61fc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6208 x23: .cfa -32 + ^
STACK CFI 6210 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6244 x19: x19 x20: x20
STACK CFI 6248 x21: x21 x22: x22
STACK CFI 624c x23: x23
STACK CFI 6250 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6254 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 62a8 x19: x19 x20: x20
STACK CFI 62ac x21: x21 x22: x22
STACK CFI 62b0 x23: x23
STACK CFI 62b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 62bc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 62cc x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 62dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 62e0 24 .cfa: sp 0 + .ra: x30
STACK CFI 62e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 62ec x19: .cfa -16 + ^
STACK CFI 6300 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6304 548 .cfa: sp 0 + .ra: x30
STACK CFI 6318 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6324 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6334 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6340 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 639c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 648c x27: x27 x28: x28
STACK CFI 650c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6510 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 6538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 653c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 65ac x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 65d8 x27: x27 x28: x28
STACK CFI 6698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 66a0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 66e4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6744 x27: x27 x28: x28
STACK CFI 6748 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6760 x27: x27 x28: x28
STACK CFI 6800 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6844 x27: x27 x28: x28
STACK CFI INIT 6850 44 .cfa: sp 0 + .ra: x30
STACK CFI 6854 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 685c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6868 x21: .cfa -16 + ^
STACK CFI 6890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6894 228 .cfa: sp 0 + .ra: x30
STACK CFI 6898 .cfa: sp 112 +
STACK CFI 68a4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 68c8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 68d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6940 x19: x19 x20: x20
STACK CFI 6944 x21: x21 x22: x22
STACK CFI 6948 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 694c .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6994 x23: .cfa -16 + ^
STACK CFI 69ec x23: x23
STACK CFI 6a58 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 6a88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6a8c .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6a9c x23: .cfa -16 + ^
STACK CFI 6aa4 x23: x23
STACK CFI 6aa8 x23: .cfa -16 + ^
STACK CFI 6aac x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 6ab0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6ab4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6ab8 x23: .cfa -16 + ^
STACK CFI INIT 6ac0 8c .cfa: sp 0 + .ra: x30
STACK CFI 6ac4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6ad4 x19: .cfa -16 + ^
STACK CFI 6b20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6b24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6b50 94 .cfa: sp 0 + .ra: x30
STACK CFI 6b54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6b64 x19: .cfa -16 + ^
STACK CFI 6bb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6bb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6be4 ac4 .cfa: sp 0 + .ra: x30
STACK CFI 6be8 .cfa: sp 192 +
STACK CFI 6bf4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6c18 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6c24 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6c30 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6c3c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6c84 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6cb8 x27: x27 x28: x28
STACK CFI 6d44 x19: x19 x20: x20
STACK CFI 6d48 x21: x21 x22: x22
STACK CFI 6d4c x23: x23 x24: x24
STACK CFI 6d50 x25: x25 x26: x26
STACK CFI 6d54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6d58 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 6e30 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7030 x27: x27 x28: x28
STACK CFI 70b8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 70e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 70ec .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 7100 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7168 x27: x27 x28: x28
STACK CFI 716c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 725c x27: x27 x28: x28
STACK CFI 72bc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 72c8 x27: x27 x28: x28
STACK CFI 72d4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 72ec x27: x27 x28: x28
STACK CFI 7314 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7318 x27: x27 x28: x28
STACK CFI 7330 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 73c4 x27: x27 x28: x28
STACK CFI 73cc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 761c x27: x27 x28: x28
STACK CFI 7620 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7634 x27: x27 x28: x28
STACK CFI 7644 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7650 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7654 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7658 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 765c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7660 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7664 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 76b0 2c .cfa: sp 0 + .ra: x30
STACK CFI 76b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 76bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 76d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 76e0 18 .cfa: sp 0 + .ra: x30
STACK CFI 76e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 76f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7700 30 .cfa: sp 0 + .ra: x30
STACK CFI 7714 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7724 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7730 94 .cfa: sp 0 + .ra: x30
STACK CFI 7734 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 773c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7750 x21: .cfa -16 + ^
STACK CFI 7780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 778c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 77c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 77c4 74 .cfa: sp 0 + .ra: x30
STACK CFI 77c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 77d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 77e4 x21: .cfa -16 + ^
STACK CFI 7808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 780c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7834 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7840 68 .cfa: sp 0 + .ra: x30
STACK CFI 7844 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7854 x19: .cfa -16 + ^
STACK CFI 78a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 78b0 16c .cfa: sp 0 + .ra: x30
STACK CFI 78b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 78c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 78dc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7928 x21: x21 x22: x22
STACK CFI 7934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7938 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7984 x21: x21 x22: x22
STACK CFI 7990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7994 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 79d8 x21: x21 x22: x22
STACK CFI INIT 7a20 1ac .cfa: sp 0 + .ra: x30
STACK CFI 7bb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7bc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7bd0 88 .cfa: sp 0 + .ra: x30
STACK CFI 7bd4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7bdc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7bf4 x21: .cfa -16 + ^
STACK CFI 7c14 x21: x21
STACK CFI 7c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7c1c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7c28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7c50 x21: x21
STACK CFI 7c54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7c60 88 .cfa: sp 0 + .ra: x30
STACK CFI 7c64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7c6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7c84 x21: .cfa -16 + ^
STACK CFI 7ca4 x21: x21
STACK CFI 7ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7cac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7cb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7cb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7ce0 x21: x21
STACK CFI 7ce4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7cf0 64 .cfa: sp 0 + .ra: x30
STACK CFI 7cf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7cfc x19: .cfa -16 + ^
STACK CFI 7d20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7d24 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7d2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7d30 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7d50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7d54 ac .cfa: sp 0 + .ra: x30
STACK CFI 7d58 .cfa: sp 48 +
STACK CFI 7d64 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7d6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7dc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7dc8 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7e00 a8 .cfa: sp 0 + .ra: x30
STACK CFI 7e04 .cfa: sp 48 +
STACK CFI 7e10 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e18 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7e70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e74 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7eb0 74 .cfa: sp 0 + .ra: x30
STACK CFI 7eb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ebc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7ee8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7eec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7ef4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7ef8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7f20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7f24 28 .cfa: sp 0 + .ra: x30
STACK CFI 7f34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7f48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7f50 150 .cfa: sp 0 + .ra: x30
STACK CFI 7f54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7f64 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 806c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 80c0 b0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8180 b0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8230 e0 .cfa: sp 0 + .ra: x30
STACK CFI 8234 .cfa: sp 64 +
STACK CFI 8248 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 826c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 82d4 x19: x19 x20: x20
STACK CFI 82d8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 82dc x19: x19 x20: x20
STACK CFI 8304 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8308 .cfa: sp 64 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 830c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 8310 11c .cfa: sp 0 + .ra: x30
STACK CFI 8314 .cfa: sp 80 +
STACK CFI 8328 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 834c x21: .cfa -16 + ^
STACK CFI 835c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 83d4 x19: x19 x20: x20
STACK CFI 83d8 x21: x21
STACK CFI 8400 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8404 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 8408 x19: x19 x20: x20
STACK CFI 840c x21: x21
STACK CFI 8410 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 8414 x19: x19 x20: x20
STACK CFI 841c x21: x21
STACK CFI 8424 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8428 x21: .cfa -16 + ^
STACK CFI INIT 8430 2cc .cfa: sp 0 + .ra: x30
STACK CFI 8434 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8448 .cfa: sp 1136 + x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 84a0 x20: .cfa -72 + ^
STACK CFI 84ac x19: .cfa -80 + ^
STACK CFI 84b0 x27: .cfa -16 + ^
STACK CFI 8574 x19: x19
STACK CFI 8578 x20: x20
STACK CFI 857c x27: x27
STACK CFI 859c .cfa: sp 96 +
STACK CFI 85ac .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 85b0 .cfa: sp 1136 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 85c8 x19: .cfa -80 + ^
STACK CFI 85cc x20: .cfa -72 + ^
STACK CFI 8658 x19: x19
STACK CFI 865c x20: x20
STACK CFI 8664 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 86cc x19: x19
STACK CFI 86d4 x20: x20
STACK CFI 86e4 x19: .cfa -80 + ^
STACK CFI 86e8 x20: .cfa -72 + ^
STACK CFI 86ec x27: .cfa -16 + ^
STACK CFI 86f0 x27: x27
STACK CFI 86f4 x19: x19
STACK CFI 86f8 x20: x20
STACK CFI INIT 8700 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8740 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8780 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 87c0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 87f0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8820 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8850 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d40 24 .cfa: sp 0 + .ra: x30
STACK CFI 1d44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1d5c .cfa: sp 0 + .ra: .ra x29: x29
