MODULE Linux arm64 A59BAE777845508F22E541D1984740360 libportal-gtk4.so.1
INFO CODE_ID 77AE9BA545788F5022E541D198474036C0EE5E16
PUBLIC 2454 0 xdp_user_information_flags_get_type
PUBLIC 24d0 0 xdp_background_flags_get_type
PUBLIC 2560 0 xdp_camera_flags_get_type
PUBLIC 25f0 0 xdp_launcher_type_get_type
PUBLIC 2680 0 xdp_email_flags_get_type
PUBLIC 2710 0 xdp_open_file_flags_get_type
PUBLIC 27a0 0 xdp_save_file_flags_get_type
PUBLIC 2830 0 xdp_inhibit_flags_get_type
PUBLIC 28c0 0 xdp_login_session_state_get_type
PUBLIC 2950 0 xdp_session_monitor_flags_get_type
PUBLIC 29e0 0 xdp_location_accuracy_get_type
PUBLIC 2a70 0 xdp_location_monitor_flags_get_type
PUBLIC 2b00 0 xdp_notification_flags_get_type
PUBLIC 2b90 0 xdp_open_uri_flags_get_type
PUBLIC 2c20 0 xdp_print_flags_get_type
PUBLIC 2cb0 0 xdp_output_type_get_type
PUBLIC 2d40 0 xdp_device_type_get_type
PUBLIC 2dd0 0 xdp_session_type_get_type
PUBLIC 2e60 0 xdp_session_state_get_type
PUBLIC 2ef0 0 xdp_screencast_flags_get_type
PUBLIC 2f80 0 xdp_cursor_mode_get_type
PUBLIC 3010 0 xdp_persist_mode_get_type
PUBLIC 30a0 0 xdp_remote_desktop_flags_get_type
PUBLIC 3130 0 xdp_button_state_get_type
PUBLIC 31c0 0 xdp_discrete_axis_get_type
PUBLIC 3250 0 xdp_key_state_get_type
PUBLIC 32e0 0 xdp_screenshot_flags_get_type
PUBLIC 3370 0 xdp_spawn_flags_get_type
PUBLIC 3400 0 xdp_update_status_get_type
PUBLIC 3490 0 xdp_update_monitor_flags_get_type
PUBLIC 3520 0 xdp_update_install_flags_get_type
PUBLIC 35b0 0 xdp_wallpaper_flags_get_type
PUBLIC 3640 0 xdp_parent_new_gtk
STACK CFI INIT 2180 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 21f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 21f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 21fc x19: .cfa -16 + ^
STACK CFI 2234 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2240 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2250 48 .cfa: sp 0 + .ra: x30
STACK CFI 2258 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2264 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2290 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 22a0 140 .cfa: sp 0 + .ra: x30
STACK CFI 22a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 22b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 22bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2358 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2360 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 23a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 23b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 23d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 23e0 74 .cfa: sp 0 + .ra: x30
STACK CFI 23e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 243c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2444 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 244c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2454 78 .cfa: sp 0 + .ra: x30
STACK CFI 245c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2464 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2488 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 24c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 24d0 88 .cfa: sp 0 + .ra: x30
STACK CFI 24d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 24e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 250c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2560 88 .cfa: sp 0 + .ra: x30
STACK CFI 2568 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2570 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 259c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 25e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 25f0 88 .cfa: sp 0 + .ra: x30
STACK CFI 25f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2600 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2624 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 262c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2680 88 .cfa: sp 0 + .ra: x30
STACK CFI 2688 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2690 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 26b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 26bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2700 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2710 88 .cfa: sp 0 + .ra: x30
STACK CFI 2718 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2720 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2744 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 274c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2790 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 27a0 88 .cfa: sp 0 + .ra: x30
STACK CFI 27a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 27d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 27dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2820 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2830 88 .cfa: sp 0 + .ra: x30
STACK CFI 2838 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2840 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 286c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 28b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 28c0 88 .cfa: sp 0 + .ra: x30
STACK CFI 28c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 28d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 28f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 28fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2940 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2950 88 .cfa: sp 0 + .ra: x30
STACK CFI 2958 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2960 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 298c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 29d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 29e0 88 .cfa: sp 0 + .ra: x30
STACK CFI 29e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2a14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2a1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2a60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2a70 88 .cfa: sp 0 + .ra: x30
STACK CFI 2a78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a80 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2aa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2aac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2af0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2b00 88 .cfa: sp 0 + .ra: x30
STACK CFI 2b08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2b34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2b80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2b90 88 .cfa: sp 0 + .ra: x30
STACK CFI 2b98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ba0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2bcc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2c20 88 .cfa: sp 0 + .ra: x30
STACK CFI 2c28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c30 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2c54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2cb0 88 .cfa: sp 0 + .ra: x30
STACK CFI 2cb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2cc0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2ce4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2cec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2d30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2d40 88 .cfa: sp 0 + .ra: x30
STACK CFI 2d48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d50 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2d74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2dc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2dd0 88 .cfa: sp 0 + .ra: x30
STACK CFI 2dd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2de0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2e04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2e50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2e60 88 .cfa: sp 0 + .ra: x30
STACK CFI 2e68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2e94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e9c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2ee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2ef0 88 .cfa: sp 0 + .ra: x30
STACK CFI 2ef8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f00 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2f24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2f2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2f70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2f80 88 .cfa: sp 0 + .ra: x30
STACK CFI 2f88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f90 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2fb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2fbc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3000 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3010 88 .cfa: sp 0 + .ra: x30
STACK CFI 3018 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3020 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3044 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 304c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3090 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 30a0 88 .cfa: sp 0 + .ra: x30
STACK CFI 30a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30b0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 30d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 30dc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3120 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3130 88 .cfa: sp 0 + .ra: x30
STACK CFI 3138 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3140 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 316c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 31b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 31c0 88 .cfa: sp 0 + .ra: x30
STACK CFI 31c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 31d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 31f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 31fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3250 88 .cfa: sp 0 + .ra: x30
STACK CFI 3258 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3260 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 328c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 32d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 32e0 88 .cfa: sp 0 + .ra: x30
STACK CFI 32e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 32f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3314 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 331c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3370 88 .cfa: sp 0 + .ra: x30
STACK CFI 3378 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3380 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 33a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 33ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 33f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3400 88 .cfa: sp 0 + .ra: x30
STACK CFI 3408 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3410 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 343c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3490 88 .cfa: sp 0 + .ra: x30
STACK CFI 3498 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 34a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 34c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 34cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3520 88 .cfa: sp 0 + .ra: x30
STACK CFI 3528 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3530 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 355c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 35a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 35b0 88 .cfa: sp 0 + .ra: x30
STACK CFI 35b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 35e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 35ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3640 58 .cfa: sp 0 + .ra: x30
STACK CFI 3648 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3650 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3690 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
