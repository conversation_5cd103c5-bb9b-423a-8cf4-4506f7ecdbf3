MODULE Linux arm64 1B07D399BAC7237AB8A095D9DBFB245F0 libnvscievent.so
INFO CODE_ID 99D3071BC7BA7A23B8A095D9DBFB245FBC7314B6
PUBLIC 2d70 0 NvSciEventLoopServiceCreateSafe
PUBLIC 3040 0 NvSciEventLoopServiceCreate
PUBLIC 3060 0 NvSciEventLoopServiceCreateSafeX
PUBLIC 30a0 0 NvSciEventMoveNotifier
PUBLIC 3380 0 NvSciEventCheckVersionCompatibility
STACK CFI INIT ca0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT cd0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT d10 48 .cfa: sp 0 + .ra: x30
STACK CFI d14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d1c x19: .cfa -16 + ^
STACK CFI d54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d70 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT d90 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT dd0 78 .cfa: sp 0 + .ra: x30
STACK CFI ddc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI e04 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI e0c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI e10 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI e28 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI e34 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT e50 d4 .cfa: sp 0 + .ra: x30
STACK CFI e60 .cfa: sp 64 + .ra: .cfa -64 + ^
STACK CFI eb0 .cfa: sp 0 + .ra: .ra
STACK CFI ec0 .cfa: sp 64 + .ra: .cfa -64 + ^
STACK CFI INIT f30 114 .cfa: sp 0 + .ra: x30
STACK CFI f44 .cfa: sp 64 + .ra: .cfa -64 + ^
STACK CFI f98 .cfa: sp 0 + .ra: .ra
STACK CFI fa8 .cfa: sp 64 + .ra: .cfa -64 + ^
STACK CFI INIT 1050 14c .cfa: sp 0 + .ra: x30
STACK CFI 105c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1074 .ra: .cfa -8 + ^
STACK CFI 1090 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1098 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10ac x21: .cfa -16 + ^
STACK CFI 10f0 x21: x21
STACK CFI 10fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1104 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 1114 x21: x21
STACK CFI 111c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 112c .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 113c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1148 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 1150 x21: x21
STACK CFI 1168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1170 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 11a0 178 .cfa: sp 0 + .ra: x30
STACK CFI 11b0 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 11b8 .ra: .cfa -80 + ^
STACK CFI 1260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1270 .cfa: sp 96 + .ra: .cfa -80 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 12a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 12ac .cfa: sp 96 + .ra: .cfa -80 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 12dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 12e8 .cfa: sp 96 + .ra: .cfa -80 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI INIT 1320 e4 .cfa: sp 0 + .ra: x30
STACK CFI 1330 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 1384 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 1394 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI INIT 1410 d8 .cfa: sp 0 + .ra: x30
STACK CFI 1420 .cfa: sp 320 +
STACK CFI 142c .ra: .cfa -320 + ^
STACK CFI 14d4 .cfa: sp 0 + .ra: .ra
STACK CFI 14e4 .cfa: sp 320 + .ra: .cfa -320 + ^
STACK CFI INIT 14f0 234 .cfa: sp 0 + .ra: x30
STACK CFI 14fc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1508 .ra: .cfa -16 + ^
STACK CFI 1528 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1538 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1548 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 15dc x21: x21 x22: x22
STACK CFI 15ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 15f4 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1624 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1628 x21: x21 x22: x22
STACK CFI 1630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1640 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1674 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 16c8 x21: x21 x22: x22
STACK CFI 16d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 16dc .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 1730 6e8 .cfa: sp 0 + .ra: x30
STACK CFI 1740 .cfa: sp 240 + x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 1748 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 1750 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 1758 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 1778 .ra: .cfa -160 + ^
STACK CFI 1874 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 1acc x23: x23 x24: x24
STACK CFI 1b10 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 1b18 x23: x23 x24: x24
STACK CFI 1b20 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 1b28 x23: x23 x24: x24
STACK CFI 1b94 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 1c94 x23: x23 x24: x24
STACK CFI 1c98 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 1d08 x23: x23 x24: x24
STACK CFI 1d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1d68 .cfa: sp 240 + .ra: .cfa -160 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 1d70 x23: x23 x24: x24
STACK CFI 1d8c x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 1d90 x23: x23 x24: x24
STACK CFI 1da4 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 1dac x23: x23 x24: x24
STACK CFI 1db4 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 1dcc x23: x23 x24: x24
STACK CFI 1e04 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI INIT 1e20 328 .cfa: sp 0 + .ra: x30
STACK CFI 1e28 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1e2c .ra: .cfa -16 + ^
STACK CFI 1e34 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1e48 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1e4c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1eb0 x21: x21 x22: x22
STACK CFI 1eb4 x23: x23 x24: x24
STACK CFI 1eb8 x25: x25 x26: x26
STACK CFI 1ec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1ed4 .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1fb8 x21: x21 x22: x22
STACK CFI 1fc0 x23: x23 x24: x24
STACK CFI 1fc4 x25: x25 x26: x26
STACK CFI 1fc8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1fd4 x21: x21 x22: x22
STACK CFI 1fdc x23: x23 x24: x24
STACK CFI 1fe0 x25: x25 x26: x26
STACK CFI 1fe8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1ff8 .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2090 x21: x21 x22: x22
STACK CFI 2094 x23: x23 x24: x24
STACK CFI 2098 x25: x25 x26: x26
STACK CFI 20a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 20e4 x21: x21 x22: x22
STACK CFI 20e8 x23: x23 x24: x24
STACK CFI 20ec x25: x25 x26: x26
STACK CFI 20f0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2108 x21: x21 x22: x22
STACK CFI 210c x23: x23 x24: x24
STACK CFI 2110 x25: x25 x26: x26
STACK CFI 2114 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 2150 294 .cfa: sp 0 + .ra: x30
STACK CFI 2158 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 215c .ra: .cfa -16 + ^
STACK CFI 2174 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 218c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 21b8 x21: x21 x22: x22
STACK CFI 21c4 x23: x23 x24: x24
STACK CFI 21e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 21f4 .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2238 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 22fc x21: x21 x22: x22
STACK CFI 2304 x23: x23 x24: x24
STACK CFI 2308 x25: x25 x26: x26
STACK CFI 230c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2324 x21: x21 x22: x22
STACK CFI 2328 x23: x23 x24: x24
STACK CFI 2350 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 235c x21: x21 x22: x22
STACK CFI 2364 x23: x23 x24: x24
STACK CFI 2368 x25: x25 x26: x26
STACK CFI 2370 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 2380 .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 23a0 x21: x21 x22: x22
STACK CFI 23a4 x23: x23 x24: x24
STACK CFI 23a8 x25: x25 x26: x26
STACK CFI 23ac x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 23c8 x21: x21 x22: x22
STACK CFI 23cc x23: x23 x24: x24
STACK CFI 23d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 23d4 x25: x25 x26: x26
STACK CFI INIT 23f0 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 2400 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 241c .ra: .cfa -40 + ^
STACK CFI 2464 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2470 x23: .cfa -48 + ^
STACK CFI 251c x21: x21 x22: x22
STACK CFI 2524 x23: x23
STACK CFI 2528 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 2530 x21: x21 x22: x22
STACK CFI 2538 x23: x23
STACK CFI 2560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 2570 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 2574 x21: x21 x22: x22
STACK CFI 2578 x23: x23
STACK CFI 2598 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 25ac x21: x21 x22: x22
STACK CFI 25b0 x23: x23
STACK CFI 25b8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 25bc x23: .cfa -48 + ^
STACK CFI INIT 25c0 27c .cfa: sp 0 + .ra: x30
STACK CFI 25c8 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 25d0 .ra: .cfa -8 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2600 x23: .cfa -16 + ^
STACK CFI 26fc x23: x23
STACK CFI 271c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 272c .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 2770 x23: x23
STACK CFI 2780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2790 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 27c4 x23: x23
STACK CFI 27f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2800 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2820 x23: .cfa -16 + ^
STACK CFI INIT 2840 1fc .cfa: sp 0 + .ra: x30
STACK CFI 2848 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 284c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2854 .ra: .cfa -16 + ^
STACK CFI 285c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2888 x21: x21 x22: x22
STACK CFI 2898 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 28a8 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2980 x21: x21 x22: x22
STACK CFI 2984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 2994 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 29c4 x21: x21 x22: x22
STACK CFI 29d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI 29e4 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 29e8 x21: x21 x22: x22
STACK CFI 2a04 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 2a40 324 .cfa: sp 0 + .ra: x30
STACK CFI 2a48 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2a50 .ra: .cfa -8 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2acc x23: .cfa -16 + ^
STACK CFI 2ba8 x23: x23
STACK CFI 2bd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2be4 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 2c10 x23: x23
STACK CFI 2c40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2c50 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 2c70 x23: x23
STACK CFI 2c78 x23: .cfa -16 + ^
STACK CFI 2c94 x23: x23
STACK CFI 2ce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2cf0 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 2d70 2cc .cfa: sp 0 + .ra: x30
STACK CFI 2d84 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2d88 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 2d90 .ra: .cfa -80 + ^
STACK CFI 2ec0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2ed0 .cfa: sp 112 + .ra: .cfa -80 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI INIT 3040 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3060 3c .cfa: sp 0 + .ra: x30
STACK CFI 3068 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 308c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 30a0 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 30a8 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 30ac .ra: .cfa -8 + ^
STACK CFI 30c8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 310c x21: x21 x22: x22
STACK CFI 311c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 312c .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3138 x23: .cfa -16 + ^
STACK CFI 3240 x21: x21 x22: x22
STACK CFI 3248 x23: x23
STACK CFI 3250 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3260 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 328c x21: x21 x22: x22
STACK CFI 3290 x23: x23
STACK CFI 3294 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 32c0 x21: x21 x22: x22 x23: x23
STACK CFI 32dc x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 3330 x21: x21 x22: x22
STACK CFI 3338 x23: x23
STACK CFI 3340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3350 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 3380 64 .cfa: sp 0 + .ra: x30
STACK CFI 33c0 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 33d0 .cfa: sp 0 + .ra: .ra
