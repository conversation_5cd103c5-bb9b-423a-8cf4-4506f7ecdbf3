MODULE Linux arm64 BEAD02635CE7F40F3D22BD3B00D80B820 libcli-ldap-samba4.so.0
INFO CODE_ID 6302ADBEE75C0FF43D22BD3B00D80B8204DFA20A
PUBLIC 9350 0 resolve_context_add_bcast_method
PUBLIC 93c0 0 resolve_context_add_bcast_method_lp
PUBLIC 9480 0 resolve_name_nbtlist_send
PUBLIC 98d0 0 resolve_name_wins_send
PUBLIC 9970 0 resolve_name_nbtlist_recv
PUBLIC 9a50 0 resolve_name_wins_recv
PUBLIC 9a70 0 resolve_context_add_wins_method
PUBLIC 9b10 0 resolve_context_add_wins_method_lp
PUBLIC 9be0 0 resolve_name_dns_ex_send
PUBLIC 9f10 0 resolve_name_host_send
PUBLIC 9f30 0 resolve_name_dns_ex_recv
PUBLIC 9fe0 0 resolve_name_host_recv
PUBLIC a000 0 resolve_context_add_host_method
PUBLIC a030 0 resolve_context_add_lmhosts_method
PUBLIC a060 0 lpcfg_resolve_context
PUBLIC a1d4 0 tstream_read_pdu_blob_send
PUBLIC a354 0 tstream_read_pdu_blob_recv
PUBLIC a424 0 ldap4_new_connection
PUBLIC a4e4 0 ldap_connect_send
PUBLIC a914 0 ldap_connect_recv
PUBLIC a954 0 ldap_connect
PUBLIC a970 0 ldap_set_reconn_params
PUBLIC a9b0 0 ldap_request_wait
PUBLIC aa30 0 ldap_check_response
PUBLIC ab14 0 ldap_errstr
PUBLIC ab80 0 ldap_result_n
PUBLIC ac60 0 ldap_result_one
PUBLIC acc0 0 ildap_count_entries
PUBLIC ad10 0 samba_ldap_control_handlers
PUBLIC ad34 0 ldap_request_send
PUBLIC af14 0 ldap_bind_simple
PUBLIC b124 0 ildap_search_bytree
PUBLIC b364 0 ildap_search
PUBLIC b440 0 ldap_bind_sasl
PUBLIC bf50 0 ldap_rebind
STACK CFI INIT 5aa0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ad0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b10 48 .cfa: sp 0 + .ra: x30
STACK CFI 5b14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5b1c x19: .cfa -16 + ^
STACK CFI 5b54 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5b60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b70 20 .cfa: sp 0 + .ra: x30
STACK CFI 5b78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5b88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5b90 30 .cfa: sp 0 + .ra: x30
STACK CFI 5b98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5ba4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5bb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5bb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5bc0 264 .cfa: sp 0 + .ra: x30
STACK CFI 5bc8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5bd4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5be4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5cc8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5cec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5cf4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5d28 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5d3c x25: .cfa -16 + ^
STACK CFI 5dbc x23: x23 x24: x24
STACK CFI 5dc0 x25: x25
STACK CFI 5dd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5de4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5dec x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 5df0 x25: x25
STACK CFI 5e10 x23: x23 x24: x24
STACK CFI 5e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5e1c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI INIT 5e24 a8 .cfa: sp 0 + .ra: x30
STACK CFI 5e2c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5e38 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5e44 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5ec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 5ed0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 5ed8 .cfa: sp 48 +
STACK CFI 5ee8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ef0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5f4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5f54 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5f80 1ac .cfa: sp 0 + .ra: x30
STACK CFI 5f88 .cfa: sp 160 +
STACK CFI 5f8c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5f94 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5fa4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5fb0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5fb8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5fc4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 60f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 60f8 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6130 160 .cfa: sp 0 + .ra: x30
STACK CFI 6138 .cfa: sp 128 +
STACK CFI 6144 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 614c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6158 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6168 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6170 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6274 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 627c .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6290 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 6298 .cfa: sp 128 +
STACK CFI 62a4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 62ac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 62bc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 62f4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6354 x19: x19 x20: x20
STACK CFI 638c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6394 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 639c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 63d0 x25: x25 x26: x26
STACK CFI 63f8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6434 x25: x25 x26: x26
STACK CFI 6438 x19: x19 x20: x20
STACK CFI 643c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6440 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 6444 330 .cfa: sp 0 + .ra: x30
STACK CFI 644c .cfa: sp 160 +
STACK CFI 6450 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6458 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6468 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6478 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 64e4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6608 x27: x27 x28: x28
STACK CFI 66a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 66b0 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 6714 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 672c x27: x27 x28: x28
STACK CFI 6730 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6734 x27: x27 x28: x28
STACK CFI 6770 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 6774 3d4 .cfa: sp 0 + .ra: x30
STACK CFI 677c .cfa: sp 144 +
STACK CFI 678c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 67b0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 689c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 68a4 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 68ac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 68b0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 68ec x25: x25 x26: x26
STACK CFI 68f0 x27: x27 x28: x28
STACK CFI 697c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6ab4 x25: x25 x26: x26
STACK CFI 6ab8 x27: x27 x28: x28
STACK CFI 6b10 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6b2c x25: x25 x26: x26
STACK CFI 6b30 x27: x27 x28: x28
STACK CFI 6b34 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6b3c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6b40 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6b44 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 6b50 26c .cfa: sp 0 + .ra: x30
STACK CFI 6b58 .cfa: sp 144 +
STACK CFI 6b64 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6b7c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6b84 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6b8c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6bb0 x21: x21 x22: x22
STACK CFI 6bb4 x25: x25 x26: x26
STACK CFI 6be0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6be8 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 6c50 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6c70 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6d6c x21: x21 x22: x22
STACK CFI 6d70 x23: x23 x24: x24
STACK CFI 6d74 x25: x25 x26: x26
STACK CFI 6d78 x27: x27 x28: x28
STACK CFI 6d7c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6d80 x21: x21 x22: x22
STACK CFI 6d84 x25: x25 x26: x26
STACK CFI 6d8c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6d90 x23: x23 x24: x24
STACK CFI 6d94 x27: x27 x28: x28
STACK CFI 6da0 x21: x21 x22: x22
STACK CFI 6da4 x25: x25 x26: x26
STACK CFI 6dac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6db0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6db4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6db8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 6dc0 ec .cfa: sp 0 + .ra: x30
STACK CFI 6dc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6e04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6e10 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6e64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6e6c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6eb0 48 .cfa: sp 0 + .ra: x30
STACK CFI 6eb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6ee0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6eec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6ef0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6f00 200 .cfa: sp 0 + .ra: x30
STACK CFI 6f08 .cfa: sp 96 +
STACK CFI 6f14 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6f1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6f28 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 6ff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6ffc .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7100 148 .cfa: sp 0 + .ra: x30
STACK CFI 7108 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7110 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7120 x21: .cfa -16 + ^
STACK CFI 720c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7214 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7250 24 .cfa: sp 0 + .ra: x30
STACK CFI 7258 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7268 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7274 11c .cfa: sp 0 + .ra: x30
STACK CFI 727c .cfa: sp 64 +
STACK CFI 7288 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7290 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7298 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 736c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7374 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7390 54 .cfa: sp 0 + .ra: x30
STACK CFI 73ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 73b8 x19: .cfa -16 + ^
STACK CFI 73dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 73e4 134 .cfa: sp 0 + .ra: x30
STACK CFI 73ec .cfa: sp 96 +
STACK CFI 73f8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7400 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7408 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7414 x23: .cfa -16 + ^
STACK CFI 7478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7480 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7520 168 .cfa: sp 0 + .ra: x30
STACK CFI 7528 .cfa: sp 96 +
STACK CFI 7534 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 753c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7544 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7550 x23: .cfa -16 + ^
STACK CFI 75b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 75bc .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7690 228 .cfa: sp 0 + .ra: x30
STACK CFI 7698 .cfa: sp 144 +
STACK CFI 76a4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 76ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 76b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 76c0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 772c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 7748 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 775c x27: .cfa -16 + ^
STACK CFI 7894 x25: x25 x26: x26
STACK CFI 7898 x27: x27
STACK CFI 78a0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 78a4 x25: x25 x26: x26
STACK CFI 78a8 x27: x27
STACK CFI 78b0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 78b4 x27: .cfa -16 + ^
STACK CFI INIT 78c0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 78c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 78d0 x23: .cfa -16 + ^
STACK CFI 78e8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 78f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 791c x19: x19 x20: x20
STACK CFI 7924 x21: x21 x22: x22
STACK CFI 792c .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 7934 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7980 x21: x21 x22: x22
STACK CFI 798c x19: x19 x20: x20
STACK CFI INIT 7990 bc .cfa: sp 0 + .ra: x30
STACK CFI 7998 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 79a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 79a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 79b8 x23: .cfa -16 + ^
STACK CFI 79ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 79f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7a50 bc .cfa: sp 0 + .ra: x30
STACK CFI 7a58 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7a60 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7a68 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7a78 x23: .cfa -16 + ^
STACK CFI 7aac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7ab4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7b10 13c .cfa: sp 0 + .ra: x30
STACK CFI 7b18 .cfa: sp 96 +
STACK CFI 7b24 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7b2c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7b34 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7b40 x23: .cfa -16 + ^
STACK CFI 7ba4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7bac .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7c50 14c .cfa: sp 0 + .ra: x30
STACK CFI 7c58 .cfa: sp 96 +
STACK CFI 7c64 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7c6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7c74 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7c80 x23: .cfa -16 + ^
STACK CFI 7ce4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7cec .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7da0 200 .cfa: sp 0 + .ra: x30
STACK CFI 7da8 .cfa: sp 112 +
STACK CFI 7db4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7dbc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7dc4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7dd0 x23: .cfa -16 + ^
STACK CFI 7e34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7e3c .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7fa0 16c .cfa: sp 0 + .ra: x30
STACK CFI 7fa8 .cfa: sp 96 +
STACK CFI 7fb4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7fbc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7fc4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7fd0 x23: .cfa -16 + ^
STACK CFI 8034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 803c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8110 150 .cfa: sp 0 + .ra: x30
STACK CFI 8118 .cfa: sp 96 +
STACK CFI 8124 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 812c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8134 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8140 x23: .cfa -16 + ^
STACK CFI 81a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 81ac .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8260 13c .cfa: sp 0 + .ra: x30
STACK CFI 8268 .cfa: sp 80 +
STACK CFI 8274 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8280 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 82fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8304 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 83a0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 83a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 83b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 83b8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8410 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8480 b4 .cfa: sp 0 + .ra: x30
STACK CFI 8488 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8490 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 84a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 84e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 84ec .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8534 b4 .cfa: sp 0 + .ra: x30
STACK CFI 853c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8544 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8558 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8598 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 85a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 85f0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 85f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8600 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8614 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8654 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 865c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 86c0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 86c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 86d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 872c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 87a0 184 .cfa: sp 0 + .ra: x30
STACK CFI 87a8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 87b8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 87cc x25: .cfa -16 + ^
STACK CFI 891c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 8924 d8 .cfa: sp 0 + .ra: x30
STACK CFI 892c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8938 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8988 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8990 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8a00 dc .cfa: sp 0 + .ra: x30
STACK CFI 8a08 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8a14 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8a8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8a94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8ae0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 8ae8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8af4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8b44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8b4c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8bd4 190 .cfa: sp 0 + .ra: x30
STACK CFI 8bdc .cfa: sp 96 +
STACK CFI 8be8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8bf4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8c00 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8d28 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8d64 14c .cfa: sp 0 + .ra: x30
STACK CFI 8d6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8d74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8d88 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8dd0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8eb0 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 8eb8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8ec0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8ec8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8ed0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8f28 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8f5c x27: .cfa -16 + ^
STACK CFI 9060 x27: x27
STACK CFI 906c x25: x25 x26: x26
STACK CFI 9070 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9074 x25: x25 x26: x26
STACK CFI 908c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9094 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 9098 x25: x25 x26: x26
STACK CFI 909c x27: x27
STACK CFI INIT 90a0 16c .cfa: sp 0 + .ra: x30
STACK CFI 90a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 90bc x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 91a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 91c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 91d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 91f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 91f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 9204 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 9210 78 .cfa: sp 0 + .ra: x30
STACK CFI 9218 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9224 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9270 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 927c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9290 b8 .cfa: sp 0 + .ra: x30
STACK CFI 9298 .cfa: sp 48 +
STACK CFI 92a8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 92b8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 932c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9334 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9350 6c .cfa: sp 0 + .ra: x30
STACK CFI 9358 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9360 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9370 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 93b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 93c0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 93c8 .cfa: sp 64 +
STACK CFI 93d4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 93dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 93e8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 946c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9474 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9480 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 9488 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9490 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 9498 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 94a0 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 94ac x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 94b8 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 9560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9568 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 9764 16c .cfa: sp 0 + .ra: x30
STACK CFI 976c .cfa: sp 128 +
STACK CFI 9770 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9780 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 978c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 97a0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 98a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 98b0 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 98d0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 98d8 .cfa: sp 96 +
STACK CFI 98dc .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 98e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 98fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9908 x23: .cfa -16 + ^
STACK CFI 9968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 9970 a8 .cfa: sp 0 + .ra: x30
STACK CFI 9978 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9984 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9990 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9a10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 9a20 30 .cfa: sp 0 + .ra: x30
STACK CFI 9a28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9a44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9a50 18 .cfa: sp 0 + .ra: x30
STACK CFI 9a58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9a60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9a70 9c .cfa: sp 0 + .ra: x30
STACK CFI 9a78 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9a80 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9a90 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9aa4 x23: .cfa -16 + ^
STACK CFI 9af4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 9b10 d0 .cfa: sp 0 + .ra: x30
STACK CFI 9b18 .cfa: sp 80 +
STACK CFI 9b24 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9b2c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9b3c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 9bd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 9bdc .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9be0 32c .cfa: sp 0 + .ra: x30
STACK CFI 9be8 .cfa: sp 224 +
STACK CFI 9bf8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9c04 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9c10 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9c8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9c94 .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 9d48 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9d4c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9d6c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9dc8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9f00 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9f04 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9f08 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 9f10 20 .cfa: sp 0 + .ra: x30
STACK CFI 9f18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9f24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9f30 a8 .cfa: sp 0 + .ra: x30
STACK CFI 9f38 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9f44 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9f50 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9fd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 9fe0 18 .cfa: sp 0 + .ra: x30
STACK CFI 9fe8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9ff0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a000 2c .cfa: sp 0 + .ra: x30
STACK CFI a008 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a014 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a030 2c .cfa: sp 0 + .ra: x30
STACK CFI a038 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a044 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a060 174 .cfa: sp 0 + .ra: x30
STACK CFI a068 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a074 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a098 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a0b0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI a144 x19: x19 x20: x20
STACK CFI a148 x25: x25 x26: x26
STACK CFI a158 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a160 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI a1cc x25: x25 x26: x26
STACK CFI a1d0 x19: x19 x20: x20
STACK CFI INIT a1d4 180 .cfa: sp 0 + .ra: x30
STACK CFI a1dc .cfa: sp 80 +
STACK CFI a1ec .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a1f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a204 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a210 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a318 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT a354 d0 .cfa: sp 0 + .ra: x30
STACK CFI a35c .cfa: sp 64 +
STACK CFI a368 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a370 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a37c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a410 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT a424 c0 .cfa: sp 0 + .ra: x30
STACK CFI a42c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a434 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a448 x21: .cfa -16 + ^
STACK CFI a4a8 x21: x21
STACK CFI a4b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a4bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI a4c0 x21: x21
STACK CFI a4c8 x21: .cfa -16 + ^
STACK CFI a4e0 x21: x21
STACK CFI INIT a4e4 430 .cfa: sp 0 + .ra: x30
STACK CFI a4ec .cfa: sp 96 +
STACK CFI a4f8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a504 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a6b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a6bc .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI a6d0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a818 x23: x23 x24: x24
STACK CFI a828 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a82c x23: x23 x24: x24
STACK CFI a844 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a8c8 x23: x23 x24: x24
STACK CFI a8d0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a8d4 x23: x23 x24: x24
STACK CFI a8d8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a8dc x23: x23 x24: x24
STACK CFI a8f8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a908 x23: x23 x24: x24
STACK CFI a910 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT a914 40 .cfa: sp 0 + .ra: x30
STACK CFI a91c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a924 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a94c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a954 1c .cfa: sp 0 + .ra: x30
STACK CFI a95c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a968 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a970 40 .cfa: sp 0 + .ra: x30
STACK CFI a980 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a988 x19: .cfa -16 + ^
STACK CFI a9a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a9b0 7c .cfa: sp 0 + .ra: x30
STACK CFI a9b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a9c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a9fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI aa04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI aa24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT aa30 e4 .cfa: sp 0 + .ra: x30
STACK CFI aa44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aa4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI aaf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ab08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT ab14 68 .cfa: sp 0 + .ra: x30
STACK CFI ab1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ab28 x19: .cfa -16 + ^
STACK CFI ab5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ab68 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI ab74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ab80 dc .cfa: sp 0 + .ra: x30
STACK CFI ab94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ab9c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI aba8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ac04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ac10 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI ac24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ac2c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI ac48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT ac60 58 .cfa: sp 0 + .ra: x30
STACK CFI ac68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ac70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI acb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT acc0 4c .cfa: sp 0 + .ra: x30
STACK CFI acc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI acf4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI acfc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ad00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ad10 24 .cfa: sp 0 + .ra: x30
STACK CFI ad18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ad2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ad34 1e0 .cfa: sp 0 + .ra: x30
STACK CFI ad3c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ad4c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ad54 x21: .cfa -16 + ^
STACK CFI adf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI adfc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT af14 210 .cfa: sp 0 + .ra: x30
STACK CFI af1c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI af24 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI af2c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI af54 x23: .cfa -16 + ^
STACK CFI afdc x21: x21 x22: x22
STACK CFI afe4 x23: x23
STACK CFI aff4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI affc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI b06c x21: x21 x22: x22
STACK CFI b080 x23: x23
STACK CFI b084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b08c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI b0bc x23: .cfa -16 + ^
STACK CFI b0c0 x23: x23
STACK CFI b0d4 x21: x21 x22: x22
STACK CFI b0d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b0e0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI b0f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b0fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI b110 x21: x21 x22: x22
STACK CFI b114 x23: x23
STACK CFI b118 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI b11c x21: x21 x22: x22
STACK CFI b120 x23: x23
STACK CFI INIT b124 240 .cfa: sp 0 + .ra: x30
STACK CFI b12c .cfa: sp 128 +
STACK CFI b138 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b140 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b148 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b150 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b15c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b168 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b2f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI b300 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT b364 dc .cfa: sp 0 + .ra: x30
STACK CFI b36c .cfa: sp 96 +
STACK CFI b370 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b378 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b380 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b390 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b39c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI b410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b418 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI b438 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT b440 b0c .cfa: sp 0 + .ra: x30
STACK CFI b448 .cfa: sp 256 +
STACK CFI b454 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI b45c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI b464 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI b488 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b4c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b4e8 x25: x25 x26: x26
STACK CFI b550 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI b558 .cfa: sp 256 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI b5a0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b930 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI b93c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b950 x23: x23 x24: x24
STACK CFI b954 x25: x25 x26: x26
STACK CFI b958 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b9b0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b9c4 x25: x25 x26: x26
STACK CFI b9cc x23: x23 x24: x24
STACK CFI b9d0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ba08 x23: x23 x24: x24
STACK CFI ba0c x25: x25 x26: x26
STACK CFI ba10 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ba20 x23: x23 x24: x24
STACK CFI ba64 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI bb3c x23: x23 x24: x24
STACK CFI bb44 x25: x25 x26: x26
STACK CFI bb48 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI bb98 x25: x25 x26: x26
STACK CFI bb9c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI bd98 x23: x23 x24: x24
STACK CFI bda0 x25: x25 x26: x26
STACK CFI bda4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI bf38 x23: x23 x24: x24
STACK CFI bf3c x25: x25 x26: x26
STACK CFI bf44 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI bf48 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT bf50 58 .cfa: sp 0 + .ra: x30
STACK CFI bf58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bf70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bf7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bf80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bf90 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bfa0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bfb0 9c .cfa: sp 0 + .ra: x30
STACK CFI bfb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bfc0 x19: .cfa -16 + ^
STACK CFI c01c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c024 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI c044 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c050 13c .cfa: sp 0 + .ra: x30
STACK CFI c058 .cfa: sp 128 +
STACK CFI c05c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c064 x19: .cfa -16 + ^
STACK CFI c0b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c0bc .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT c190 1c4 .cfa: sp 0 + .ra: x30
STACK CFI c198 .cfa: sp 144 +
STACK CFI c1a8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c1b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c230 .cfa: sp 144 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI c244 x21: .cfa -16 + ^
STACK CFI c2b4 x21: x21
STACK CFI c2e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c2e8 .cfa: sp 144 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI c304 x21: x21
STACK CFI c308 x21: .cfa -16 + ^
STACK CFI c340 x21: x21
STACK CFI c344 x21: .cfa -16 + ^
STACK CFI c34c x21: x21
STACK CFI c350 x21: .cfa -16 + ^
STACK CFI INIT c354 98 .cfa: sp 0 + .ra: x30
STACK CFI c364 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c370 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c3bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c3c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI c3e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT c3f0 3c8 .cfa: sp 0 + .ra: x30
STACK CFI c3f8 .cfa: sp 96 +
STACK CFI c404 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c410 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI c45c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c5ec x19: x19 x20: x20
STACK CFI c618 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI c620 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI c62c x19: x19 x20: x20
STACK CFI c630 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c684 x19: x19 x20: x20
STACK CFI c6a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c6cc x19: x19 x20: x20
STACK CFI c6d0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c748 x19: x19 x20: x20
STACK CFI c74c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c7b0 x19: x19 x20: x20
STACK CFI c7b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI INIT c7c0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI c7c8 .cfa: sp 64 +
STACK CFI c7d4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c7dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c7e4 x21: .cfa -16 + ^
STACK CFI c880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c888 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
