MODULE Linux arm64 3F7589BFC67B77112E427E2CF026E82F0 libcmdline-contexts-samba4.so.0
INFO CODE_ID BF89753F7BC611772E427E2CF026E82F283B5C30
PUBLIC ad0 0 cmdline_messaging_context
PUBLIC be0 0 cmdline_messaging_context_free
STACK CFI INIT a00 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT a30 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT a70 48 .cfa: sp 0 + .ra: x30
STACK CFI a74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a7c x19: .cfa -16 + ^
STACK CFI ab4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ac0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad0 108 .cfa: sp 0 + .ra: x30
STACK CFI ad8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ae0 x19: .cfa -16 + ^
STACK CFI b10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b18 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI b6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT be0 18 .cfa: sp 0 + .ra: x30
STACK CFI be8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bf0 .cfa: sp 0 + .ra: .ra x29: x29
