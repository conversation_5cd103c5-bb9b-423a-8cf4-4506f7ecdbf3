MODULE Linux arm64 3F56198944D2DA28963E10B14A19A6D00 libcryptsetup-token-systemd-tpm2.so
INFO CODE_ID 8919563FD24428DA963E10B14A19A6D040AD01A6
PUBLIC 18c0 0 cryptsetup_token_version
PUBLIC 18e0 0 cryptsetup_token_open_pin
PUBLIC 2330 0 cryptsetup_token_open
PUBLIC 2360 0 cryptsetup_token_buffer_free
PUBLIC 23d0 0 cryptsetup_token_dump
PUBLIC 2944 0 cryptsetup_token_validate
STACK CFI INIT 15c0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15f0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1630 48 .cfa: sp 0 + .ra: x30
STACK CFI 1634 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 163c x19: .cfa -16 + ^
STACK CFI 1674 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1680 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1690 bc .cfa: sp 0 + .ra: x30
STACK CFI 1698 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 16a8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 16fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1704 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1750 168 .cfa: sp 0 + .ra: x30
STACK CFI 1758 .cfa: sp 112 +
STACK CFI 1764 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 176c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1788 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1794 x27: .cfa -16 + ^
STACK CFI 17a8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 17bc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1820 x23: x23 x24: x24
STACK CFI 1824 x25: x25 x26: x26
STACK CFI 1828 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 182c x23: x23 x24: x24
STACK CFI 1830 x25: x25 x26: x26
STACK CFI 186c x19: x19 x20: x20
STACK CFI 1874 x27: x27
STACK CFI 1878 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 1880 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 18a0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 18a4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 18a8 x27: .cfa -16 + ^
STACK CFI 18ac x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 18b0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 18b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 18c0 20 .cfa: sp 0 + .ra: x30
STACK CFI 18c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 18d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18e0 a48 .cfa: sp 0 + .ra: x30
STACK CFI 18e8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 18f8 .cfa: sp 784 +
STACK CFI 193c x19: .cfa -80 + ^
STACK CFI 1944 x20: .cfa -72 + ^
STACK CFI 194c x23: .cfa -48 + ^
STACK CFI 1954 x24: .cfa -40 + ^
STACK CFI 1958 x25: .cfa -32 + ^
STACK CFI 1960 x26: .cfa -24 + ^
STACK CFI 1968 x21: .cfa -64 + ^
STACK CFI 1970 x22: .cfa -56 + ^
STACK CFI 19b8 x27: .cfa -16 + ^
STACK CFI 19bc x28: .cfa -8 + ^
STACK CFI 1c4c x27: x27
STACK CFI 1c50 x28: x28
STACK CFI 1c74 x27: .cfa -16 + ^
STACK CFI 1c78 x28: .cfa -8 + ^
STACK CFI 1c90 x27: x27 x28: x28
STACK CFI 1dbc x19: x19
STACK CFI 1dc4 x20: x20
STACK CFI 1dc8 x21: x21
STACK CFI 1dcc x22: x22
STACK CFI 1dd0 x23: x23
STACK CFI 1dd4 x24: x24
STACK CFI 1dd8 x25: x25
STACK CFI 1ddc x26: x26
STACK CFI 1de0 .cfa: sp 96 +
STACK CFI 1de4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1dec .cfa: sp 784 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 1e10 x27: .cfa -16 + ^
STACK CFI 1e14 x28: .cfa -8 + ^
STACK CFI 1e84 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1ea8 x19: .cfa -80 + ^
STACK CFI 1eac x20: .cfa -72 + ^
STACK CFI 1eb0 x21: .cfa -64 + ^
STACK CFI 1eb4 x22: .cfa -56 + ^
STACK CFI 1eb8 x23: .cfa -48 + ^
STACK CFI 1ebc x24: .cfa -40 + ^
STACK CFI 1ec0 x25: .cfa -32 + ^
STACK CFI 1ec4 x26: .cfa -24 + ^
STACK CFI 1ec8 x27: .cfa -16 + ^
STACK CFI 1ecc x28: .cfa -8 + ^
STACK CFI 1f00 x27: x27
STACK CFI 1f04 x28: x28
STACK CFI 1f08 x21: x21 x22: x22
STACK CFI 1f2c x21: .cfa -64 + ^
STACK CFI 1f30 x22: .cfa -56 + ^
STACK CFI 1f34 x27: .cfa -16 + ^
STACK CFI 1f38 x28: .cfa -8 + ^
STACK CFI 1f3c x27: x27 x28: x28
STACK CFI 1f90 x27: .cfa -16 + ^
STACK CFI 1f94 x28: .cfa -8 + ^
STACK CFI 1f98 x27: x27 x28: x28
STACK CFI 1fbc x27: .cfa -16 + ^
STACK CFI 1fc0 x28: .cfa -8 + ^
STACK CFI 1fc4 x27: x27 x28: x28
STACK CFI 1fe8 x27: .cfa -16 + ^
STACK CFI 1fec x28: .cfa -8 + ^
STACK CFI 1ff0 x27: x27 x28: x28
STACK CFI 2014 x27: .cfa -16 + ^
STACK CFI 2018 x28: .cfa -8 + ^
STACK CFI 202c x27: x27
STACK CFI 2034 x28: x28
STACK CFI 2038 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2124 x27: x27
STACK CFI 2128 x28: x28
STACK CFI 212c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 231c x27: x27 x28: x28
STACK CFI 2320 x27: .cfa -16 + ^
STACK CFI 2324 x28: .cfa -8 + ^
STACK CFI INIT 2330 2c .cfa: sp 0 + .ra: x30
STACK CFI 2338 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2344 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2360 70 .cfa: sp 0 + .ra: x30
STACK CFI 2370 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2378 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 23a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 23ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 23c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 23d0 574 .cfa: sp 0 + .ra: x30
STACK CFI 23d8 .cfa: sp 304 +
STACK CFI 23e4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 23ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 242c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2430 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2520 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 26f4 x25: x25 x26: x26
STACK CFI 27bc x21: x21 x22: x22
STACK CFI 27c0 x23: x23 x24: x24
STACK CFI 27c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 27cc .cfa: sp 304 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 2858 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 285c x25: x25 x26: x26
STACK CFI 2904 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2938 x25: x25 x26: x26
STACK CFI 2940 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 2944 41c .cfa: sp 0 + .ra: x30
STACK CFI 294c .cfa: sp 64 +
STACK CFI 2958 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2964 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2b44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2b4c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
