MODULE Linux arm64 A9FB1D0411A8B46F41B75E2D2C5F3BD70 lmregexp.so
INFO CODE_ID 041DFBA9A8116FB441B75E2D2C5F3BD71F614D2F
PUBLIC 1010 0 regexpQueryInterface
PUBLIC 17a4 0 regexpClassInit
PUBLIC 18e0 0 regexpClassExit
PUBLIC 1950 0 modInit
STACK CFI INIT e30 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT e60 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT ea0 48 .cfa: sp 0 + .ra: x30
STACK CFI ea4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI eac x19: .cfa -16 + ^
STACK CFI ee4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ef0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT f00 28 .cfa: sp 0 + .ra: x30
STACK CFI f0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI f20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f30 24 .cfa: sp 0 + .ra: x30
STACK CFI f3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI f4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f54 1c .cfa: sp 0 + .ra: x30
STACK CFI f5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI f64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT f70 28 .cfa: sp 0 + .ra: x30
STACK CFI f78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI f88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT fa0 24 .cfa: sp 0 + .ra: x30
STACK CFI fa8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI fb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT fc4 48 .cfa: sp 0 + .ra: x30
STACK CFI fcc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI fe4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ff0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ffc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1010 5c .cfa: sp 0 + .ra: x30
STACK CFI 1018 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 105c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1064 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1070 1c .cfa: sp 0 + .ra: x30
STACK CFI 1078 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1084 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1090 2c .cfa: sp 0 + .ra: x30
STACK CFI 109c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10c0 21c .cfa: sp 0 + .ra: x30
STACK CFI 10c8 .cfa: sp 240 +
STACK CFI 10dc .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 10ec x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1180 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1188 .cfa: sp 240 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 118c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 11ac x25: .cfa -16 + ^
STACK CFI 1220 x23: x23 x24: x24
STACK CFI 1228 x25: x25
STACK CFI 122c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1230 x23: x23 x24: x24
STACK CFI 1234 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 1290 x23: x23 x24: x24 x25: x25
STACK CFI 1294 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1298 x25: .cfa -16 + ^
STACK CFI 12b8 x23: x23 x24: x24
STACK CFI 12bc x25: x25
STACK CFI 12c0 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 12d4 x23: x23 x24: x24
STACK CFI 12d8 x25: x25
STACK CFI INIT 12e0 94 .cfa: sp 0 + .ra: x30
STACK CFI 12e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 12f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1348 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1350 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1374 fc .cfa: sp 0 + .ra: x30
STACK CFI 1384 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 138c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1394 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13c0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 13c4 x25: .cfa -16 + ^
STACK CFI 1400 x19: x19 x20: x20
STACK CFI 1410 x25: x25
STACK CFI 1414 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 141c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 1454 x19: x19 x20: x20 x25: x25
STACK CFI 1464 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 1470 128 .cfa: sp 0 + .ra: x30
STACK CFI 1478 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1480 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1488 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 14bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14c4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 14d0 x23: .cfa -16 + ^
STACK CFI 155c x23: x23
STACK CFI 1560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1568 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 158c x23: x23
STACK CFI 1590 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 15a0 7c .cfa: sp 0 + .ra: x30
STACK CFI 15a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 15b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 15bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 15c8 x23: .cfa -16 + ^
STACK CFI 1614 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1620 9c .cfa: sp 0 + .ra: x30
STACK CFI 1628 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1630 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1638 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1648 x23: .cfa -16 + ^
STACK CFI 168c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1694 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 16b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 16c0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 16d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1714 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 171c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 179c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 17a4 138 .cfa: sp 0 + .ra: x30
STACK CFI 17ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 17c4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1810 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 188c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1894 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 18e0 68 .cfa: sp 0 + .ra: x30
STACK CFI 18e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 18f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1950 dc .cfa: sp 0 + .ra: x30
STACK CFI 1958 .cfa: sp 64 +
STACK CFI 1964 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 196c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1978 x21: .cfa -16 + ^
STACK CFI 1a08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1a10 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
