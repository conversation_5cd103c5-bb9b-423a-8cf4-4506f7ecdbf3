MODULE Linux arm64 1215E2DA4DF57CD04234E4881558965E0 libcap-ng.so.0
INFO CODE_ID DAE21512F54DD07C4234E4881558965E96A509F9
PUBLIC 1b20 0 capng_clear
PUBLIC 1c30 0 capng_fill
PUBLIC 1d64 0 capng_setpid
PUBLIC 1de0 0 capng_get_rootid
PUBLIC 1e14 0 capng_set_rootid
PUBLIC 1ea4 0 capng_get_caps_process
PUBLIC 2150 0 capng_get_caps_fd
PUBLIC 2380 0 capng_update
PUBLIC 2830 0 capng_updatev
PUBLIC 2990 0 capng_lock
PUBLIC 2a20 0 capng_have_capabilities
PUBLIC 2d04 0 capng_apply_caps_fd
PUBLIC 2f04 0 capng_have_permitted_capabilities
PUBLIC 2ff4 0 capng_have_capability
PUBLIC 3244 0 capng_apply
PUBLIC 3510 0 capng_change_id
PUBLIC 3850 0 capng_print_caps_numeric
PUBLIC 3bd4 0 capng_save_state
PUBLIC 3c30 0 capng_restore_state
PUBLIC 3ca0 0 capng_name_to_capability
PUBLIC 3d20 0 capng_capability_to_name
PUBLIC 3df4 0 capng_print_caps_text
STACK CFI INIT 1710 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1740 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1780 48 .cfa: sp 0 + .ra: x30
STACK CFI 1784 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 178c x19: .cfa -16 + ^
STACK CFI 17c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 17d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17e0 34 .cfa: sp 0 + .ra: x30
STACK CFI 17e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 180c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1814 104 .cfa: sp 0 + .ra: x30
STACK CFI 1820 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 182c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 185c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1864 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 18f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1900 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1920 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 1928 .cfa: sp 144 +
STACK CFI 192c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 194c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1968 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1a38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1a40 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1a58 x23: .cfa -16 + ^
STACK CFI 1af8 x23: x23
STACK CFI 1b00 x23: .cfa -16 + ^
STACK CFI 1b04 x23: x23
STACK CFI 1b0c x23: .cfa -16 + ^
STACK CFI 1b10 x23: x23
STACK CFI 1b14 x23: .cfa -16 + ^
STACK CFI INIT 1480 238 .cfa: sp 0 + .ra: x30
STACK CFI 1488 .cfa: sp 208 +
STACK CFI 1498 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 14a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 15c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15cc .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 15dc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 15e0 x23: .cfa -16 + ^
STACK CFI 1630 x21: x21 x22: x22
STACK CFI 1634 x23: x23
STACK CFI 1638 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 16ac x21: x21 x22: x22 x23: x23
STACK CFI 16b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 16b4 x23: .cfa -16 + ^
STACK CFI INIT 16c0 34 .cfa: sp 0 + .ra: x30
STACK CFI 16d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 16e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b20 108 .cfa: sp 0 + .ra: x30
STACK CFI 1b28 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1b30 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1b4c x21: .cfa -16 + ^
STACK CFI 1bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1bb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1c30 134 .cfa: sp 0 + .ra: x30
STACK CFI 1c38 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1c40 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1c5c x21: .cfa -16 + ^
STACK CFI 1d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1d14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1d64 78 .cfa: sp 0 + .ra: x30
STACK CFI 1d6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1d74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1d90 x21: .cfa -16 + ^
STACK CFI 1dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1dd0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1de0 34 .cfa: sp 0 + .ra: x30
STACK CFI 1de8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1e08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1e14 90 .cfa: sp 0 + .ra: x30
STACK CFI 1e1c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1e24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e40 x21: .cfa -16 + ^
STACK CFI 1e88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1e90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1ea4 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 1eac .cfa: sp 144 +
STACK CFI 1eb0 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1ed0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1ed8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1f78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1f80 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1fb8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2060 x23: x23 x24: x24
STACK CFI 2064 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2128 x23: x23 x24: x24
STACK CFI 2138 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 213c x23: x23 x24: x24
STACK CFI 2144 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 2150 228 .cfa: sp 0 + .ra: x30
STACK CFI 2158 .cfa: sp 80 +
STACK CFI 215c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2164 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2180 x21: .cfa -16 + ^
STACK CFI 22b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 22bc .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2380 4ac .cfa: sp 0 + .ra: x30
STACK CFI 2388 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 242c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2434 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2830 158 .cfa: sp 0 + .ra: x30
STACK CFI 2838 .cfa: sp 160 +
STACK CFI 2844 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 284c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2854 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 28ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 28b4 .cfa: sp 160 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 28b8 x23: .cfa -64 + ^
STACK CFI 2964 x23: x23
STACK CFI 2968 x23: .cfa -64 + ^
STACK CFI 2974 x23: x23
STACK CFI 2984 x23: .cfa -64 + ^
STACK CFI INIT 2990 90 .cfa: sp 0 + .ra: x30
STACK CFI 2998 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29a0 x19: .cfa -16 + ^
STACK CFI 29b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 29f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 29fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2a20 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 2a28 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a30 x21: .cfa -16 + ^
STACK CFI 2a38 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2bc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2bcc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2c2c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2c58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2c9c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2d04 200 .cfa: sp 0 + .ra: x30
STACK CFI 2d0c .cfa: sp 208 +
STACK CFI 2d10 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2d18 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d34 x21: .cfa -16 + ^
STACK CFI 2e10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2e18 .cfa: sp 208 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2f04 f0 .cfa: sp 0 + .ra: x30
STACK CFI 2f0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f14 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2f68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2f70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2f8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2fa0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2fd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2ff4 250 .cfa: sp 0 + .ra: x30
STACK CFI 2ffc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3004 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 300c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 30b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 30c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3244 2c8 .cfa: sp 0 + .ra: x30
STACK CFI 324c .cfa: sp 160 +
STACK CFI 3250 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3258 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3260 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 329c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 32d4 x19: x19 x20: x20
STACK CFI 3304 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 330c .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 3310 x25: .cfa -16 + ^
STACK CFI 3394 x25: x25
STACK CFI 3410 x19: x19 x20: x20
STACK CFI 341c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3478 x19: x19 x20: x20
STACK CFI 3480 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^
STACK CFI 34a8 x25: x25
STACK CFI 34b4 x25: .cfa -16 + ^
STACK CFI 34c8 x25: x25
STACK CFI 34d0 x19: x19 x20: x20
STACK CFI 34d8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^
STACK CFI 34dc x19: x19 x20: x20
STACK CFI 34e4 x25: x25
STACK CFI 34e8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 34ec x19: x19 x20: x20
STACK CFI 34f4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 34f8 x19: x19 x20: x20
STACK CFI 3504 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3508 x25: .cfa -16 + ^
STACK CFI INIT 3510 33c .cfa: sp 0 + .ra: x30
STACK CFI 3518 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3520 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3540 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3558 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 36bc x23: x23 x24: x24
STACK CFI 36d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 36dc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3798 x23: x23 x24: x24
STACK CFI 379c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 37a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3824 x23: x23 x24: x24
STACK CFI 3834 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3838 x23: x23 x24: x24
STACK CFI 3840 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3844 x23: x23 x24: x24
STACK CFI INIT 3850 384 .cfa: sp 0 + .ra: x30
STACK CFI 3858 .cfa: sp 96 +
STACK CFI 3860 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3878 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3880 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3890 x23: .cfa -16 + ^
STACK CFI 39d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 39dc .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3bd4 54 .cfa: sp 0 + .ra: x30
STACK CFI 3bdc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3c30 6c .cfa: sp 0 + .ra: x30
STACK CFI 3c40 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c4c x19: .cfa -16 + ^
STACK CFI 3c90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3ca0 78 .cfa: sp 0 + .ra: x30
STACK CFI 3ca8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3cb0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3cc0 x21: .cfa -16 + ^
STACK CFI 3cf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3d00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3d20 d4 .cfa: sp 0 + .ra: x30
STACK CFI 3d34 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d3c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3d9c x21: .cfa -16 + ^
STACK CFI 3dd0 x21: x21
STACK CFI 3dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ddc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3de0 x21: x21
STACK CFI 3dec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3df4 234 .cfa: sp 0 + .ra: x30
STACK CFI 3dfc .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3e08 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3e2c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3e40 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3e4c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3e5c x27: .cfa -32 + ^
STACK CFI 3eec x19: x19 x20: x20
STACK CFI 3ef8 x23: x23 x24: x24
STACK CFI 3f00 x27: x27
STACK CFI 3f04 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 3f0c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 3f88 x19: x19 x20: x20
STACK CFI 3f94 x23: x23 x24: x24
STACK CFI 3f9c x27: x27
STACK CFI 3fa0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 3fa8 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 3fdc x19: x19 x20: x20
STACK CFI 3fec x23: x23 x24: x24
STACK CFI 3ff4 x27: x27
STACK CFI 3ff8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 4000 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 4004 x19: x19 x20: x20
STACK CFI 4008 x23: x23 x24: x24
STACK CFI 400c x27: x27
STACK CFI 4020 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI INIT 4030 18 .cfa: sp 0 + .ra: x30
STACK CFI 4034 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4040 .cfa: sp 0 + .ra: .ra x29: x29
