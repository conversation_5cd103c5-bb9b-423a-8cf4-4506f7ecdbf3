MODULE Linux arm64 6E700470F04900582D0B573B0F00903D0 libpixbufloader-gif.so
INFO CODE_ID 7004706E49F058002D0B573B0F00903DFB467144
PUBLIC 3b90 0 fill_vtable
PUBLIC 3be0 0 fill_info
STACK CFI INIT 1710 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1740 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1780 48 .cfa: sp 0 + .ra: x30
STACK CFI 1784 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 178c x19: .cfa -16 + ^
STACK CFI 17c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 17d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17e0 18 .cfa: sp 0 + .ra: x30
STACK CFI 17e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 17f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1800 18 .cfa: sp 0 + .ra: x30
STACK CFI 1808 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1810 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1820 18 .cfa: sp 0 + .ra: x30
STACK CFI 1828 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1830 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1840 3c .cfa: sp 0 + .ra: x30
STACK CFI 1848 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 185c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 186c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1870 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1880 30 .cfa: sp 0 + .ra: x30
STACK CFI 1888 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 18a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18b0 18 .cfa: sp 0 + .ra: x30
STACK CFI 18b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 18c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18d0 3c .cfa: sp 0 + .ra: x30
STACK CFI 18d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 18fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1904 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1910 3c .cfa: sp 0 + .ra: x30
STACK CFI 1918 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 192c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 193c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1940 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1950 18 .cfa: sp 0 + .ra: x30
STACK CFI 1958 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1960 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1970 18 .cfa: sp 0 + .ra: x30
STACK CFI 1978 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1980 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1990 f8 .cfa: sp 0 + .ra: x30
STACK CFI 1998 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 19a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 19b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 19dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 19f4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1a50 x23: x23 x24: x24
STACK CFI INIT 1a90 9c .cfa: sp 0 + .ra: x30
STACK CFI 1a98 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1aa0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1aac x21: .cfa -16 + ^
STACK CFI 1adc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1ae4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1b1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1b24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1b30 a5c .cfa: sp 0 + .ra: x30
STACK CFI 1b38 .cfa: sp 128 +
STACK CFI 1b44 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1b4c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1b54 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1b60 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1b68 x25: .cfa -16 + ^
STACK CFI 1c70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 1c78 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2590 40 .cfa: sp 0 + .ra: x30
STACK CFI 2598 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 25a0 x19: .cfa -16 + ^
STACK CFI 25c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 25d0 120 .cfa: sp 0 + .ra: x30
STACK CFI 26c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 26f0 dc .cfa: sp 0 + .ra: x30
STACK CFI 26f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2700 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 270c x21: .cfa -16 + ^
STACK CFI 274c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2754 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 27d0 44 .cfa: sp 0 + .ra: x30
STACK CFI 27d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27e0 x19: .cfa -16 + ^
STACK CFI 2804 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2814 80 .cfa: sp 0 + .ra: x30
STACK CFI 281c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2824 x19: .cfa -16 + ^
STACK CFI 287c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2884 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2894 80 .cfa: sp 0 + .ra: x30
STACK CFI 289c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 28a4 x19: .cfa -16 + ^
STACK CFI 28fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2904 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2914 50 .cfa: sp 0 + .ra: x30
STACK CFI 291c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2924 x19: .cfa -16 + ^
STACK CFI 2944 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 294c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 295c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2964 b4 .cfa: sp 0 + .ra: x30
STACK CFI 296c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2978 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 2a08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2a20 48 .cfa: sp 0 + .ra: x30
STACK CFI 2a28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2a3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2a70 70 .cfa: sp 0 + .ra: x30
STACK CFI 2a78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a80 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2aa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2aac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2ad8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2ae0 58 .cfa: sp 0 + .ra: x30
STACK CFI 2ae8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2af0 x19: .cfa -16 + ^
STACK CFI 2b18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2b40 19c .cfa: sp 0 + .ra: x30
STACK CFI 2b48 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2b54 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2b60 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2b74 x25: .cfa -16 + ^
STACK CFI 2b90 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2bf8 x23: x23 x24: x24
STACK CFI 2c00 x19: x19 x20: x20
STACK CFI 2c04 x21: x21 x22: x22
STACK CFI 2c08 x25: x25
STACK CFI 2c0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c14 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2c34 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 2c60 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2c64 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2c68 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2c6c x25: .cfa -16 + ^
STACK CFI 2c70 x23: x23 x24: x24 x25: x25
STACK CFI 2c9c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2ca0 x25: .cfa -16 + ^
STACK CFI 2ca4 x19: x19 x20: x20 x23: x23 x24: x24 x25: x25
STACK CFI 2cd0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2cd4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2cd8 x25: .cfa -16 + ^
STACK CFI INIT 2ce0 16c .cfa: sp 0 + .ra: x30
STACK CFI 2ce8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2cf0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d10 x21: .cfa -16 + ^
STACK CFI 2d7c x21: x21
STACK CFI 2d88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2db4 x21: x21
STACK CFI 2ddc x21: .cfa -16 + ^
STACK CFI 2e14 x21: x21
STACK CFI 2e18 x21: .cfa -16 + ^
STACK CFI INIT 2e50 16c .cfa: sp 0 + .ra: x30
STACK CFI 2e58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2e60 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2e80 x21: .cfa -16 + ^
STACK CFI 2ef4 x21: x21
STACK CFI 2f00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2f08 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2f58 x21: x21
STACK CFI 2f80 x21: .cfa -16 + ^
STACK CFI 2fb8 x21: x21
STACK CFI INIT 2fc0 118 .cfa: sp 0 + .ra: x30
STACK CFI 2fc8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2fd8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ff8 x19: x19 x20: x20
STACK CFI 2ffc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3004 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3008 x21: .cfa -16 + ^
STACK CFI 303c x21: x21
STACK CFI 3040 x19: x19 x20: x20
STACK CFI 306c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3070 x21: .cfa -16 + ^
STACK CFI 3074 x21: x21
STACK CFI 30a0 x21: .cfa -16 + ^
STACK CFI 30a4 x19: x19 x20: x20 x21: x21
STACK CFI 30d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 30d4 x21: .cfa -16 + ^
STACK CFI INIT 30e0 58 .cfa: sp 0 + .ra: x30
STACK CFI 30e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30f0 x19: .cfa -16 + ^
STACK CFI 3118 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3140 ac .cfa: sp 0 + .ra: x30
STACK CFI 3148 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3150 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 315c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 31c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 31cc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 31f0 68c .cfa: sp 0 + .ra: x30
STACK CFI 31f8 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3200 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3208 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 321c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 32e4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 32e8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3650 x25: x25 x26: x26
STACK CFI 3654 x27: x27 x28: x28
STACK CFI 3678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3680 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 36e0 x25: x25 x26: x26
STACK CFI 36e4 x27: x27 x28: x28
STACK CFI 36e8 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3748 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 37f4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 37f8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3808 x25: x25 x26: x26
STACK CFI 380c x27: x27 x28: x28
STACK CFI 3810 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3848 x25: x25 x26: x26
STACK CFI 384c x27: x27 x28: x28
STACK CFI 3858 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3874 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 3880 278 .cfa: sp 0 + .ra: x30
STACK CFI 3888 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3898 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 38e4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3900 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3a10 x21: x21 x22: x22
STACK CFI 3a14 x19: x19 x20: x20
STACK CFI 3a24 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3a2c .cfa: sp 80 + .ra: .cfa -72 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3a3c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3a84 x19: x19 x20: x20
STACK CFI 3a9c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3aa4 .cfa: sp 80 + .ra: .cfa -72 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3ae4 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3aec .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3af0 x19: x19 x20: x20
STACK CFI INIT 3b00 88 .cfa: sp 0 + .ra: x30
STACK CFI 3b08 .cfa: sp 64 +
STACK CFI 3b14 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3b7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3b84 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3b90 4c .cfa: sp 0 + .ra: x30
STACK CFI 3b98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3bac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3be0 5c .cfa: sp 0 + .ra: x30
STACK CFI 3be8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3bfc .cfa: sp 0 + .ra: .ra x29: x29
