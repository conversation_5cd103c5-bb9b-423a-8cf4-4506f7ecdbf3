MODULE Linux arm64 C7767CEE3B424AB7E873C838A4E4656D0 libcolord_sensor_sane.so
INFO CODE_ID EE7C76C7423BB74AE873C838A4E4656D9A021CF1
PUBLIC c54 0 cd_plugin_get_description
PUBLIC c74 0 cd_plugin_config_enabled
PUBLIC c90 0 cd_plugin_coldplug
PUBLIC ce0 0 cd_plugin_initialize
PUBLIC d90 0 cd_plugin_destroy
STACK CFI INIT a00 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT a30 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT a70 48 .cfa: sp 0 + .ra: x30
STACK CFI a74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a7c x19: .cfa -16 + ^
STACK CFI ab4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ac0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad0 1c .cfa: sp 0 + .ra: x30
STACK CFI ad8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ae4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT af0 fc .cfa: sp 0 + .ra: x30
STACK CFI af8 .cfa: sp 80 +
STACK CFI b04 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b0c x19: .cfa -16 + ^
STACK CFI b5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI b64 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT bf0 64 .cfa: sp 0 + .ra: x30
STACK CFI bf8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c04 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c44 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI c4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT c54 20 .cfa: sp 0 + .ra: x30
STACK CFI c5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c74 1c .cfa: sp 0 + .ra: x30
STACK CFI c7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c88 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c90 50 .cfa: sp 0 + .ra: x30
STACK CFI c98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ca0 x19: .cfa -16 + ^
STACK CFI cc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ce0 a8 .cfa: sp 0 + .ra: x30
STACK CFI ce8 .cfa: sp 80 +
STACK CFI cfc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d08 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d14 x21: .cfa -16 + ^
STACK CFI d7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI d84 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT d90 20 .cfa: sp 0 + .ra: x30
STACK CFI d98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI da4 .cfa: sp 0 + .ra: .ra x29: x29
