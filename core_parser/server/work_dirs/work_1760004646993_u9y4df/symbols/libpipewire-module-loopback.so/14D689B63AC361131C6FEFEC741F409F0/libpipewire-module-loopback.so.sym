MODULE Linux arm64 14D689B63AC361131C6FEFEC741F409F0 libpipewire-module-loopback.so
INFO CODE_ID B689D614C33A13611C6FEFEC741F409F71AA73A8
PUBLIC 80b0 0 pipewire__module_init
STACK CFI INIT 2210 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2240 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2280 48 .cfa: sp 0 + .ra: x30
STACK CFI 2284 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 228c x19: .cfa -16 + ^
STACK CFI 22c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 22d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22e0 340 .cfa: sp 0 + .ra: x30
STACK CFI 22e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 23d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 23e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2518 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2524 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2574 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 257c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 25f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 25fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2620 50 .cfa: sp 0 + .ra: x30
STACK CFI 2628 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2630 x19: .cfa -16 + ^
STACK CFI 2668 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2670 24 .cfa: sp 0 + .ra: x30
STACK CFI 2678 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 268c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2694 50 .cfa: sp 0 + .ra: x30
STACK CFI 269c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 26a4 x19: .cfa -16 + ^
STACK CFI 26dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 26e4 2c88 .cfa: sp 0 + .ra: x30
STACK CFI 26ec .cfa: sp 480 +
STACK CFI 2700 .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 2718 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 2720 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 2738 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 2ee0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2ee8 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 5370 330 .cfa: sp 0 + .ra: x30
STACK CFI 5378 .cfa: sp 96 +
STACK CFI 537c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5384 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5398 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 552c .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 56a0 de8 .cfa: sp 0 + .ra: x30
STACK CFI 56a8 .cfa: sp 352 +
STACK CFI 56bc .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 56c4 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 56d4 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 56e0 x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 56e8 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 5ab0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5ab8 .cfa: sp 352 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT 6490 1c .cfa: sp 0 + .ra: x30
STACK CFI 6498 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 64a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 64b0 5f0 .cfa: sp 0 + .ra: x30
STACK CFI 64b8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 64bc .cfa: x29 96 +
STACK CFI 64c0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 64e0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6838 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6840 .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6aa0 440 .cfa: sp 0 + .ra: x30
STACK CFI 6aa8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6ab0 .cfa: sp 1392 +
STACK CFI 6ad0 x21: .cfa -64 + ^
STACK CFI 6ad8 x22: .cfa -56 + ^
STACK CFI 6b4c x19: .cfa -80 + ^
STACK CFI 6b50 x20: .cfa -72 + ^
STACK CFI 6b54 x23: .cfa -48 + ^
STACK CFI 6b58 x24: .cfa -40 + ^
STACK CFI 6b5c x25: .cfa -32 + ^
STACK CFI 6b60 x26: .cfa -24 + ^
STACK CFI 6b64 x27: .cfa -16 + ^
STACK CFI 6d80 x19: x19
STACK CFI 6d84 x20: x20
STACK CFI 6d88 x21: x21
STACK CFI 6d8c x22: x22
STACK CFI 6d90 x23: x23
STACK CFI 6d94 x24: x24
STACK CFI 6d98 x25: x25
STACK CFI 6d9c x26: x26
STACK CFI 6da0 x27: x27
STACK CFI 6da4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6da8 x21: x21
STACK CFI 6dac x22: x22
STACK CFI 6dcc .cfa: sp 96 +
STACK CFI 6dd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6dd8 .cfa: sp 1392 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 6eb8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 6ebc x19: .cfa -80 + ^
STACK CFI 6ec0 x20: .cfa -72 + ^
STACK CFI 6ec4 x21: .cfa -64 + ^
STACK CFI 6ec8 x22: .cfa -56 + ^
STACK CFI 6ecc x23: .cfa -48 + ^
STACK CFI 6ed0 x24: .cfa -40 + ^
STACK CFI 6ed4 x25: .cfa -32 + ^
STACK CFI 6ed8 x26: .cfa -24 + ^
STACK CFI 6edc x27: .cfa -16 + ^
STACK CFI INIT 6ee0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 6ee8 .cfa: sp 32 +
STACK CFI 6ef8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6f40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6f48 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6f8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6f94 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6fa0 54 .cfa: sp 0 + .ra: x30
STACK CFI 6fa8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6fb0 x19: .cfa -16 + ^
STACK CFI 6fec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6ff4 2f0 .cfa: sp 0 + .ra: x30
STACK CFI 6ffc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7004 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 704c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7054 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7090 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 70bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 70c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 70f4 x21: .cfa -16 + ^
STACK CFI 712c x21: x21
STACK CFI 71a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 71d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7270 x21: .cfa -16 + ^
STACK CFI INIT 72e4 2e8 .cfa: sp 0 + .ra: x30
STACK CFI 72ec .cfa: sp 464 +
STACK CFI 72f8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7300 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7330 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 74a0 x21: x21 x22: x22
STACK CFI 74c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 74d0 .cfa: sp 464 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 751c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7524 .cfa: sp 464 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 753c x21: x21 x22: x22
STACK CFI 7540 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7570 x23: .cfa -16 + ^
STACK CFI 75a0 x23: x23
STACK CFI 75a8 x23: .cfa -16 + ^
STACK CFI 75bc x23: x23
STACK CFI 75c0 x21: x21 x22: x22
STACK CFI 75c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 75c8 x23: .cfa -16 + ^
STACK CFI INIT 75d0 198 .cfa: sp 0 + .ra: x30
STACK CFI 75d8 .cfa: sp 96 +
STACK CFI 75dc .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 75e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 75f0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7640 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7648 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 76e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 76ec .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7770 b8 .cfa: sp 0 + .ra: x30
STACK CFI 7778 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7780 x19: .cfa -16 + ^
STACK CFI 77f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 77f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7830 50 .cfa: sp 0 + .ra: x30
STACK CFI 7838 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7840 x19: .cfa -16 + ^
STACK CFI 7878 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7880 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 7888 .cfa: sp 448 +
STACK CFI 7898 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 78ac x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 78b4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 78c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7a38 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7d44 12c .cfa: sp 0 + .ra: x30
STACK CFI 7d4c .cfa: sp 64 +
STACK CFI 7d58 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7d60 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7d6c x21: .cfa -16 + ^
STACK CFI 7e30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7e38 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7e6c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7e70 98 .cfa: sp 0 + .ra: x30
STACK CFI 7e78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7e80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7e9c x21: .cfa -16 + ^
STACK CFI 7ec4 x21: x21
STACK CFI 7ecc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7ed4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7ee0 x21: x21
STACK CFI 7eec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7ef4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7f10 19c .cfa: sp 0 + .ra: x30
STACK CFI 7f18 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7f24 .cfa: sp 1152 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7fa0 x21: .cfa -32 + ^
STACK CFI 7fa4 x22: .cfa -24 + ^
STACK CFI 7fac x23: .cfa -16 + ^
STACK CFI 8054 x21: x21
STACK CFI 8058 x22: x22
STACK CFI 805c x23: x23
STACK CFI 807c .cfa: sp 64 +
STACK CFI 8084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 808c .cfa: sp 1152 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 8090 x21: x21
STACK CFI 8094 x22: x22
STACK CFI 8098 x23: x23
STACK CFI 80a0 x21: .cfa -32 + ^
STACK CFI 80a4 x22: .cfa -24 + ^
STACK CFI 80a8 x23: .cfa -16 + ^
STACK CFI INIT 80b0 9c4 .cfa: sp 0 + .ra: x30
STACK CFI 80b8 .cfa: sp 160 +
STACK CFI 80c4 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 80cc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 80d4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 80ec x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 81bc x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 81c8 v8: .cfa -16 + ^
STACK CFI 84d4 x27: x27 x28: x28
STACK CFI 84d8 v8: v8
STACK CFI 85c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 85c8 .cfa: sp 160 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 862c v8: .cfa -16 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 863c v8: v8 x27: x27 x28: x28
STACK CFI 8654 v8: .cfa -16 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 8774 x27: x27 x28: x28
STACK CFI 8778 v8: v8
STACK CFI 877c v8: .cfa -16 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 880c x27: x27 x28: x28
STACK CFI 8810 v8: v8
STACK CFI 8814 v8: .cfa -16 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 89b0 v8: v8 x27: x27 x28: x28
STACK CFI 89e0 v8: .cfa -16 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 8a58 v8: v8 x27: x27 x28: x28
STACK CFI 8a6c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 8a70 v8: .cfa -16 + ^
