MODULE Linux arm64 CE9E1774BC3494EB0FAA40C8964C3C550 libpcsclite.so.1
INFO CODE_ID 74179ECE34BCEB940FAA40C8964C3C55DF4CB78C
PUBLIC 2dd4 0 pcsc_stringify_error
PUBLIC 3130 0 SCardEstablishContext
PUBLIC 34d0 0 SCardReleaseContext
PUBLIC 3810 0 SCardConnect
PUBLIC 3aa4 0 SCardReconnect
PUBLIC 3cc0 0 SCardDisconnect
PUBLIC 3ea4 0 SCardBeginTransaction
PUBLIC 4064 0 SCardEndTransaction
PUBLIC 41d0 0 SCardStatus
PUBLIC 46c0 0 SCardGetStatusChange
PUBLIC 55c0 0 SCardControl
PUBLIC 57d4 0 SCardGetAttrib
PUBLIC 58a0 0 SCardSetAttrib
PUBLIC 58f0 0 SCardTransmit
PUBLIC 5bf0 0 SCardListReaders
PUBLIC 5e50 0 SCardFreeMemory
PUBLIC 5f10 0 SCardListReaderGroups
PUBLIC 6080 0 SCardCancel
PUBLIC 61f0 0 SCardIsValidContext
STACK CFI INIT 1600 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1630 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1670 48 .cfa: sp 0 + .ra: x30
STACK CFI 1674 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 167c x19: .cfa -16 + ^
STACK CFI 16b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 16c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16d0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 16e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16e8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 16f8 x21: .cfa -16 + ^
STACK CFI 1744 x21: x21
STACK CFI 1748 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1750 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1754 x21: x21
STACK CFI 1760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1770 2bc .cfa: sp 0 + .ra: x30
STACK CFI 1778 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 178c .cfa: sp 2416 + x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 1828 .cfa: sp 256 +
STACK CFI 1838 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1840 .cfa: sp 2416 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x29: .cfa -256 + ^
STACK CFI 195c x25: .cfa -192 + ^
STACK CFI 19ac x25: x25
STACK CFI 1a10 x25: .cfa -192 + ^
STACK CFI 1a20 x25: x25
STACK CFI 1a28 x25: .cfa -192 + ^
STACK CFI INIT 1a30 6c .cfa: sp 0 + .ra: x30
STACK CFI 1a5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1aa0 70 .cfa: sp 0 + .ra: x30
STACK CFI 1acc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b10 1ec .cfa: sp 0 + .ra: x30
STACK CFI 1b18 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1b20 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1b28 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1b4c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1b68 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1c18 x21: x21 x22: x22
STACK CFI 1c1c x25: x25 x26: x26
STACK CFI 1c30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 1c38 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 1cac x21: x21 x22: x22
STACK CFI 1cb4 x25: x25 x26: x26
STACK CFI 1ccc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 1cd4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1d00 84 .cfa: sp 0 + .ra: x30
STACK CFI 1d08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d18 x19: .cfa -16 + ^
STACK CFI 1d48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1d50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1d7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1d84 180 .cfa: sp 0 + .ra: x30
STACK CFI 1d8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d94 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1e88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1e90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1f04 25c .cfa: sp 0 + .ra: x30
STACK CFI 1f0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1f14 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1f20 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 207c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2084 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2160 294 .cfa: sp 0 + .ra: x30
STACK CFI 2168 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2170 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2194 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 21c0 x23: .cfa -16 + ^
STACK CFI 21ec x23: x23
STACK CFI 22fc x21: x21 x22: x22
STACK CFI 2308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2310 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2324 x21: x21 x22: x22
STACK CFI 232c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 23b0 x21: x21 x22: x22
STACK CFI 23b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 23dc x23: .cfa -16 + ^
STACK CFI 23e8 x21: x21 x22: x22
STACK CFI 23f0 x23: x23
STACK CFI INIT 23f4 14c .cfa: sp 0 + .ra: x30
STACK CFI 23fc .cfa: sp 80 +
STACK CFI 2408 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2410 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 241c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2428 x23: .cfa -16 + ^
STACK CFI 24bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 24c4 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2540 d8 .cfa: sp 0 + .ra: x30
STACK CFI 2548 .cfa: sp 176 +
STACK CFI 2558 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2564 x19: .cfa -16 + ^
STACK CFI 25c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 25cc .cfa: sp 176 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2620 94 .cfa: sp 0 + .ra: x30
STACK CFI 262c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2634 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 26a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 26ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 26b4 c8 .cfa: sp 0 + .ra: x30
STACK CFI 26bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 26c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 273c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2744 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2780 20c .cfa: sp 0 + .ra: x30
STACK CFI 2788 .cfa: sp 176 +
STACK CFI 2798 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 27a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 27c8 x21: .cfa -16 + ^
STACK CFI 2854 x21: x21
STACK CFI 287c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2884 .cfa: sp 176 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 28c4 x21: .cfa -16 + ^
STACK CFI 290c x21: x21
STACK CFI 2914 x21: .cfa -16 + ^
STACK CFI 2984 x21: x21
STACK CFI 2988 x21: .cfa -16 + ^
STACK CFI INIT 2990 154 .cfa: sp 0 + .ra: x30
STACK CFI 2998 .cfa: sp 80 +
STACK CFI 29a4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 29ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 29b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 29c4 x23: .cfa -16 + ^
STACK CFI 2a58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2a60 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2ae4 250 .cfa: sp 0 + .ra: x30
STACK CFI 2aec .cfa: sp 416 +
STACK CFI 2af8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2b0c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2b28 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2b30 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2b3c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2b94 x21: x21 x22: x22
STACK CFI 2b98 x23: x23 x24: x24
STACK CFI 2b9c x25: x25 x26: x26
STACK CFI 2bc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2bd0 .cfa: sp 416 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 2bd8 x27: .cfa -16 + ^
STACK CFI 2c6c x27: x27
STACK CFI 2c74 x27: .cfa -16 + ^
STACK CFI 2c78 x27: x27
STACK CFI 2c9c x21: x21 x22: x22
STACK CFI 2ca0 x23: x23 x24: x24
STACK CFI 2ca4 x25: x25 x26: x26
STACK CFI 2cb0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2d1c x27: x27
STACK CFI 2d20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 2d24 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2d28 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2d2c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2d30 x27: .cfa -16 + ^
STACK CFI INIT 2d34 a0 .cfa: sp 0 + .ra: x30
STACK CFI 2d3c .cfa: sp 64 +
STACK CFI 2d4c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d58 x19: .cfa -16 + ^
STACK CFI 2dc8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2dd0 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2dd4 354 .cfa: sp 0 + .ra: x30
STACK CFI 2ddc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2de4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2e20 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2e68 x21: x21 x22: x22
STACK CFI 2e88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3130 39c .cfa: sp 0 + .ra: x30
STACK CFI 3138 .cfa: sp 160 +
STACK CFI 3144 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3150 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3158 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 31a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 31b0 .cfa: sp 160 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 3208 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 325c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3298 v8: .cfa -16 + ^
STACK CFI 3310 x27: x27 x28: x28
STACK CFI 3314 v8: v8
STACK CFI 3324 x25: x25 x26: x26
STACK CFI 3340 v8: .cfa -16 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3374 x25: x25 x26: x26
STACK CFI 3378 x27: x27 x28: x28
STACK CFI 337c v8: v8
STACK CFI 3398 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 339c x27: x27 x28: x28
STACK CFI 33d0 v8: .cfa -16 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 33d4 x27: x27 x28: x28
STACK CFI 33dc v8: v8
STACK CFI 33e0 v8: .cfa -16 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3474 x25: x25 x26: x26
STACK CFI 3478 x27: x27 x28: x28
STACK CFI 347c v8: v8
STACK CFI 3480 v8: .cfa -16 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 34ac v8: v8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 34b0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 34b4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 34b8 v8: .cfa -16 + ^
STACK CFI 34c0 x25: x25 x26: x26
STACK CFI 34c4 x27: x27 x28: x28
STACK CFI 34c8 v8: v8
STACK CFI INIT 15c0 24 .cfa: sp 0 + .ra: x30
STACK CFI 15c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 34d0 338 .cfa: sp 0 + .ra: x30
STACK CFI 34d8 .cfa: sp 128 +
STACK CFI 34e4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 34f0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3514 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3540 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 35c4 x27: .cfa -16 + ^
STACK CFI 36bc x27: x27
STACK CFI 36cc x25: x25 x26: x26
STACK CFI 3700 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3708 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 372c x27: .cfa -16 + ^
STACK CFI 37b8 x25: x25 x26: x26 x27: x27
STACK CFI 37cc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 37fc x25: x25 x26: x26 x27: x27
STACK CFI 3800 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3804 x27: .cfa -16 + ^
STACK CFI INIT 3810 294 .cfa: sp 0 + .ra: x30
STACK CFI 3818 .cfa: sp 288 +
STACK CFI 3824 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 382c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3848 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 385c v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 386c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 388c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3898 x27: .cfa -32 + ^
STACK CFI 3940 x23: x23 x24: x24
STACK CFI 3944 x25: x25 x26: x26
STACK CFI 3948 x27: x27
STACK CFI 394c v8: v8 v9: v9
STACK CFI 3950 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3954 x23: x23 x24: x24
STACK CFI 395c v8: v8 v9: v9
STACK CFI 3990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3998 .cfa: sp 288 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI 39a4 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI 39b8 x23: x23 x24: x24
STACK CFI 39bc x25: x25 x26: x26
STACK CFI 39c0 x27: x27
STACK CFI 39c4 v8: v8 v9: v9
STACK CFI 39c8 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI 39fc v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 3a08 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI 3a90 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 3a94 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3a98 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3a9c x27: .cfa -32 + ^
STACK CFI 3aa0 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI INIT 3aa4 218 .cfa: sp 0 + .ra: x30
STACK CFI 3aac .cfa: sp 208 +
STACK CFI 3ab8 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3ad0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3ad8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3aec x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3b00 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3b10 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3b1c v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 3c34 x21: x21 x22: x22
STACK CFI 3c38 x23: x23 x24: x24
STACK CFI 3c3c x25: x25 x26: x26
STACK CFI 3c40 x27: x27 x28: x28
STACK CFI 3c44 v8: v8 v9: v9
STACK CFI 3c70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c78 .cfa: sp 208 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 3c84 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3c88 x23: x23 x24: x24
STACK CFI 3c8c x27: x27 x28: x28
STACK CFI 3c90 v8: v8 v9: v9
STACK CFI 3c98 x21: x21 x22: x22
STACK CFI 3ca0 x25: x25 x26: x26
STACK CFI 3ca8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3cac x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3cb0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3cb4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3cb8 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI INIT 3cc0 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 3cc8 .cfa: sp 128 +
STACK CFI 3cd4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3cdc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3d00 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3d0c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3db8 x21: x21 x22: x22
STACK CFI 3dbc x23: x23 x24: x24
STACK CFI 3dc0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3dc4 x21: x21 x22: x22
STACK CFI 3dc8 x23: x23 x24: x24
STACK CFI 3dfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e04 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3e98 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 3e9c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3ea0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 3ea4 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 3eac .cfa: sp 176 +
STACK CFI 3eb8 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3ec4 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3ee8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 3efc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 3f08 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3f0c v8: .cfa -16 + ^
STACK CFI 3ff0 x23: x23 x24: x24
STACK CFI 3ff4 x25: x25 x26: x26
STACK CFI 3ff8 x27: x27 x28: x28
STACK CFI 3ffc v8: v8
STACK CFI 402c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4034 .cfa: sp 176 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 4038 x23: x23 x24: x24
STACK CFI 403c x25: x25 x26: x26
STACK CFI 4040 x27: x27 x28: x28
STACK CFI 4044 v8: v8
STACK CFI 4054 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4058 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 405c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4060 v8: .cfa -16 + ^
STACK CFI INIT 4064 164 .cfa: sp 0 + .ra: x30
STACK CFI 406c .cfa: sp 112 +
STACK CFI 4078 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4080 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 40a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 40b0 x23: .cfa -16 + ^
STACK CFI 4154 x21: x21 x22: x22
STACK CFI 4158 x23: x23
STACK CFI 4184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 418c .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4190 x21: x21 x22: x22
STACK CFI 4194 x23: x23
STACK CFI 41a0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 41bc x21: x21 x22: x22 x23: x23
STACK CFI 41c0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 41c4 x23: .cfa -16 + ^
STACK CFI INIT 41d0 4e8 .cfa: sp 0 + .ra: x30
STACK CFI 41d8 .cfa: sp 256 +
STACK CFI 41e4 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 41ec x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 426c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4278 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 4288 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4294 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4298 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4310 x19: x19 x20: x20
STACK CFI 4314 x21: x21 x22: x22
STACK CFI 4318 x25: x25 x26: x26
STACK CFI 431c x27: x27 x28: x28
STACK CFI 4324 v8: v8 v9: v9
STACK CFI 4350 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 4358 .cfa: sp 256 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 4490 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 44b4 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4608 x19: x19 x20: x20
STACK CFI 460c x21: x21 x22: x22
STACK CFI 4610 x25: x25 x26: x26
STACK CFI 4614 x27: x27 x28: x28
STACK CFI 4618 v8: v8 v9: v9
STACK CFI 4624 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4694 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4698 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 469c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 46a0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 46a4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 46a8 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI INIT 46c0 efc .cfa: sp 0 + .ra: x30
STACK CFI 46c8 .cfa: sp 288 +
STACK CFI 46d8 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 46fc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 470c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4714 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 471c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4744 x21: x21 x22: x22
STACK CFI 474c x23: x23 x24: x24
STACK CFI 4754 x25: x25 x26: x26
STACK CFI 4780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4788 .cfa: sp 288 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 4794 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 47d0 x21: x21 x22: x22
STACK CFI 47d4 x23: x23 x24: x24
STACK CFI 47d8 x25: x25 x26: x26
STACK CFI 47dc x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 48c0 x21: x21 x22: x22
STACK CFI 48c4 x23: x23 x24: x24
STACK CFI 48c8 x25: x25 x26: x26
STACK CFI 48cc x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 48e0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 490c v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 4968 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI 497c x21: x21 x22: x22
STACK CFI 4980 x23: x23 x24: x24
STACK CFI 4984 x25: x25 x26: x26
STACK CFI 4988 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4cc0 x27: x27 x28: x28
STACK CFI 4cc4 v8: v8 v9: v9
STACK CFI 4cd4 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4f78 x27: x27 x28: x28
STACK CFI 4f7c v8: v8 v9: v9
STACK CFI 4f80 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5030 x27: x27 x28: x28
STACK CFI 5038 v8: v8 v9: v9
STACK CFI 503c v8: .cfa -16 + ^ v9: .cfa -8 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5180 x27: x27 x28: x28
STACK CFI 5184 v8: v8 v9: v9
STACK CFI 5188 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5244 v8: v8 v9: v9 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 524c v8: .cfa -16 + ^ v9: .cfa -8 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5470 x27: x27 x28: x28
STACK CFI 5474 v8: v8 v9: v9
STACK CFI 5478 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 559c v8: v8 v9: v9
STACK CFI 55a0 x27: x27 x28: x28
STACK CFI 55a4 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 55a8 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 55ac x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 55b0 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 55b4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 55b8 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI INIT 55c0 214 .cfa: sp 0 + .ra: x30
STACK CFI 55c8 .cfa: sp 160 +
STACK CFI 55d4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 55dc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 55e4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 55ec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 55f8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5610 v8: .cfa -8 + ^
STACK CFI 5630 x27: .cfa -16 + ^
STACK CFI 568c x27: x27
STACK CFI 56c8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 56d0 .cfa: sp 160 + .ra: .cfa -88 + ^ v8: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 5794 x27: x27
STACK CFI 57a0 x27: .cfa -16 + ^
STACK CFI 57cc x27: x27
STACK CFI 57d0 x27: .cfa -16 + ^
STACK CFI INIT 57d4 c4 .cfa: sp 0 + .ra: x30
STACK CFI 57e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 57ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 57fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5838 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5870 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 588c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 58a0 50 .cfa: sp 0 + .ra: x30
STACK CFI 58a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 58d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 58dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 58e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 58f0 300 .cfa: sp 0 + .ra: x30
STACK CFI 58f8 .cfa: sp 240 +
STACK CFI 5908 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 592c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5938 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5948 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 596c v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 597c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 598c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5b34 x19: x19 x20: x20
STACK CFI 5b38 x21: x21 x22: x22
STACK CFI 5b3c x23: x23 x24: x24
STACK CFI 5b40 x25: x25 x26: x26
STACK CFI 5b44 x27: x27 x28: x28
STACK CFI 5b4c v8: v8 v9: v9
STACK CFI 5b74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5b7c .cfa: sp 240 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5b88 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5b90 x25: x25 x26: x26
STACK CFI 5b98 x27: x27 x28: x28
STACK CFI 5b9c v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5ba0 x19: x19 x20: x20
STACK CFI 5ba4 x21: x21 x22: x22
STACK CFI 5ba8 v8: v8 v9: v9
STACK CFI 5bb0 x23: x23 x24: x24
STACK CFI 5bb8 x27: x27 x28: x28
STACK CFI 5bc0 x25: x25 x26: x26
STACK CFI 5bc4 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5bd4 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5bd8 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5bdc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5be0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5be4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 5be8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5bec v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI INIT 5bf0 260 .cfa: sp 0 + .ra: x30
STACK CFI 5bf8 .cfa: sp 112 +
STACK CFI 5c04 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5c18 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5c20 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5c2c x27: .cfa -16 + ^
STACK CFI 5c34 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5c44 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5ccc x19: x19 x20: x20
STACK CFI 5cd0 x21: x21 x22: x22
STACK CFI 5cd4 x25: x25 x26: x26
STACK CFI 5cd8 x27: x27
STACK CFI 5d04 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 5d0c .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 5d20 x19: x19 x20: x20
STACK CFI 5d24 x21: x21 x22: x22
STACK CFI 5d28 x25: x25 x26: x26
STACK CFI 5d2c x27: x27
STACK CFI 5d30 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 5de4 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27
STACK CFI 5df0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 5e30 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27
STACK CFI 5e34 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5e38 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5e3c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5e40 x27: .cfa -16 + ^
STACK CFI INIT 5e50 b8 .cfa: sp 0 + .ra: x30
STACK CFI 5e58 .cfa: sp 64 +
STACK CFI 5e64 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5e6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5e78 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5ef0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5ef8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5f10 168 .cfa: sp 0 + .ra: x30
STACK CFI 5f18 .cfa: sp 112 +
STACK CFI 5f2c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5f3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5f50 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 6018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6020 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6080 168 .cfa: sp 0 + .ra: x30
STACK CFI 6088 .cfa: sp 96 +
STACK CFI 6094 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 609c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 60a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 60e4 x23: .cfa -16 + ^
STACK CFI 60fc x23: x23
STACK CFI 612c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6134 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 618c x23: x23
STACK CFI 6190 x23: .cfa -16 + ^
STACK CFI 61b8 x23: x23
STACK CFI 61bc x23: .cfa -16 + ^
STACK CFI 61c4 x23: x23
STACK CFI 61e4 x23: .cfa -16 + ^
STACK CFI INIT 61f0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 61f8 .cfa: sp 64 +
STACK CFI 6204 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 620c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6218 x21: .cfa -16 + ^
STACK CFI 628c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6294 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
