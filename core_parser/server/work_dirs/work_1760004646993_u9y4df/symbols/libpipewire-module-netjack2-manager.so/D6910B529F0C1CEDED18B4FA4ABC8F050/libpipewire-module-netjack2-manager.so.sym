MODULE Linux arm64 D6910B529F0C1CEDED18B4FA4ABC8F050 libpipewire-module-netjack2-manager.so
INFO CODE_ID 520B91D60C9FED1CED18B4FA4ABC8F05FB537D4F
PUBLIC 111e0 0 pipewire__module_init
STACK CFI INIT 6120 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6150 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6190 48 .cfa: sp 0 + .ra: x30
STACK CFI 6194 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 619c x19: .cfa -16 + ^
STACK CFI 61d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 61e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 61f0 340 .cfa: sp 0 + .ra: x30
STACK CFI 61f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 62e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 62f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6428 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6434 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6484 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 648c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6504 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 650c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6530 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 6538 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6540 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 654c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6560 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6564 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 675c x19: x19 x20: x20
STACK CFI 6764 x23: x23 x24: x24
STACK CFI 6778 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6780 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6784 x19: x19 x20: x20
STACK CFI 6790 x23: x23 x24: x24
STACK CFI 679c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 67a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 67ac x19: x19 x20: x20
STACK CFI 67b8 x23: x23 x24: x24
STACK CFI 67c4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 67cc .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 67d8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 67dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT 67e0 68 .cfa: sp 0 + .ra: x30
STACK CFI 67e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 67f0 x19: .cfa -16 + ^
STACK CFI 6840 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6850 38 .cfa: sp 0 + .ra: x30
STACK CFI 6858 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 686c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6874 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6880 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6890 2c8c .cfa: sp 0 + .ra: x30
STACK CFI 6898 .cfa: sp 480 +
STACK CFI 68ac .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 68c4 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 68cc x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 68e4 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 7090 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7098 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 9520 360 .cfa: sp 0 + .ra: x30
STACK CFI 9528 .cfa: sp 112 +
STACK CFI 9534 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 953c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9550 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9558 v8: .cfa -16 + ^
STACK CFI 9704 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 970c .cfa: sp 112 + .ra: .cfa -56 + ^ v8: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9880 2ac .cfa: sp 0 + .ra: x30
STACK CFI 9888 .cfa: sp 128 +
STACK CFI 988c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9894 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 98a8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9a38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9a40 .cfa: sp 128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9b30 de8 .cfa: sp 0 + .ra: x30
STACK CFI 9b38 .cfa: sp 352 +
STACK CFI 9b4c .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 9b54 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 9b64 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 9b70 x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 9b78 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 9f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9f48 .cfa: sp 352 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT a920 96c .cfa: sp 0 + .ra: x30
STACK CFI a928 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a930 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a940 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI aac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI aac8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI ad0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ad30 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b290 70 .cfa: sp 0 + .ra: x30
STACK CFI b298 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b2c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b2cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b2dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b2e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b2f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b300 54 .cfa: sp 0 + .ra: x30
STACK CFI b308 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b310 x19: .cfa -16 + ^
STACK CFI b34c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT b354 12dc .cfa: sp 0 + .ra: x30
STACK CFI b35c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI b364 .cfa: x29 112 +
STACK CFI b384 v8: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI bc04 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI bc0c .cfa: x29 112 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT c630 1354 .cfa: sp 0 + .ra: x30
STACK CFI c638 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI c640 .cfa: x29 112 +
STACK CFI c644 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI c660 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI cdd4 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI cddc .cfa: x29 112 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT d984 170 .cfa: sp 0 + .ra: x30
STACK CFI d98c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI d994 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d99c x21: .cfa -16 + ^
STACK CFI daec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT daf4 60 .cfa: sp 0 + .ra: x30
STACK CFI dafc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI db08 x19: .cfa -16 + ^
STACK CFI db4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT db54 3cc .cfa: sp 0 + .ra: x30
STACK CFI db5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI db6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI db74 .cfa: sp 688 +
STACK CFI dbb0 .cfa: sp 32 +
STACK CFI dbb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dbc0 .cfa: sp 688 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI dc5c .cfa: sp 32 +
STACK CFI dc68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dc70 .cfa: sp 688 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT df20 148 .cfa: sp 0 + .ra: x30
STACK CFI df28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI df30 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI df78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI df80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI df88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI df90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI e000 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e020 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT e070 608 .cfa: sp 0 + .ra: x30
STACK CFI e078 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI e084 .cfa: sp 1200 + x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI e0ec x21: .cfa -80 + ^
STACK CFI e0f4 x22: .cfa -72 + ^
STACK CFI e0fc x23: .cfa -64 + ^
STACK CFI e104 x27: .cfa -32 + ^
STACK CFI e10c x28: .cfa -24 + ^
STACK CFI e114 v8: .cfa -16 + ^
STACK CFI e120 x19: .cfa -96 + ^
STACK CFI e128 x20: .cfa -88 + ^
STACK CFI e12c x24: .cfa -56 + ^
STACK CFI e450 x19: x19
STACK CFI e454 x20: x20
STACK CFI e458 x21: x21
STACK CFI e45c x22: x22
STACK CFI e460 x23: x23
STACK CFI e464 x24: x24
STACK CFI e468 x27: x27
STACK CFI e46c x28: x28
STACK CFI e470 v8: v8
STACK CFI e490 .cfa: sp 112 +
STACK CFI e498 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI e4a0 .cfa: sp 1200 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI e650 v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI e654 x19: .cfa -96 + ^
STACK CFI e658 x20: .cfa -88 + ^
STACK CFI e65c x21: .cfa -80 + ^
STACK CFI e660 x22: .cfa -72 + ^
STACK CFI e664 x23: .cfa -64 + ^
STACK CFI e668 x24: .cfa -56 + ^
STACK CFI e66c x27: .cfa -32 + ^
STACK CFI e670 x28: .cfa -24 + ^
STACK CFI e674 v8: .cfa -16 + ^
STACK CFI INIT e680 4b8 .cfa: sp 0 + .ra: x30
STACK CFI e688 .cfa: sp 448 +
STACK CFI e698 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e6ac x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e6b4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e6c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI e838 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT eb40 25c .cfa: sp 0 + .ra: x30
STACK CFI eb48 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI eb5c .cfa: sp 1440 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI eca8 .cfa: sp 96 +
STACK CFI ecb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI ecc0 .cfa: sp 1440 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI ed0c x26: .cfa -24 + ^
STACK CFI ed14 x27: .cfa -16 + ^
STACK CFI ed1c x25: .cfa -32 + ^
STACK CFI ed64 x25: x25
STACK CFI ed68 x26: x26
STACK CFI ed6c x27: x27
STACK CFI ed74 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI ed8c x25: x25 x26: x26 x27: x27
STACK CFI ed90 x25: .cfa -32 + ^
STACK CFI ed94 x26: .cfa -24 + ^
STACK CFI ed98 x27: .cfa -16 + ^
STACK CFI INIT eda0 3e4 .cfa: sp 0 + .ra: x30
STACK CFI eda8 .cfa: sp 288 +
STACK CFI edb4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI edbc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ee00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ee08 .cfa: sp 288 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI ee0c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ee6c x21: x21 x22: x22
STACK CFI ee70 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI eec0 x21: x21 x22: x22
STACK CFI eec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI eecc .cfa: sp 288 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI eefc x21: x21 x22: x22
STACK CFI ef00 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI f09c x21: x21 x22: x22
STACK CFI f0a0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI f0d0 x21: x21 x22: x22
STACK CFI f0d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f0dc .cfa: sp 288 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI f178 x21: x21 x22: x22
STACK CFI f180 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT f184 1a0 .cfa: sp 0 + .ra: x30
STACK CFI f18c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI f198 .cfa: sp 1184 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI f1f0 x21: .cfa -32 + ^
STACK CFI f1f8 x22: .cfa -24 + ^
STACK CFI f200 x23: .cfa -16 + ^
STACK CFI f2b4 x21: x21
STACK CFI f2b8 x22: x22
STACK CFI f2bc x23: x23
STACK CFI f2dc .cfa: sp 64 +
STACK CFI f2e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f2ec .cfa: sp 1184 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI f304 x21: x21 x22: x22 x23: x23
STACK CFI f318 x21: .cfa -32 + ^
STACK CFI f31c x22: .cfa -24 + ^
STACK CFI f320 x23: .cfa -16 + ^
STACK CFI INIT f324 104 .cfa: sp 0 + .ra: x30
STACK CFI f32c .cfa: sp 96 +
STACK CFI f330 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI f338 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI f344 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI f380 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f388 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI f420 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT f430 118 .cfa: sp 0 + .ra: x30
STACK CFI f438 .cfa: sp 48 +
STACK CFI f444 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f44c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI f4fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f504 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI f534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f544 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT f550 a4 .cfa: sp 0 + .ra: x30
STACK CFI f558 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f560 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI f5e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f5ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT f5f4 50 .cfa: sp 0 + .ra: x30
STACK CFI f5fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f604 x19: .cfa -16 + ^
STACK CFI f63c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f644 98 .cfa: sp 0 + .ra: x30
STACK CFI f64c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f654 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f670 x21: .cfa -16 + ^
STACK CFI f698 x21: x21
STACK CFI f6a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f6a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI f6b4 x21: x21
STACK CFI f6c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f6c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT f6e0 98 .cfa: sp 0 + .ra: x30
STACK CFI f6e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f6f4 x19: .cfa -16 + ^
STACK CFI f728 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f730 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI f74c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI f754 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI f770 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f780 6fc .cfa: sp 0 + .ra: x30
STACK CFI f788 .cfa: sp 400 +
STACK CFI f798 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI f7a4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI f7ac x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI f7b8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI f824 x25: .cfa -16 + ^
STACK CFI f954 x25: x25
STACK CFI f98c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI f994 .cfa: sp 400 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI f9b0 x25: .cfa -16 + ^
STACK CFI f9c8 x25: x25
STACK CFI fa0c x25: .cfa -16 + ^
STACK CFI fa9c x25: x25
STACK CFI faa4 x25: .cfa -16 + ^
STACK CFI fb74 x25: x25
STACK CFI fb78 x25: .cfa -16 + ^
STACK CFI fbf0 x25: x25
STACK CFI fc34 x25: .cfa -16 + ^
STACK CFI fe74 x25: x25
STACK CFI fe78 x25: .cfa -16 + ^
STACK CFI INIT fe80 132c .cfa: sp 0 + .ra: x30
STACK CFI fe88 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI fe98 .cfa: sp 1200 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ff04 x25: .cfa -32 + ^
STACK CFI ff10 x26: .cfa -24 + ^
STACK CFI ff2c x25: x25
STACK CFI ff30 x26: x26
STACK CFI ff50 .cfa: sp 96 +
STACK CFI ff5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ff64 .cfa: sp 1200 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI ff68 x25: .cfa -32 + ^
STACK CFI ff74 x26: .cfa -24 + ^
STACK CFI ffd0 x25: x25 x26: x26
STACK CFI ffd4 x25: .cfa -32 + ^
STACK CFI ffe0 x26: .cfa -24 + ^
STACK CFI 1003c x25: x25
STACK CFI 10044 x26: x26
STACK CFI 10048 .cfa: sp 96 +
STACK CFI 10054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1005c .cfa: sp 1200 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 1008c x25: x25 x26: x26
STACK CFI 100b0 x25: .cfa -32 + ^
STACK CFI 100bc x23: .cfa -48 + ^
STACK CFI 100c0 x24: .cfa -40 + ^
STACK CFI 100c4 x26: .cfa -24 + ^
STACK CFI 100c8 x27: .cfa -16 + ^
STACK CFI 100cc x28: .cfa -8 + ^
STACK CFI 1042c x23: x23
STACK CFI 10430 x24: x24
STACK CFI 10434 x25: x25
STACK CFI 10438 x26: x26
STACK CFI 1043c x27: x27
STACK CFI 10440 x28: x28
STACK CFI 10444 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1045c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10828 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 1086c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 110e8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 110ec x23: .cfa -48 + ^
STACK CFI 110f0 x24: .cfa -40 + ^
STACK CFI 110f4 x25: .cfa -32 + ^
STACK CFI 110f8 x26: .cfa -24 + ^
STACK CFI 110fc x27: .cfa -16 + ^
STACK CFI 11100 x28: .cfa -8 + ^
STACK CFI 11198 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 1119c x23: .cfa -48 + ^
STACK CFI 111a0 x24: .cfa -40 + ^
STACK CFI 111a4 x27: .cfa -16 + ^
STACK CFI 111a8 x28: .cfa -8 + ^
STACK CFI INIT 111b0 28 .cfa: sp 0 + .ra: x30
STACK CFI 111b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 111c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 111cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 111d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 111e0 918 .cfa: sp 0 + .ra: x30
STACK CFI 111e8 .cfa: sp 144 +
STACK CFI 111f4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 111fc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 11204 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 11218 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 11320 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1142c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 116ec x25: x25 x26: x26
STACK CFI 116f0 x27: x27 x28: x28
STACK CFI 117c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 117d0 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 117e8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 117f4 x25: x25 x26: x26
STACK CFI 1180c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11814 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1181c x25: x25 x26: x26
STACK CFI 11820 x27: x27 x28: x28
STACK CFI 11824 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 118c8 x25: x25 x26: x26
STACK CFI 118cc x27: x27 x28: x28
STACK CFI 11908 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11910 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11914 x27: x27 x28: x28
STACK CFI 11930 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 119d0 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 11a00 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11a20 x25: x25 x26: x26
STACK CFI 11a28 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11adc x25: x25 x26: x26
STACK CFI 11af0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11af4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
