MODULE Linux arm64 D9C27BDCF1A79BA8D45C82ECF14B3C580 libraw1394.so.11
INFO CODE_ID DC7BC2D9A7F1A89BD45C82ECF14B3C585C7148BA
PUBLIC 36f0 0 _raw1394_sync_cb
PUBLIC 47f0 0 ieee1394_new_handle
PUBLIC 4934 0 ieee1394_get_fd
PUBLIC 4950 0 raw1394_get_generation
PUBLIC 49a0 0 raw1394_update_generation
PUBLIC 4a00 0 ieee1394_get_nodecount
PUBLIC 4a40 0 ieee1394_get_local_id
PUBLIC 4a80 0 ieee1394_get_irm_id
PUBLIC 4ac0 0 raw1394_set_userdata
PUBLIC 4b00 0 raw1394_get_userdata
PUBLIC 4b50 0 ieee1394_get_port_info
PUBLIC 4c30 0 ieee1394_set_port
PUBLIC 4d70 0 ieee1394_reset_bus_new
PUBLIC 4e10 0 ieee1394_busreset_notify
PUBLIC 4eb0 0 ieee1394_update_config_rom
PUBLIC 4f60 0 ieee1394_get_config_rom
PUBLIC 5000 0 raw1394_set_bus_reset_handler
PUBLIC 5060 0 raw1394_set_tag_handler
PUBLIC 50c0 0 raw1394_set_arm_tag_handler
PUBLIC 5120 0 raw1394_set_fcp_handler
PUBLIC 5180 0 raw1394_get_errcode
PUBLIC 51c4 0 ieee1394_errcode_to_errno
PUBLIC 5270 0 ieee1394_start_read
PUBLIC 5310 0 ieee1394_start_write
PUBLIC 53b0 0 ieee1394_start_lock
PUBLIC 5490 0 ieee1394_start_lock64
PUBLIC 5570 0 ieee1394_start_async_stream
PUBLIC 5614 0 ieee1394_start_async_send
PUBLIC 56c0 0 ieee1394_start_phy_packet_write
PUBLIC 5760 0 ieee1394_echo_request
PUBLIC 57f0 0 ieee1394_wake_up
PUBLIC 5810 0 ieee1394_iso_xmit_init
PUBLIC 5880 0 ieee1394_iso_recv_init
PUBLIC 58e0 0 ieee1394_iso_multichannel_recv_init
PUBLIC 5944 0 ieee1394_iso_recv_listen_channel
PUBLIC 5990 0 ieee1394_iso_recv_unlisten_channel
PUBLIC 59e0 0 ieee1394_iso_recv_flush
PUBLIC 5a30 0 ieee1394_iso_recv_set_channel_mask
PUBLIC 5a84 0 ieee1394_iso_recv_start
PUBLIC 5b34 0 ieee1394_iso_xmit_start
PUBLIC 5be0 0 ieee1394_iso_xmit_sync
PUBLIC 5c30 0 ieee1394_iso_stop
PUBLIC 5c70 0 ieee1394_iso_shutdown
PUBLIC 5cf4 0 ieee1394_destroy_handle
PUBLIC 5d60 0 ieee1394_new_handle_on_port
PUBLIC 5de0 0 ieee1394_read_cycle_timer
PUBLIC 5e70 0 _ieee1394_iso_iterate
PUBLIC 6294 0 ieee1394_loop_iterate
PUBLIC 6444 0 ieee1394_iso_xmit_write
PUBLIC 6720 0 ieee1394_start_fcp_listen
PUBLIC 6740 0 ieee1394_stop_fcp_listen
PUBLIC 6760 0 ieee1394_arm_register
PUBLIC 6820 0 ieee1394_arm_unregister
PUBLIC 68c0 0 ieee1394_arm_set_buf
PUBLIC 6960 0 ieee1394_arm_get_buf
PUBLIC 69f4 0 raw1394_get_libversion
PUBLIC 6a14 0 raw1394_errcode_to_errno
PUBLIC 6a30 0 fw_errcode_to_errno
PUBLIC 6e10 0 fw_loop_iterate
PUBLIC 6f00 0 raw1394_loop_iterate
PUBLIC 6f40 0 ieee1394_read
PUBLIC 7010 0 ieee1394_write
PUBLIC 70e0 0 ieee1394_lock
PUBLIC 71b0 0 ieee1394_lock64
PUBLIC 7280 0 ieee1394_async_stream
PUBLIC 7350 0 ieee1394_async_send
PUBLIC 7420 0 ieee1394_phy_packet_write
PUBLIC 7784 0 fw_new_handle
PUBLIC 7b54 0 raw1394_new_handle
PUBLIC 7c40 0 fw_busreset_notify
PUBLIC 7c64 0 raw1394_busreset_notify
PUBLIC 7cb0 0 fw_get_fd
PUBLIC 7cd0 0 raw1394_get_fd
PUBLIC 7d14 0 fw_get_local_id
PUBLIC 7d30 0 raw1394_get_local_id
PUBLIC 7d74 0 fw_get_irm_id
PUBLIC 7d90 0 raw1394_get_irm_id
PUBLIC 7dd4 0 fw_get_nodecount
PUBLIC 7e00 0 raw1394_get_nodecount
PUBLIC 7e44 0 fw_get_speed
PUBLIC 7f10 0 raw1394_get_speed
PUBLIC 7f70 0 fw_get_port_info
PUBLIC 7ff4 0 raw1394_get_port_info
PUBLIC 8040 0 fw_set_port
PUBLIC 8330 0 raw1394_set_port
PUBLIC 8374 0 fw_reset_bus_new
PUBLIC 8400 0 raw1394_reset_bus_new
PUBLIC 8444 0 raw1394_reset_bus
PUBLIC 8460 0 fw_arm_register
PUBLIC 85c0 0 raw1394_arm_register
PUBLIC 8610 0 fw_arm_unregister
PUBLIC 86e4 0 raw1394_arm_unregister
PUBLIC 8730 0 fw_arm_set_buf
PUBLIC 87b0 0 raw1394_arm_set_buf
PUBLIC 87f4 0 fw_arm_get_buf
PUBLIC 8870 0 raw1394_arm_get_buf
PUBLIC 88b4 0 fw_echo_request
PUBLIC 88e0 0 raw1394_echo_request
PUBLIC 8924 0 fw_wake_up
PUBLIC 8940 0 raw1394_wake_up
PUBLIC 8984 0 fw_start_read
PUBLIC 89d0 0 raw1394_start_read
PUBLIC 8a20 0 fw_start_write
PUBLIC 8a70 0 raw1394_start_write
PUBLIC 8ac0 0 fw_start_lock
PUBLIC 8b90 0 raw1394_start_lock
PUBLIC 8be0 0 fw_start_lock64
PUBLIC 8cb0 0 raw1394_start_lock64
PUBLIC 8d00 0 fw_start_async_stream
PUBLIC 8d60 0 raw1394_start_async_stream
PUBLIC 8da4 0 fw_start_phy_packet_write
PUBLIC 8ec0 0 raw1394_start_phy_packet_write
PUBLIC 8f04 0 fw_start_async_send
PUBLIC 8f30 0 raw1394_start_async_send
PUBLIC 8f74 0 fw_read
PUBLIC 9010 0 raw1394_read
PUBLIC 9054 0 fw_write
PUBLIC 9090 0 raw1394_write
PUBLIC 90d4 0 fw_lock
PUBLIC 91a0 0 raw1394_lock
PUBLIC 91e4 0 ieee1394_bandwidth_modify
PUBLIC 9360 0 ieee1394_channel_modify
PUBLIC 94d0 0 fw_lock64
PUBLIC 95a0 0 raw1394_lock64
PUBLIC 95e4 0 fw_async_stream
PUBLIC 9630 0 raw1394_async_stream
PUBLIC 9670 0 fw_phy_packet_write
PUBLIC 9740 0 raw1394_phy_packet_write
PUBLIC 9780 0 fw_async_send
PUBLIC 97b0 0 raw1394_async_send
PUBLIC 97f4 0 fw_start_fcp_listen
PUBLIC 98c4 0 raw1394_start_fcp_listen
PUBLIC 9910 0 fw_stop_fcp_listen
PUBLIC 9990 0 raw1394_stop_fcp_listen
PUBLIC 99d4 0 fw_update_config_rom
PUBLIC 9a00 0 raw1394_update_config_rom
PUBLIC 9a50 0 fw_add_config_rom_descriptor
PUBLIC 9af0 0 raw1394_add_config_rom_descriptor
PUBLIC 9b44 0 fw_remove_config_rom_descriptor
PUBLIC 9bc0 0 raw1394_remove_config_rom_descriptor
PUBLIC 9c14 0 fw_get_config_rom
PUBLIC 9ce4 0 raw1394_get_config_rom
PUBLIC 9d30 0 fw_bandwidth_modify
PUBLIC 9d70 0 raw1394_bandwidth_modify
PUBLIC 9db0 0 fw_channel_modify
PUBLIC 9df0 0 raw1394_channel_modify
PUBLIC 9e30 0 fw_iso_xmit_sync
PUBLIC 9f10 0 raw1394_iso_xmit_sync
PUBLIC 9f54 0 fw_iso_recv_flush
PUBLIC 9fd0 0 raw1394_iso_recv_flush
PUBLIC a014 0 fw_iso_xmit_init
PUBLIC a060 0 raw1394_iso_xmit_init
PUBLIC a0b0 0 fw_iso_recv_init
PUBLIC a0f4 0 raw1394_iso_recv_init
PUBLIC a140 0 fw_iso_multichannel_recv_init
PUBLIC a170 0 raw1394_iso_multichannel_recv_init
PUBLIC a1b4 0 fw_iso_recv_listen_channel
PUBLIC a1e0 0 raw1394_iso_recv_listen_channel
PUBLIC a230 0 fw_iso_recv_unlisten_channel
PUBLIC a260 0 raw1394_iso_recv_unlisten_channel
PUBLIC a2b0 0 fw_iso_recv_set_channel_mask
PUBLIC a2e0 0 raw1394_iso_recv_set_channel_mask
PUBLIC a324 0 fw_iso_stop
PUBLIC a3c0 0 raw1394_iso_stop
PUBLIC a7d0 0 fw_iso_shutdown
PUBLIC a844 0 raw1394_iso_shutdown
PUBLIC a884 0 fw_destroy_handle
PUBLIC a910 0 raw1394_destroy_handle
PUBLIC a970 0 fw_new_handle_on_port
PUBLIC a9c0 0 raw1394_new_handle_on_port
PUBLIC aa70 0 fw_read_cycle_timer
PUBLIC ab00 0 raw1394_read_cycle_timer
PUBLIC ab44 0 fw_iso_xmit_write
PUBLIC ace4 0 raw1394_iso_xmit_write
PUBLIC ad30 0 fw_iso_xmit_start
PUBLIC aea0 0 raw1394_iso_xmit_start
PUBLIC aee4 0 fw_iso_recv_start
PUBLIC b020 0 raw1394_iso_recv_start
PUBLIC b064 0 fw_read_cycle_timer_and_clock
PUBLIC b124 0 raw1394_read_cycle_timer_and_clock
STACK CFI INIT 35a0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 35d0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3610 48 .cfa: sp 0 + .ra: x30
STACK CFI 3614 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 361c x19: .cfa -16 + ^
STACK CFI 3654 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3660 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3670 34 .cfa: sp 0 + .ra: x30
STACK CFI 3678 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3688 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3694 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3698 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 36a4 44 .cfa: sp 0 + .ra: x30
STACK CFI 36ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 36bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 36d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 36dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 36f0 24 .cfa: sp 0 + .ra: x30
STACK CFI 36f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 370c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3714 34 .cfa: sp 0 + .ra: x30
STACK CFI 371c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 372c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3738 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 373c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3750 44 .cfa: sp 0 + .ra: x30
STACK CFI 3758 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3768 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3784 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3788 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3794 2c .cfa: sp 0 + .ra: x30
STACK CFI 37a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 37b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 37c0 24 .cfa: sp 0 + .ra: x30
STACK CFI 37c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 37dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 37e4 78 .cfa: sp 0 + .ra: x30
STACK CFI 37ec .cfa: sp 32 +
STACK CFI 37fc .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3848 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3850 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3860 12c .cfa: sp 0 + .ra: x30
STACK CFI 3868 .cfa: sp 96 +
STACK CFI 3878 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3888 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 392c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3934 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3990 118 .cfa: sp 0 + .ra: x30
STACK CFI 3998 .cfa: sp 96 +
STACK CFI 39a8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 39b8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 39c8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 39d0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3a60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3a68 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3ab0 110 .cfa: sp 0 + .ra: x30
STACK CFI 3ab8 .cfa: sp 48 +
STACK CFI 3ad0 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3bb4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3bbc .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3bc0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 3bc8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3bd0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3be0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3bec x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3bf8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3cc4 x21: x21 x22: x22
STACK CFI 3cc8 x23: x23 x24: x24
STACK CFI 3ccc x25: x25 x26: x26
STACK CFI 3cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ce0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 3ce8 x21: x21 x22: x22
STACK CFI 3cf0 x23: x23 x24: x24
STACK CFI 3cf4 x25: x25 x26: x26
STACK CFI 3d00 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3d04 x21: x21 x22: x22
STACK CFI 3d0c x23: x23 x24: x24
STACK CFI 3d10 x25: x25 x26: x26
STACK CFI 3d14 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3d30 x21: x21 x22: x22
STACK CFI 3d34 x23: x23 x24: x24
STACK CFI 3d38 x25: x25 x26: x26
STACK CFI 3d3c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 3d64 304 .cfa: sp 0 + .ra: x30
STACK CFI 3d6c .cfa: sp 160 +
STACK CFI 3d70 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3d78 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3d8c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3da0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3da8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3f9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3fa4 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4070 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 4078 .cfa: sp 80 +
STACK CFI 407c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4084 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 409c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 416c .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 41c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 41c8 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4268 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 42a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 42b0 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 42d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 42dc .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 434c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4354 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4370 21c .cfa: sp 0 + .ra: x30
STACK CFI 4378 .cfa: sp 128 +
STACK CFI 437c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4384 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 43ac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 43c0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 44c0 x23: x23 x24: x24
STACK CFI 4508 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4510 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4530 x23: x23 x24: x24
STACK CFI 454c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4564 x23: x23 x24: x24
STACK CFI 456c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4578 x23: x23 x24: x24
STACK CFI 4588 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 4590 260 .cfa: sp 0 + .ra: x30
STACK CFI 4598 .cfa: sp 272 +
STACK CFI 45a8 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 45b0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 45c4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 45e8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 45f8 v8: .cfa -16 + ^
STACK CFI 4604 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4608 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 46b0 x23: x23 x24: x24
STACK CFI 46b4 x25: x25 x26: x26
STACK CFI 46b8 x27: x27 x28: x28
STACK CFI 46bc v8: v8
STACK CFI 46ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 46f4 .cfa: sp 272 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 47c8 v8: v8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 47d0 v8: .cfa -16 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 47dc v8: v8 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 47e0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 47e4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 47e8 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 47ec v8: .cfa -16 + ^
STACK CFI INIT 47f0 144 .cfa: sp 0 + .ra: x30
STACK CFI 47f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4804 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 488c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4894 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4934 1c .cfa: sp 0 + .ra: x30
STACK CFI 493c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4944 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4950 50 .cfa: sp 0 + .ra: x30
STACK CFI 497c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4998 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 49a0 3c .cfa: sp 0 + .ra: x30
STACK CFI 49a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 49c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 49cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 49d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 49e0 20 .cfa: sp 0 + .ra: x30
STACK CFI 49e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 49f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a00 3c .cfa: sp 0 + .ra: x30
STACK CFI 4a18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a40 3c .cfa: sp 0 + .ra: x30
STACK CFI 4a58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a80 3c .cfa: sp 0 + .ra: x30
STACK CFI 4a98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4ab4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4ac0 3c .cfa: sp 0 + .ra: x30
STACK CFI 4ac8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4ae4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4aec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4af4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4b00 50 .cfa: sp 0 + .ra: x30
STACK CFI 4b2c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4b48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4b50 e0 .cfa: sp 0 + .ra: x30
STACK CFI 4b58 .cfa: sp 96 +
STACK CFI 4b6c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b78 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4c1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4c24 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4c30 140 .cfa: sp 0 + .ra: x30
STACK CFI 4c38 .cfa: sp 96 +
STACK CFI 4c48 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c58 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4cfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4d04 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4d70 98 .cfa: sp 0 + .ra: x30
STACK CFI 4d78 .cfa: sp 80 +
STACK CFI 4d8c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4dfc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4e04 .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4e10 98 .cfa: sp 0 + .ra: x30
STACK CFI 4e18 .cfa: sp 80 +
STACK CFI 4e2c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4e9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4ea4 .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4eb0 ac .cfa: sp 0 + .ra: x30
STACK CFI 4eb8 .cfa: sp 96 +
STACK CFI 4ec8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4f48 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4f50 .cfa: sp 96 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4f60 9c .cfa: sp 0 + .ra: x30
STACK CFI 4f68 .cfa: sp 96 +
STACK CFI 4f7c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4fe8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4ff0 .cfa: sp 96 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5000 58 .cfa: sp 0 + .ra: x30
STACK CFI 5034 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5050 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5060 58 .cfa: sp 0 + .ra: x30
STACK CFI 5094 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 50b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 50c0 58 .cfa: sp 0 + .ra: x30
STACK CFI 50f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5110 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5120 58 .cfa: sp 0 + .ra: x30
STACK CFI 5154 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5170 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5180 44 .cfa: sp 0 + .ra: x30
STACK CFI 5188 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 51a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 51ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 51b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 51bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 51c4 ac .cfa: sp 0 + .ra: x30
STACK CFI 51cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 51fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5204 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5214 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5220 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5240 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5250 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5264 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5270 98 .cfa: sp 0 + .ra: x30
STACK CFI 5278 .cfa: sp 80 +
STACK CFI 5294 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 52fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5304 .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5310 98 .cfa: sp 0 + .ra: x30
STACK CFI 5318 .cfa: sp 80 +
STACK CFI 532c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 539c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 53a4 .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 53b0 e0 .cfa: sp 0 + .ra: x30
STACK CFI 53b8 .cfa: sp 96 +
STACK CFI 53c8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 546c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5474 .cfa: sp 96 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5490 e0 .cfa: sp 0 + .ra: x30
STACK CFI 5498 .cfa: sp 96 +
STACK CFI 54a8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 554c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5554 .cfa: sp 96 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5570 a4 .cfa: sp 0 + .ra: x30
STACK CFI 5578 .cfa: sp 80 +
STACK CFI 5594 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5608 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5610 .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5614 a4 .cfa: sp 0 + .ra: x30
STACK CFI 561c .cfa: sp 80 +
STACK CFI 5630 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 56ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 56b4 .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 56c0 98 .cfa: sp 0 + .ra: x30
STACK CFI 56c8 .cfa: sp 80 +
STACK CFI 56dc .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 574c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5754 .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5760 90 .cfa: sp 0 + .ra: x30
STACK CFI 5768 .cfa: sp 80 +
STACK CFI 577c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 57e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 57ec .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 57f0 1c .cfa: sp 0 + .ra: x30
STACK CFI 57f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5804 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5810 68 .cfa: sp 0 + .ra: x30
STACK CFI 5818 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 582c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5868 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5870 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5880 60 .cfa: sp 0 + .ra: x30
STACK CFI 5888 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 589c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 58d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 58d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 58e0 64 .cfa: sp 0 + .ra: x30
STACK CFI 58e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5904 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 593c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5944 4c .cfa: sp 0 + .ra: x30
STACK CFI 5970 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5988 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5990 4c .cfa: sp 0 + .ra: x30
STACK CFI 59bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 59d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 59e0 48 .cfa: sp 0 + .ra: x30
STACK CFI 5a08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5a20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5a30 54 .cfa: sp 0 + .ra: x30
STACK CFI 5a38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5a6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5a84 b0 .cfa: sp 0 + .ra: x30
STACK CFI 5a8c .cfa: sp 64 +
STACK CFI 5a94 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a9c x19: .cfa -16 + ^
STACK CFI 5b14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5b1c .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5b34 a8 .cfa: sp 0 + .ra: x30
STACK CFI 5b3c .cfa: sp 48 +
STACK CFI 5b44 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5b4c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5bbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5bc4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5be0 48 .cfa: sp 0 + .ra: x30
STACK CFI 5c08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5c20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5c30 40 .cfa: sp 0 + .ra: x30
STACK CFI 5c38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c40 x19: .cfa -16 + ^
STACK CFI 5c68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5c70 84 .cfa: sp 0 + .ra: x30
STACK CFI 5c78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c80 x19: .cfa -16 + ^
STACK CFI 5cd0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5cd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5cf4 64 .cfa: sp 0 + .ra: x30
STACK CFI 5d04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5d10 x19: .cfa -16 + ^
STACK CFI 5d2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5d34 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5d4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5d60 7c .cfa: sp 0 + .ra: x30
STACK CFI 5d68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5d70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5de0 90 .cfa: sp 0 + .ra: x30
STACK CFI 5de8 .cfa: sp 64 +
STACK CFI 5df4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e00 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5e6c .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5e70 424 .cfa: sp 0 + .ra: x30
STACK CFI 5e78 .cfa: sp 112 +
STACK CFI 5e7c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5e84 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5ea8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5ee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5ee8 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 5f44 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6028 x23: x23 x24: x24
STACK CFI 6038 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 604c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6180 x23: x23 x24: x24
STACK CFI 6184 x25: x25 x26: x26
STACK CFI 6188 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6190 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 61d0 x25: x25 x26: x26
STACK CFI 61d8 x23: x23 x24: x24
STACK CFI 61dc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6204 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6220 x23: x23 x24: x24
STACK CFI 6224 x25: x25 x26: x26
STACK CFI 6228 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6264 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6268 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 626c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6270 x25: x25 x26: x26
STACK CFI INIT 6294 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 629c .cfa: sp 112 +
STACK CFI 62a0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 62a8 x21: .cfa -16 + ^
STACK CFI 62b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6390 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6398 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6444 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 644c .cfa: sp 112 +
STACK CFI 6450 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6458 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6468 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6490 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6554 x23: x23 x24: x24
STACK CFI 6594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 659c .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 65a4 x23: x23 x24: x24
STACK CFI 65b4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 65b8 x23: x23 x24: x24
STACK CFI 65c0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 65c4 x23: x23 x24: x24
STACK CFI 65c8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 65cc x23: x23 x24: x24
STACK CFI 65e4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 65f0 130 .cfa: sp 0 + .ra: x30
STACK CFI 65f8 .cfa: sp 128 +
STACK CFI 660c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6620 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 670c .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6720 1c .cfa: sp 0 + .ra: x30
STACK CFI 6728 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6734 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6740 1c .cfa: sp 0 + .ra: x30
STACK CFI 6748 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6754 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6760 b8 .cfa: sp 0 + .ra: x30
STACK CFI 6768 .cfa: sp 80 +
STACK CFI 6780 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 680c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6814 .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6820 a0 .cfa: sp 0 + .ra: x30
STACK CFI 6828 .cfa: sp 80 +
STACK CFI 683c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 68b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 68bc .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 68c0 98 .cfa: sp 0 + .ra: x30
STACK CFI 68c8 .cfa: sp 80 +
STACK CFI 68dc .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 694c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6954 .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6960 94 .cfa: sp 0 + .ra: x30
STACK CFI 6968 .cfa: sp 80 +
STACK CFI 6984 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 69e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 69f0 .cfa: sp 80 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 69f4 20 .cfa: sp 0 + .ra: x30
STACK CFI 69fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6a0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6a14 18 .cfa: sp 0 + .ra: x30
STACK CFI 6a1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6a24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6a30 a4 .cfa: sp 0 + .ra: x30
STACK CFI 6a38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6a74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6a88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6a9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6aa4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6aa8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6ab4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6ac8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6ad4 1ac .cfa: sp 0 + .ra: x30
STACK CFI 6adc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6ae8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6af0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6af8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6b00 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6b0c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6c14 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6c80 190 .cfa: sp 0 + .ra: x30
STACK CFI 6c88 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6c94 .cfa: sp 1136 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6cf8 x22: .cfa -24 + ^
STACK CFI 6d10 x21: .cfa -32 + ^
STACK CFI 6d18 x23: .cfa -16 + ^
STACK CFI 6d20 x24: .cfa -8 + ^
STACK CFI 6d94 x21: x21
STACK CFI 6d98 x22: x22
STACK CFI 6d9c x23: x23
STACK CFI 6da0 x24: x24
STACK CFI 6dc0 .cfa: sp 64 +
STACK CFI 6dc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6dd0 .cfa: sp 1136 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 6e00 x21: .cfa -32 + ^
STACK CFI 6e04 x22: .cfa -24 + ^
STACK CFI 6e08 x23: .cfa -16 + ^
STACK CFI 6e0c x24: .cfa -8 + ^
STACK CFI INIT 6e10 e8 .cfa: sp 0 + .ra: x30
STACK CFI 6e18 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6e24 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6e34 .cfa: sp 592 + x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 6ed0 .cfa: sp 64 +
STACK CFI 6ee4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6eec .cfa: sp 592 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6f00 40 .cfa: sp 0 + .ra: x30
STACK CFI 6f20 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6f38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6f40 c8 .cfa: sp 0 + .ra: x30
STACK CFI 6f48 .cfa: sp 64 +
STACK CFI 6f58 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f64 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6ffc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7004 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7010 c8 .cfa: sp 0 + .ra: x30
STACK CFI 7018 .cfa: sp 64 +
STACK CFI 7028 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7034 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 70cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 70d4 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 70e0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 70e8 .cfa: sp 64 +
STACK CFI 70f8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7104 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 719c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 71a4 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 71b0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 71b8 .cfa: sp 64 +
STACK CFI 71c8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 71d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 726c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7274 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7280 c8 .cfa: sp 0 + .ra: x30
STACK CFI 7288 .cfa: sp 64 +
STACK CFI 7298 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 72a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 733c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7344 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7350 c8 .cfa: sp 0 + .ra: x30
STACK CFI 7358 .cfa: sp 64 +
STACK CFI 7368 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7374 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 740c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7414 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7420 c8 .cfa: sp 0 + .ra: x30
STACK CFI 7428 .cfa: sp 64 +
STACK CFI 7438 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7444 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 74dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 74e4 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 74f0 15c .cfa: sp 0 + .ra: x30
STACK CFI 74f8 .cfa: sp 208 +
STACK CFI 7504 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 750c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 751c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7534 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7540 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7554 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7560 v8: .cfa -16 + ^
STACK CFI 7610 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7618 .cfa: sp 208 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 7650 134 .cfa: sp 0 + .ra: x30
STACK CFI 7658 .cfa: sp 112 +
STACK CFI 7664 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 766c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 76ac x21: .cfa -16 + ^
STACK CFI 7720 x21: x21
STACK CFI 7748 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7750 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 7758 x21: x21
STACK CFI 7780 x21: .cfa -16 + ^
STACK CFI INIT 7784 3d0 .cfa: sp 0 + .ra: x30
STACK CFI 778c .cfa: sp 240 +
STACK CFI 779c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 77b4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 77d4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 77dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 78b8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 78bc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7a58 x21: x21 x22: x22
STACK CFI 7a5c x25: x25 x26: x26
STACK CFI 7a60 x27: x27 x28: x28
STACK CFI 7a68 x23: x23 x24: x24
STACK CFI 7a6c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7a98 x21: x21 x22: x22
STACK CFI 7a9c x23: x23 x24: x24
STACK CFI 7acc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7ad4 .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 7b1c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7b30 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7b34 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7b38 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7b3c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7b44 x21: x21 x22: x22
STACK CFI 7b48 x23: x23 x24: x24
STACK CFI 7b4c x25: x25 x26: x26
STACK CFI 7b50 x27: x27 x28: x28
STACK CFI INIT 7b54 ec .cfa: sp 0 + .ra: x30
STACK CFI 7b5c .cfa: sp 96 +
STACK CFI 7b6c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7b80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7b90 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7bc0 x21: x21 x22: x22
STACK CFI 7bcc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7be4 x21: x21 x22: x22
STACK CFI 7c1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7c24 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7c30 x21: x21 x22: x22
STACK CFI 7c3c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 7c40 24 .cfa: sp 0 + .ra: x30
STACK CFI 7c4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7c5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7c64 44 .cfa: sp 0 + .ra: x30
STACK CFI 7c88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7ca0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7cb0 1c .cfa: sp 0 + .ra: x30
STACK CFI 7cb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7cc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7cd0 44 .cfa: sp 0 + .ra: x30
STACK CFI 7cf4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7d0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7d14 1c .cfa: sp 0 + .ra: x30
STACK CFI 7d1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7d24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7d30 44 .cfa: sp 0 + .ra: x30
STACK CFI 7d54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7d6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7d74 1c .cfa: sp 0 + .ra: x30
STACK CFI 7d7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7d84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7d90 44 .cfa: sp 0 + .ra: x30
STACK CFI 7db4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7dcc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7dd4 24 .cfa: sp 0 + .ra: x30
STACK CFI 7ddc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7de8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7e00 44 .cfa: sp 0 + .ra: x30
STACK CFI 7e24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7e3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7e44 cc .cfa: sp 0 + .ra: x30
STACK CFI 7e4c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ea4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7eb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ec4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7ed0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ed4 x19: .cfa -16 + ^
STACK CFI 7eec x19: x19
STACK CFI 7ef4 x19: .cfa -16 + ^
STACK CFI 7f0c x19: x19
STACK CFI INIT 7f10 58 .cfa: sp 0 + .ra: x30
STACK CFI 7f18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7f2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7f3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7f4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7f58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7f70 84 .cfa: sp 0 + .ra: x30
STACK CFI 7f78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7f80 x21: .cfa -16 + ^
STACK CFI 7fa4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7fe0 x19: x19 x20: x20
STACK CFI 7fec .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 7ff4 44 .cfa: sp 0 + .ra: x30
STACK CFI 8018 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8030 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8040 2f0 .cfa: sp 0 + .ra: x30
STACK CFI 8048 .cfa: sp 240 +
STACK CFI 804c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8054 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8064 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 808c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 80a4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 80ac x27: .cfa -16 + ^
STACK CFI 8244 x21: x21 x22: x22
STACK CFI 824c x23: x23 x24: x24
STACK CFI 8250 x27: x27
STACK CFI 827c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 8284 .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 82a4 x21: x21 x22: x22
STACK CFI 82ac x23: x23 x24: x24
STACK CFI 82b0 x27: x27
STACK CFI 82bc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^
STACK CFI 82f0 x21: x21 x22: x22
STACK CFI 82f8 x23: x23 x24: x24
STACK CFI 82fc x27: x27
STACK CFI 8314 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8318 x23: x23 x24: x24
STACK CFI 8324 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8328 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 832c x27: .cfa -16 + ^
STACK CFI INIT 8330 44 .cfa: sp 0 + .ra: x30
STACK CFI 8354 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 836c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8374 84 .cfa: sp 0 + .ra: x30
STACK CFI 837c .cfa: sp 32 +
STACK CFI 8388 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 83dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 83e4 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8400 44 .cfa: sp 0 + .ra: x30
STACK CFI 8424 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 843c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8444 1c .cfa: sp 0 + .ra: x30
STACK CFI 844c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8458 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8460 158 .cfa: sp 0 + .ra: x30
STACK CFI 8468 .cfa: sp 144 +
STACK CFI 8474 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 847c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8484 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8490 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 84a0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 84a8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8590 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 85c0 50 .cfa: sp 0 + .ra: x30
STACK CFI 85f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8608 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8610 d4 .cfa: sp 0 + .ra: x30
STACK CFI 8618 .cfa: sp 48 +
STACK CFI 8628 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8630 x19: .cfa -16 + ^
STACK CFI 86ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 86b4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 86e4 44 .cfa: sp 0 + .ra: x30
STACK CFI 8708 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8720 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8730 7c .cfa: sp 0 + .ra: x30
STACK CFI 8738 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 87a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 87b0 44 .cfa: sp 0 + .ra: x30
STACK CFI 87d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 87ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 87f4 78 .cfa: sp 0 + .ra: x30
STACK CFI 87fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8864 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8870 44 .cfa: sp 0 + .ra: x30
STACK CFI 8894 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 88ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 88b4 2c .cfa: sp 0 + .ra: x30
STACK CFI 88bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 88d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 88e0 44 .cfa: sp 0 + .ra: x30
STACK CFI 8904 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 891c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8924 1c .cfa: sp 0 + .ra: x30
STACK CFI 892c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8938 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8940 44 .cfa: sp 0 + .ra: x30
STACK CFI 8964 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 897c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8984 4c .cfa: sp 0 + .ra: x30
STACK CFI 898c .cfa: sp 32 +
STACK CFI 89a0 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 89c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 89d0 48 .cfa: sp 0 + .ra: x30
STACK CFI 89f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8a10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8a20 4c .cfa: sp 0 + .ra: x30
STACK CFI 8a28 .cfa: sp 32 +
STACK CFI 8a3c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8a64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8a70 48 .cfa: sp 0 + .ra: x30
STACK CFI 8a98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8ab0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8ac0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 8ac8 .cfa: sp 48 +
STACK CFI 8ad8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8b60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8b68 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8b90 48 .cfa: sp 0 + .ra: x30
STACK CFI 8bb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8bd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8be0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 8be8 .cfa: sp 64 +
STACK CFI 8bf8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8c80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8c88 .cfa: sp 64 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8cb0 48 .cfa: sp 0 + .ra: x30
STACK CFI 8cd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8cf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8d00 5c .cfa: sp 0 + .ra: x30
STACK CFI 8d08 .cfa: sp 32 +
STACK CFI 8d1c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8d54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8d60 44 .cfa: sp 0 + .ra: x30
STACK CFI 8d84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8d9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8da4 118 .cfa: sp 0 + .ra: x30
STACK CFI 8dac .cfa: sp 96 +
STACK CFI 8db0 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8db8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8dc8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8dd0 x23: .cfa -16 + ^
STACK CFI 8e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8e6c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8ec0 44 .cfa: sp 0 + .ra: x30
STACK CFI 8ee4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8efc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8f04 2c .cfa: sp 0 + .ra: x30
STACK CFI 8f0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8f28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8f30 44 .cfa: sp 0 + .ra: x30
STACK CFI 8f54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8f6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8f74 94 .cfa: sp 0 + .ra: x30
STACK CFI 8f7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8fc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8fd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8ff0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9000 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9010 44 .cfa: sp 0 + .ra: x30
STACK CFI 9034 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 904c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9054 3c .cfa: sp 0 + .ra: x30
STACK CFI 905c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9068 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9090 44 .cfa: sp 0 + .ra: x30
STACK CFI 90b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 90cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 90d4 cc .cfa: sp 0 + .ra: x30
STACK CFI 90dc .cfa: sp 32 +
STACK CFI 90ec .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9170 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9178 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 91a0 44 .cfa: sp 0 + .ra: x30
STACK CFI 91c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 91dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 91e4 178 .cfa: sp 0 + .ra: x30
STACK CFI 91ec .cfa: sp 112 +
STACK CFI 91f8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9234 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 923c .cfa: sp 112 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9240 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9248 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9250 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9284 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9298 x27: .cfa -16 + ^
STACK CFI 9314 x25: x25 x26: x26
STACK CFI 9318 x27: x27
STACK CFI 9320 x19: x19 x20: x20
STACK CFI 9324 x21: x21 x22: x22
STACK CFI 9328 x23: x23 x24: x24
STACK CFI 932c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 9330 x19: x19 x20: x20
STACK CFI 9334 x21: x21 x22: x22
STACK CFI 9338 x23: x23 x24: x24
STACK CFI 933c x25: x25 x26: x26
STACK CFI 9340 x27: x27
STACK CFI 9348 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 934c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9350 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9354 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9358 x27: .cfa -16 + ^
STACK CFI INIT 9360 16c .cfa: sp 0 + .ra: x30
STACK CFI 9368 .cfa: sp 80 +
STACK CFI 9378 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9380 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9388 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 93b0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9434 x23: x23 x24: x24
STACK CFI 9464 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 946c .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 948c x23: x23 x24: x24
STACK CFI 949c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 94bc x23: x23 x24: x24
STACK CFI 94c8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 94d0 cc .cfa: sp 0 + .ra: x30
STACK CFI 94d8 .cfa: sp 48 +
STACK CFI 94e8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 956c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9574 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 95a0 44 .cfa: sp 0 + .ra: x30
STACK CFI 95c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 95dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 95e4 4c .cfa: sp 0 + .ra: x30
STACK CFI 95ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 95f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9630 40 .cfa: sp 0 + .ra: x30
STACK CFI 9650 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9668 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9670 d0 .cfa: sp 0 + .ra: x30
STACK CFI 9678 .cfa: sp 80 +
STACK CFI 9688 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9690 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 96e0 x21: .cfa -16 + ^
STACK CFI 9700 x21: x21
STACK CFI 9730 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9738 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 973c x21: .cfa -16 + ^
STACK CFI INIT 9740 40 .cfa: sp 0 + .ra: x30
STACK CFI 9760 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9778 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9780 2c .cfa: sp 0 + .ra: x30
STACK CFI 9788 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 97a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 97b0 44 .cfa: sp 0 + .ra: x30
STACK CFI 97d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 97ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 97f4 d0 .cfa: sp 0 + .ra: x30
STACK CFI 97fc .cfa: sp 80 +
STACK CFI 9808 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9810 x19: .cfa -16 + ^
STACK CFI 98a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 98ac .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 98c4 44 .cfa: sp 0 + .ra: x30
STACK CFI 98e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9900 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9910 78 .cfa: sp 0 + .ra: x30
STACK CFI 9918 .cfa: sp 32 +
STACK CFI 992c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 997c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9984 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9990 44 .cfa: sp 0 + .ra: x30
STACK CFI 99b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 99cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 99d4 2c .cfa: sp 0 + .ra: x30
STACK CFI 99dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 99f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9a00 48 .cfa: sp 0 + .ra: x30
STACK CFI 9a28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9a40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9a50 a0 .cfa: sp 0 + .ra: x30
STACK CFI 9a58 .cfa: sp 64 +
STACK CFI 9a68 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9a7c x19: .cfa -16 + ^
STACK CFI 9ae4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 9aec .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9af0 54 .cfa: sp 0 + .ra: x30
STACK CFI 9af8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9b0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9b18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9b28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9b34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9b44 74 .cfa: sp 0 + .ra: x30
STACK CFI 9b4c .cfa: sp 32 +
STACK CFI 9b5c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9bac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9bb4 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9bc0 54 .cfa: sp 0 + .ra: x30
STACK CFI 9bc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9bdc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9be8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9bf8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9c04 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9c14 d0 .cfa: sp 0 + .ra: x30
STACK CFI 9c1c .cfa: sp 80 +
STACK CFI 9c28 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9c58 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9c90 x19: x19 x20: x20
STACK CFI 9cb4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9cbc .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9cc0 x19: x19 x20: x20
STACK CFI 9ce0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 9ce4 44 .cfa: sp 0 + .ra: x30
STACK CFI 9d08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9d20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9d30 3c .cfa: sp 0 + .ra: x30
STACK CFI 9d38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9d50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9d60 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9d64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9d70 40 .cfa: sp 0 + .ra: x30
STACK CFI 9d90 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9da8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9db0 40 .cfa: sp 0 + .ra: x30
STACK CFI 9db8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9dd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9de4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9de8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9df0 40 .cfa: sp 0 + .ra: x30
STACK CFI 9e10 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9e28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9e30 d8 .cfa: sp 0 + .ra: x30
STACK CFI 9e38 .cfa: sp 80 +
STACK CFI 9e48 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9e54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9ef4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9efc .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9f10 44 .cfa: sp 0 + .ra: x30
STACK CFI 9f34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9f4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9f54 78 .cfa: sp 0 + .ra: x30
STACK CFI 9f5c .cfa: sp 32 +
STACK CFI 9f70 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9fc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9fc8 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 9fd0 44 .cfa: sp 0 + .ra: x30
STACK CFI 9ff4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a00c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a014 48 .cfa: sp 0 + .ra: x30
STACK CFI a01c .cfa: sp 32 +
STACK CFI a030 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a054 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a060 48 .cfa: sp 0 + .ra: x30
STACK CFI a088 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a0a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a0b0 44 .cfa: sp 0 + .ra: x30
STACK CFI a0b8 .cfa: sp 32 +
STACK CFI a0cc .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a0ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a0f4 48 .cfa: sp 0 + .ra: x30
STACK CFI a11c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a134 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a140 2c .cfa: sp 0 + .ra: x30
STACK CFI a148 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a164 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a170 44 .cfa: sp 0 + .ra: x30
STACK CFI a194 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a1ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a1b4 2c .cfa: sp 0 + .ra: x30
STACK CFI a1bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a1d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a1e0 48 .cfa: sp 0 + .ra: x30
STACK CFI a208 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a220 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a230 2c .cfa: sp 0 + .ra: x30
STACK CFI a238 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a254 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a260 48 .cfa: sp 0 + .ra: x30
STACK CFI a288 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a2a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a2b0 2c .cfa: sp 0 + .ra: x30
STACK CFI a2b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a2d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a2e0 44 .cfa: sp 0 + .ra: x30
STACK CFI a304 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a31c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a324 98 .cfa: sp 0 + .ra: x30
STACK CFI a32c .cfa: sp 48 +
STACK CFI a33c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a34c x19: .cfa -16 + ^
STACK CFI a3b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a3b8 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT a3c0 40 .cfa: sp 0 + .ra: x30
STACK CFI a3c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a3e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a3e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a3ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a3f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a3f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a400 160 .cfa: sp 0 + .ra: x30
STACK CFI a408 .cfa: sp 96 +
STACK CFI a40c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a414 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a41c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a454 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a460 x25: .cfa -16 + ^
STACK CFI a49c x23: x23 x24: x24
STACK CFI a4a0 x25: x25
STACK CFI a4d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a4d8 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI a53c x23: x23 x24: x24
STACK CFI a540 x25: x25
STACK CFI a544 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI a548 x23: x23 x24: x24
STACK CFI a550 x25: x25
STACK CFI a558 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a55c x25: .cfa -16 + ^
STACK CFI INIT a560 26c .cfa: sp 0 + .ra: x30
STACK CFI a568 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a574 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a57c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a584 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a5b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a5c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a6d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a6dc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a740 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a74c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT a7d0 74 .cfa: sp 0 + .ra: x30
STACK CFI a7d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a7e0 x19: .cfa -16 + ^
STACK CFI a830 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a838 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT a844 40 .cfa: sp 0 + .ra: x30
STACK CFI a84c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a864 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a86c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a870 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a878 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a87c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a884 84 .cfa: sp 0 + .ra: x30
STACK CFI a88c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a894 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a8a0 x21: .cfa -16 + ^
STACK CFI a900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT a910 5c .cfa: sp 0 + .ra: x30
STACK CFI a920 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a92c x19: .cfa -16 + ^
STACK CFI a948 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a950 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a960 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a970 50 .cfa: sp 0 + .ra: x30
STACK CFI a978 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a980 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a9a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a9b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT a9c0 a8 .cfa: sp 0 + .ra: x30
STACK CFI a9c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a9d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a9e8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI aa04 x21: x21 x22: x22
STACK CFI aa18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI aa20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI aa34 x21: x21 x22: x22
STACK CFI aa4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI aa54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI aa60 x21: x21 x22: x22
STACK CFI INIT aa70 90 .cfa: sp 0 + .ra: x30
STACK CFI aa78 .cfa: sp 64 +
STACK CFI aa84 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aa90 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI aaf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI aafc .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT ab00 44 .cfa: sp 0 + .ra: x30
STACK CFI ab24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ab3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ab44 1a0 .cfa: sp 0 + .ra: x30
STACK CFI ab4c .cfa: sp 96 +
STACK CFI ab50 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ab58 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ab68 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI aba0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI ac1c x23: x23 x24: x24
STACK CFI ac48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ac50 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI acb4 x23: x23 x24: x24
STACK CFI acd0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI acd4 x23: x23 x24: x24
STACK CFI ace0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT ace4 48 .cfa: sp 0 + .ra: x30
STACK CFI ad0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ad24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ad30 170 .cfa: sp 0 + .ra: x30
STACK CFI ad38 .cfa: sp 96 +
STACK CFI ad44 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ad4c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ad54 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ae14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ae1c .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT aea0 44 .cfa: sp 0 + .ra: x30
STACK CFI aec4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI aedc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT aee4 138 .cfa: sp 0 + .ra: x30
STACK CFI aeec .cfa: sp 96 +
STACK CFI aef0 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI aef8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI af0c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI af1c x23: .cfa -16 + ^
STACK CFI b008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI b010 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT b020 44 .cfa: sp 0 + .ra: x30
STACK CFI b044 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b05c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b064 c0 .cfa: sp 0 + .ra: x30
STACK CFI b06c .cfa: sp 64 +
STACK CFI b078 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b084 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b118 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b120 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT b124 54 .cfa: sp 0 + .ra: x30
STACK CFI b12c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b140 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b14c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b15c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI b168 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
