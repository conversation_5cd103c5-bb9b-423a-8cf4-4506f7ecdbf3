MODULE Linux arm64 C6C7C44E5A234F98CD7240BBCAF3D5CD0 libcluster-samba4.so.0
INFO CODE_ID 4EC4C7C6235A984FCD7240BBCAF3D5CD48AA92D9
PUBLIC e20 0 cluster_set_ops
PUBLIC e40 0 cluster_backend_handle
PUBLIC e70 0 cluster_local_init
PUBLIC e90 0 cluster_id
PUBLIC f30 0 cluster_db_tmp_open
PUBLIC fa0 0 cluster_message_init
PUBLIC 1010 0 cluster_message_send
STACK CFI INIT b70 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT ba0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT be0 48 .cfa: sp 0 + .ra: x30
STACK CFI be4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bec x19: .cfa -16 + ^
STACK CFI c24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c40 1c .cfa: sp 0 + .ra: x30
STACK CFI c48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c60 1c .cfa: sp 0 + .ra: x30
STACK CFI c68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c80 20 .cfa: sp 0 + .ra: x30
STACK CFI c88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ca0 a0 .cfa: sp 0 + .ra: x30
STACK CFI ca8 .cfa: sp 80 +
STACK CFI cb4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI cbc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cc8 x21: .cfa -16 + ^
STACK CFI d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI d3c .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT d40 dc .cfa: sp 0 + .ra: x30
STACK CFI d48 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d54 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d6c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI d74 x23: .cfa -16 + ^
STACK CFI e14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT e20 20 .cfa: sp 0 + .ra: x30
STACK CFI e28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e40 28 .cfa: sp 0 + .ra: x30
STACK CFI e4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e70 20 .cfa: sp 0 + .ra: x30
STACK CFI e78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e90 9c .cfa: sp 0 + .ra: x30
STACK CFI e98 .cfa: sp 64 +
STACK CFI ea4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI eac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI f04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f0c .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT f30 70 .cfa: sp 0 + .ra: x30
STACK CFI f38 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI f44 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI f54 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI f84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f8c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT fa0 6c .cfa: sp 0 + .ra: x30
STACK CFI fa8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI fb4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ff0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ff8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1010 64 .cfa: sp 0 + .ra: x30
STACK CFI 1018 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1024 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1058 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1060 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
