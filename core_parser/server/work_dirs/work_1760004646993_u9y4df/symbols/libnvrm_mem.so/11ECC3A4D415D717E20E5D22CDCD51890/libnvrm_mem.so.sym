MODULE Linux arm64 11ECC3A4D415D717E20E5D22CDCD51890 libnvrm_mem.so
INFO CODE_ID A4C3EC1115D417D7E20E5D22CDCD5189D9DAF61B
PUBLIC 1d20 0 NvRmMemRd08
PUBLIC 1d90 0 NvRmMemRd16
PUBLIC 1e00 0 NvRmMemRd32
PUBLIC 1e70 0 NvRmMemWr08
PUBLIC 1ee0 0 NvRmMemWr16
PUBLIC 1f50 0 NvRmMemWr32
PUBLIC 20f0 0 NvRmMemGetImportIdMult
PUBLIC 2110 0 NvRmMemHandleFromImportIdMult
PUBLIC 2130 0 NvRmMemEnableEvent
PUBLIC 2150 0 NvRmMemDisableEvent
PUBLIC 2170 0 NvRmMemHandleFromImportId
PUBLIC 2190 0 NvRmMemGetImportId
PUBLIC 21b0 0 NvRmMemMgrInit
PUBLIC 2320 0 NvRmMemHandleAllocAttr
PUBLIC 23b0 0 NvRmMemQueryHandleParams
PUBLIC 2450 0 NvRmMemHandleFromFd
PUBLIC 24c0 0 NvRmMemHandleDuplicate
PUBLIC 2540 0 NvRmMemHandleFree
PUBLIC 25c0 0 NvRmMemGetFd
PUBLIC 2640 0 NvRmMemMap
PUBLIC 26e0 0 NvRmMemCacheSyncForCpu
PUBLIC 2770 0 NvRmMemCacheSyncForDevice
PUBLIC 2800 0 NvRmMemUnmap
PUBLIC 2880 0 NvRmMemQueryHeaps
PUBLIC 28f0 0 NvRmMemQueryHandleParameters
PUBLIC 2990 0 NvRmMemGetPhysInfo
PUBLIC 29b0 0 NvRmMemGetMssHWCapability
PUBLIC 2a80 0 NvRmMemGetSciIpcId
PUBLIC 2b60 0 NvRmMemHandleFromSciIpcId
PUBLIC 2bf0 0 NvRmMemHandleFromSciIpcIdMult
PUBLIC 2c10 0 NvRmMemGetSciIpcIdMult
PUBLIC 2c30 0 NvRmMemQueryHeapParams
PUBLIC 2cd0 0 NvRmMemQueryHeapParamsNuma
PUBLIC 2d70 0 NvRmMemDmaMap
PUBLIC 2d90 0 NvRmMemDmaUnmap
PUBLIC 2db0 0 NvRmMemDmaMapMult
PUBLIC 2dd0 0 NvRmMemDmaUnmapMult
PUBLIC 2df0 0 NvRmMemGetPhysInfoMult
PUBLIC 2e10 0 NvRmMemQueryHeapManager
PUBLIC 2e90 0 NvRmMemGetHwid
PUBLIC 2eb0 0 NvRm_MemmgrGetIoctlFile
PUBLIC 2ed0 0 NvRmMemHandleCreate
PUBLIC 2ef0 0 NvRmMemHandleAlloc
PUBLIC 3020 0 NvRmMemPutImportId
PUBLIC 3040 0 NvRmMemPutImportIdMult
PUBLIC 3060 0 NvRmMemHandleFromId
PUBLIC 3080 0 NvRmMemHandleDup
PUBLIC 30f0 0 NvRmMemSetVprFloor
PUBLIC 3170 0 NvRmMemAlloc
PUBLIC 3190 0 NvRmMemGetId
PUBLIC 31f0 0 NvRmMemPin
PUBLIC 3210 0 NvRmMemUnpin
PUBLIC 3230 0 NvRmMemCacheListSyncForCpu64
PUBLIC 32c0 0 NvRmMemCacheListSyncForCpu
PUBLIC 3350 0 NvRmMemCacheListSyncForDevice64
PUBLIC 33e0 0 NvRmMemCacheListSyncForDevice
PUBLIC 3470 0 NvRmMemReserve64
PUBLIC 3490 0 NvRmMemReserve
PUBLIC 34b0 0 NvRmMemReadStrided
PUBLIC 3570 0 NvRmMemRead
PUBLIC 3590 0 NvRmMemWriteStrided
PUBLIC 3650 0 NvRmMemWrite
PUBLIC 3670 0 NvRmMemGetSize
PUBLIC 36f0 0 NvRmMemMove
PUBLIC 37f0 0 NvRmMemGetAddress
PUBLIC 3810 0 NvRmMemQueryHeapSize
PUBLIC 3880 0 NvRmMemGetIVCId
PUBLIC 3900 0 NvRmMemHandleFromIVCId
PUBLIC 3970 0 NvRmMemSetAllocationTagLabel
PUBLIC 39e0 0 NvRmMemQueryPeerVmId
PUBLIC 3a00 0 NvRmMemGetFdForRangeFromList
STACK CFI INIT 1c50 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c80 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1cc0 48 .cfa: sp 0 + .ra: x30
STACK CFI 1cc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ccc x19: .cfa -16 + ^
STACK CFI 1d04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1d10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d20 64 .cfa: sp 0 + .ra: x30
STACK CFI 1d38 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 1d70 .cfa: sp 0 + .ra: .ra
STACK CFI 1d80 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI INIT 1d90 64 .cfa: sp 0 + .ra: x30
STACK CFI 1da8 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 1de0 .cfa: sp 0 + .ra: .ra
STACK CFI 1df0 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI INIT 1e00 64 .cfa: sp 0 + .ra: x30
STACK CFI 1e18 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 1e50 .cfa: sp 0 + .ra: .ra
STACK CFI 1e60 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI INIT 1e70 64 .cfa: sp 0 + .ra: x30
STACK CFI 1e88 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 1ec0 .cfa: sp 0 + .ra: .ra
STACK CFI 1ed0 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI INIT 1ee0 64 .cfa: sp 0 + .ra: x30
STACK CFI 1ef8 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 1f30 .cfa: sp 0 + .ra: .ra
STACK CFI 1f40 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI INIT 1f50 68 .cfa: sp 0 + .ra: x30
STACK CFI 1f6c .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 1fa4 .cfa: sp 0 + .ra: .ra
STACK CFI 1fb4 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI INIT 1fc0 6c .cfa: sp 0 + .ra: x30
STACK CFI 1fc8 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1fd8 .ra: .cfa -16 + ^
STACK CFI 2008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 2018 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 2030 bc .cfa: sp 0 + .ra: x30
STACK CFI 2040 .cfa: sp 288 +
STACK CFI 204c .ra: .cfa -288 + ^
STACK CFI 20d8 .cfa: sp 0 + .ra: .ra
STACK CFI 20e8 .cfa: sp 288 + .ra: .cfa -288 + ^
STACK CFI INIT 20f0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2110 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2130 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2150 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2170 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2190 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 21b0 170 .cfa: sp 0 + .ra: x30
STACK CFI 21b8 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 21cc .ra: .cfa -16 + ^
STACK CFI 221c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 222c .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 22cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 22dc .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 22fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 230c .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 2320 88 .cfa: sp 0 + .ra: x30
STACK CFI 2328 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 2358 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 236c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 2380 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 2390 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 2398 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 23b0 94 .cfa: sp 0 + .ra: x30
STACK CFI 23bc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 23d0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 2408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 241c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 2434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 2450 68 .cfa: sp 0 + .ra: x30
STACK CFI 245c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 2480 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 2494 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 24a8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 24c0 7c .cfa: sp 0 + .ra: x30
STACK CFI 24cc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 24e0 .ra: .cfa -32 + ^
STACK CFI 2500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 2514 .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 252c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 2540 7c .cfa: sp 0 + .ra: x30
STACK CFI 2574 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 2594 .cfa: sp 0 + .ra: .ra
STACK CFI 25a8 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 25ac .cfa: sp 0 + .ra: .ra
STACK CFI INIT 25c0 80 .cfa: sp 0 + .ra: x30
STACK CFI 25f4 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 2614 .cfa: sp 0 + .ra: .ra
STACK CFI 2628 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 2630 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 2640 98 .cfa: sp 0 + .ra: x30
STACK CFI 264c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2660 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 266c .ra: .cfa -32 + ^
STACK CFI 2698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 26ac .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 26c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 26e0 84 .cfa: sp 0 + .ra: x30
STACK CFI 26ec .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2700 .ra: .cfa -32 + ^
STACK CFI 2728 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 273c .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2754 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 2770 84 .cfa: sp 0 + .ra: x30
STACK CFI 277c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2790 .ra: .cfa -32 + ^
STACK CFI 27b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 27cc .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 27e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 2800 7c .cfa: sp 0 + .ra: x30
STACK CFI 280c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2820 .ra: .cfa -32 + ^
STACK CFI 2840 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 2854 .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 286c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 2880 68 .cfa: sp 0 + .ra: x30
STACK CFI 288c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 28b0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 28c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 28d8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 28f0 94 .cfa: sp 0 + .ra: x30
STACK CFI 28fc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2910 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 2948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 295c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 2974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 1bf0 54 .cfa: sp 0 + .ra: x30
STACK CFI 1c08 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 1c14 .cfa: sp 0 + .ra: .ra
STACK CFI 1c24 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 1c30 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 1be0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2990 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 29b0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 29bc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 29fc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 2a10 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 2a70 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 2a80 d4 .cfa: sp 0 + .ra: x30
STACK CFI 2a94 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2aa4 .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 2b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 2b20 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT 2b60 8c .cfa: sp 0 + .ra: x30
STACK CFI 2b94 .cfa: sp 48 + .ra: .cfa -48 + ^
STACK CFI 2bc4 .cfa: sp 0 + .ra: .ra
STACK CFI 2bd8 .cfa: sp 48 + .ra: .cfa -48 + ^
STACK CFI 2bdc .cfa: sp 0 + .ra: .ra
STACK CFI INIT 2bf0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c10 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c30 98 .cfa: sp 0 + .ra: x30
STACK CFI 2c3c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2c50 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2c5c .ra: .cfa -32 + ^
STACK CFI 2c88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 2c9c .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2cb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 2cd0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 2cdc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2cf0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2cfc .ra: .cfa -24 + ^ x23: .cfa -32 + ^
STACK CFI 2d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 2d44 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 2d60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 2d70 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2d90 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2db0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2dd0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2df0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2e10 7c .cfa: sp 0 + .ra: x30
STACK CFI 2e44 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 2e64 .cfa: sp 0 + .ra: .ra
STACK CFI 2e78 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 2e7c .cfa: sp 0 + .ra: .ra
STACK CFI INIT 2e90 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2eb0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ed0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ef0 124 .cfa: sp 0 + .ra: x30
STACK CFI 2f04 .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2f20 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 2f2c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 2f38 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 2f40 .ra: .cfa -136 + ^ x27: .cfa -144 + ^
STACK CFI 2fdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 2fec .cfa: sp 208 + .ra: .cfa -136 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^
STACK CFI INIT 3020 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3040 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3060 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3080 68 .cfa: sp 0 + .ra: x30
STACK CFI 308c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 30b0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 30c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 30d8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 30f0 7c .cfa: sp 0 + .ra: x30
STACK CFI 3124 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 3144 .cfa: sp 0 + .ra: .ra
STACK CFI 3158 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 315c .cfa: sp 0 + .ra: .ra
STACK CFI INIT 3170 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3190 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31b0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31d0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 31f0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3210 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3230 90 .cfa: sp 0 + .ra: x30
STACK CFI 323c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3250 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 3284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 3298 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 32b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 32c0 90 .cfa: sp 0 + .ra: x30
STACK CFI 32cc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 32e0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 3314 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 3328 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 3340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 3350 90 .cfa: sp 0 + .ra: x30
STACK CFI 335c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3370 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 33a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 33b8 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 33d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 33e0 90 .cfa: sp 0 + .ra: x30
STACK CFI 33ec .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3400 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 3434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 3448 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 3460 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 3470 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3490 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 34b0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 34bc .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 34d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 34dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 34e8 .ra: .cfa -32 + ^
STACK CFI 3520 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 3534 .cfa: sp 80 + .ra: .cfa -32 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI INIT 3570 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3590 b4 .cfa: sp 0 + .ra: x30
STACK CFI 359c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 35b0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 35bc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 35c8 .ra: .cfa -32 + ^
STACK CFI 3600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 3614 .cfa: sp 80 + .ra: .cfa -32 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI INIT 3650 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3670 80 .cfa: sp 0 + .ra: x30
STACK CFI 36a4 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 36c4 .cfa: sp 0 + .ra: .ra
STACK CFI 36d8 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 36e0 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 36f0 fc .cfa: sp 0 + .ra: x30
STACK CFI 3704 .cfa: sp 8288 +
STACK CFI 3708 x25: .cfa -8240 + ^ x26: .cfa -8232 + ^
STACK CFI 371c .ra: .cfa -8216 + ^
STACK CFI 3728 x19: .cfa -8288 + ^ x20: .cfa -8280 + ^
STACK CFI 3730 x21: .cfa -8272 + ^ x22: .cfa -8264 + ^
STACK CFI 373c x23: .cfa -8256 + ^ x24: .cfa -8248 + ^
STACK CFI 3744 x27: .cfa -8224 + ^
STACK CFI 3790 x19: x19 x20: x20
STACK CFI 3794 x21: x21 x22: x22
STACK CFI 3798 x23: x23 x24: x24
STACK CFI 379c x27: x27
STACK CFI 37c8 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26
STACK CFI 37d8 .cfa: sp 8288 + .ra: .cfa -8216 + ^ x25: .cfa -8240 + ^ x26: .cfa -8232 + ^
STACK CFI 37dc x19: .cfa -8288 + ^ x20: .cfa -8280 + ^
STACK CFI 37e0 x21: .cfa -8272 + ^ x22: .cfa -8264 + ^
STACK CFI 37e4 x23: .cfa -8256 + ^ x24: .cfa -8248 + ^
STACK CFI 37e8 x27: .cfa -8224 + ^
STACK CFI INIT 37f0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3810 68 .cfa: sp 0 + .ra: x30
STACK CFI 381c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 3840 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 3854 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 3868 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 3880 80 .cfa: sp 0 + .ra: x30
STACK CFI 38b4 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 38d4 .cfa: sp 0 + .ra: .ra
STACK CFI 38e8 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 38f0 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 3900 68 .cfa: sp 0 + .ra: x30
STACK CFI 390c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 3930 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 3944 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 3958 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 3970 68 .cfa: sp 0 + .ra: x30
STACK CFI 397c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 39a0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 39b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 39c8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 39e0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3a00 84 .cfa: sp 0 + .ra: x30
STACK CFI 3a0c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3a20 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 3a48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 3a5c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI 3a74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 3a90 c4 .cfa: sp 0 + .ra: x30
STACK CFI 3aac .cfa: sp 64 + .ra: .cfa -64 + ^
STACK CFI 3b18 .cfa: sp 0 + .ra: .ra
STACK CFI 3b28 .cfa: sp 64 + .ra: .cfa -64 + ^
STACK CFI INIT 3b60 98 .cfa: sp 0 + .ra: x30
STACK CFI 3b7c .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 3bc0 .cfa: sp 0 + .ra: .ra
STACK CFI 3bd0 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI INIT 3c00 98 .cfa: sp 0 + .ra: x30
STACK CFI 3c1c .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI 3c60 .cfa: sp 0 + .ra: .ra
STACK CFI 3c70 .cfa: sp 32 + .ra: .cfa -32 + ^
STACK CFI INIT 3ca0 7c .cfa: sp 0 + .ra: x30
STACK CFI 3ca8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 3cd0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 3ce0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 3d0c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 3d20 1dc .cfa: sp 0 + .ra: x30
STACK CFI 3d30 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3d38 .ra: .cfa -80 + ^
STACK CFI 3dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3de4 .cfa: sp 96 + .ra: .cfa -80 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI INIT 3f00 204 .cfa: sp 0 + .ra: x30
STACK CFI 3f14 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 3f38 .ra: .cfa -56 + ^
STACK CFI 3f40 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 3f48 x23: .cfa -64 + ^
STACK CFI 3f80 x21: x21 x22: x22
STACK CFI 3f84 x23: x23
STACK CFI 3fa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 3fb8 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 404c x21: x21 x22: x22
STACK CFI 4050 x23: x23
STACK CFI 4058 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 40f8 x21: x21 x22: x22 x23: x23
STACK CFI 40fc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4100 x23: .cfa -64 + ^
STACK CFI INIT 4110 20c .cfa: sp 0 + .ra: x30
STACK CFI 4124 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4148 .ra: .cfa -56 + ^
STACK CFI 4154 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 415c x23: .cfa -64 + ^
STACK CFI 4194 x21: x21 x22: x22
STACK CFI 4198 x23: x23
STACK CFI 41bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 41cc .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 4264 x21: x21 x22: x22
STACK CFI 4268 x23: x23
STACK CFI 4270 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 4310 x21: x21 x22: x22 x23: x23
STACK CFI 4314 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4318 x23: .cfa -64 + ^
STACK CFI INIT 4320 264 .cfa: sp 0 + .ra: x30
STACK CFI 4334 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 435c .ra: .cfa -72 + ^
STACK CFI 437c x21: .cfa -80 + ^
STACK CFI 4470 x21: x21
STACK CFI 4498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 44a8 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 44c8 x21: .cfa -80 + ^
STACK CFI 44d4 x21: x21
STACK CFI 44dc x21: .cfa -80 + ^
STACK CFI 457c x21: x21
STACK CFI 4580 x21: .cfa -80 + ^
STACK CFI INIT 4590 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 4598 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 45a0 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 45e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 45f4 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 4630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 4640 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 4760 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 4774 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 477c .ra: .cfa -48 + ^
STACK CFI 47f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 4804 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 4950 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 496c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 49bc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 49cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI INIT 4b10 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 4b2c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 4b7c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 4b8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI INIT 4cd0 1bc .cfa: sp 0 + .ra: x30
STACK CFI 4ce0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -48 + ^
STACK CFI 4d48 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 4d58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -48 + ^
STACK CFI INIT 4e90 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 4e9c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4eb0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 4ec4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4ef8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 4f10 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 4f3c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 4f4c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI INIT 5030 234 .cfa: sp 0 + .ra: x30
STACK CFI 5040 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5048 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5054 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 5060 .ra: .cfa -72 + ^ x25: .cfa -80 + ^
STACK CFI 50dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 50ec .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^
STACK CFI INIT 5270 324 .cfa: sp 0 + .ra: x30
STACK CFI 5284 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -96 + ^
STACK CFI 53d8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 53e8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -96 + ^
STACK CFI INIT 55a0 20c .cfa: sp 0 + .ra: x30
STACK CFI 55b0 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 55b4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 55d0 .ra: .cfa -56 + ^
STACK CFI 55ec x23: .cfa -64 + ^
STACK CFI 564c x23: x23
STACK CFI 5654 x23: .cfa -64 + ^
STACK CFI 5658 x23: x23
STACK CFI 5688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 5698 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 56c4 x23: x23
STACK CFI 56d0 x23: .cfa -64 + ^
STACK CFI 56fc x23: x23
STACK CFI 5704 x23: .cfa -64 + ^
STACK CFI 57a4 x23: x23
STACK CFI 57a8 x23: .cfa -64 + ^
STACK CFI INIT 57b0 23c .cfa: sp 0 + .ra: x30
STACK CFI 57c4 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 57d0 .ra: .cfa -56 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI 5894 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 58a4 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^
STACK CFI INIT 59f0 25c .cfa: sp 0 + .ra: x30
STACK CFI 5a00 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -80 + ^
STACK CFI 5ae0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 5af0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -80 + ^
STACK CFI INIT 5c50 264 .cfa: sp 0 + .ra: x30
STACK CFI 5c60 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -80 + ^
STACK CFI 5d48 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 5d58 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -80 + ^
STACK CFI INIT 5ec0 1ec .cfa: sp 0 + .ra: x30
STACK CFI 5ed8 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5ee4 .ra: .cfa -80 + ^
STACK CFI 5f44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 5f54 .cfa: sp 96 + .ra: .cfa -80 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI INIT 60b0 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 60c4 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 60cc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 60d4 .ra: .cfa -96 + ^
STACK CFI 6194 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 61a4 .cfa: sp 128 + .ra: .cfa -96 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 6224 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 622c .cfa: sp 128 + .ra: .cfa -96 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI INIT 63b0 d00 .cfa: sp 0 + .ra: x30
STACK CFI 63c0 .cfa: sp 224 + x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 63d4 .ra: .cfa -144 + ^
STACK CFI 63dc x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 63e8 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 63fc x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 64d0 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 65b4 x25: x25 x26: x26
STACK CFI 6678 x21: x21 x22: x22
STACK CFI 667c x23: x23 x24: x24
STACK CFI 6680 x27: x27 x28: x28
STACK CFI 6684 x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 6890 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6928 x27: x27 x28: x28
STACK CFI 6934 x21: x21 x22: x22
STACK CFI 6960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 6970 .cfa: sp 224 + .ra: .cfa -144 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 6a14 x21: x21 x22: x22
STACK CFI 6a18 x23: x23 x24: x24
STACK CFI 6a1c x25: x25 x26: x26
STACK CFI 6a20 x27: x27 x28: x28
STACK CFI 6a30 x21: .cfa -208 + ^ x22: .cfa -200 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 6a5c x21: x21 x22: x22
STACK CFI 6a60 x27: x27 x28: x28
STACK CFI 6a68 x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 6adc x21: x21 x22: x22
STACK CFI 6ae0 x23: x23 x24: x24
STACK CFI 6ae4 x27: x27 x28: x28
STACK CFI 6aec x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 6b6c x21: x21 x22: x22
STACK CFI 6b70 x23: x23 x24: x24
STACK CFI 6b74 x25: x25 x26: x26
STACK CFI 6b78 x27: x27 x28: x28
STACK CFI 6b7c x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 6bbc x21: x21 x22: x22
STACK CFI 6bc0 x23: x23 x24: x24
STACK CFI 6bc4 x27: x27 x28: x28
STACK CFI 6bcc x21: .cfa -208 + ^ x22: .cfa -200 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 6bec x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 6c58 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6c5c x25: x25 x26: x26
STACK CFI 6c74 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6c78 x25: x25 x26: x26
STACK CFI 6c84 x23: x23 x24: x24
STACK CFI 6c94 x21: x21 x22: x22
STACK CFI 6c98 x27: x27 x28: x28
STACK CFI 6ca0 x21: .cfa -208 + ^ x22: .cfa -200 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 6cac x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 6cbc x21: x21 x22: x22
STACK CFI 6cc0 x23: x23 x24: x24
STACK CFI 6cc4 x27: x27 x28: x28
STACK CFI 6ccc x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 6cd8 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6cdc x25: x25 x26: x26
STACK CFI 6ce8 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6cec x25: x25 x26: x26
STACK CFI 6cf8 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6cfc x25: x25 x26: x26
STACK CFI 6d08 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6d0c x25: x25 x26: x26
STACK CFI 6d40 x23: x23 x24: x24
STACK CFI 6d64 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 6d74 x23: x23 x24: x24
STACK CFI 6d84 x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6d8c x25: x25 x26: x26
STACK CFI 6d94 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6d98 x25: x25 x26: x26
STACK CFI 6da8 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6dac x25: x25 x26: x26
STACK CFI 6db8 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6dbc x25: x25 x26: x26
STACK CFI 6dcc x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6dd0 x25: x25 x26: x26
STACK CFI 6ddc x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6de0 x25: x25 x26: x26
STACK CFI 6dec x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6df0 x25: x25 x26: x26
STACK CFI 6dfc x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6e00 x25: x25 x26: x26
STACK CFI 6e10 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6e14 x25: x25 x26: x26
STACK CFI 6e24 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6e28 x25: x25 x26: x26
STACK CFI 6e38 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6e3c x25: x25 x26: x26
STACK CFI 6e48 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 6e4c x25: x25 x26: x26
STACK CFI 6ee0 x23: x23 x24: x24
STACK CFI 6f68 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 6f84 x23: x23 x24: x24
STACK CFI 6f94 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 6fa0 x23: x23 x24: x24
STACK CFI 6fac x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 7000 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 700c x25: x25 x26: x26
STACK CFI 7020 x23: x23 x24: x24
STACK CFI 7040 x21: x21 x22: x22
STACK CFI 7044 x27: x27 x28: x28
STACK CFI 704c x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 7074 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 7090 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7094 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 7098 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 709c x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 70a0 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 70a8 x25: x25 x26: x26
STACK CFI INIT 70b0 24c .cfa: sp 0 + .ra: x30
STACK CFI 70bc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 70c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 70cc .ra: .cfa -16 + ^
STACK CFI 711c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 712c .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7154 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 7164 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 7300 14c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7450 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 745c .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7464 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7470 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 747c .ra: .cfa -16 + ^
STACK CFI 7490 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 751c x25: x25 x26: x26
STACK CFI 7538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 7548 .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 75a0 x25: x25 x26: x26
STACK CFI 75c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7604 x25: x25 x26: x26
STACK CFI 760c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7720 x25: x25 x26: x26
STACK CFI INIT 7730 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 773c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7748 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 779c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 77ac .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 7900 bc .cfa: sp 0 + .ra: x30
STACK CFI 790c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7910 .ra: .cfa -16 + ^
STACK CFI 7948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 7958 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 79c0 11c .cfa: sp 0 + .ra: x30
STACK CFI 79cc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 79d4 .ra: .cfa -16 + ^
STACK CFI 79f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 7a04 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 7ae0 50 .cfa: sp 0 + .ra: x30
STACK CFI 7ae8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 7b08 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 7b18 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 7b1c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 7b30 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7b80 1c .cfa: sp 0 + .ra: x30
STACK CFI 7b88 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 7b90 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 7ba0 58 .cfa: sp 0 + .ra: x30
STACK CFI 7bac .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 7bd0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 7be0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 7be4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 7c00 c .cfa: sp 0 + .ra: x30
