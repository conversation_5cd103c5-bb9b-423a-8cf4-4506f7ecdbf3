MODULE Linux arm64 C9112B4A2554CE4F37309173D3F68C7B0 libpipewire-module-access.so
INFO CODE_ID 4A2B11C954254FCE37309173D3F68C7B5D1F1EF5
PUBLIC 2dc4 0 pipewire__module_init
STACK CFI INIT 13b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1420 48 .cfa: sp 0 + .ra: x30
STACK CFI 1424 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 142c x19: .cfa -16 + ^
STACK CFI 1464 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1470 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1480 340 .cfa: sp 0 + .ra: x30
STACK CFI 1488 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1574 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1580 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 16b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 16c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1714 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 171c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1794 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 179c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 17c0 88 .cfa: sp 0 + .ra: x30
STACK CFI 17c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17d0 x19: .cfa -16 + ^
STACK CFI 1840 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1850 33c .cfa: sp 0 + .ra: x30
STACK CFI 1858 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1864 x19: .cfa -16 + ^
STACK CFI 18ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 18b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1b90 fc .cfa: sp 0 + .ra: x30
STACK CFI 1b98 .cfa: sp 352 +
STACK CFI 1ba8 .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 1bb8 x19: .cfa -192 + ^
STACK CFI 1c78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1c80 .cfa: sp 352 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x29: .cfa -208 + ^
STACK CFI INIT 1c90 5cc .cfa: sp 0 + .ra: x30
STACK CFI 1c98 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1cb0 .cfa: sp 4528 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1e20 .cfa: sp 80 +
STACK CFI 1e34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1e3c .cfa: sp 4528 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 215c x25: .cfa -16 + ^
STACK CFI 216c x26: .cfa -8 + ^
STACK CFI 21b0 x25: x25
STACK CFI 21b8 x26: x26
STACK CFI 21c0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 21c8 x25: x25 x26: x26
STACK CFI 223c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 2244 x25: x25
STACK CFI 224c x26: x26
STACK CFI 2254 x25: .cfa -16 + ^
STACK CFI 2258 x26: .cfa -8 + ^
STACK CFI INIT 2260 52c .cfa: sp 0 + .ra: x30
STACK CFI 2268 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2278 .cfa: sp 2272 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 22e4 x23: .cfa -32 + ^
STACK CFI 22e8 x24: .cfa -24 + ^
STACK CFI 2318 x23: x23
STACK CFI 2320 x24: x24
STACK CFI 2344 .cfa: sp 80 +
STACK CFI 2354 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 235c .cfa: sp 2272 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 2374 x23: x23 x24: x24
STACK CFI 24bc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 250c x23: x23 x24: x24
STACK CFI 2540 x23: .cfa -32 + ^
STACK CFI 2548 x24: .cfa -24 + ^
STACK CFI 2578 x23: x23
STACK CFI 257c x24: x24
STACK CFI 25ac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 25f0 x25: .cfa -16 + ^
STACK CFI 2660 x23: x23
STACK CFI 2664 x24: x24
STACK CFI 2668 x25: x25
STACK CFI 266c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 26e8 x23: x23
STACK CFI 26ec x24: x24
STACK CFI 26f0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2720 x23: x23
STACK CFI 2724 x24: x24
STACK CFI 2728 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2740 x23: x23 x24: x24
STACK CFI 2744 x23: .cfa -32 + ^
STACK CFI 2748 x24: .cfa -24 + ^
STACK CFI 274c x25: .cfa -16 + ^
STACK CFI 2774 x25: x25
STACK CFI 2778 x25: .cfa -16 + ^
STACK CFI 2788 x25: x25
STACK CFI INIT 2790 634 .cfa: sp 0 + .ra: x30
STACK CFI 2798 .cfa: sp 208 +
STACK CFI 27a4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 27ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 27b4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 27dc x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^
STACK CFI 2850 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x29: x29
STACK CFI 2858 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 2880 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2974 x25: x25 x26: x26
STACK CFI 2978 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2a50 x25: x25 x26: x26
STACK CFI 2a64 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2c58 x25: x25 x26: x26
STACK CFI 2c8c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2dbc x25: x25 x26: x26
STACK CFI 2dc0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 2dc4 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 2dcc .cfa: sp 96 +
STACK CFI 2dd8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2de0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2de8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2df0 x23: .cfa -16 + ^
STACK CFI 2f30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2f38 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
