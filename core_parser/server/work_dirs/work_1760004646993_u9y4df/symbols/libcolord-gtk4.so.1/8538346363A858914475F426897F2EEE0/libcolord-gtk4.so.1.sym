MODULE Linux arm64 8538346363A858914475F426897F2EEE0 libcolord-gtk4.so.1
INFO CODE_ID 63343885A86391584475F426897F2EEEB6503D73
PUBLIC 3594 0 cd_sample_widget_get_type
PUBLIC 3604 0 cd_sample_widget_set_color
PUBLIC 36c0 0 cd_sample_widget_new
PUBLIC 3784 0 cd_sample_window_get_type
PUBLIC 37f4 0 cd_sample_window_set_fraction
PUBLIC 38a0 0 cd_sample_window_set_color
PUBLIC 3920 0 cd_sample_window_new
PUBLIC 39d0 0 cd_window_get_type
PUBLIC 3b20 0 cd_window_get_profile
PUBLIC 3d84 0 cd_window_error_quark
PUBLIC 4294 0 cd_window_get_last_profile
PUBLIC 4320 0 cd_window_get_profile_finish
PUBLIC 44c0 0 cd_window_new
PUBLIC 44e0 0 cd_window_get_profile_sync
STACK CFI INIT 2ab0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ae0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b20 48 .cfa: sp 0 + .ra: x30
STACK CFI 2b24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b2c x19: .cfa -16 + ^
STACK CFI 2b64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2b70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b80 70 .cfa: sp 0 + .ra: x30
STACK CFI 2b88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b90 x19: .cfa -16 + ^
STACK CFI 2bc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2bc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2be8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2bf0 88 .cfa: sp 0 + .ra: x30
STACK CFI 2bf8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c00 x19: .cfa -16 + ^
STACK CFI 2c60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2c68 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2c80 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2c88 .cfa: sp 64 +
STACK CFI 2c90 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2c98 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2cb8 x21: .cfa -16 + ^
STACK CFI 2d14 x21: x21
STACK CFI 2d18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d20 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2d34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2d40 c0 .cfa: sp 0 + .ra: x30
STACK CFI 2d48 .cfa: sp 64 +
STACK CFI 2d50 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2d58 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d60 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2de8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2e00 48 .cfa: sp 0 + .ra: x30
STACK CFI 2e08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2e1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2e50 78 .cfa: sp 0 + .ra: x30
STACK CFI 2e58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e60 x19: .cfa -16 + ^
STACK CFI 2ec0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2ed0 74 .cfa: sp 0 + .ra: x30
STACK CFI 2ed8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ee4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2f34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2f44 16c .cfa: sp 0 + .ra: x30
STACK CFI 2f4c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2f58 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2f64 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2f80 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 30a8 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 30b0 70 .cfa: sp 0 + .ra: x30
STACK CFI 30b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 30c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 30ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 30fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3120 78 .cfa: sp 0 + .ra: x30
STACK CFI 3128 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3130 x19: .cfa -16 + ^
STACK CFI 3190 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 31a0 30 .cfa: sp 0 + .ra: x30
STACK CFI 31ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 31c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 31d0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 31d8 .cfa: sp 64 +
STACK CFI 31dc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 31e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31f0 x21: .cfa -16 + ^
STACK CFI 32b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 32b8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 32d0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 32d8 .cfa: sp 64 +
STACK CFI 32dc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 32e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3300 x21: .cfa -16 + ^
STACK CFI 335c x21: x21
STACK CFI 3360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3368 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 337c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3390 e0 .cfa: sp 0 + .ra: x30
STACK CFI 33ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 33b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 33c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 33d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 33d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3428 x21: x21 x22: x22
STACK CFI 3438 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 344c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 346c x21: x21 x22: x22
STACK CFI INIT 3470 124 .cfa: sp 0 + .ra: x30
STACK CFI 3478 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3480 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 34bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 34c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 34e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 34f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3514 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 351c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3540 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 358c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3594 70 .cfa: sp 0 + .ra: x30
STACK CFI 359c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 35c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 35d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 35fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3604 bc .cfa: sp 0 + .ra: x30
STACK CFI 3614 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 361c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3674 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 367c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3688 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 36c0 20 .cfa: sp 0 + .ra: x30
STACK CFI 36c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 36d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 36e0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 36e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 36f4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 36fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 377c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3784 70 .cfa: sp 0 + .ra: x30
STACK CFI 378c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3794 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 37b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 37c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 37ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 37f4 a4 .cfa: sp 0 + .ra: x30
STACK CFI 37fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3810 x19: .cfa -32 + ^
STACK CFI 3834 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 383c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 3858 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3860 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 386c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3874 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI 3890 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 38a0 78 .cfa: sp 0 + .ra: x30
STACK CFI 38a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 38b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 38d4 x21: .cfa -16 + ^
STACK CFI 3910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 3920 a8 .cfa: sp 0 + .ra: x30
STACK CFI 3928 .cfa: sp 96 +
STACK CFI 392c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 39c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 39d0 70 .cfa: sp 0 + .ra: x30
STACK CFI 39d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 39e0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3a38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3a40 dc .cfa: sp 0 + .ra: x30
STACK CFI 3a48 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a50 x19: .cfa -16 + ^
STACK CFI 3ae0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3af0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3afc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3b20 264 .cfa: sp 0 + .ra: x30
STACK CFI 3b28 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3b30 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3b3c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3b48 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3c9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3ca4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3cd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3cd8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3cf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3d0c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3d28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3d30 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3d84 50 .cfa: sp 0 + .ra: x30
STACK CFI 3d8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d94 x19: .cfa -16 + ^
STACK CFI 3dac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3db4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3dcc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3dd4 38 .cfa: sp 0 + .ra: x30
STACK CFI 3ddc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3de4 x19: .cfa -16 + ^
STACK CFI 3e04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3e10 d8 .cfa: sp 0 + .ra: x30
STACK CFI 3e18 .cfa: sp 48 +
STACK CFI 3e24 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e2c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e84 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3ef0 150 .cfa: sp 0 + .ra: x30
STACK CFI 3ef8 .cfa: sp 64 +
STACK CFI 3f04 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3f0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f7c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3fe0 x21: .cfa -16 + ^
STACK CFI 4034 x21: x21
STACK CFI 403c x21: .cfa -16 + ^
STACK CFI INIT 4040 168 .cfa: sp 0 + .ra: x30
STACK CFI 4048 .cfa: sp 48 +
STACK CFI 4054 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 405c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 410c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 41b0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 41b8 .cfa: sp 48 +
STACK CFI 41c4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4230 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4294 8c .cfa: sp 0 + .ra: x30
STACK CFI 429c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42a4 x19: .cfa -16 + ^
STACK CFI 42e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 42ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4318 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4320 158 .cfa: sp 0 + .ra: x30
STACK CFI 4328 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4330 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 433c x21: .cfa -16 + ^
STACK CFI 43c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 43d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4408 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4438 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4440 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4480 38 .cfa: sp 0 + .ra: x30
STACK CFI 4488 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4490 x19: .cfa -16 + ^
STACK CFI 44b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 44c0 20 .cfa: sp 0 + .ra: x30
STACK CFI 44c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 44d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 44e0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 44e8 .cfa: sp 96 +
STACK CFI 44f4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 44fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4508 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4590 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
