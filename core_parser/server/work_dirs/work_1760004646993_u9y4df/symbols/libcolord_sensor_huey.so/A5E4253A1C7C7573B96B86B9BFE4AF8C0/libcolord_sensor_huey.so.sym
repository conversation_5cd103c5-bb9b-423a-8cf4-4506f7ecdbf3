MODULE Linux arm64 A5E4253A1C7C7573B96B86B9BFE4AF8C0 libcolord_sensor_huey.so
INFO CODE_ID 3A25E4A57C1C7375B96B86B9BFE4AF8C2CE392A3
PUBLIC 2974 0 cd_sensor_get_sample_async
PUBLIC 2a84 0 cd_sensor_get_sample_finish
PUBLIC 2b00 0 cd_sensor_lock_async
PUBLIC 2bd4 0 cd_sensor_lock_finish
PUBLIC 2c50 0 cd_sensor_unlock_async
PUBLIC 2d24 0 cd_sensor_unlock_finish
PUBLIC 2da0 0 huey_ctx_get_type
PUBLIC 2ec0 0 huey_ctx_get_device
PUBLIC 2f50 0 huey_ctx_set_device
PUBLIC 2ff4 0 huey_ctx_get_calibration_lcd
PUBLIC 3080 0 huey_ctx_get_calibration_crt
PUBLIC 3110 0 huey_ctx_get_calibration_value
PUBLIC 31a4 0 huey_ctx_get_dark_offset
PUBLIC 3230 0 huey_ctx_get_unlock_string
PUBLIC 32c0 0 huey_ctx_new
PUBLIC 32e0 0 cd_sensor_coldplug
PUBLIC 3380 0 huey_device_send_data
PUBLIC 3824 0 huey_ctx_take_sample
PUBLIC 3e40 0 huey_device_get_status
PUBLIC 3fb0 0 huey_device_unlock
PUBLIC 4174 0 huey_device_set_leds
PUBLIC 42a0 0 huey_device_get_ambient
PUBLIC 44f0 0 huey_device_read_register_byte
PUBLIC 4630 0 cd_sensor_dump_device
PUBLIC 47d0 0 huey_device_read_register_string
PUBLIC 48f0 0 huey_device_get_unlock_string
PUBLIC 4a30 0 huey_device_read_register_word
PUBLIC 4ba0 0 huey_device_get_serial_number
PUBLIC 4cb4 0 huey_device_read_register_float
PUBLIC 4de0 0 huey_device_read_register_vector
PUBLIC 4f50 0 huey_device_read_register_matrix
PUBLIC 50c0 0 huey_ctx_setup
PUBLIC 54a0 0 huey_rc_to_string
PUBLIC 5544 0 huey_cmd_code_to_string
STACK CFI INIT 24b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2520 48 .cfa: sp 0 + .ra: x30
STACK CFI 2524 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 252c x19: .cfa -16 + ^
STACK CFI 2564 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2570 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2580 3c .cfa: sp 0 + .ra: x30
STACK CFI 2588 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2590 x19: .cfa -16 + ^
STACK CFI 25b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 25c0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 25c8 .cfa: sp 48 +
STACK CFI 25d4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 25dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2670 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 26a4 98 .cfa: sp 0 + .ra: x30
STACK CFI 26ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 26b4 x19: .cfa -16 + ^
STACK CFI 2724 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 272c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2740 70 .cfa: sp 0 + .ra: x30
STACK CFI 2748 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2758 x19: .cfa -16 + ^
STACK CFI 27a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 27b0 38 .cfa: sp 0 + .ra: x30
STACK CFI 27bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27c8 x19: .cfa -16 + ^
STACK CFI 27e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 27f0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 27f8 .cfa: sp 64 +
STACK CFI 2800 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2808 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2828 x21: .cfa -16 + ^
STACK CFI 2884 x21: x21
STACK CFI 2888 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2890 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 28a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 28b0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 28b8 .cfa: sp 64 +
STACK CFI 28c0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 28c8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 28d0 x21: .cfa -16 + ^
STACK CFI 2944 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 294c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 296c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2974 110 .cfa: sp 0 + .ra: x30
STACK CFI 297c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2984 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2990 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 299c x23: .cfa -16 + ^
STACK CFI 2a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2a28 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2a4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2a54 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2a70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2a84 74 .cfa: sp 0 + .ra: x30
STACK CFI 2a8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a94 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2abc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ac4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2af0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2b00 d4 .cfa: sp 0 + .ra: x30
STACK CFI 2b08 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b10 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2b1c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2b88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2b90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2b9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2ba4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2bb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2bd4 74 .cfa: sp 0 + .ra: x30
STACK CFI 2bdc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2be4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2c40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2c50 d4 .cfa: sp 0 + .ra: x30
STACK CFI 2c58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2c60 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2c6c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2ce0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2cec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2cf4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2d08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2d24 74 .cfa: sp 0 + .ra: x30
STACK CFI 2d2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2d34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2d5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d64 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2d90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2da0 70 .cfa: sp 0 + .ra: x30
STACK CFI 2da8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2db0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ddc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2e08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2e10 b0 .cfa: sp 0 + .ra: x30
STACK CFI 2e18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2e20 x21: .cfa -16 + ^
STACK CFI 2e2c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2e80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2e90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2ea4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2ec0 88 .cfa: sp 0 + .ra: x30
STACK CFI 2ec8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ed4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2f20 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2f50 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2f58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2f64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2f70 x21: .cfa -16 + ^
STACK CFI 2fbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2fc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2fd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2ff4 88 .cfa: sp 0 + .ra: x30
STACK CFI 2ffc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3008 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 304c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3054 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3080 88 .cfa: sp 0 + .ra: x30
STACK CFI 3088 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3094 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 30d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 30e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3110 94 .cfa: sp 0 + .ra: x30
STACK CFI 3118 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3124 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3170 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 319c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 31a4 88 .cfa: sp 0 + .ra: x30
STACK CFI 31ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 31b8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 31fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3204 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3230 88 .cfa: sp 0 + .ra: x30
STACK CFI 3238 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3244 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3290 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 32c0 20 .cfa: sp 0 + .ra: x30
STACK CFI 32c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 32d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 32e0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 32e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 32fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3380 320 .cfa: sp 0 + .ra: x30
STACK CFI 3388 .cfa: sp 128 +
STACK CFI 338c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3394 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 33a0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 33ac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 33b8 x25: .cfa -16 + ^
STACK CFI 3504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 350c .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 36a0 184 .cfa: sp 0 + .ra: x30
STACK CFI 36a8 .cfa: sp 112 +
STACK CFI 36b8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 36c4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 36d0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 36f0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3778 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3780 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 3784 x25: .cfa -16 + ^
STACK CFI 37fc x25: x25
STACK CFI 3800 x25: .cfa -16 + ^
STACK CFI 380c x25: x25
STACK CFI 3820 x25: .cfa -16 + ^
STACK CFI INIT 3824 518 .cfa: sp 0 + .ra: x30
STACK CFI 382c .cfa: sp 192 +
STACK CFI 383c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3844 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3850 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3878 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3880 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 38ec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3b14 x23: x23 x24: x24
STACK CFI 3b18 x25: x25 x26: x26
STACK CFI 3b1c x27: x27 x28: x28
STACK CFI 3b20 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3b40 x23: x23 x24: x24
STACK CFI 3b44 x27: x27 x28: x28
STACK CFI 3b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3b7c .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 3c50 x23: x23 x24: x24
STACK CFI 3c54 x25: x25 x26: x26
STACK CFI 3c58 x27: x27 x28: x28
STACK CFI 3c5c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3c74 x25: x25 x26: x26
STACK CFI 3c98 x23: x23 x24: x24
STACK CFI 3c9c x27: x27 x28: x28
STACK CFI 3ca0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3ca4 x23: x23 x24: x24
STACK CFI 3ca8 x27: x27 x28: x28
STACK CFI 3ccc x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3d2c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 3d30 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3d34 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3d38 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 3d40 f8 .cfa: sp 0 + .ra: x30
STACK CFI 3d48 .cfa: sp 64 +
STACK CFI 3d54 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d68 x21: .cfa -16 + ^
STACK CFI 3dfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3e04 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3e40 168 .cfa: sp 0 + .ra: x30
STACK CFI 3e48 .cfa: sp 80 +
STACK CFI 3e54 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3e5c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3f1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3f24 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3fb0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 3fb8 .cfa: sp 80 +
STACK CFI 3fc4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3fcc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3fd8 x21: .cfa -16 + ^
STACK CFI 40f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 40f8 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4174 124 .cfa: sp 0 + .ra: x30
STACK CFI 417c .cfa: sp 64 +
STACK CFI 418c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4194 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 423c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4244 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 42a0 13c .cfa: sp 0 + .ra: x30
STACK CFI 42a8 .cfa: sp 64 +
STACK CFI 42b4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 436c .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 43e0 10c .cfa: sp 0 + .ra: x30
STACK CFI 43e8 .cfa: sp 96 +
STACK CFI 43f4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 43fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4404 x21: .cfa -16 + ^
STACK CFI 44b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 44b8 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 44f0 140 .cfa: sp 0 + .ra: x30
STACK CFI 44f8 .cfa: sp 80 +
STACK CFI 4504 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 450c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4518 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 45d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 45dc .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4630 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 4638 .cfa: sp 80 +
STACK CFI 4644 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 464c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4654 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 465c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 47bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 47c4 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 47d0 120 .cfa: sp 0 + .ra: x30
STACK CFI 47d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 47e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 47e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 47f4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 488c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 48e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 48f0 138 .cfa: sp 0 + .ra: x30
STACK CFI 48f8 .cfa: sp 64 +
STACK CFI 4904 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 490c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4934 x21: .cfa -16 + ^
STACK CFI 498c x21: x21
STACK CFI 49b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 49bc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 49c0 x21: x21
STACK CFI 49c8 x21: .cfa -16 + ^
STACK CFI 49ec x21: x21
STACK CFI 49f4 x21: .cfa -16 + ^
STACK CFI 49f8 x21: x21
STACK CFI 4a24 x21: .cfa -16 + ^
STACK CFI INIT 4a30 16c .cfa: sp 0 + .ra: x30
STACK CFI 4a38 .cfa: sp 96 +
STACK CFI 4a44 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4a4c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4a58 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4a60 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4ab0 x25: .cfa -16 + ^
STACK CFI 4af4 x25: x25
STACK CFI 4b30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4b38 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4b88 x25: .cfa -16 + ^
STACK CFI 4b8c x25: x25
STACK CFI 4b98 x25: .cfa -16 + ^
STACK CFI INIT 4ba0 114 .cfa: sp 0 + .ra: x30
STACK CFI 4ba8 .cfa: sp 48 +
STACK CFI 4bb4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4bbc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4c58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4c60 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4cb4 124 .cfa: sp 0 + .ra: x30
STACK CFI 4cbc .cfa: sp 64 +
STACK CFI 4cc8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4cd0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4cdc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4d7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4d84 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4de0 168 .cfa: sp 0 + .ra: x30
STACK CFI 4de8 .cfa: sp 80 +
STACK CFI 4df4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4dfc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4e08 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4e60 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4eac x23: x23 x24: x24
STACK CFI 4edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4ee4 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4f34 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4f38 x23: x23 x24: x24
STACK CFI 4f44 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 4f50 16c .cfa: sp 0 + .ra: x30
STACK CFI 4f58 .cfa: sp 80 +
STACK CFI 4f64 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4f6c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4f78 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4fcc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5020 x23: x23 x24: x24
STACK CFI 5050 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5058 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 50a8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 50ac x23: x23 x24: x24
STACK CFI 50b8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 50c0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 50c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 50d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 50e0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 516c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5278 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5280 218 .cfa: sp 0 + .ra: x30
STACK CFI 5288 .cfa: sp 96 +
STACK CFI 5294 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 529c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 52a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 52ac x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 545c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 54a0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 54a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 54e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 54f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 54f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5504 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5508 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5518 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 551c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 552c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5544 174 .cfa: sp 0 + .ra: x30
STACK CFI 554c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5568 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5570 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
