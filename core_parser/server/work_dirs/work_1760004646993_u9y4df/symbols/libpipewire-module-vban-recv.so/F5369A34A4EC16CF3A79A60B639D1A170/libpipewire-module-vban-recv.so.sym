MODULE Linux arm64 F5369A34A4EC16CF3A79A60B639D1A170 libpipewire-module-vban-recv.so
INFO CODE_ID 349A36F5ECA4CF163A79A60B639D1A17D78A8F0A
PUBLIC aef0 0 pipewire__module_init
STACK CFI INIT 2a40 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2a70 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2ab0 48 .cfa: sp 0 + .ra: x30
STACK CFI 2ab4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2abc x19: .cfa -16 + ^
STACK CFI 2af4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2b00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b10 1c .cfa: sp 0 + .ra: x30
STACK CFI 2b18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2b30 340 .cfa: sp 0 + .ra: x30
STACK CFI 2b38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c30 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2d68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2d74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2dc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2dcc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2e44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2e4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2e70 50 .cfa: sp 0 + .ra: x30
STACK CFI 2e78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2e90 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2e98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2ea4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2eac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2eb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2ec0 50 .cfa: sp 0 + .ra: x30
STACK CFI 2ec8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ed0 x19: .cfa -16 + ^
STACK CFI 2f08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2f10 12c .cfa: sp 0 + .ra: x30
STACK CFI 2f18 .cfa: sp 96 +
STACK CFI 2f20 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f28 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3038 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3040 f8 .cfa: sp 0 + .ra: x30
STACK CFI 3048 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3054 x19: .cfa -16 + ^
STACK CFI 3090 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3098 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3140 30c .cfa: sp 0 + .ra: x30
STACK CFI 3148 .cfa: sp 96 +
STACK CFI 3154 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 315c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 31dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3200 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3250 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3450 54 .cfa: sp 0 + .ra: x30
STACK CFI 3458 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3460 x19: .cfa -16 + ^
STACK CFI 349c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 34a4 864 .cfa: sp 0 + .ra: x30
STACK CFI 34ac .cfa: sp 176 +
STACK CFI 34b8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 34c0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3520 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3528 .cfa: sp 176 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 3578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3580 .cfa: sp 176 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 3640 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3644 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3648 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3750 x21: x21 x22: x22
STACK CFI 3754 x23: x23 x24: x24
STACK CFI 3758 x25: x25 x26: x26
STACK CFI 375c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 37ec x21: x21 x22: x22
STACK CFI 37f0 x23: x23 x24: x24
STACK CFI 37f4 x25: x25 x26: x26
STACK CFI 37f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 38d0 x21: x21 x22: x22
STACK CFI 38d4 x23: x23 x24: x24
STACK CFI 38d8 x25: x25 x26: x26
STACK CFI 38dc x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 397c x21: x21 x22: x22
STACK CFI 3984 x23: x23 x24: x24
STACK CFI 398c x25: x25 x26: x26
STACK CFI 3994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 39b0 .cfa: sp 176 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 39e0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 3ce0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3ce4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3ce8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3cec x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 3d10 200 .cfa: sp 0 + .ra: x30
STACK CFI 3d18 .cfa: sp 112 +
STACK CFI 3d1c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d24 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d34 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3efc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3f04 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3f10 50 .cfa: sp 0 + .ra: x30
STACK CFI 3f18 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f20 x19: .cfa -16 + ^
STACK CFI 3f58 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3f60 104 .cfa: sp 0 + .ra: x30
STACK CFI 3f68 .cfa: sp 96 +
STACK CFI 3f6c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3f74 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3f80 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3fbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3fc4 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 405c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4064 180 .cfa: sp 0 + .ra: x30
STACK CFI 406c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4078 .cfa: sp 2096 + x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4100 .cfa: sp 32 +
STACK CFI 4108 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4110 .cfa: sp 2096 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 41e4 28 .cfa: sp 0 + .ra: x30
STACK CFI 41ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4200 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4204 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4210 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 4218 .cfa: sp 64 +
STACK CFI 421c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4224 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4238 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 42dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 42e4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 43d4 2b28 .cfa: sp 0 + .ra: x30
STACK CFI 43dc .cfa: sp 480 +
STACK CFI 43f0 .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 4408 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 4410 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 4428 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 4b80 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4b88 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 6f00 58c .cfa: sp 0 + .ra: x30
STACK CFI 6f08 .cfa: sp 240 +
STACK CFI 6f14 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6f1c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6f3c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6f44 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6fec x21: x21 x22: x22
STACK CFI 6ff0 x23: x23 x24: x24
STACK CFI 6ffc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7004 .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 7008 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 708c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 71d0 x27: x27 x28: x28
STACK CFI 71dc x25: x25 x26: x26
STACK CFI 71e0 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 7230 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7238 .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 7250 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7258 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 729c x27: x27 x28: x28
STACK CFI 72b4 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 72e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7308 .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 732c x27: x27 x28: x28
STACK CFI 735c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7448 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 744c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7450 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7454 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7458 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 7490 4d8 .cfa: sp 0 + .ra: x30
STACK CFI 7498 .cfa: sp 160 +
STACK CFI 749c .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 74a4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 74b8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 74c0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 74c4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 74c8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 751c v8: .cfa -48 + ^
STACK CFI 766c v8: v8
STACK CFI 7694 x21: x21 x22: x22
STACK CFI 7698 x23: x23 x24: x24
STACK CFI 769c x25: x25 x26: x26
STACK CFI 76a0 x27: x27 x28: x28
STACK CFI 76ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 76b4 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 76c4 v8: .cfa -48 + ^
STACK CFI 7708 v8: v8
STACK CFI 7790 v8: .cfa -48 + ^
STACK CFI 77dc v8: v8
STACK CFI 77f4 v8: .cfa -48 + ^
STACK CFI 780c v8: v8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7840 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7848 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 7860 v8: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 788c v8: v8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 78a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 78c4 .cfa: sp 160 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 7970 5c0 .cfa: sp 0 + .ra: x30
STACK CFI 7978 .cfa: sp 304 +
STACK CFI 7984 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 798c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 79ac x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 79b8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 79c0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 79c4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7c6c x19: x19 x20: x20
STACK CFI 7c70 x21: x21 x22: x22
STACK CFI 7c74 x25: x25 x26: x26
STACK CFI 7c78 x27: x27 x28: x28
STACK CFI 7ca0 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 7ca8 .cfa: sp 304 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 7d48 x19: x19 x20: x20
STACK CFI 7d4c x21: x21 x22: x22
STACK CFI 7d50 x25: x25 x26: x26
STACK CFI 7d54 x27: x27 x28: x28
STACK CFI 7d58 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7e38 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7e90 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 7eb4 .cfa: sp 304 + .ra: .cfa -88 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 7ecc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7f1c x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7f20 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7f24 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7f28 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7f2c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 7f30 6d0 .cfa: sp 0 + .ra: x30
STACK CFI 7f38 .cfa: sp 288 +
STACK CFI 7f44 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7f4c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7f70 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7f78 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7f7c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7f80 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8050 x21: x21 x22: x22
STACK CFI 8054 x23: x23 x24: x24
STACK CFI 8058 x25: x25 x26: x26
STACK CFI 805c x27: x27 x28: x28
STACK CFI 8084 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 808c .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 80ac x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8128 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 8140 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 85ec x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 85f0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 85f4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 85f8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 85fc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 8600 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 8608 .cfa: sp 448 +
STACK CFI 8618 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 862c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8634 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8644 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 87b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 87b8 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8ac4 1654 .cfa: sp 0 + .ra: x30
STACK CFI 8acc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 8adc .cfa: sp 1328 + x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 8b18 x23: .cfa -80 + ^
STACK CFI 8b1c x24: .cfa -72 + ^
STACK CFI 8b24 x25: .cfa -64 + ^
STACK CFI 8b2c x26: .cfa -56 + ^
STACK CFI 8b34 x27: .cfa -48 + ^
STACK CFI 8b3c x28: .cfa -40 + ^
STACK CFI 8c48 v8: .cfa -32 + ^
STACK CFI 8c50 v9: .cfa -24 + ^
STACK CFI 8c58 v10: .cfa -16 + ^
STACK CFI 8d48 v10: v10 v8: v8 v9: v9
STACK CFI 8f3c v8: .cfa -32 + ^
STACK CFI 8f44 v9: .cfa -24 + ^
STACK CFI 8f4c v10: .cfa -16 + ^
STACK CFI 9330 x23: x23
STACK CFI 9334 x24: x24
STACK CFI 9338 x25: x25
STACK CFI 933c x26: x26
STACK CFI 9340 x27: x27
STACK CFI 9344 x28: x28
STACK CFI 9348 v8: v8
STACK CFI 934c v9: v9
STACK CFI 9350 v10: v10
STACK CFI 9384 .cfa: sp 128 +
STACK CFI 9394 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 939c .cfa: sp 1328 + .ra: .cfa -120 + ^ v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 95c4 x23: x23
STACK CFI 95c8 x24: x24
STACK CFI 95cc x25: x25
STACK CFI 95d0 x26: x26
STACK CFI 95d4 x27: x27
STACK CFI 95d8 x28: x28
STACK CFI 95dc v8: v8
STACK CFI 95e0 v9: v9
STACK CFI 95e4 v10: v10
STACK CFI 95e8 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 95f0 v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 9814 v10: v10 v8: v8 v9: v9
STACK CFI 986c x23: x23
STACK CFI 9874 x24: x24
STACK CFI 9878 x25: x25
STACK CFI 987c x26: x26
STACK CFI 9880 x27: x27
STACK CFI 9884 x28: x28
STACK CFI 9888 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 98fc v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 99c8 v10: v10 v8: v8 v9: v9
STACK CFI 99e0 v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 9a88 v10: v10 v8: v8 v9: v9
STACK CFI 9ae4 v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 9c3c v10: v10 v8: v8 v9: v9
STACK CFI 9c54 v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 9ef4 v10: v10 v8: v8 v9: v9
STACK CFI 9f24 v10: .cfa -16 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI a0e4 v10: v10 v8: v8 v9: v9 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a0f4 x23: .cfa -80 + ^
STACK CFI a0f8 x24: .cfa -72 + ^
STACK CFI a0fc x25: .cfa -64 + ^
STACK CFI a100 x26: .cfa -56 + ^
STACK CFI a104 x27: .cfa -48 + ^
STACK CFI a108 x28: .cfa -40 + ^
STACK CFI a10c v8: .cfa -32 + ^
STACK CFI a110 v9: .cfa -24 + ^
STACK CFI a114 v10: .cfa -16 + ^
STACK CFI INIT a120 178 .cfa: sp 0 + .ra: x30
STACK CFI a128 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a138 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a144 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a14c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a15c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a168 .cfa: sp 624 + x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a224 .cfa: sp 96 +
STACK CFI a23c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a244 .cfa: sp 624 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT a2a0 5a4 .cfa: sp 0 + .ra: x30
STACK CFI a2a8 .cfa: sp 160 +
STACK CFI a2ac .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI a2b4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI a2c4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI a2d0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI a324 x19: x19 x20: x20
STACK CFI a32c x23: x23 x24: x24
STACK CFI a334 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI a338 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI a33c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI a340 v8: .cfa -48 + ^
STACK CFI a4c8 x19: x19 x20: x20
STACK CFI a4cc x21: x21 x22: x22
STACK CFI a4d0 x23: x23 x24: x24
STACK CFI a4d4 x27: x27 x28: x28
STACK CFI a4d8 v8: v8
STACK CFI a4e8 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI a4f0 .cfa: sp 160 + .ra: .cfa -136 + ^ v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI a6ac v8: v8 x21: x21 x22: x22 x27: x27 x28: x28
STACK CFI a6c4 v8: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI a790 v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI a808 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI a834 v8: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI a838 x21: x21 x22: x22
STACK CFI a83c x27: x27 x28: x28
STACK CFI a840 v8: v8
STACK CFI INIT a844 6a8 .cfa: sp 0 + .ra: x30
STACK CFI a84c .cfa: sp 240 +
STACK CFI a85c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a87c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a884 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a8d8 x19: x19 x20: x20
STACK CFI a8e0 x27: x27 x28: x28
STACK CFI a8e8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a8ec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a8f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a8f4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI abdc x19: x19 x20: x20
STACK CFI abe0 x21: x21 x22: x22
STACK CFI abe4 x23: x23 x24: x24
STACK CFI abe8 x25: x25 x26: x26
STACK CFI abec x27: x27 x28: x28
STACK CFI ac14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ac1c .cfa: sp 240 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI addc x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI ae28 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ae40 x19: x19 x20: x20 x27: x27 x28: x28
STACK CFI ae44 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ae48 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ae4c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ae50 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ae54 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ae84 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI aeb0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI aeb4 x21: x21 x22: x22
STACK CFI aeb8 x23: x23 x24: x24
STACK CFI aebc x25: x25 x26: x26
STACK CFI INIT aef0 ca8 .cfa: sp 0 + .ra: x30
STACK CFI aef8 .cfa: sp 160 +
STACK CFI af04 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI af0c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI af18 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI b000 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b008 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI b040 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b048 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b478 x25: x25 x26: x26
STACK CFI b480 x27: x27 x28: x28
STACK CFI b484 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b66c x25: x25 x26: x26
STACK CFI b674 x27: x27 x28: x28
STACK CFI b678 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b744 x25: x25 x26: x26
STACK CFI b74c x27: x27 x28: x28
STACK CFI b750 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b768 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI b798 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b994 x25: x25 x26: x26
STACK CFI b998 x27: x27 x28: x28
STACK CFI b99c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ba38 x25: x25 x26: x26
STACK CFI ba3c x27: x27 x28: x28
STACK CFI ba40 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI baa0 x25: x25 x26: x26
STACK CFI baa4 x27: x27 x28: x28
STACK CFI baa8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI bb74 x25: x25 x26: x26
STACK CFI bb78 x27: x27 x28: x28
STACK CFI bb90 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI bb94 x27: .cfa -16 + ^ x28: .cfa -8 + ^
