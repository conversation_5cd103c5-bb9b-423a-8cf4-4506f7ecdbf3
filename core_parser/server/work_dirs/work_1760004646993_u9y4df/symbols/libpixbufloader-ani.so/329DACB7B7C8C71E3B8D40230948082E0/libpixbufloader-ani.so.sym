MODULE Linux arm64 329DACB7B7C8C71E3B8D40230948082E0 libpixbufloader-ani.so
INFO CODE_ID B7AC9D32C8B71EC73B8D40230948082E78B08403
PUBLIC 28d0 0 fill_vtable
PUBLIC 2910 0 fill_info
STACK CFI INIT 1240 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1270 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 12b0 48 .cfa: sp 0 + .ra: x30
STACK CFI 12b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12bc x19: .cfa -16 + ^
STACK CFI 12f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1300 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1310 24 .cfa: sp 0 + .ra: x30
STACK CFI 1318 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1324 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1334 24 .cfa: sp 0 + .ra: x30
STACK CFI 133c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1350 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1360 30 .cfa: sp 0 + .ra: x30
STACK CFI 1368 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1388 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1390 18 .cfa: sp 0 + .ra: x30
STACK CFI 1398 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 13a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 13b0 38 .cfa: sp 0 + .ra: x30
STACK CFI 13b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 13d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 13f0 6c .cfa: sp 0 + .ra: x30
STACK CFI 13f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1438 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1440 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1448 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1454 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1460 5c .cfa: sp 0 + .ra: x30
STACK CFI 1468 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1494 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 14ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14c0 18 .cfa: sp 0 + .ra: x30
STACK CFI 14c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14e0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 14e8 .cfa: sp 64 +
STACK CFI 14f8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1500 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 150c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1584 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 158c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1590 84 .cfa: sp 0 + .ra: x30
STACK CFI 1598 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15a0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1604 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1614 40 .cfa: sp 0 + .ra: x30
STACK CFI 161c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1624 x19: .cfa -16 + ^
STACK CFI 1644 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1654 6c .cfa: sp 0 + .ra: x30
STACK CFI 1660 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1668 x19: .cfa -16 + ^
STACK CFI 16b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 16c0 1dc .cfa: sp 0 + .ra: x30
STACK CFI 16c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 16d0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 16e4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1784 x21: x21 x22: x22
STACK CFI 178c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1794 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 17d8 x23: .cfa -16 + ^
STACK CFI 1860 x23: x23
STACK CFI 1864 x23: .cfa -16 + ^
STACK CFI INIT 18a0 60 .cfa: sp 0 + .ra: x30
STACK CFI 18a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 18b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 18bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 18c8 x23: .cfa -16 + ^
STACK CFI 18f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 1900 140 .cfa: sp 0 + .ra: x30
STACK CFI 1908 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1918 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1988 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1a40 114 .cfa: sp 0 + .ra: x30
STACK CFI 1b24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1b54 ac .cfa: sp 0 + .ra: x30
STACK CFI 1b5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1b64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1b8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1b9c x21: .cfa -16 + ^
STACK CFI 1bd4 x21: x21
STACK CFI INIT 1c00 80 .cfa: sp 0 + .ra: x30
STACK CFI 1c08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c10 x19: .cfa -16 + ^
STACK CFI 1c68 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1c70 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1c80 80 .cfa: sp 0 + .ra: x30
STACK CFI 1c88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c90 x19: .cfa -16 + ^
STACK CFI 1ce8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1cf0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1d00 58 .cfa: sp 0 + .ra: x30
STACK CFI 1d08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d10 x19: .cfa -16 + ^
STACK CFI 1d38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1d60 a70 .cfa: sp 0 + .ra: x30
STACK CFI 1d68 .cfa: sp 144 +
STACK CFI 1d6c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1d74 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1d80 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1db4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1de8 x23: x23 x24: x24
STACK CFI 1edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1ee4 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI 1ee8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1ef4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1fec x23: x23 x24: x24
STACK CFI 1ff0 x25: x25 x26: x26
STACK CFI 1ff4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 20e0 x25: x25 x26: x26
STACK CFI 2104 x23: x23 x24: x24
STACK CFI 2108 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2150 x23: x23 x24: x24
STACK CFI 2158 x25: x25 x26: x26
STACK CFI 215c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 224c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 22a8 x27: x27 x28: x28
STACK CFI 23bc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2454 x27: x27 x28: x28
STACK CFI 2458 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 25b8 x27: x27 x28: x28
STACK CFI 25c8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2670 x27: x27 x28: x28
STACK CFI 26cc x25: x25 x26: x26
STACK CFI 2700 x23: x23 x24: x24
STACK CFI 2708 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 273c x27: x27 x28: x28
STACK CFI 2740 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 276c x27: x27 x28: x28
STACK CFI 2778 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2780 x27: x27 x28: x28
STACK CFI 278c x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 2790 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2794 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2798 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 279c x27: x27 x28: x28
STACK CFI INIT 27d0 58 .cfa: sp 0 + .ra: x30
STACK CFI 27d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27e0 x19: .cfa -16 + ^
STACK CFI 2808 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2830 a0 .cfa: sp 0 + .ra: x30
STACK CFI 2838 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2840 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 284c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 28a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 28b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 28d0 38 .cfa: sp 0 + .ra: x30
STACK CFI 28d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 28ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2910 5c .cfa: sp 0 + .ra: x30
STACK CFI 2918 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 292c .cfa: sp 0 + .ra: .ra x29: x29
