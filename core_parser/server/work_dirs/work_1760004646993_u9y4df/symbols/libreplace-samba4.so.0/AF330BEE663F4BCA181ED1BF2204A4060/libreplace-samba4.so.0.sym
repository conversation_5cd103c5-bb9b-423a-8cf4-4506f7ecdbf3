MODULE Linux arm64 AF330BEE663F4BCA181ED1BF2204A4060 libreplace-samba4.so.0
INFO CODE_ID EE0B33AF3F66CA4B181ED1BF2204A406207D63CA
PUBLIC 8f0 0 replace_dummy
PUBLIC 910 0 rep_strerror_r
PUBLIC 9a0 0 rep_memset_s
PUBLIC a04 0 rep_getprogname
PUBLIC a30 0 rep_openat2
STACK CFI INIT 820 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 850 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 890 48 .cfa: sp 0 + .ra: x30
STACK CFI 894 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 89c x19: .cfa -16 + ^
STACK CFI 8d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f0 18 .cfa: sp 0 + .ra: x30
STACK CFI 8f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 900 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 910 8c .cfa: sp 0 + .ra: x30
STACK CFI 918 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 920 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 928 x21: .cfa -16 + ^
STACK CFI 97c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 984 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 994 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9a0 64 .cfa: sp 0 + .ra: x30
STACK CFI 9d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9d8 x19: .cfa -16 + ^
STACK CFI 9ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a04 28 .cfa: sp 0 + .ra: x30
STACK CFI a18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a30 34 .cfa: sp 0 + .ra: x30
STACK CFI a38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a44 .cfa: sp 0 + .ra: .ra x29: x29
