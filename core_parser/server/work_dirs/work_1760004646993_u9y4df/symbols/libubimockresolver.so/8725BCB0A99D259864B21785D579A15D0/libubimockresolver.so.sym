MODULE Linux arm64 8725BCB0A99D259864B21785D579A15D0 libubimockresolver.so.0
INFO CODE_ID B0BC25879DA9982564B21785D579A15DE233C966
PUBLIC 1520 0 ubiquity_mock_resolver_get_type
PUBLIC 15d0 0 ubiquity_mock_resolver_new
STACK CFI INIT 1020 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1050 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1090 48 .cfa: sp 0 + .ra: x30
STACK CFI 1094 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 109c x19: .cfa -16 + ^
STACK CFI 10d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10f0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 10f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1100 x19: .cfa -16 + ^
STACK CFI 1198 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 11a0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 11b0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 11b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 11c4 x21: .cfa -16 + ^
STACK CFI 11cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1230 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1238 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1270 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1280 44 .cfa: sp 0 + .ra: x30
STACK CFI 1288 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1290 x19: .cfa -16 + ^
STACK CFI 12b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 12c4 58 .cfa: sp 0 + .ra: x30
STACK CFI 12cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12d4 x19: .cfa -16 + ^
STACK CFI 12fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1320 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1328 .cfa: sp 64 +
STACK CFI 132c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1334 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1350 x21: .cfa -16 + ^
STACK CFI 13a8 x21: x21
STACK CFI 13ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13b4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 13c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 13d4 cc .cfa: sp 0 + .ra: x30
STACK CFI 13dc .cfa: sp 64 +
STACK CFI 13e0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 13e8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1404 x21: .cfa -16 + ^
STACK CFI 145c x21: x21
STACK CFI 1460 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1468 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 14a0 80 .cfa: sp 0 + .ra: x30
STACK CFI 14a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 14b4 x19: .cfa -16 + ^
STACK CFI 14ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 14f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1518 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1520 70 .cfa: sp 0 + .ra: x30
STACK CFI 1528 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1530 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 155c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1588 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1590 3c .cfa: sp 0 + .ra: x30
STACK CFI 1598 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15a0 x19: .cfa -16 + ^
STACK CFI 15c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 15d0 20 .cfa: sp 0 + .ra: x30
STACK CFI 15d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15e4 .cfa: sp 0 + .ra: .ra x29: x29
