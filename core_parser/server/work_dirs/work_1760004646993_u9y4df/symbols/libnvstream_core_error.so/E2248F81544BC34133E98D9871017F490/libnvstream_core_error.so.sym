MODULE Linux arm64 E2248F81544BC34133E98D9871017F490 libnvstream_core_error.so
INFO CODE_ID 818F24E24B5441C333E98D9871017F49
PUBLIC 1688 0 _init
PUBLIC 1810 0 __static_initialization_and_destruction_0()
PUBLIC 2010 0 _GLOBAL__sub_I_error_nvsci_error.cpp
PUBLIC 2020 0 _GLOBAL__sub_I_error_nvsci_event.cpp
PUBLIC 21c0 0 call_weak_fn
PUBLIC 21e0 0 deregister_tm_clones
PUBLIC 2210 0 register_tm_clones
PUBLIC 2250 0 __do_global_dtors_aux
PUBLIC 22a0 0 frame_dummy
PUBLIC 22b0 0 linvs::error::NvSciErrorStr(int)
PUBLIC 23a0 0 std::unordered_map<int, char const*, std::hash<int>, std::equal_to<int>, std::allocator<std::pair<int const, char const*> > >::~unordered_map()
PUBLIC 2410 0 std::_Hashtable<int, std::pair<int const, char const*>, std::allocator<std::pair<int const, char const*> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::clear()
PUBLIC 2460 0 std::_Hashtable<int, std::pair<int const, char const*>, std::allocator<std::pair<int const, char const*> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_deallocate_buckets()
PUBLIC 2480 0 std::_Hashtable<int, std::pair<int const, char const*>, std::allocator<std::pair<int const, char const*> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC 25b0 0 std::_Hashtable<int, std::pair<int const, char const*>, std::allocator<std::pair<int const, char const*> >, std::__detail::_Select1st, std::equal_to<int>, std::hash<int>, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<false, false, true> >::_Hashtable<std::pair<int const, char const*> const*>(std::pair<int const, char const*> const*, std::pair<int const, char const*> const*, unsigned long, std::hash<int> const&, std::equal_to<int> const&, std::allocator<std::pair<int const, char const*> > const&, std::integral_constant<bool, true>)
PUBLIC 2900 0 linvs::error::NvSciEventStr(int)
PUBLIC 29f0 0 _fini
STACK CFI INIT 21e0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2210 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2250 48 .cfa: sp 0 + .ra: x30
STACK CFI 2254 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 225c x19: .cfa -16 + ^
STACK CFI 2294 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 22a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23a0 6c .cfa: sp 0 + .ra: x30
STACK CFI 23a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 23fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2400 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 22b0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 22d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 22f0 x19: .cfa -16 + ^
STACK CFI 2318 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2410 4c .cfa: sp 0 + .ra: x30
STACK CFI 2414 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 241c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2460 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2480 12c .cfa: sp 0 + .ra: x30
STACK CFI 2484 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2490 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2498 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 253c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2540 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 25b0 34c .cfa: sp 0 + .ra: x30
STACK CFI 25b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 25cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 25d4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 25e0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 25ec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 26d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 26dc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1810 7f8 .cfa: sp 0 + .ra: x30
STACK CFI 1814 .cfa: sp 1520 +
STACK CFI 1828 .ra: .cfa -1512 + ^ x29: .cfa -1520 + ^
STACK CFI 1834 x19: .cfa -1504 + ^ x20: .cfa -1496 + ^
STACK CFI 1ff8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2004 .cfa: sp 1520 + .ra: .cfa -1512 + ^ x19: .cfa -1504 + ^ x20: .cfa -1496 + ^ x29: .cfa -1520 + ^
STACK CFI INIT 2010 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2900 f0 .cfa: sp 0 + .ra: x30
STACK CFI 2920 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2940 x19: .cfa -16 + ^
STACK CFI 2968 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2020 1a0 .cfa: sp 0 + .ra: x30
STACK CFI 2024 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 2044 x19: .cfa -240 + ^
STACK CFI 21b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 21bc .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x29: .cfa -256 + ^
