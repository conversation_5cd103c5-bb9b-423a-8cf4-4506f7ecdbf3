MODULE Linux arm64 14A8675E6D5D94FD4D1D28018F91B5EE0 libcaca.so.0
INFO CODE_ID 5E67A8145D6DFD944D1D28018F91B5EE72441B36
PUBLIC 6d50 0 caca_get_display_driver_list
PUBLIC 6d60 0 caca_get_display_driver
PUBLIC 6d70 0 caca_get_canvas
PUBLIC 6d80 0 caca_get_version
PUBLIC 6d90 0 __caca0_sqrt
PUBLIC 6ea0 0 __caca0_get_feature
PUBLIC 6ef0 0 __caca0_get_feature_name
PUBLIC 6fc0 0 __caca0_get_color_name
PUBLIC 6ff0 0 caca_manage_canvas
PUBLIC 7040 0 caca_unmanage_canvas
PUBLIC 70a0 0 caca_get_canvas_width
PUBLIC 70b0 0 caca_get_canvas_height
PUBLIC 73c0 0 caca_set_display_driver
PUBLIC 7420 0 caca_get_canvas_chars
PUBLIC 7430 0 caca_get_canvas_attrs
PUBLIC 7440 0 caca_rand
PUBLIC 7580 0 caca_disable_dirty_rect
PUBLIC 75a0 0 caca_enable_dirty_rect
PUBLIC 75f0 0 caca_get_dirty_rect_count
PUBLIC 7600 0 caca_get_dirty_rect
PUBLIC 7680 0 caca_add_dirty_rect
PUBLIC 7f50 0 caca_set_canvas_size
PUBLIC 8060 0 caca_remove_dirty_rect
PUBLIC 80e0 0 caca_clear_dirty_rect_list
PUBLIC 80f4 0 caca_gotoxy
PUBLIC 8120 0 caca_wherex
PUBLIC 8140 0 caca_wherey
PUBLIC 8160 0 caca_get_char
PUBLIC 81a4 0 caca_clear_canvas
PUBLIC 8220 0 caca_set_canvas_handle
PUBLIC 8244 0 caca_get_canvas_handle_x
PUBLIC 8260 0 caca_get_canvas_handle_y
PUBLIC 8280 0 caca_blit
PUBLIC 8650 0 caca_invert
PUBLIC 86b0 0 caca_flip
PUBLIC 8920 0 caca_flop
PUBLIC 8b10 0 caca_rotate_180
PUBLIC 8da0 0 caca_rotate_left
PUBLIC 9114 0 caca_rotate_right
PUBLIC 9490 0 caca_stretch_left
PUBLIC 9710 0 caca_stretch_right
PUBLIC 9990 0 caca_utf8_to_utf32
PUBLIC 9a20 0 caca_utf32_to_utf8
PUBLIC 9ab4 0 caca_utf32_to_cp437
PUBLIC 9b40 0 caca_cp437_to_utf32
PUBLIC 9b94 0 caca_utf32_to_ascii
PUBLIC 9f20 0 caca_utf32_is_fullwidth
PUBLIC a000 0 caca_put_char
PUBLIC a980 0 caca_put_str
PUBLIC ab04 0 caca_vprintf
PUBLIC ac60 0 caca_printf
PUBLIC ad10 0 caca_get_attr
PUBLIC ad50 0 caca_set_attr
PUBLIC ad80 0 caca_unset_attr
PUBLIC ada0 0 caca_toggle_attr
PUBLIC adc0 0 caca_put_attr
PUBLIC aea0 0 caca_set_color_ansi
PUBLIC af10 0 caca_create_canvas
PUBLIC b120 0 caca_set_color_argb
PUBLIC b180 0 caca_attr_to_ansi
PUBLIC b1d0 0 caca_attr_to_ansi_fg
PUBLIC b1e0 0 caca_attr_to_ansi_bg
PUBLIC b1f0 0 caca_attr_to_rgb12_fg
PUBLIC b2a4 0 caca_attr_to_rgb12_bg
PUBLIC b354 0 caca_attr_to_argb64
PUBLIC b4d0 0 caca_draw_line
PUBLIC b550 0 caca_draw_polyline
PUBLIC b620 0 caca_draw_thin_line
PUBLIC b694 0 caca_draw_thin_polyline
PUBLIC b760 0 caca_draw_box
PUBLIC b810 0 caca_draw_thin_box
PUBLIC b824 0 caca_draw_cp437_box
PUBLIC b840 0 caca_fill_box
PUBLIC b930 0 caca_draw_circle
PUBLIC b9f0 0 caca_fill_ellipse
PUBLIC bc70 0 caca_draw_ellipse
PUBLIC be34 0 caca_draw_thin_ellipse
PUBLIC c050 0 caca_draw_triangle
PUBLIC c0f0 0 caca_draw_thin_triangle
PUBLIC c170 0 caca_fill_triangle
PUBLIC c3a4 0 caca_fill_triangle_textured
PUBLIC c9e4 0 caca_get_frame_count
PUBLIC c9f0 0 caca_set_frame
PUBLIC caa0 0 caca_get_frame_name
PUBLIC cac0 0 caca_set_frame_name
PUBLIC cb34 0 caca_create_frame
PUBLIC ccb0 0 caca_set_canvas_boundaries
PUBLIC ce84 0 caca_free_frame
PUBLIC d364 0 caca_create_dither
PUBLIC d690 0 caca_set_dither_palette
PUBLIC d760 0 caca_set_dither_brightness
PUBLIC d774 0 caca_get_dither_brightness
PUBLIC d780 0 caca_set_dither_gamma
PUBLIC d8c0 0 caca_get_dither_gamma
PUBLIC d8d0 0 caca_set_dither_contrast
PUBLIC d8e4 0 caca_get_dither_contrast
PUBLIC d8f0 0 caca_set_dither_antialias
PUBLIC d994 0 caca_get_dither_antialias_list
PUBLIC d9b0 0 caca_get_dither_antialias
PUBLIC d9c0 0 caca_set_dither_color
PUBLIC db40 0 caca_get_dither_color_list
PUBLIC db54 0 caca_get_dither_color
PUBLIC db60 0 caca_set_dither_charset
PUBLIC dc64 0 caca_get_dither_charset_list
PUBLIC dc80 0 caca_get_dither_charset
PUBLIC dc90 0 caca_set_dither_algorithm
PUBLIC de80 0 __caca0_set_feature
PUBLIC dfe4 0 __caca0_create_bitmap
PUBLIC e080 0 caca_get_dither_algorithm_list
PUBLIC e094 0 caca_get_dither_algorithm
PUBLIC e0a0 0 caca_dither_bitmap
PUBLIC e9f0 0 caca_free_dither
PUBLIC ea20 0 __caca0_free_bitmap
PUBLIC eab0 0 caca_load_font
PUBLIC ee40 0 caca_get_font_list
PUBLIC ee54 0 caca_get_font_width
PUBLIC ee60 0 caca_get_font_height
PUBLIC ee70 0 caca_get_font_blocks
PUBLIC ee80 0 caca_free_font
PUBLIC eec0 0 caca_render_canvas
PUBLIC f330 0 caca_file_open
PUBLIC f4d0 0 caca_file_close
PUBLIC f530 0 caca_file_tell
PUBLIC f550 0 caca_file_read
PUBLIC f5a0 0 caca_file_write
PUBLIC f600 0 caca_file_gets
PUBLIC f6d4 0 caca_file_eof
PUBLIC f6f0 0 caca_set_figfont_width
PUBLIC f724 0 caca_set_figfont_smush
PUBLIC f7e0 0 caca_put_figchar
PUBLIC fbf0 0 caca_flush_figlet
PUBLIC fd00 0 caca_set_display_title
PUBLIC fd34 0 caca_get_display_width
PUBLIC fd44 0 caca_get_display_height
PUBLIC fd54 0 caca_set_display_time
PUBLIC fd94 0 caca_get_display_time
PUBLIC fda0 0 caca_refresh_display
PUBLIC 10100 0 caca_set_cursor
PUBLIC 10140 0 caca_set_mouse
PUBLIC 101f0 0 caca_get_event
PUBLIC 10434 0 caca_get_mouse_x
PUBLIC 10464 0 caca_get_mouse_y
PUBLIC 10494 0 caca_get_event_type
PUBLIC 104a0 0 caca_get_event_key_ch
PUBLIC 104b0 0 caca_get_event_key_utf32
PUBLIC 104c0 0 caca_get_event_key_utf8
PUBLIC 104e0 0 caca_get_event_mouse_button
PUBLIC 104f0 0 caca_get_event_mouse_x
PUBLIC 10500 0 caca_get_event_mouse_y
PUBLIC 10510 0 __caca0_get_event
PUBLIC 10644 0 caca_get_event_resize_width
PUBLIC 10650 0 caca_get_event_resize_height
PUBLIC 10660 0 caca_getopt
PUBLIC 106d0 0 caca_import_canvas_from_memory
PUBLIC 10f50 0 caca_canvas_set_figfont
PUBLIC 11060 0 caca_free_canvas
PUBLIC 11120 0 caca_create_display_with_driver
PUBLIC 11270 0 caca_create_display
PUBLIC 11384 0 caca_conio_clreol
PUBLIC 114e0 0 caca_conio_clrscr
PUBLIC 11600 0 caca_conio_cprintf
PUBLIC 117d0 0 caca_conio_cputs
PUBLIC 11974 0 caca_conio_delay
PUBLIC 11be4 0 caca_conio_getch
PUBLIC 11d70 0 caca_conio_cgets
PUBLIC 11f64 0 caca_conio_getpass
PUBLIC 120b0 0 caca_conio_gotoxy
PUBLIC 121d0 0 caca_conio_kbhit
PUBLIC 12314 0 caca_conio_movetext
PUBLIC 12410 0 caca_conio_printf
PUBLIC 125e0 0 caca_conio_getche
PUBLIC 12610 0 caca_conio_putch
PUBLIC 126a0 0 caca_conio__setcursortype
PUBLIC 127e0 0 caca_conio_sleep
PUBLIC 12a50 0 caca_conio_textbackground
PUBLIC 12aa0 0 caca_conio_textcolor
PUBLIC 12af0 0 caca_conio_wherex
PUBLIC 12b20 0 caca_conio_wherey
PUBLIC 12b50 0 caca_conio_cscanf
PUBLIC 12c30 0 caca_conio_delline
PUBLIC 12d34 0 caca_conio_gettext
PUBLIC 12e14 0 caca_conio_gettextinfo
PUBLIC 12f14 0 caca_conio_highvideo
PUBLIC 13014 0 caca_conio_insline
PUBLIC 13114 0 caca_conio_lowvideo
PUBLIC 13214 0 caca_conio_normvideo
PUBLIC 13314 0 caca_conio_nosound
PUBLIC 13414 0 caca_conio_puttext
PUBLIC 134f4 0 caca_conio_sound
PUBLIC 135f4 0 caca_conio_textattr
PUBLIC 136f4 0 caca_conio_textmode
PUBLIC 137f4 0 caca_conio_ungetch
PUBLIC 13900 0 caca_conio_window
PUBLIC 13a04 0 caca_free_display
PUBLIC 13ab4 0 __caca0_init
PUBLIC 13b30 0 __caca0_end
PUBLIC 13b70 0 caca_import_canvas_from_file
PUBLIC 13c44 0 __caca0_load_sprite
PUBLIC 13ca0 0 caca_import_area_from_memory
PUBLIC 13d34 0 caca_import_area_from_file
PUBLIC 13dc0 0 caca_get_import_list
PUBLIC 13dd4 0 caca_export_memory
PUBLIC 179b0 0 caca_get_export_list
PUBLIC 17a60 0 caca_export_area_to_memory
STACK CFI INIT 62f0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6320 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6360 48 .cfa: sp 0 + .ra: x30
STACK CFI 6364 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 636c x19: .cfa -16 + ^
STACK CFI 63a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 63b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 63c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 63d0 9c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6470 144 .cfa: sp 0 + .ra: x30
STACK CFI INIT 65b4 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 65c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 65d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 65e0 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6620 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6640 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6664 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 66a0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 66c0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 66f0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6730 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6750 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6780 17c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6900 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6910 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6920 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6930 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6940 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6950 11c .cfa: sp 0 + .ra: x30
STACK CFI 6958 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 6970 .cfa: sp 560 + x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI 6a10 .cfa: sp 32 +
STACK CFI 6a18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 6a20 .cfa: sp 560 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI INIT 6a70 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 6a78 .cfa: sp 80 +
STACK CFI 6a88 .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^
STACK CFI 6a94 x21: .cfa -24 + ^
STACK CFI 6ad0 x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI 6b14 x22: x22 x23: x23
STACK CFI 6b40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 6b48 .cfa: sp 80 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI 6c18 x22: x22 x23: x23
STACK CFI 6c30 x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI 6c34 x22: x22 x23: x23
STACK CFI 6c3c x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI 6c58 x22: x22 x23: x23
STACK CFI 6c60 x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI INIT 6c64 e4 .cfa: sp 0 + .ra: x30
STACK CFI 6c6c .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^
STACK CFI 6c74 x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI 6c7c x22: .cfa -16 + ^
STACK CFI 6cf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 6cf8 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI 6d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 6d40 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI INIT 6d50 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d60 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d70 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d80 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d90 108 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ea0 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ef0 d0 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6fc0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ff0 48 .cfa: sp 0 + .ra: x30
STACK CFI 7018 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 7024 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 7040 5c .cfa: sp 0 + .ra: x30
STACK CFI 707c .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 7088 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 70a0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 70b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 70c0 300 .cfa: sp 0 + .ra: x30
STACK CFI 70c8 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 70d0 x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI 7260 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 7268 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI INIT 73c0 60 .cfa: sp 0 + .ra: x30
STACK CFI 73c8 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 73d0 x20: .cfa -16 + ^
STACK CFI 7400 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 7408 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 7420 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7430 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7440 130 .cfa: sp 0 + .ra: x30
STACK CFI 7448 .cfa: sp 80 +
STACK CFI 7454 x21: .cfa -24 + ^
STACK CFI 7464 .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^
STACK CFI 7480 v8: .cfa -8 + ^
STACK CFI 74dc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21
STACK CFI 74e4 .cfa: sp 80 + .ra: .cfa -48 + ^ v8: .cfa -8 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI 74e8 x22: .cfa -16 + ^
STACK CFI 7550 x22: x22
STACK CFI 7558 x22: .cfa -16 + ^
STACK CFI 7568 x22: x22
STACK CFI 756c x22: .cfa -16 + ^
STACK CFI INIT 7570 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7580 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 75a0 4c .cfa: sp 0 + .ra: x30
STACK CFI 75cc .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 75d8 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 75f0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7600 80 .cfa: sp 0 + .ra: x30
STACK CFI 7660 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 766c .cfa: sp 0 + .ra: .ra
STACK CFI INIT 7680 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 7688 .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^
STACK CFI 7690 x20: .cfa -48 + ^ x21: .cfa -40 + ^
STACK CFI 7698 x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI 76f4 x24: .cfa -16 + ^
STACK CFI 7900 x24: x24
STACK CFI 7910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 7918 .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^ x20: .cfa -48 + ^ x21: .cfa -40 + ^ x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI INIT 7930 61c .cfa: sp 0 + .ra: x30
STACK CFI 7938 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^
STACK CFI 793c x22: .cfa -80 + ^ x23: .cfa -72 + ^
STACK CFI 7948 x20: .cfa -96 + ^ x21: .cfa -88 + ^
STACK CFI 795c x24: .cfa -64 + ^ x25: .cfa -56 + ^
STACK CFI 796c x26: .cfa -48 + ^ x27: .cfa -40 + ^
STACK CFI 79c8 x28: .cfa -32 + ^
STACK CFI 7a40 x28: x28
STACK CFI 7c54 x26: x26 x27: x27
STACK CFI 7c70 x24: x24 x25: x25
STACK CFI 7c84 x20: x20 x21: x21
STACK CFI 7c88 .cfa: sp 0 + .ra: .ra x19: x19 x22: x22 x23: x23
STACK CFI 7c90 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^ x20: .cfa -96 + ^ x21: .cfa -88 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^
STACK CFI 7cf0 x28: .cfa -32 + ^
STACK CFI 7db8 x28: x28
STACK CFI 7e58 x28: .cfa -32 + ^
STACK CFI 7e60 x20: x20 x21: x21
STACK CFI 7e68 x24: x24 x25: x25
STACK CFI 7e6c x26: x26 x27: x27
STACK CFI 7e70 x28: x28
STACK CFI 7e80 .cfa: sp 0 + .ra: .ra x19: x19 x22: x22 x23: x23
STACK CFI 7e88 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^ x20: .cfa -96 + ^ x21: .cfa -88 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^
STACK CFI 7f14 x20: x20 x21: x21
STACK CFI 7f1c x24: x24 x25: x25
STACK CFI 7f20 x26: x26 x27: x27
STACK CFI 7f40 x20: .cfa -96 + ^ x21: .cfa -88 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^
STACK CFI INIT 7f50 88 .cfa: sp 0 + .ra: x30
STACK CFI 7f58 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 7f5c x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI 7fa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 7fa8 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI 7fc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 7fc8 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI INIT 7fe0 78 .cfa: sp 0 + .ra: x30
STACK CFI 7fe8 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 7ff4 x20: .cfa -16 + ^
STACK CFI 802c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 8034 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 8050 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 8060 80 .cfa: sp 0 + .ra: x30
STACK CFI 80c0 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 80cc .cfa: sp 0 + .ra: .ra
STACK CFI INIT 80e0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 80f4 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8120 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8140 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8160 44 .cfa: sp 0 + .ra: x30
STACK CFI INIT 81a4 7c .cfa: sp 0 + .ra: x30
STACK CFI 8208 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 8214 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 8220 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8244 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8260 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8280 3c8 .cfa: sp 0 + .ra: x30
STACK CFI 8288 .cfa: sp 160 + .ra: .cfa -160 + ^ x19: .cfa -152 + ^
STACK CFI 828c x22: .cfa -128 + ^ x23: .cfa -120 + ^
STACK CFI 8294 x24: .cfa -112 + ^ x25: .cfa -104 + ^
STACK CFI 829c x28: .cfa -80 + ^
STACK CFI 82c4 x20: .cfa -144 + ^ x21: .cfa -136 + ^
STACK CFI 831c x20: x20 x21: x21
STACK CFI 8330 .cfa: sp 0 + .ra: .ra x19: x19 x22: x22 x23: x23 x24: x24 x25: x25 x28: x28
STACK CFI 8338 .cfa: sp 160 + .ra: .cfa -160 + ^ x19: .cfa -152 + ^ x20: .cfa -144 + ^ x21: .cfa -136 + ^ x22: .cfa -128 + ^ x23: .cfa -120 + ^ x24: .cfa -112 + ^ x25: .cfa -104 + ^ x28: .cfa -80 + ^
STACK CFI 8360 x26: .cfa -96 + ^ x27: .cfa -88 + ^
STACK CFI 862c x26: x26 x27: x27
STACK CFI 8630 x20: x20 x21: x21
STACK CFI INIT 8650 60 .cfa: sp 0 + .ra: x30
STACK CFI 8698 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 86a4 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 86b0 270 .cfa: sp 0 + .ra: x30
STACK CFI 86b8 .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI 87f8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 8804 .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI 8914 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8920 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 8af8 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 8b04 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 8b10 28c .cfa: sp 0 + .ra: x30
STACK CFI 8d00 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 8d0c .cfa: sp 0 + .ra: .ra
STACK CFI INIT 8da0 374 .cfa: sp 0 + .ra: x30
STACK CFI 8da8 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^
STACK CFI 8dac x26: .cfa -48 + ^ x27: .cfa -40 + ^
STACK CFI 8db8 x22: .cfa -80 + ^ x23: .cfa -72 + ^
STACK CFI 8dc4 x24: .cfa -64 + ^ x25: .cfa -56 + ^
STACK CFI 8e20 x20: .cfa -96 + ^ x21: .cfa -88 + ^
STACK CFI 8e94 x28: .cfa -32 + ^
STACK CFI 8f64 x28: x28
STACK CFI 8ff8 x20: x20 x21: x21
STACK CFI 9000 x22: x22 x23: x23
STACK CFI 9004 x24: x24 x25: x25
STACK CFI 900c .cfa: sp 0 + .ra: .ra x19: x19 x26: x26 x27: x27
STACK CFI 9014 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^ x20: .cfa -96 + ^ x21: .cfa -88 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^ x28: .cfa -32 + ^
STACK CFI 9078 x28: x28
STACK CFI 907c x20: x20 x21: x21
STACK CFI 9084 x22: x22 x23: x23
STACK CFI 908c x24: x24 x25: x25
STACK CFI 90a0 .cfa: sp 0 + .ra: .ra x19: x19 x26: x26 x27: x27
STACK CFI 90a8 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^ x20: .cfa -96 + ^ x21: .cfa -88 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^
STACK CFI 90bc x20: x20 x21: x21
STACK CFI 90c4 x22: x22 x23: x23
STACK CFI 90c8 x24: x24 x25: x25
STACK CFI 90d0 .cfa: sp 0 + .ra: .ra x19: x19 x26: x26 x27: x27
STACK CFI 90d8 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^
STACK CFI 90ec x20: .cfa -96 + ^ x21: .cfa -88 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^
STACK CFI 9100 x20: x20 x21: x21
STACK CFI 9108 x22: x22 x23: x23
STACK CFI 910c x24: x24 x25: x25
STACK CFI INIT 9114 378 .cfa: sp 0 + .ra: x30
STACK CFI 911c .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^
STACK CFI 9120 x26: .cfa -48 + ^ x27: .cfa -40 + ^
STACK CFI 912c x22: .cfa -80 + ^ x23: .cfa -72 + ^
STACK CFI 9138 x24: .cfa -64 + ^ x25: .cfa -56 + ^
STACK CFI 919c x20: .cfa -96 + ^ x21: .cfa -88 + ^
STACK CFI 920c x28: .cfa -32 + ^
STACK CFI 92dc x28: x28
STACK CFI 9370 x20: x20 x21: x21
STACK CFI 9378 x22: x22 x23: x23
STACK CFI 937c x24: x24 x25: x25
STACK CFI 9384 .cfa: sp 0 + .ra: .ra x19: x19 x26: x26 x27: x27
STACK CFI 938c .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^ x20: .cfa -96 + ^ x21: .cfa -88 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^ x28: .cfa -32 + ^
STACK CFI 93f0 x28: x28
STACK CFI 93f4 x20: x20 x21: x21
STACK CFI 93fc x22: x22 x23: x23
STACK CFI 9404 x24: x24 x25: x25
STACK CFI 9418 .cfa: sp 0 + .ra: .ra x19: x19 x26: x26 x27: x27
STACK CFI 9420 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^ x20: .cfa -96 + ^ x21: .cfa -88 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^
STACK CFI 9434 x20: x20 x21: x21
STACK CFI 943c x22: x22 x23: x23
STACK CFI 9440 x24: x24 x25: x25
STACK CFI 9448 .cfa: sp 0 + .ra: .ra x19: x19 x26: x26 x27: x27
STACK CFI 9450 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^
STACK CFI 9464 x20: .cfa -96 + ^ x21: .cfa -88 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^
STACK CFI 9478 x20: x20 x21: x21
STACK CFI 9480 x22: x22 x23: x23
STACK CFI 9484 x24: x24 x25: x25
STACK CFI INIT 9490 278 .cfa: sp 0 + .ra: x30
STACK CFI 9498 .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^
STACK CFI 94a4 x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI 94b4 x24: .cfa -16 + ^ x25: .cfa -8 + ^
STACK CFI 9500 x20: .cfa -48 + ^ x21: .cfa -40 + ^
STACK CFI 9654 x20: x20 x21: x21
STACK CFI 965c x22: x22 x23: x23
STACK CFI 9660 x24: x24 x25: x25
STACK CFI 9664 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 966c .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^ x20: .cfa -48 + ^ x21: .cfa -40 + ^ x22: .cfa -32 + ^ x23: .cfa -24 + ^ x24: .cfa -16 + ^ x25: .cfa -8 + ^
STACK CFI 96a4 x20: x20 x21: x21
STACK CFI 96ac x22: x22 x23: x23
STACK CFI 96b4 x24: x24 x25: x25
STACK CFI 96c4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 96cc .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^
STACK CFI 96e0 x20: .cfa -48 + ^ x21: .cfa -40 + ^ x22: .cfa -32 + ^ x23: .cfa -24 + ^ x24: .cfa -16 + ^ x25: .cfa -8 + ^
STACK CFI 96f4 x20: x20 x21: x21
STACK CFI 96fc x22: x22 x23: x23
STACK CFI 9700 x24: x24 x25: x25
STACK CFI INIT 9710 27c .cfa: sp 0 + .ra: x30
STACK CFI 9718 .cfa: sp 80 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^
STACK CFI 9724 x22: .cfa -48 + ^ x23: .cfa -40 + ^
STACK CFI 9734 x24: .cfa -32 + ^ x25: .cfa -24 + ^
STACK CFI 974c x26: .cfa -16 + ^
STACK CFI 9784 x20: .cfa -64 + ^ x21: .cfa -56 + ^
STACK CFI 98d0 x20: x20 x21: x21
STACK CFI 98d8 x22: x22 x23: x23
STACK CFI 98dc x24: x24 x25: x25
STACK CFI 98e0 x26: x26
STACK CFI 98e4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 98ec .cfa: sp 80 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^ x20: .cfa -64 + ^ x21: .cfa -56 + ^ x22: .cfa -48 + ^ x23: .cfa -40 + ^ x24: .cfa -32 + ^ x25: .cfa -24 + ^ x26: .cfa -16 + ^
STACK CFI 9920 x20: x20 x21: x21
STACK CFI 9928 x22: x22 x23: x23
STACK CFI 9930 x24: x24 x25: x25
STACK CFI 9938 x26: x26
STACK CFI 9944 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 994c .cfa: sp 80 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^
STACK CFI 9960 x20: .cfa -64 + ^ x21: .cfa -56 + ^ x22: .cfa -48 + ^ x23: .cfa -40 + ^ x24: .cfa -32 + ^ x25: .cfa -24 + ^ x26: .cfa -16 + ^
STACK CFI 9974 x20: x20 x21: x21
STACK CFI 997c x22: x22 x23: x23
STACK CFI 9980 x24: x24 x25: x25
STACK CFI 9984 x26: x26
STACK CFI INIT 9990 8c .cfa: sp 0 + .ra: x30
STACK CFI INIT 9a20 94 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9ab4 88 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9b40 54 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9b94 388 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9f20 dc .cfa: sp 0 + .ra: x30
STACK CFI INIT a000 200 .cfa: sp 0 + .ra: x30
STACK CFI a008 .cfa: sp 80 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^
STACK CFI a00c x20: .cfa -64 + ^ x21: .cfa -56 + ^
STACK CFI a014 x24: .cfa -32 + ^ x25: .cfa -24 + ^
STACK CFI a024 x22: .cfa -48 + ^ x23: .cfa -40 + ^
STACK CFI a05c x22: x22 x23: x23
STACK CFI a06c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x24: x24 x25: x25
STACK CFI a074 .cfa: sp 80 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^ x20: .cfa -64 + ^ x21: .cfa -56 + ^ x22: .cfa -48 + ^ x23: .cfa -40 + ^ x24: .cfa -32 + ^ x25: .cfa -24 + ^
STACK CFI a084 x26: .cfa -16 + ^ x27: .cfa -8 + ^
STACK CFI a10c x22: x22 x23: x23
STACK CFI a124 x26: x26 x27: x27
STACK CFI a128 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x24: x24 x25: x25
STACK CFI a130 .cfa: sp 80 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^ x20: .cfa -64 + ^ x21: .cfa -56 + ^ x22: .cfa -48 + ^ x23: .cfa -40 + ^ x24: .cfa -32 + ^ x25: .cfa -24 + ^ x26: .cfa -16 + ^ x27: .cfa -8 + ^
STACK CFI a1ec x22: x22 x23: x23
STACK CFI a1f0 x26: x26 x27: x27
STACK CFI a1f4 x22: .cfa -48 + ^ x23: .cfa -40 + ^ x26: .cfa -16 + ^ x27: .cfa -8 + ^
STACK CFI INIT a200 120 .cfa: sp 0 + .ra: x30
STACK CFI a208 .cfa: sp 96 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^
STACK CFI a20c x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI a214 x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI a224 x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI a248 x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI a254 x28: .cfa -16 + ^
STACK CFI a2d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a2d8 .cfa: sp 96 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^ x28: .cfa -16 + ^
STACK CFI INIT a320 21c .cfa: sp 0 + .ra: x30
STACK CFI a328 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^
STACK CFI a32c x20: .cfa -96 + ^ x21: .cfa -88 + ^
STACK CFI a348 x22: .cfa -80 + ^ x23: .cfa -72 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^ x28: .cfa -32 + ^
STACK CFI a410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a418 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^ x20: .cfa -96 + ^ x21: .cfa -88 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^ x28: .cfa -32 + ^
STACK CFI INIT a540 21c .cfa: sp 0 + .ra: x30
STACK CFI a554 .cfa: sp 96 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^
STACK CFI a560 x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI a56c x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI a57c x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI a5a4 x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI a5ac x28: .cfa -16 + ^
STACK CFI a6bc x22: x22 x23: x23
STACK CFI a6c0 x28: x28
STACK CFI a6d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI a6dc .cfa: sp 96 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^ x28: .cfa -16 + ^
STACK CFI INIT a760 21c .cfa: sp 0 + .ra: x30
STACK CFI a768 .cfa: sp 80 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^
STACK CFI a774 x20: .cfa -64 + ^ x21: .cfa -56 + ^ x22: .cfa -48 + ^ x23: .cfa -40 + ^
STACK CFI a780 x24: .cfa -32 + ^ x25: .cfa -24 + ^
STACK CFI a788 x26: .cfa -16 + ^ x27: .cfa -8 + ^
STACK CFI a868 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI a870 .cfa: sp 80 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^ x20: .cfa -64 + ^ x21: .cfa -56 + ^ x22: .cfa -48 + ^ x23: .cfa -40 + ^ x24: .cfa -32 + ^ x25: .cfa -24 + ^ x26: .cfa -16 + ^ x27: .cfa -8 + ^
STACK CFI a92c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI a934 .cfa: sp 80 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^ x20: .cfa -64 + ^ x21: .cfa -56 + ^ x22: .cfa -48 + ^ x23: .cfa -40 + ^ x24: .cfa -32 + ^ x25: .cfa -24 + ^ x26: .cfa -16 + ^ x27: .cfa -8 + ^
STACK CFI INIT a980 184 .cfa: sp 0 + .ra: x30
STACK CFI a988 .cfa: sp 96 +
STACK CFI a998 .ra: .cfa -80 + ^ x19: .cfa -72 + ^ x20: .cfa -64 + ^
STACK CFI a9a4 x21: .cfa -56 + ^ x22: .cfa -48 + ^ x23: .cfa -40 + ^
STACK CFI a9c0 x24: .cfa -32 + ^ x25: .cfa -24 + ^
STACK CFI a9e4 x26: .cfa -16 + ^
STACK CFI aa64 x26: x26
STACK CFI aa68 x24: x24 x25: x25
STACK CFI aae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI aaec .cfa: sp 96 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^ x20: .cfa -64 + ^ x21: .cfa -56 + ^ x22: .cfa -48 + ^ x23: .cfa -40 + ^ x24: .cfa -32 + ^ x25: .cfa -24 + ^ x26: .cfa -16 + ^
STACK CFI aaf0 x24: x24 x25: x25
STACK CFI aaf4 x26: x26
STACK CFI aafc x24: .cfa -32 + ^ x25: .cfa -24 + ^
STACK CFI ab00 x26: .cfa -16 + ^
STACK CFI INIT ab04 158 .cfa: sp 0 + .ra: x30
STACK CFI ab0c .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^
STACK CFI ab20 .cfa: sp 8336 + x20: .cfa -48 + ^ x21: .cfa -40 + ^ x22: .cfa -32 + ^ x23: .cfa -24 + ^ x24: .cfa -16 + ^
STACK CFI abd4 .cfa: sp 64 +
STACK CFI abe8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI abf0 .cfa: sp 8336 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^ x20: .cfa -48 + ^ x21: .cfa -40 + ^ x22: .cfa -32 + ^ x23: .cfa -24 + ^ x24: .cfa -16 + ^
STACK CFI INIT ac60 ac .cfa: sp 0 + .ra: x30
STACK CFI ac68 .cfa: sp 256 +
STACK CFI ac78 .ra: .cfa -176 + ^
STACK CFI ad00 .cfa: sp 0 + .ra: .ra
STACK CFI ad08 .cfa: sp 256 + .ra: .cfa -176 + ^
STACK CFI INIT ad10 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT ad50 28 .cfa: sp 0 + .ra: x30
STACK CFI INIT ad80 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT ada0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT adc0 e0 .cfa: sp 0 + .ra: x30
STACK CFI ae60 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI ae78 .cfa: sp 0 + .ra: .ra
STACK CFI INIT aea0 6c .cfa: sp 0 + .ra: x30
STACK CFI aeec .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI aef8 .cfa: sp 0 + .ra: .ra
STACK CFI INIT af10 13c .cfa: sp 0 + .ra: x30
STACK CFI af18 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^
STACK CFI af1c x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI af28 x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI afe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI afe8 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI INIT b050 cc .cfa: sp 0 + .ra: x30
STACK CFI b058 .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^
STACK CFI b064 x20: .cfa -48 + ^ x21: .cfa -40 + ^
STACK CFI b06c x24: .cfa -16 + ^
STACK CFI b0a0 x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI b104 x22: x22 x23: x23
STACK CFI b114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x24: x24
STACK CFI INIT b120 60 .cfa: sp 0 + .ra: x30
STACK CFI INIT b180 4c .cfa: sp 0 + .ra: x30
STACK CFI b18c .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI b1c0 .cfa: sp 0 + .ra: .ra
STACK CFI INIT b1d0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b1e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT b1f0 4c .cfa: sp 0 + .ra: x30
STACK CFI INIT b240 64 .cfa: sp 0 + .ra: x30
STACK CFI b248 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI b24c x20: .cfa -16 + ^
STACK CFI b29c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT b2a4 48 .cfa: sp 0 + .ra: x30
STACK CFI INIT b2f0 64 .cfa: sp 0 + .ra: x30
STACK CFI b2f8 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI b2fc x20: .cfa -16 + ^
STACK CFI b34c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT b354 17c .cfa: sp 0 + .ra: x30
STACK CFI INIT b4d0 78 .cfa: sp 0 + .ra: x30
STACK CFI b4d8 .cfa: sp 64 +
STACK CFI b4e8 .ra: .cfa -16 + ^
STACK CFI b53c .cfa: sp 0 + .ra: .ra
STACK CFI b544 .cfa: sp 64 + .ra: .cfa -16 + ^
STACK CFI INIT b550 d0 .cfa: sp 0 + .ra: x30
STACK CFI b558 .cfa: sp 96 +
STACK CFI b584 .ra: .cfa -48 + ^
STACK CFI b58c x19: .cfa -40 + ^ x20: .cfa -32 + ^
STACK CFI b598 x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI b5a4 x23: .cfa -8 + ^
STACK CFI b5d8 x19: x19 x20: x20
STACK CFI b5dc x21: x21 x22: x22
STACK CFI b5e0 x23: x23
STACK CFI b608 .cfa: sp 0 + .ra: .ra
STACK CFI b610 .cfa: sp 96 + .ra: .cfa -48 + ^
STACK CFI b614 x19: .cfa -40 + ^ x20: .cfa -32 + ^
STACK CFI b618 x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI b61c x23: .cfa -8 + ^
STACK CFI INIT b620 74 .cfa: sp 0 + .ra: x30
STACK CFI b628 .cfa: sp 64 +
STACK CFI b638 .ra: .cfa -16 + ^
STACK CFI b688 .cfa: sp 0 + .ra: .ra
STACK CFI b690 .cfa: sp 64 + .ra: .cfa -16 + ^
STACK CFI INIT b694 cc .cfa: sp 0 + .ra: x30
STACK CFI b69c .cfa: sp 96 +
STACK CFI b6c4 .ra: .cfa -48 + ^
STACK CFI b6cc x19: .cfa -40 + ^ x20: .cfa -32 + ^
STACK CFI b6d8 x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI b6e4 x23: .cfa -8 + ^
STACK CFI b718 x19: x19 x20: x20
STACK CFI b71c x21: x21 x22: x22
STACK CFI b720 x23: x23
STACK CFI b748 .cfa: sp 0 + .ra: .ra
STACK CFI b750 .cfa: sp 96 + .ra: .cfa -48 + ^
STACK CFI b754 x19: .cfa -40 + ^ x20: .cfa -32 + ^
STACK CFI b758 x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI b75c x23: .cfa -8 + ^
STACK CFI INIT b760 b0 .cfa: sp 0 + .ra: x30
STACK CFI b768 .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^
STACK CFI b770 x20: .cfa -48 + ^ x21: .cfa -40 + ^
STACK CFI b78c x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI b798 x24: .cfa -16 + ^
STACK CFI b808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI INIT b810 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT b824 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT b840 e8 .cfa: sp 0 + .ra: x30
STACK CFI b854 .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^
STACK CFI b864 x24: .cfa -16 + ^ x25: .cfa -8 + ^
STACK CFI b874 x20: .cfa -48 + ^ x21: .cfa -40 + ^
STACK CFI b87c x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI b920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI INIT b930 c0 .cfa: sp 0 + .ra: x30
STACK CFI b940 .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^
STACK CFI b948 x20: .cfa -48 + ^ x21: .cfa -40 + ^
STACK CFI b954 x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI b960 x24: .cfa -16 + ^ x25: .cfa -8 + ^
STACK CFI b9e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI INIT b9f0 27c .cfa: sp 0 + .ra: x30
STACK CFI b9f8 .cfa: sp 128 + .ra: .cfa -128 + ^ x19: .cfa -120 + ^
STACK CFI b9fc x24: .cfa -80 + ^ x25: .cfa -72 + ^
STACK CFI ba08 x28: .cfa -48 + ^
STACK CFI ba1c x20: .cfa -112 + ^ x21: .cfa -104 + ^
STACK CFI ba30 x22: .cfa -96 + ^ x23: .cfa -88 + ^
STACK CFI ba38 x26: .cfa -64 + ^ x27: .cfa -56 + ^
STACK CFI bc50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI bc58 .cfa: sp 128 + .ra: .cfa -128 + ^ x19: .cfa -120 + ^ x20: .cfa -112 + ^ x21: .cfa -104 + ^ x22: .cfa -96 + ^ x23: .cfa -88 + ^ x24: .cfa -80 + ^ x25: .cfa -72 + ^ x26: .cfa -64 + ^ x27: .cfa -56 + ^ x28: .cfa -48 + ^
STACK CFI INIT bc70 1c4 .cfa: sp 0 + .ra: x30
STACK CFI bc78 .cfa: sp 128 + .ra: .cfa -128 + ^ x19: .cfa -120 + ^
STACK CFI bc80 x28: .cfa -48 + ^
STACK CFI bc88 x20: .cfa -112 + ^ x21: .cfa -104 + ^
STACK CFI bc94 x24: .cfa -80 + ^ x25: .cfa -72 + ^
STACK CFI bca8 x22: .cfa -96 + ^ x23: .cfa -88 + ^
STACK CFI bcb4 x26: .cfa -64 + ^ x27: .cfa -56 + ^
STACK CFI be1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI be24 .cfa: sp 128 + .ra: .cfa -128 + ^ x19: .cfa -120 + ^ x20: .cfa -112 + ^ x21: .cfa -104 + ^ x22: .cfa -96 + ^ x23: .cfa -88 + ^ x24: .cfa -80 + ^ x25: .cfa -72 + ^ x26: .cfa -64 + ^ x27: .cfa -56 + ^ x28: .cfa -48 + ^
STACK CFI INIT be34 21c .cfa: sp 0 + .ra: x30
STACK CFI be3c .cfa: sp 128 + .ra: .cfa -128 + ^ x19: .cfa -120 + ^
STACK CFI be44 x28: .cfa -48 + ^
STACK CFI be4c x20: .cfa -112 + ^ x21: .cfa -104 + ^
STACK CFI be58 x24: .cfa -80 + ^ x25: .cfa -72 + ^
STACK CFI be74 x22: .cfa -96 + ^ x23: .cfa -88 + ^
STACK CFI be80 x26: .cfa -64 + ^ x27: .cfa -56 + ^
STACK CFI c038 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI c040 .cfa: sp 128 + .ra: .cfa -128 + ^ x19: .cfa -120 + ^ x20: .cfa -112 + ^ x21: .cfa -104 + ^ x22: .cfa -96 + ^ x23: .cfa -88 + ^ x24: .cfa -80 + ^ x25: .cfa -72 + ^ x26: .cfa -64 + ^ x27: .cfa -56 + ^ x28: .cfa -48 + ^
STACK CFI INIT c050 98 .cfa: sp 0 + .ra: x30
STACK CFI c058 .cfa: sp 80 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^
STACK CFI c060 x20: .cfa -64 + ^ x21: .cfa -56 + ^
STACK CFI c070 x22: .cfa -48 + ^ x23: .cfa -40 + ^
STACK CFI c07c x24: .cfa -32 + ^ x25: .cfa -24 + ^
STACK CFI c088 x26: .cfa -16 + ^
STACK CFI c0e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI INIT c0f0 80 .cfa: sp 0 + .ra: x30
STACK CFI c0f8 .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^
STACK CFI c100 x20: .cfa -48 + ^ x21: .cfa -40 + ^
STACK CFI c10c x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI c118 x24: .cfa -16 + ^ x25: .cfa -8 + ^
STACK CFI c168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI INIT c170 234 .cfa: sp 0 + .ra: x30
STACK CFI c178 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^
STACK CFI c17c x22: .cfa -80 + ^ x23: .cfa -72 + ^
STACK CFI c194 x20: .cfa -96 + ^ x21: .cfa -88 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^ x28: .cfa -32 + ^
STACK CFI c328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI c330 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^ x20: .cfa -96 + ^ x21: .cfa -88 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^ x28: .cfa -32 + ^
STACK CFI INIT c3a4 640 .cfa: sp 0 + .ra: x30
STACK CFI c3ac .cfa: sp 208 + .ra: .cfa -208 + ^ x19: .cfa -200 + ^
STACK CFI c3b4 x20: .cfa -192 + ^ x21: .cfa -184 + ^
STACK CFI c3c4 x22: .cfa -176 + ^ x23: .cfa -168 + ^
STACK CFI c3e4 v10: .cfa -96 + ^ v11: .cfa -88 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x24: .cfa -160 + ^ x25: .cfa -152 + ^
STACK CFI c3f4 v12: .cfa -80 + ^ v13: .cfa -72 + ^ v14: .cfa -64 + ^ v15: .cfa -56 + ^
STACK CFI c410 x26: .cfa -144 + ^ x27: .cfa -136 + ^
STACK CFI c488 x26: x26 x27: x27
STACK CFI c4ac .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI c4b4 .cfa: sp 208 + .ra: .cfa -208 + ^ v10: .cfa -96 + ^ v11: .cfa -88 + ^ v12: .cfa -80 + ^ v13: .cfa -72 + ^ v14: .cfa -64 + ^ v15: .cfa -56 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x19: .cfa -200 + ^ x20: .cfa -192 + ^ x21: .cfa -184 + ^ x22: .cfa -176 + ^ x23: .cfa -168 + ^ x24: .cfa -160 + ^ x25: .cfa -152 + ^ x26: .cfa -144 + ^ x27: .cfa -136 + ^
STACK CFI c658 x28: .cfa -128 + ^
STACK CFI c6f4 x28: x28
STACK CFI c7ac x26: x26 x27: x27
STACK CFI c7b4 x26: .cfa -144 + ^ x27: .cfa -136 + ^ x28: .cfa -128 + ^
STACK CFI c820 x28: x28
STACK CFI c8c4 x28: .cfa -128 + ^
STACK CFI c900 x28: x28
STACK CFI c948 x28: .cfa -128 + ^
STACK CFI c95c x28: x28
STACK CFI INIT c9e4 c .cfa: sp 0 + .ra: x30
STACK CFI INIT c9f0 ac .cfa: sp 0 + .ra: x30
STACK CFI c9f8 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI ca6c .cfa: sp 0 + .ra: .ra
STACK CFI ca74 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI INIT caa0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT cac0 74 .cfa: sp 0 + .ra: x30
STACK CFI cac8 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI cae0 x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI cb10 x20: x20 x21: x21
STACK CFI cb14 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI cb1c .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI INIT cb34 178 .cfa: sp 0 + .ra: x30
STACK CFI cb3c .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^
STACK CFI cb44 x20: .cfa -48 + ^ x21: .cfa -40 + ^
STACK CFI cb54 x22: .cfa -32 + ^ x23: .cfa -24 + ^ x24: .cfa -16 + ^
STACK CFI cc98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI cca0 .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^ x20: .cfa -48 + ^ x21: .cfa -40 + ^ x22: .cfa -32 + ^ x23: .cfa -24 + ^ x24: .cfa -16 + ^
STACK CFI INIT ccb0 1d4 .cfa: sp 0 + .ra: x30
STACK CFI ccb8 .cfa: sp 96 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^
STACK CFI ccbc x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI ccc8 x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI ccd4 x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI cce0 x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI ccf0 x28: .cfa -16 + ^
STACK CFI cdfc x20: x20 x21: x21
STACK CFI ce00 x22: x22 x23: x23
STACK CFI ce04 x24: x24 x25: x25
STACK CFI ce08 x28: x28
STACK CFI ce14 .cfa: sp 0 + .ra: .ra x19: x19 x26: x26 x27: x27
STACK CFI ce1c .cfa: sp 96 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^ x28: .cfa -16 + ^
STACK CFI ce30 x20: x20 x21: x21
STACK CFI ce38 x22: x22 x23: x23
STACK CFI ce3c x24: x24 x25: x25
STACK CFI ce44 x28: x28
STACK CFI ce48 .cfa: sp 0 + .ra: .ra x19: x19 x26: x26 x27: x27
STACK CFI ce50 .cfa: sp 96 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI ce5c x20: x20 x21: x21
STACK CFI ce64 x22: x22 x23: x23
STACK CFI ce68 x24: x24 x25: x25
STACK CFI INIT ce84 160 .cfa: sp 0 + .ra: x30
STACK CFI ce8c .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^
STACK CFI ce9c x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI ceb8 x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI cf58 x20: x20 x21: x21
STACK CFI cf60 x22: x22 x23: x23
STACK CFI cf64 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI cf6c .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI cfb4 x22: x22 x23: x23
STACK CFI cfb8 x20: x20 x21: x21
STACK CFI cfcc x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI cfd4 x20: x20 x21: x21
STACK CFI INIT cfe4 380 .cfa: sp 0 + .ra: x30
STACK CFI cfec .cfa: sp 160 + .ra: .cfa -160 + ^ x19: .cfa -152 + ^
STACK CFI d000 x20: .cfa -144 + ^ x21: .cfa -136 + ^
STACK CFI d074 x22: .cfa -128 + ^ x23: .cfa -120 + ^
STACK CFI d080 x24: .cfa -112 + ^ x25: .cfa -104 + ^
STACK CFI d164 x26: .cfa -96 + ^ x27: .cfa -88 + ^
STACK CFI d168 x28: .cfa -80 + ^
STACK CFI d2f4 x26: x26 x27: x27
STACK CFI d2f8 x28: x28
STACK CFI d308 x20: x20 x21: x21
STACK CFI d30c x22: x22 x23: x23
STACK CFI d310 x24: x24 x25: x25
STACK CFI d318 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI d320 .cfa: sp 160 + .ra: .cfa -160 + ^ x19: .cfa -152 + ^ x20: .cfa -144 + ^ x21: .cfa -136 + ^
STACK CFI d324 x20: x20 x21: x21
STACK CFI d328 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI d330 .cfa: sp 160 + .ra: .cfa -160 + ^ x19: .cfa -152 + ^ x20: .cfa -144 + ^ x21: .cfa -136 + ^
STACK CFI d334 x20: x20 x21: x21
STACK CFI d34c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI d354 .cfa: sp 160 + .ra: .cfa -160 + ^ x19: .cfa -152 + ^ x20: .cfa -144 + ^ x21: .cfa -136 + ^ x22: .cfa -128 + ^ x23: .cfa -120 + ^ x24: .cfa -112 + ^ x25: .cfa -104 + ^
STACK CFI d358 x20: x20 x21: x21
STACK CFI d35c x22: x22 x23: x23
STACK CFI d360 x24: x24 x25: x25
STACK CFI INIT d364 324 .cfa: sp 0 + .ra: x30
STACK CFI d36c .cfa: sp 80 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^
STACK CFI d370 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI d37c x24: .cfa -32 + ^
STACK CFI d390 x22: .cfa -48 + ^ x23: .cfa -40 + ^
STACK CFI d3b4 x20: .cfa -64 + ^ x21: .cfa -56 + ^
STACK CFI d530 x20: x20 x21: x21
STACK CFI d538 x22: x22 x23: x23
STACK CFI d5c0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x24: x24
STACK CFI d5c8 .cfa: sp 80 + .ra: .cfa -80 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -72 + ^ x22: .cfa -48 + ^ x23: .cfa -40 + ^ x24: .cfa -32 + ^
STACK CFI d5cc x22: x22 x23: x23
STACK CFI d5e0 x20: .cfa -64 + ^ x21: .cfa -56 + ^ x22: .cfa -48 + ^ x23: .cfa -40 + ^
STACK CFI d674 x20: x20 x21: x21
STACK CFI d67c x22: x22 x23: x23
STACK CFI INIT d690 cc .cfa: sp 0 + .ra: x30
STACK CFI d6a4 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI d6ec .cfa: sp 0 + .ra: .ra
STACK CFI d700 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI d74c .cfa: sp 0 + .ra: .ra
STACK CFI INIT d760 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT d774 c .cfa: sp 0 + .ra: x30
STACK CFI INIT d780 13c .cfa: sp 0 + .ra: x30
STACK CFI d89c .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI d8a8 .cfa: sp 0 + .ra: .ra
STACK CFI INIT d8c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT d8d0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT d8e4 c .cfa: sp 0 + .ra: x30
STACK CFI INIT d8f0 a4 .cfa: sp 0 + .ra: x30
STACK CFI d8f8 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI d900 x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI d934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI d93c .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI INIT d994 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT d9b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT d9c0 180 .cfa: sp 0 + .ra: x30
STACK CFI d9c8 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI d9d0 x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI da04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI da0c .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI INIT db40 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT db54 c .cfa: sp 0 + .ra: x30
STACK CFI INIT db60 104 .cfa: sp 0 + .ra: x30
STACK CFI db68 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI db70 x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI dbb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI dbc0 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI INIT dc64 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT dc80 c .cfa: sp 0 + .ra: x30
STACK CFI INIT dc90 1e8 .cfa: sp 0 + .ra: x30
STACK CFI dc98 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI dca0 x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI dcf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI dcf8 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI INIT de80 164 .cfa: sp 0 + .ra: x30
STACK CFI de88 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI ded8 x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI df04 x20: x20 x21: x21
STACK CFI df08 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI df10 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI df44 x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI df74 x20: x20 x21: x21
STACK CFI dfb0 x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI dfdc x20: x20 x21: x21
STACK CFI INIT dfe4 94 .cfa: sp 0 + .ra: x30
STACK CFI dfec .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI e000 x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI e068 x20: x20 x21: x21
STACK CFI e070 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT e080 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT e094 c .cfa: sp 0 + .ra: x30
STACK CFI INIT e0a0 94c .cfa: sp 0 + .ra: x30
STACK CFI e0a8 .cfa: sp 304 +
STACK CFI e0c0 .ra: .cfa -96 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI e108 .cfa: sp 0 + .ra: .ra x20: x20 x21: x21
STACK CFI e110 .cfa: sp 304 + .ra: .cfa -96 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI e114 x19: .cfa -88 + ^
STACK CFI e124 x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI e130 x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI e140 x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI e148 x28: .cfa -16 + ^
STACK CFI e660 x19: x19
STACK CFI e664 x22: x22 x23: x23
STACK CFI e668 x24: x24 x25: x25
STACK CFI e66c x26: x26 x27: x27
STACK CFI e670 x28: x28
STACK CFI e674 x19: .cfa -88 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^ x28: .cfa -16 + ^
STACK CFI e9d4 x19: x19 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI e9d8 x19: .cfa -88 + ^
STACK CFI e9dc x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI e9e0 x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI e9e4 x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI e9e8 x28: .cfa -16 + ^
STACK CFI INIT e9f0 2c .cfa: sp 0 + .ra: x30
STACK CFI ea00 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI ea08 .cfa: sp 0 + .ra: .ra
STACK CFI INIT ea20 88 .cfa: sp 0 + .ra: x30
STACK CFI ea28 .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI eaa0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT eab0 38c .cfa: sp 0 + .ra: x30
STACK CFI eab8 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^
STACK CFI eac4 x20: .cfa -96 + ^ x21: .cfa -88 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^
STACK CFI eafc x22: .cfa -80 + ^ x23: .cfa -72 + ^
STACK CFI eb00 x26: .cfa -48 + ^ x27: .cfa -40 + ^
STACK CFI eb04 x28: .cfa -32 + ^
STACK CFI eb08 v8: .cfa -24 + ^
STACK CFI ec8c x22: x22 x23: x23
STACK CFI ec90 x26: x26 x27: x27
STACK CFI ec94 x28: x28
STACK CFI ec98 v8: v8
STACK CFI ecb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x24: x24 x25: x25
STACK CFI ecb8 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^ x20: .cfa -96 + ^ x21: .cfa -88 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^
STACK CFI ecfc v8: .cfa -24 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^ x28: .cfa -32 + ^
STACK CFI edc8 v8: v8 x22: x22 x23: x23 x26: x26 x27: x27 x28: x28
STACK CFI edd8 v8: .cfa -24 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^ x28: .cfa -32 + ^
STACK CFI ede0 v8: v8
STACK CFI ede8 x22: x22 x23: x23
STACK CFI edf0 x26: x26 x27: x27
STACK CFI edf4 x28: x28
STACK CFI edfc v8: .cfa -24 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^ x28: .cfa -32 + ^
STACK CFI ee2c v8: v8 x22: x22 x23: x23 x26: x26 x27: x27 x28: x28
STACK CFI INIT ee40 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ee54 c .cfa: sp 0 + .ra: x30
STACK CFI INIT ee60 c .cfa: sp 0 + .ra: x30
STACK CFI INIT ee70 c .cfa: sp 0 + .ra: x30
STACK CFI INIT ee80 3c .cfa: sp 0 + .ra: x30
STACK CFI ee88 .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI eeb0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT eec0 46c .cfa: sp 0 + .ra: x30
STACK CFI eec8 .cfa: sp 160 +
STACK CFI eed8 .ra: .cfa -96 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI eee4 x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI ef10 x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI ef1c x19: .cfa -88 + ^
STACK CFI ef24 x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI ef7c x28: .cfa -16 + ^
STACK CFI f25c x28: x28
STACK CFI f274 x22: x22 x23: x23
STACK CFI f27c x26: x26 x27: x27
STACK CFI f280 x19: x19
STACK CFI f2ac .cfa: sp 0 + .ra: .ra x20: x20 x21: x21 x24: x24 x25: x25
STACK CFI f2b4 .cfa: sp 160 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI f300 x19: x19 x22: x22 x23: x23 x26: x26 x27: x27
STACK CFI f31c x19: .cfa -88 + ^
STACK CFI f320 x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI f324 x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI f328 x28: .cfa -16 + ^
STACK CFI INIT f330 1a0 .cfa: sp 0 + .ra: x30
STACK CFI f338 .cfa: sp 48 +
STACK CFI f348 .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI f350 x21: .cfa -8 + ^
STACK CFI f3e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI f3f0 .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI INIT f4d0 5c .cfa: sp 0 + .ra: x30
STACK CFI f4d8 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI f4e4 x20: .cfa -16 + ^
STACK CFI f500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI f508 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI f524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT f530 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT f550 50 .cfa: sp 0 + .ra: x30
STACK CFI f560 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI f578 .cfa: sp 0 + .ra: .ra
STACK CFI f584 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI f594 .cfa: sp 0 + .ra: .ra
STACK CFI INIT f5a0 58 .cfa: sp 0 + .ra: x30
STACK CFI f5b8 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI f5c4 .cfa: sp 0 + .ra: .ra
STACK CFI f5d8 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI f5e8 .cfa: sp 0 + .ra: .ra
STACK CFI INIT f600 d4 .cfa: sp 0 + .ra: x30
STACK CFI f608 .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^
STACK CFI f60c x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI f61c x20: .cfa -48 + ^ x21: .cfa -40 + ^
STACK CFI f628 x24: .cfa -16 + ^
STACK CFI f678 x20: x20 x21: x21
STACK CFI f684 x24: x24
STACK CFI f688 .cfa: sp 0 + .ra: .ra x19: x19 x22: x22 x23: x23
STACK CFI f690 .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^ x20: .cfa -48 + ^ x21: .cfa -40 + ^ x22: .cfa -32 + ^ x23: .cfa -24 + ^ x24: .cfa -16 + ^
STACK CFI f6a8 x20: x20 x21: x21
STACK CFI f6ac x24: x24
STACK CFI f6b0 .cfa: sp 0 + .ra: .ra x19: x19 x22: x22 x23: x23
STACK CFI f6b8 .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^ x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI f6c4 .cfa: sp 0 + .ra: .ra x19: x19 x22: x22 x23: x23
STACK CFI f6cc .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^ x20: .cfa -48 + ^ x21: .cfa -40 + ^ x22: .cfa -32 + ^ x23: .cfa -24 + ^ x24: .cfa -16 + ^
STACK CFI INIT f6d4 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT f6f0 34 .cfa: sp 0 + .ra: x30
STACK CFI f704 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI f710 .cfa: sp 0 + .ra: .ra
STACK CFI INIT f724 b8 .cfa: sp 0 + .ra: x30
STACK CFI f72c .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI f730 x20: .cfa -16 + ^
STACK CFI f76c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI f774 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT f7e0 410 .cfa: sp 0 + .ra: x30
STACK CFI f7e8 .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^
STACK CFI f808 x22: .cfa -80 + ^ x23: .cfa -72 + ^
STACK CFI f810 x24: .cfa -64 + ^ x25: .cfa -56 + ^
STACK CFI f850 x26: .cfa -48 + ^ x27: .cfa -40 + ^
STACK CFI f8dc x20: .cfa -96 + ^ x21: .cfa -88 + ^
STACK CFI f8e0 x28: .cfa -32 + ^
STACK CFI f99c x20: x20 x21: x21
STACK CFI f9a4 x28: x28
STACK CFI f9ac x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI f9c4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI f9cc .cfa: sp 112 + .ra: .cfa -112 + ^ x19: .cfa -104 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^
STACK CFI fa2c x20: .cfa -96 + ^ x21: .cfa -88 + ^
STACK CFI fa3c x28: .cfa -32 + ^
STACK CFI faf8 x20: x20 x21: x21
STACK CFI fafc x28: x28
STACK CFI fb08 x22: x22 x23: x23
STACK CFI fb10 x24: x24 x25: x25
STACK CFI fb14 x26: x26 x27: x27
STACK CFI fb1c x22: .cfa -80 + ^ x23: .cfa -72 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^
STACK CFI fb28 x22: x22 x23: x23
STACK CFI fb2c x24: x24 x25: x25
STACK CFI fb30 x26: x26 x27: x27
STACK CFI fb38 x20: .cfa -96 + ^ x21: .cfa -88 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^ x28: .cfa -32 + ^
STACK CFI fb8c x20: x20 x21: x21 x26: x26 x27: x27 x28: x28
STACK CFI fb90 x22: x22 x23: x23
STACK CFI fb94 x24: x24 x25: x25
STACK CFI fb98 x20: .cfa -96 + ^ x21: .cfa -88 + ^ x22: .cfa -80 + ^ x23: .cfa -72 + ^ x24: .cfa -64 + ^ x25: .cfa -56 + ^ x26: .cfa -48 + ^ x27: .cfa -40 + ^ x28: .cfa -32 + ^
STACK CFI fbe0 x28: x28
STACK CFI fbe4 x20: x20 x21: x21
STACK CFI INIT fbf0 10c .cfa: sp 0 + .ra: x30
STACK CFI fbf8 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^
STACK CFI fbfc x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI fc0c x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI fcdc x20: x20 x21: x21
STACK CFI fcec .cfa: sp 0 + .ra: .ra x19: x19 x22: x22 x23: x23
STACK CFI fcf4 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI INIT fd00 34 .cfa: sp 0 + .ra: x30
STACK CFI fd08 .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI fd2c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT fd34 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT fd44 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT fd54 40 .cfa: sp 0 + .ra: x30
STACK CFI fd74 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI fd80 .cfa: sp 0 + .ra: .ra
STACK CFI INIT fd94 c .cfa: sp 0 + .ra: x30
STACK CFI INIT fda0 25c .cfa: sp 0 + .ra: x30
STACK CFI fda8 .cfa: sp 96 +
STACK CFI fdb8 x19: .cfa -56 + ^ x20: .cfa -48 + ^
STACK CFI fdc8 .ra: .cfa -64 + ^ x21: .cfa -40 + ^
STACK CFI fde0 x24: .cfa -16 + ^
STACK CFI fed8 x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI ff64 x22: x22 x23: x23
STACK CFI ffac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x24: x24
STACK CFI ffb4 .cfa: sp 96 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^ x20: .cfa -48 + ^ x21: .cfa -40 + ^ x24: .cfa -16 + ^
STACK CFI fff8 x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI INIT 10000 fc .cfa: sp 0 + .ra: x30
STACK CFI 10008 .cfa: sp 64 +
STACK CFI 1001c .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 100bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 100c4 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 10100 40 .cfa: sp 0 + .ra: x30
STACK CFI 10110 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 10120 .cfa: sp 0 + .ra: .ra
STACK CFI 10128 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI INIT 10140 40 .cfa: sp 0 + .ra: x30
STACK CFI 10150 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 10160 .cfa: sp 0 + .ra: .ra
STACK CFI 10168 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI INIT 10180 6c .cfa: sp 0 + .ra: x30
STACK CFI 1018c .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI 101b0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 101b8 .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI 101e4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 101f0 244 .cfa: sp 0 + .ra: x30
STACK CFI 101f8 .cfa: sp 160 +
STACK CFI 10214 .ra: .cfa -96 + ^
STACK CFI 1021c x19: .cfa -88 + ^ x20: .cfa -80 + ^
STACK CFI 10224 x21: .cfa -72 + ^ x22: .cfa -64 + ^
STACK CFI 10230 x23: .cfa -56 + ^ x24: .cfa -48 + ^
STACK CFI 10238 x25: .cfa -40 + ^ x26: .cfa -32 + ^
STACK CFI 10240 x27: .cfa -24 + ^ x28: .cfa -16 + ^
STACK CFI 10384 x19: x19 x20: x20
STACK CFI 10388 x21: x21 x22: x22
STACK CFI 1038c x23: x23 x24: x24
STACK CFI 10390 x25: x25 x26: x26
STACK CFI 10394 x27: x27 x28: x28
STACK CFI 103b8 .cfa: sp 0 + .ra: .ra
STACK CFI 103c0 .cfa: sp 160 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^ x28: .cfa -16 + ^
STACK CFI 10404 x19: x19 x20: x20
STACK CFI 10408 x21: x21 x22: x22
STACK CFI 1040c x23: x23 x24: x24
STACK CFI 10410 x25: x25 x26: x26
STACK CFI 10414 x27: x27 x28: x28
STACK CFI 10420 x19: .cfa -88 + ^ x20: .cfa -80 + ^
STACK CFI 10424 x21: .cfa -72 + ^ x22: .cfa -64 + ^
STACK CFI 10428 x23: .cfa -56 + ^ x24: .cfa -48 + ^
STACK CFI 1042c x25: .cfa -40 + ^ x26: .cfa -32 + ^
STACK CFI 10430 x27: .cfa -24 + ^ x28: .cfa -16 + ^
STACK CFI INIT 10434 30 .cfa: sp 0 + .ra: x30
STACK CFI 1043c .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI 10454 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 10464 30 .cfa: sp 0 + .ra: x30
STACK CFI 1046c .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI 10484 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 10494 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 104a0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 104b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 104c0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 104e0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 104f0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10500 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10510 134 .cfa: sp 0 + .ra: x30
STACK CFI 10524 .cfa: sp 64 +
STACK CFI 1053c .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI 105c4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 105cc .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI INIT 10644 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10650 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 10660 68 .cfa: sp 0 + .ra: x30
STACK CFI 10668 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^
STACK CFI 1066c x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI 10684 x22: .cfa -16 + ^
STACK CFI 106c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 106d0 254 .cfa: sp 0 + .ra: x30
STACK CFI 106d8 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^
STACK CFI 106e4 x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI 106f8 x22: .cfa -16 + ^
STACK CFI 107c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 107cc .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI 10848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 10850 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI 108a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 108b0 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI 108c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 108d0 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI 108ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 108f4 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI 10904 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1090c .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI INIT 10924 628 .cfa: sp 0 + .ra: x30
STACK CFI 1092c .cfa: sp 80 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^
STACK CFI 10934 .cfa: sp 2192 + x20: .cfa -64 + ^ x21: .cfa -56 + ^
STACK CFI 10974 x22: .cfa -48 + ^
STACK CFI 10978 x23: .cfa -40 + ^
STACK CFI 10988 x24: .cfa -32 + ^
STACK CFI 1098c x25: .cfa -24 + ^
STACK CFI 10a34 x26: .cfa -16 + ^
STACK CFI 10a3c x27: .cfa -8 + ^
STACK CFI 10ce8 x22: x22
STACK CFI 10cec x23: x23
STACK CFI 10cf0 x24: x24
STACK CFI 10cf4 x25: x25
STACK CFI 10cf8 x26: x26
STACK CFI 10cfc x27: x27
STACK CFI 10d1c .cfa: sp 80 +
STACK CFI 10d28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 10d30 .cfa: sp 2192 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^ x20: .cfa -64 + ^ x21: .cfa -56 + ^ x22: .cfa -48 + ^ x23: .cfa -40 + ^ x24: .cfa -32 + ^ x25: .cfa -24 + ^ x26: .cfa -16 + ^ x27: .cfa -8 + ^
STACK CFI 10dec x26: x26 x27: x27
STACK CFI 10e08 x22: x22
STACK CFI 10e10 x23: x23
STACK CFI 10e14 x24: x24
STACK CFI 10e18 x25: x25
STACK CFI 10e20 x22: .cfa -48 + ^ x23: .cfa -40 + ^
STACK CFI 10e68 x24: .cfa -32 + ^
STACK CFI 10e6c x25: .cfa -24 + ^
STACK CFI 10e74 x24: x24 x25: x25
STACK CFI 10ec8 x22: x22
STACK CFI 10ed0 x23: x23
STACK CFI 10ed8 x22: .cfa -48 + ^ x23: .cfa -40 + ^ x24: .cfa -32 + ^ x25: .cfa -24 + ^ x26: .cfa -16 + ^ x27: .cfa -8 + ^
STACK CFI 10efc x22: x22
STACK CFI 10f04 x23: x23
STACK CFI 10f08 x24: x24
STACK CFI 10f0c x25: x25
STACK CFI 10f10 x26: x26
STACK CFI 10f14 x27: x27
STACK CFI 10f20 x22: .cfa -48 + ^
STACK CFI 10f24 x23: .cfa -40 + ^
STACK CFI 10f28 x24: .cfa -32 + ^
STACK CFI 10f2c x25: .cfa -24 + ^
STACK CFI 10f30 x26: .cfa -16 + ^
STACK CFI 10f34 x27: .cfa -8 + ^
STACK CFI 10f38 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI INIT 10f50 110 .cfa: sp 0 + .ra: x30
STACK CFI 10f58 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 10f68 x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI 10ff0 x20: x20 x21: x21
STACK CFI 10ff8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 11000 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 11010 x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI 1104c x20: x20 x21: x21
STACK CFI 11054 x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI 11058 x20: x20 x21: x21
STACK CFI INIT 11060 bc .cfa: sp 0 + .ra: x30
STACK CFI 11068 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^
STACK CFI 1106c x22: .cfa -16 + ^
STACK CFI 11078 x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI 110f4 x20: x20 x21: x21
STACK CFI 11100 .cfa: sp 0 + .ra: .ra x19: x19 x22: x22
STACK CFI 11108 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x22: .cfa -16 + ^
STACK CFI INIT 11120 148 .cfa: sp 0 + .ra: x30
STACK CFI 11128 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^
STACK CFI 1112c x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI 11138 x22: .cfa -16 + ^
STACK CFI 11198 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 111a0 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI 11200 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 11208 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI INIT 11270 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 11280 104 .cfa: sp 0 + .ra: x30
STACK CFI 11288 .cfa: sp 64 +
STACK CFI 11294 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 112a4 .ra: .cfa -32 + ^
STACK CFI 112e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 112f0 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 11378 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 11380 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 11384 15c .cfa: sp 0 + .ra: x30
STACK CFI 1138c .cfa: sp 96 +
STACK CFI 1139c .ra: .cfa -64 + ^ x19: .cfa -56 + ^ x20: .cfa -48 + ^
STACK CFI 113ac x21: .cfa -40 + ^ x22: .cfa -32 + ^ x23: .cfa -24 + ^ x24: .cfa -16 + ^
STACK CFI 114a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 114a8 .cfa: sp 96 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^ x20: .cfa -48 + ^ x21: .cfa -40 + ^ x22: .cfa -32 + ^ x23: .cfa -24 + ^ x24: .cfa -16 + ^
STACK CFI INIT 114e0 120 .cfa: sp 0 + .ra: x30
STACK CFI 114e8 .cfa: sp 64 +
STACK CFI 114f8 .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 11504 x21: .cfa -8 + ^
STACK CFI 115c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 115c8 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI INIT 11600 1cc .cfa: sp 0 + .ra: x30
STACK CFI 11608 .cfa: sp 336 +
STACK CFI 11618 .ra: .cfa -240 + ^ x19: .cfa -232 + ^ x20: .cfa -224 + ^
STACK CFI 11624 x21: .cfa -216 + ^ x22: .cfa -208 + ^
STACK CFI 1162c x23: .cfa -200 + ^
STACK CFI 1178c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 11794 .cfa: sp 336 + .ra: .cfa -240 + ^ x19: .cfa -232 + ^ x20: .cfa -224 + ^ x21: .cfa -216 + ^ x22: .cfa -208 + ^ x23: .cfa -200 + ^
STACK CFI INIT 117d0 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 117d8 .cfa: sp 96 +
STACK CFI 117f0 .ra: .cfa -64 + ^ x19: .cfa -56 + ^ x20: .cfa -48 + ^ x21: .cfa -40 + ^ x22: .cfa -32 + ^ x23: .cfa -24 + ^ x24: .cfa -16 + ^
STACK CFI 118ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 118f4 .cfa: sp 96 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^ x20: .cfa -48 + ^ x21: .cfa -40 + ^ x22: .cfa -32 + ^ x23: .cfa -24 + ^ x24: .cfa -16 + ^
STACK CFI INIT 11974 270 .cfa: sp 0 + .ra: x30
STACK CFI 1197c .cfa: sp 128 +
STACK CFI 11990 .ra: .cfa -96 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI 11998 x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI 119a0 x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI 119d8 x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI 119e4 x28: .cfa -16 + ^
STACK CFI 11a00 x19: .cfa -88 + ^
STACK CFI 11ae8 x26: x26 x27: x27
STACK CFI 11aec x19: x19
STACK CFI 11af0 x28: x28
STACK CFI 11b98 .cfa: sp 0 + .ra: .ra x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 11ba0 .cfa: sp 128 + .ra: .cfa -96 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI 11bd8 x19: .cfa -88 + ^
STACK CFI 11bdc x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI 11be0 x28: .cfa -16 + ^
STACK CFI INIT 11be4 184 .cfa: sp 0 + .ra: x30
STACK CFI 11bec .cfa: sp 128 +
STACK CFI 11c08 .ra: .cfa -64 + ^ x20: .cfa -48 + ^ x21: .cfa -40 + ^
STACK CFI 11c28 x19: .cfa -56 + ^
STACK CFI 11c3c x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI 11c48 x24: .cfa -16 + ^
STACK CFI 11cec x22: x22 x23: x23 x24: x24
STACK CFI 11cf0 x19: x19
STACK CFI 11d24 .cfa: sp 0 + .ra: .ra x20: x20 x21: x21
STACK CFI 11d2c .cfa: sp 128 + .ra: .cfa -64 + ^ x20: .cfa -48 + ^ x21: .cfa -40 + ^
STACK CFI 11d38 x19: .cfa -56 + ^ x22: .cfa -32 + ^ x23: .cfa -24 + ^ x24: .cfa -16 + ^
STACK CFI 11d4c x19: x19
STACK CFI 11d50 x22: x22 x23: x23
STACK CFI 11d54 x24: x24
STACK CFI 11d5c x19: .cfa -56 + ^
STACK CFI 11d60 x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI 11d64 x24: .cfa -16 + ^
STACK CFI INIT 11d70 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 11d78 .cfa: sp 112 +
STACK CFI 11d7c x23: .cfa -40 + ^ x24: .cfa -32 + ^
STACK CFI 11da8 .ra: .cfa -80 + ^ x19: .cfa -72 + ^ x20: .cfa -64 + ^ x21: .cfa -56 + ^ x22: .cfa -48 + ^ x25: .cfa -24 + ^
STACK CFI 11dc4 x26: .cfa -16 + ^ x27: .cfa -8 + ^
STACK CFI 11e4c x26: x26 x27: x27
STACK CFI 11efc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 11f04 .cfa: sp 112 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^ x20: .cfa -64 + ^ x21: .cfa -56 + ^ x22: .cfa -48 + ^ x23: .cfa -40 + ^ x24: .cfa -32 + ^ x25: .cfa -24 + ^ x26: .cfa -16 + ^ x27: .cfa -8 + ^
STACK CFI 11f10 x26: x26 x27: x27
STACK CFI 11f60 x26: .cfa -16 + ^ x27: .cfa -8 + ^
STACK CFI INIT 11f64 144 .cfa: sp 0 + .ra: x30
STACK CFI 11f6c .cfa: sp 80 +
STACK CFI 11f7c .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^
STACK CFI 11f88 x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI 12068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 12070 .cfa: sp 80 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI INIT 120b0 120 .cfa: sp 0 + .ra: x30
STACK CFI 120b8 .cfa: sp 64 +
STACK CFI 120c8 .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 120d4 x21: .cfa -8 + ^
STACK CFI 12190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 12198 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI INIT 121d0 144 .cfa: sp 0 + .ra: x30
STACK CFI 121d8 .cfa: sp 96 +
STACK CFI 121e8 .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 121f4 x21: .cfa -8 + ^
STACK CFI 1227c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 12284 .cfa: sp 96 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI INIT 12314 fc .cfa: sp 0 + .ra: x30
STACK CFI 1231c .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^
STACK CFI 12324 x20: .cfa -48 + ^ x21: .cfa -40 + ^
STACK CFI 12330 x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI 1233c x24: .cfa -16 + ^ x25: .cfa -8 + ^
STACK CFI 1238c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 12394 .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^ x20: .cfa -48 + ^ x21: .cfa -40 + ^ x22: .cfa -32 + ^ x23: .cfa -24 + ^ x24: .cfa -16 + ^ x25: .cfa -8 + ^
STACK CFI INIT 12410 1cc .cfa: sp 0 + .ra: x30
STACK CFI 12418 .cfa: sp 336 +
STACK CFI 12428 .ra: .cfa -240 + ^ x19: .cfa -232 + ^ x20: .cfa -224 + ^
STACK CFI 12434 x21: .cfa -216 + ^ x22: .cfa -208 + ^
STACK CFI 1243c x23: .cfa -200 + ^
STACK CFI 1259c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 125a4 .cfa: sp 336 + .ra: .cfa -240 + ^ x19: .cfa -232 + ^ x20: .cfa -224 + ^ x21: .cfa -216 + ^ x22: .cfa -208 + ^ x23: .cfa -200 + ^
STACK CFI INIT 125e0 30 .cfa: sp 0 + .ra: x30
STACK CFI 125e8 .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI 12608 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 12610 90 .cfa: sp 0 + .ra: x30
STACK CFI 12618 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^
STACK CFI 12624 x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI 1262c x22: .cfa -16 + ^
STACK CFI 12698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI INIT 126a0 13c .cfa: sp 0 + .ra: x30
STACK CFI 126a8 .cfa: sp 64 +
STACK CFI 126b8 .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 12784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1278c .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 127e0 268 .cfa: sp 0 + .ra: x30
STACK CFI 127e8 .cfa: sp 128 +
STACK CFI 127f4 x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI 12804 .ra: .cfa -96 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI 1280c x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI 12814 x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI 12850 x28: .cfa -16 + ^
STACK CFI 12868 x19: .cfa -88 + ^
STACK CFI 12950 x19: x19
STACK CFI 12954 x28: x28
STACK CFI 12a00 .cfa: sp 0 + .ra: .ra x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 12a08 .cfa: sp 128 + .ra: .cfa -96 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI 12a40 x19: .cfa -88 + ^
STACK CFI 12a44 x28: .cfa -16 + ^
STACK CFI INIT 12a50 4c .cfa: sp 0 + .ra: x30
STACK CFI 12a58 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 12a60 x20: .cfa -16 + ^
STACK CFI 12a94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 12aa0 4c .cfa: sp 0 + .ra: x30
STACK CFI 12aa8 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 12ab0 x20: .cfa -16 + ^
STACK CFI 12ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 12af0 28 .cfa: sp 0 + .ra: x30
STACK CFI 12af8 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 12b10 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 12b20 28 .cfa: sp 0 + .ra: x30
STACK CFI 12b28 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 12b40 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 12b50 dc .cfa: sp 0 + .ra: x30
STACK CFI 12b58 .cfa: sp 64 +
STACK CFI 12b64 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 12b74 .ra: .cfa -32 + ^
STACK CFI 12bbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 12bc4 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 12c30 104 .cfa: sp 0 + .ra: x30
STACK CFI 12c38 .cfa: sp 64 +
STACK CFI 12c44 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 12c54 .ra: .cfa -32 + ^
STACK CFI 12c98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 12ca0 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 12d28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 12d30 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 12d34 e0 .cfa: sp 0 + .ra: x30
STACK CFI 12d3c .cfa: sp 64 +
STACK CFI 12d48 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 12d58 .ra: .cfa -32 + ^
STACK CFI 12da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 12da8 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 12e14 100 .cfa: sp 0 + .ra: x30
STACK CFI 12e1c .cfa: sp 64 +
STACK CFI 12e28 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 12e38 .ra: .cfa -32 + ^
STACK CFI 12e7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 12e84 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 12f08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 12f10 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 12f14 100 .cfa: sp 0 + .ra: x30
STACK CFI 12f1c .cfa: sp 64 +
STACK CFI 12f28 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 12f38 .ra: .cfa -32 + ^
STACK CFI 12f7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 12f84 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 13008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13010 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 13014 100 .cfa: sp 0 + .ra: x30
STACK CFI 1301c .cfa: sp 64 +
STACK CFI 13028 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 13038 .ra: .cfa -32 + ^
STACK CFI 1307c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13084 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 13108 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13110 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 13114 100 .cfa: sp 0 + .ra: x30
STACK CFI 1311c .cfa: sp 64 +
STACK CFI 13128 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 13138 .ra: .cfa -32 + ^
STACK CFI 1317c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13184 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 13208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13210 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 13214 100 .cfa: sp 0 + .ra: x30
STACK CFI 1321c .cfa: sp 64 +
STACK CFI 13228 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 13238 .ra: .cfa -32 + ^
STACK CFI 1327c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13284 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 13308 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13310 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 13314 100 .cfa: sp 0 + .ra: x30
STACK CFI 1331c .cfa: sp 64 +
STACK CFI 13328 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 13338 .ra: .cfa -32 + ^
STACK CFI 1337c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13384 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 13408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13410 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 13414 e0 .cfa: sp 0 + .ra: x30
STACK CFI 1341c .cfa: sp 64 +
STACK CFI 13428 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 13438 .ra: .cfa -32 + ^
STACK CFI 13480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13488 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 134f4 100 .cfa: sp 0 + .ra: x30
STACK CFI 134fc .cfa: sp 64 +
STACK CFI 13508 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 13518 .ra: .cfa -32 + ^
STACK CFI 1355c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13564 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 135e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 135f0 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 135f4 100 .cfa: sp 0 + .ra: x30
STACK CFI 135fc .cfa: sp 64 +
STACK CFI 13608 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 13618 .ra: .cfa -32 + ^
STACK CFI 1365c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13664 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 136e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 136f0 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 136f4 100 .cfa: sp 0 + .ra: x30
STACK CFI 136fc .cfa: sp 64 +
STACK CFI 13708 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 13718 .ra: .cfa -32 + ^
STACK CFI 1375c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13764 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 137e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 137f0 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 137f4 108 .cfa: sp 0 + .ra: x30
STACK CFI 137fc .cfa: sp 64 +
STACK CFI 13808 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 13814 x21: .cfa -8 + ^
STACK CFI 1381c .ra: .cfa -32 + ^
STACK CFI 13880 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 13888 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI INIT 13900 104 .cfa: sp 0 + .ra: x30
STACK CFI 13908 .cfa: sp 64 +
STACK CFI 13914 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 13924 .ra: .cfa -32 + ^
STACK CFI 13968 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13970 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 139f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13a00 .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 13a04 74 .cfa: sp 0 + .ra: x30
STACK CFI 13a0c .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI 13a4c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 13a58 .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI 13a6c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 13a80 34 .cfa: sp 0 + .ra: x30
STACK CFI 13a88 .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI 13aac .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 13ab4 7c .cfa: sp 0 + .ra: x30
STACK CFI 13abc .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI 13b10 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 13b18 .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI INIT 13b30 3c .cfa: sp 0 + .ra: x30
STACK CFI 13b38 .cfa: sp 16 + .ra: .cfa -16 + ^ x19: .cfa -8 + ^
STACK CFI 13b64 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 13b70 d4 .cfa: sp 0 + .ra: x30
STACK CFI 13b78 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^
STACK CFI 13b80 x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI 13c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 13c2c .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI INIT 13c44 58 .cfa: sp 0 + .ra: x30
STACK CFI 13c4c .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 13c54 x20: .cfa -16 + ^
STACK CFI 13c84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 13c8c .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 13ca0 94 .cfa: sp 0 + .ra: x30
STACK CFI 13ca8 .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^
STACK CFI 13cb0 x20: .cfa -48 + ^ x21: .cfa -40 + ^
STACK CFI 13cb8 x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI 13ccc x24: .cfa -16 + ^ x25: .cfa -8 + ^
STACK CFI 13d2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI INIT 13d34 8c .cfa: sp 0 + .ra: x30
STACK CFI 13d3c .cfa: sp 64 + .ra: .cfa -64 + ^ x19: .cfa -56 + ^
STACK CFI 13d44 x20: .cfa -48 + ^ x21: .cfa -40 + ^
STACK CFI 13d4c x22: .cfa -32 + ^ x23: .cfa -24 + ^
STACK CFI 13d60 x24: .cfa -16 + ^
STACK CFI 13db8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI INIT 13dc0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13dd4 38a4 .cfa: sp 0 + .ra: x30
STACK CFI 13ddc .cfa: sp 256 +
STACK CFI 13df0 .ra: .cfa -96 + ^ x19: .cfa -88 + ^
STACK CFI 13df8 x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI 13ebc x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI 13f18 x24: x24 x25: x25
STACK CFI 13f28 x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI 141b4 x20: x20 x21: x21
STACK CFI 141e0 .cfa: sp 0 + .ra: .ra x19: x19 x26: x26 x27: x27
STACK CFI 141e8 .cfa: sp 256 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI 1421c .cfa: sp 0 + .ra: .ra x19: x19 x26: x26 x27: x27
STACK CFI 14224 .cfa: sp 256 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI 14228 x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI 14230 x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI 1427c x28: .cfa -16 + ^
STACK CFI 1429c x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI 14570 x22: x22 x23: x23
STACK CFI 1457c x28: x28
STACK CFI 145a8 x20: x20 x21: x21
STACK CFI 145b0 x24: x24 x25: x25
STACK CFI 145bc .cfa: sp 0 + .ra: .ra x19: x19 x26: x26 x27: x27
STACK CFI 145c4 .cfa: sp 256 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI 145f8 .cfa: sp 0 + .ra: .ra x19: x19 x26: x26 x27: x27
STACK CFI 14600 .cfa: sp 256 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI 14604 x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI 14614 x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI 14618 x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI 147f8 x28: .cfa -16 + ^
STACK CFI 14acc x28: x28
STACK CFI 14b14 x20: x20 x21: x21
STACK CFI 14b1c x22: x22 x23: x23
STACK CFI 14b20 x24: x24 x25: x25
STACK CFI 14b24 x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^ x28: .cfa -16 + ^
STACK CFI 14e0c x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x28: x28
STACK CFI 14e10 x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI 14e18 x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI 14e24 x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI 14e2c x28: .cfa -16 + ^
STACK CFI 15af4 x20: x20 x21: x21
STACK CFI 15af8 x22: x22 x23: x23
STACK CFI 15b00 x24: x24 x25: x25
STACK CFI 15b08 x28: x28
STACK CFI 15b0c .cfa: sp 0 + .ra: .ra x19: x19 x26: x26 x27: x27
STACK CFI 15b14 .cfa: sp 256 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^ x28: .cfa -16 + ^
STACK CFI 15b48 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x28: x28
STACK CFI 15b54 x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI 15b5c x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI 15b60 x28: .cfa -16 + ^
STACK CFI 15bdc x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI 15e3c x22: x22 x23: x23
STACK CFI 15e90 x20: x20 x21: x21
STACK CFI 15e94 x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI 15fc8 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x28: x28
STACK CFI 15fd0 x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI 16028 x28: .cfa -16 + ^
STACK CFI 16040 x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI 16044 x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI 165d0 x22: x22 x23: x23
STACK CFI 165d8 x24: x24 x25: x25
STACK CFI 165dc x28: x28
STACK CFI 1660c x20: x20 x21: x21
STACK CFI 16610 x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI 16614 x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI 1661c x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI 16624 x28: .cfa -16 + ^
STACK CFI 16a24 v8: .cfa -8 + ^
STACK CFI 16a28 v8: v8 x20: x20 x21: x21 x22: x22 x23: x23 x28: x28
STACK CFI 16a34 x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI 16a40 x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI 16a48 x28: .cfa -16 + ^
STACK CFI 16a4c v8: .cfa -8 + ^
STACK CFI 16f9c x20: x20 x21: x21
STACK CFI 16fa0 x22: x22 x23: x23
STACK CFI 16fa4 x24: x24 x25: x25
STACK CFI 16fa8 x28: x28
STACK CFI 16fac v8: v8
STACK CFI 16fb0 x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI 16fb4 x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI 16fc0 x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI 16fc4 x28: .cfa -16 + ^
STACK CFI 17418 x22: x22 x23: x23 x28: x28
STACK CFI 17420 x20: x20 x21: x21
STACK CFI 17434 x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI 1743c x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI 17448 x28: .cfa -16 + ^
STACK CFI 17620 x20: x20 x21: x21
STACK CFI 17624 x22: x22 x23: x23
STACK CFI 17628 x24: x24 x25: x25
STACK CFI 1762c x28: x28
STACK CFI 17630 x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI 17638 x20: x20 x21: x21
STACK CFI 1763c x20: .cfa -80 + ^ x21: .cfa -72 + ^
STACK CFI 17640 x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI 17644 x24: .cfa -48 + ^ x25: .cfa -40 + ^
STACK CFI 17648 x28: .cfa -16 + ^
STACK CFI 1764c v8: .cfa -8 + ^
STACK CFI 17650 v8: v8 x22: x22 x23: x23 x28: x28
STACK CFI 17654 x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI 17658 x28: .cfa -16 + ^
STACK CFI 1765c v8: .cfa -8 + ^
STACK CFI 17660 v8: v8
STACK CFI 1766c x22: x22 x23: x23
STACK CFI 17670 x22: .cfa -64 + ^ x23: .cfa -56 + ^
STACK CFI 17674 v8: .cfa -8 + ^
STACK CFI INIT 17680 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 17690 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 176a0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 176b0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 176c0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 176d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 176e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 176f0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17700 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 17710 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 17720 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 17730 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 17740 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17750 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17760 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17780 44 .cfa: sp 0 + .ra: x30
STACK CFI 17798 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 177ac .cfa: sp 0 + .ra: .ra
STACK CFI 177b4 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 177b8 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 177c4 20 .cfa: sp 0 + .ra: x30
STACK CFI 177cc .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 177d8 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 177e4 20 .cfa: sp 0 + .ra: x30
STACK CFI 177ec .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 177f8 .cfa: sp 0 + .ra: .ra
STACK CFI INIT 17804 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17830 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17854 a0 .cfa: sp 0 + .ra: x30
STACK CFI 17860 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 1789c x20: .cfa -16 + ^
STACK CFI 178dc x20: x20
STACK CFI 178e8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 178f4 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17904 a4 .cfa: sp 0 + .ra: x30
STACK CFI 17910 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 17950 x20: .cfa -16 + ^
STACK CFI 17990 x20: x20
STACK CFI 1799c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 179b0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 179c0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 179c8 .cfa: sp 48 +
STACK CFI 179f0 .ra: .cfa -32 + ^
STACK CFI 179fc x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 17a2c x19: x19 x20: x20
STACK CFI 17a50 .cfa: sp 0 + .ra: .ra
STACK CFI 17a58 .cfa: sp 48 + .ra: .cfa -32 + ^
STACK CFI 17a5c x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 17a60 d0 .cfa: sp 0 + .ra: x30
STACK CFI 17a68 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^
STACK CFI 17a6c x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI 17ab4 x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI 17b00 x22: x22 x23: x23
STACK CFI 17b04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 17b0c .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI 17b28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 17b30 12c .cfa: sp 0 + .ra: x30
STACK CFI 17b40 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 17b50 x20: .cfa -16 + ^
STACK CFI 17bcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 17bd4 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 17c20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 17c24 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 17c60 158 .cfa: sp 0 + .ra: x30
STACK CFI 17c68 .cfa: sp 80 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^
STACK CFI 17c74 x22: .cfa -48 + ^ x23: .cfa -40 + ^
STACK CFI 17c7c x24: .cfa -32 + ^ x25: .cfa -24 + ^
STACK CFI 17c98 x20: .cfa -64 + ^ x21: .cfa -56 + ^
STACK CFI 17ca0 x26: .cfa -16 + ^ x27: .cfa -8 + ^
STACK CFI 17d38 x20: x20 x21: x21
STACK CFI 17d48 x26: x26 x27: x27
STACK CFI 17d4c .cfa: sp 0 + .ra: .ra x19: x19 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 17d54 .cfa: sp 80 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^ x20: .cfa -64 + ^ x21: .cfa -56 + ^ x22: .cfa -48 + ^ x23: .cfa -40 + ^ x24: .cfa -32 + ^ x25: .cfa -24 + ^ x26: .cfa -16 + ^ x27: .cfa -8 + ^
STACK CFI 17d7c x20: x20 x21: x21
STACK CFI 17d80 x26: x26 x27: x27
STACK CFI 17d90 .cfa: sp 0 + .ra: .ra x19: x19 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 17d98 .cfa: sp 80 + .ra: .cfa -80 + ^ x19: .cfa -72 + ^ x20: .cfa -64 + ^ x21: .cfa -56 + ^ x22: .cfa -48 + ^ x23: .cfa -40 + ^ x24: .cfa -32 + ^ x25: .cfa -24 + ^ x26: .cfa -16 + ^ x27: .cfa -8 + ^
STACK CFI 17db0 x20: x20 x21: x21
STACK CFI 17db4 x26: x26 x27: x27
STACK CFI INIT 17dc0 da4 .cfa: sp 0 + .ra: x30
STACK CFI 17dc8 .cfa: sp 128 +
STACK CFI 17dd4 x21: .cfa -24 + ^
STACK CFI 17de4 .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^
STACK CFI 17e20 x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI 17ea4 x22: x22 x23: x23
STACK CFI 17edc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 17ee4 .cfa: sp 128 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI 17fec x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI 1800c x22: x22 x23: x23
STACK CFI 18b60 x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI INIT 18b64 488 .cfa: sp 0 + .ra: x30
STACK CFI 18b6c .cfa: sp 112 +
STACK CFI 18b70 x19: .cfa -40 + ^ x20: .cfa -32 + ^
STACK CFI 18b9c .ra: .cfa -48 + ^ x21: .cfa -24 + ^
STACK CFI 18bf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 18bf8 .cfa: sp 112 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI 18c38 x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI 18ccc x22: x22 x23: x23
STACK CFI 18ea0 x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI 18f60 x22: x22 x23: x23
STACK CFI 18f68 x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI 18f88 x22: x22 x23: x23
STACK CFI 18f8c x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI 18fd0 x22: x22 x23: x23
STACK CFI 18fd8 x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI 18fdc x22: x22 x23: x23
STACK CFI 18fe8 x22: .cfa -16 + ^ x23: .cfa -8 + ^
STACK CFI INIT 18ff0 244 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19234 d30 .cfa: sp 0 + .ra: x30
STACK CFI 1923c .cfa: sp 96 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^
STACK CFI 1925c .cfa: sp 624 + x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^ x28: .cfa -16 + ^
STACK CFI 1930c x26: .cfa -32 + ^
STACK CFI 19310 x27: .cfa -24 + ^
STACK CFI 19464 x26: x26
STACK CFI 19468 x27: x27
STACK CFI 1949c x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI 19698 x26: x26
STACK CFI 1969c x27: x27
STACK CFI 196c0 .cfa: sp 96 +
STACK CFI 196d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x28: x28
STACK CFI 196dc .cfa: sp 624 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^ x28: .cfa -16 + ^
STACK CFI 196f0 x26: x26 x27: x27
STACK CFI 19728 x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI 19a7c x26: x26 x27: x27
STACK CFI 19ac0 x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI 19ad8 x26: x26 x27: x27
STACK CFI 19ae0 x26: .cfa -32 + ^ x27: .cfa -24 + ^
STACK CFI 19f24 x26: x26 x27: x27
STACK CFI 19f28 x26: .cfa -32 + ^
STACK CFI 19f2c x27: .cfa -24 + ^
STACK CFI INIT 19f64 430 .cfa: sp 0 + .ra: x30
STACK CFI 19f6c .cfa: sp 160 + .ra: .cfa -160 + ^ x19: .cfa -152 + ^
STACK CFI 19f74 x20: .cfa -144 + ^ x21: .cfa -136 + ^
STACK CFI 19f7c x22: .cfa -128 + ^ x23: .cfa -120 + ^
STACK CFI 19fd0 x28: .cfa -80 + ^
STACK CFI 19fe8 x24: .cfa -112 + ^ x25: .cfa -104 + ^
STACK CFI 19fec x26: .cfa -96 + ^ x27: .cfa -88 + ^
STACK CFI 1a354 x24: x24 x25: x25
STACK CFI 1a358 x26: x26 x27: x27
STACK CFI 1a35c x28: x28
STACK CFI 1a368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 1a370 .cfa: sp 160 + .ra: .cfa -160 + ^ x19: .cfa -152 + ^ x20: .cfa -144 + ^ x21: .cfa -136 + ^ x22: .cfa -128 + ^ x23: .cfa -120 + ^
STACK CFI 1a38c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 1a394 300 .cfa: sp 0 + .ra: x30
STACK CFI 1a39c .cfa: sp 128 + .ra: .cfa -128 + ^ x19: .cfa -120 + ^
STACK CFI 1a3a0 x22: .cfa -96 + ^ x23: .cfa -88 + ^
STACK CFI 1a3b8 x20: .cfa -112 + ^ x21: .cfa -104 + ^ x24: .cfa -80 + ^ x25: .cfa -72 + ^ x26: .cfa -64 + ^ x27: .cfa -56 + ^ x28: .cfa -48 + ^
STACK CFI 1a678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1a680 .cfa: sp 128 + .ra: .cfa -128 + ^ x19: .cfa -120 + ^ x20: .cfa -112 + ^ x21: .cfa -104 + ^ x22: .cfa -96 + ^ x23: .cfa -88 + ^ x24: .cfa -80 + ^ x25: .cfa -72 + ^ x26: .cfa -64 + ^ x27: .cfa -56 + ^ x28: .cfa -48 + ^
STACK CFI INIT 1a694 238 .cfa: sp 0 + .ra: x30
STACK CFI 1a69c .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^
STACK CFI 1a6a0 x22: .cfa -16 + ^
STACK CFI 1a6ac x20: .cfa -32 + ^ x21: .cfa -24 + ^
STACK CFI 1a8b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 1a8c0 .cfa: sp 48 + .ra: .cfa -48 + ^ x19: .cfa -40 + ^ x20: .cfa -32 + ^ x21: .cfa -24 + ^ x22: .cfa -16 + ^
STACK CFI INIT 1a8d0 28 .cfa: sp 0 + .ra: x30
STACK CFI 1a8e0 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 1a8ec .cfa: sp 0 + .ra: .ra
STACK CFI INIT 1a900 28 .cfa: sp 0 + .ra: x30
STACK CFI 1a910 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 1a91c .cfa: sp 0 + .ra: .ra
STACK CFI INIT 1a930 28 .cfa: sp 0 + .ra: x30
STACK CFI 1a940 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 1a94c .cfa: sp 0 + .ra: .ra
STACK CFI INIT 1a960 c4 .cfa: sp 0 + .ra: x30
STACK CFI 1a970 .cfa: sp 48 +
STACK CFI 1a980 .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 1a9ac x20: .cfa -16 + ^
STACK CFI 1a9c8 x20: x20
STACK CFI 1a9f0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 1a9f8 .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 1aa20 x20: .cfa -16 + ^
STACK CFI INIT 1aa24 e4 .cfa: sp 0 + .ra: x30
STACK CFI 1aa2c .cfa: sp 48 +
STACK CFI 1aa30 x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI 1aa44 .ra: .cfa -32 + ^
STACK CFI 1aae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 1aaec .cfa: sp 48 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^
STACK CFI INIT 1ab10 28 .cfa: sp 0 + .ra: x30
STACK CFI 1ab20 .cfa: sp 16 + .ra: .cfa -16 + ^
STACK CFI 1ab2c .cfa: sp 0 + .ra: .ra
STACK CFI INIT 1ab40 84 .cfa: sp 0 + .ra: x30
STACK CFI 1ab48 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^
STACK CFI 1ab50 x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI 1ab98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 1aba0 .cfa: sp 32 + .ra: .cfa -32 + ^ x19: .cfa -24 + ^ x20: .cfa -16 + ^ x21: .cfa -8 + ^
STACK CFI 1abbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 1abc4 274 .cfa: sp 0 + .ra: x30
STACK CFI 1abcc .cfa: sp 160 +
STACK CFI 1abd0 x25: .cfa -40 + ^ x26: .cfa -32 + ^
STACK CFI 1abe4 x19: .cfa -88 + ^ x20: .cfa -80 + ^
STACK CFI 1abf4 .ra: .cfa -96 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^
STACK CFI 1abfc x27: .cfa -24 + ^ x28: .cfa -16 + ^
STACK CFI 1adec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1adf4 .cfa: sp 160 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^ x28: .cfa -16 + ^
STACK CFI INIT 1ae40 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 1ae48 .cfa: sp 160 +
STACK CFI 1ae60 .ra: .cfa -96 + ^ x19: .cfa -88 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^
STACK CFI 1ae6c x25: .cfa -40 + ^ x26: .cfa -32 + ^
STACK CFI 1ae78 x27: .cfa -24 + ^ x28: .cfa -16 + ^
STACK CFI 1b028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1b030 .cfa: sp 160 + .ra: .cfa -96 + ^ x19: .cfa -88 + ^ x20: .cfa -80 + ^ x21: .cfa -72 + ^ x22: .cfa -64 + ^ x23: .cfa -56 + ^ x24: .cfa -48 + ^ x25: .cfa -40 + ^ x26: .cfa -32 + ^ x27: .cfa -24 + ^ x28: .cfa -16 + ^
STACK CFI INIT 1b040 1c .cfa: sp 0 + .ra: x30
STACK CFI 1b044 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b054 .cfa: sp 0 + .ra: .ra x29: x29
