MODULE Linux arm64 4CCF9CBA95AE705B77D0BEF2464B4F830 libpipewire-module-raop-sink.so
INFO CODE_ID BA9CCF4CAE955B7077D0BEF2464B4F83DD2AB1BD
PUBLIC ade0 0 pipewire__module_init
STACK CFI INIT 3f50 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f80 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3fc0 48 .cfa: sp 0 + .ra: x30
STACK CFI 3fc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3fcc x19: .cfa -16 + ^
STACK CFI 4004 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4010 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4020 130 .cfa: sp 0 + .ra: x30
STACK CFI 4028 .cfa: sp 112 +
STACK CFI 403c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4144 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 414c .cfa: sp 112 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4150 1c .cfa: sp 0 + .ra: x30
STACK CFI 4158 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4164 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4170 88 .cfa: sp 0 + .ra: x30
STACK CFI 41b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4200 7c .cfa: sp 0 + .ra: x30
STACK CFI 4214 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 422c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4234 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4248 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4280 138 .cfa: sp 0 + .ra: x30
STACK CFI 4288 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4290 x23: .cfa -16 + ^
STACK CFI 42a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4320 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 43c0 25c .cfa: sp 0 + .ra: x30
STACK CFI 43c8 .cfa: sp 240 +
STACK CFI 43cc .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 43d4 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 4400 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^
STACK CFI 4534 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 453c .cfa: sp 240 + .ra: .cfa -72 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4620 6a8 .cfa: sp 0 + .ra: x30
STACK CFI 4628 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 462c .cfa: x29 96 +
STACK CFI 4630 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4648 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 484c .cfa: x29 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4cd0 38c .cfa: sp 0 + .ra: x30
STACK CFI 4cd8 .cfa: sp 144 +
STACK CFI 4ce4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4cec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4d0c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4d4c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4d94 x25: .cfa -16 + ^
STACK CFI 4e24 x25: x25
STACK CFI 4e28 x23: x23 x24: x24
STACK CFI 4e84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4e8c .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 4eb4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4f20 x23: x23 x24: x24
STACK CFI 4f24 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 4f44 x23: x23 x24: x24
STACK CFI 4f4c x25: x25
STACK CFI 4f50 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4fd0 x23: x23 x24: x24
STACK CFI 5000 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 5018 x23: x23 x24: x24 x25: x25
STACK CFI 5020 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 5050 x23: x23 x24: x24 x25: x25
STACK CFI 5054 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5058 x25: .cfa -16 + ^
STACK CFI INIT 5060 32c .cfa: sp 0 + .ra: x30
STACK CFI 5068 .cfa: sp 128 +
STACK CFI 5074 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 507c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5084 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 508c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 515c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5164 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5390 340 .cfa: sp 0 + .ra: x30
STACK CFI 5398 .cfa: sp 240 +
STACK CFI 53a4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 53ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 53f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 53f8 .cfa: sp 240 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 540c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5464 x21: x21 x22: x22
STACK CFI 54d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 54e0 .cfa: sp 240 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 54f8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 55d0 x21: x21 x22: x22
STACK CFI 55d4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 560c x21: x21 x22: x22
STACK CFI 5610 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5660 x21: x21 x22: x22
STACK CFI 5664 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 567c x21: x21 x22: x22
STACK CFI 56b0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 56c8 x21: x21 x22: x22
STACK CFI 56cc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 56d0 2cc .cfa: sp 0 + .ra: x30
STACK CFI 56d8 .cfa: sp 48 +
STACK CFI 56e4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 56ec x19: .cfa -16 + ^
STACK CFI 5730 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5738 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 581c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5824 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 59a0 1ac .cfa: sp 0 + .ra: x30
STACK CFI 59a8 .cfa: sp 48 +
STACK CFI 59b4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 59bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5a88 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5ad8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5ae0 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5b50 78c .cfa: sp 0 + .ra: x30
STACK CFI 5b58 .cfa: sp 160 +
STACK CFI 5b64 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5b6c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5b80 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5b9c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5c8c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5dfc x27: x27 x28: x28
STACK CFI 5e38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5e40 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 5e58 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5e84 x27: x27 x28: x28
STACK CFI 5e90 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5edc x27: x27 x28: x28
STACK CFI 5f48 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5f84 x27: x27 x28: x28
STACK CFI 6024 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6050 x27: x27 x28: x28
STACK CFI 60ac x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 60b4 x27: x27 x28: x28
STACK CFI 60c0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6104 x27: x27 x28: x28
STACK CFI 6108 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 617c x27: x27 x28: x28
STACK CFI 6180 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6184 x27: x27 x28: x28
STACK CFI 6188 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 620c x27: x27 x28: x28
STACK CFI 6210 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 62d4 x27: x27 x28: x28
STACK CFI 62d8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 62e0 140 .cfa: sp 0 + .ra: x30
STACK CFI 62e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 62f0 x19: .cfa -16 + ^
STACK CFI 6418 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6420 b0 .cfa: sp 0 + .ra: x30
STACK CFI 6428 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6438 x19: .cfa -16 + ^
STACK CFI 6468 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6470 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 64c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 64d0 ac .cfa: sp 0 + .ra: x30
STACK CFI 64d8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 64e0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 64ec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6550 x21: x21 x22: x22
STACK CFI 6554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 655c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6568 x21: x21 x22: x22
STACK CFI 6574 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6580 90 .cfa: sp 0 + .ra: x30
STACK CFI 6590 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 659c x19: .cfa -16 + ^
STACK CFI 65c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 65d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6610 104 .cfa: sp 0 + .ra: x30
STACK CFI 6618 .cfa: sp 96 +
STACK CFI 661c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6624 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6630 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 666c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6674 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 670c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6714 54 .cfa: sp 0 + .ra: x30
STACK CFI 671c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6724 x19: .cfa -16 + ^
STACK CFI 6760 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6770 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 6778 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6788 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6790 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 682c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 6898 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 68a8 x25: .cfa -16 + ^
STACK CFI 68fc x23: x23 x24: x24
STACK CFI 6900 x25: x25
STACK CFI 6904 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 690c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 6914 x23: x23 x24: x24 x25: x25
STACK CFI 692c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6934 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 6938 x23: x23 x24: x24
STACK CFI 6940 x25: x25
STACK CFI INIT 6944 354 .cfa: sp 0 + .ra: x30
STACK CFI 694c .cfa: sp 112 +
STACK CFI 6950 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6958 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6968 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6c48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6c50 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6ca0 50 .cfa: sp 0 + .ra: x30
STACK CFI 6ca8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6cb0 x19: .cfa -16 + ^
STACK CFI 6ce8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6cf0 288 .cfa: sp 0 + .ra: x30
STACK CFI 6cf8 .cfa: sp 144 +
STACK CFI 6d08 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6d18 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6d38 x23: .cfa -16 + ^
STACK CFI 6d44 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6dd4 x21: x21 x22: x22
STACK CFI 6dd8 x23: x23
STACK CFI 6e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6e08 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6f6c x21: x21 x22: x22 x23: x23
STACK CFI 6f70 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6f74 x23: .cfa -16 + ^
STACK CFI INIT 6f80 284 .cfa: sp 0 + .ra: x30
STACK CFI 6f88 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6f98 .cfa: sp 4192 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6fc0 x21: .cfa -48 + ^
STACK CFI 6fc4 x22: .cfa -40 + ^
STACK CFI 6fc8 x23: .cfa -32 + ^
STACK CFI 6fcc x24: .cfa -24 + ^
STACK CFI 6fd4 x25: .cfa -16 + ^
STACK CFI 70e8 x21: x21
STACK CFI 70ec x22: x22
STACK CFI 70f0 x23: x23
STACK CFI 70f4 x24: x24
STACK CFI 70f8 x25: x25
STACK CFI 7130 .cfa: sp 80 +
STACK CFI 7138 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7140 .cfa: sp 4192 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 7148 x21: x21
STACK CFI 714c x22: x22
STACK CFI 7150 x23: x23
STACK CFI 7154 x24: x24
STACK CFI 7158 x25: x25
STACK CFI 715c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 71b8 x21: x21
STACK CFI 71bc x22: x22
STACK CFI 71c0 x23: x23
STACK CFI 71c4 x24: x24
STACK CFI 71c8 x25: x25
STACK CFI 71cc x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 71ec x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 71f0 x21: .cfa -48 + ^
STACK CFI 71f4 x22: .cfa -40 + ^
STACK CFI 71f8 x23: .cfa -32 + ^
STACK CFI 71fc x24: .cfa -24 + ^
STACK CFI 7200 x25: .cfa -16 + ^
STACK CFI INIT 7204 b30 .cfa: sp 0 + .ra: x30
STACK CFI 720c .cfa: sp 224 +
STACK CFI 7218 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7220 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7228 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7240 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 73f8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 746c x25: x25 x26: x26
STACK CFI 74c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 74d0 .cfa: sp 224 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7504 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 750c .cfa: sp 224 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 7648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7650 .cfa: sp 224 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 76dc x25: x25 x26: x26
STACK CFI 7708 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 772c x25: x25 x26: x26
STACK CFI 780c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7810 x25: x25 x26: x26
STACK CFI 7814 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7820 x25: x25 x26: x26
STACK CFI 7914 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7954 x25: x25 x26: x26
STACK CFI 7984 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 79d0 x25: x25 x26: x26
STACK CFI 79d8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7a8c x25: x25 x26: x26
STACK CFI 7acc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7ae4 x25: x25 x26: x26
STACK CFI 7b18 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7b5c x25: x25 x26: x26
STACK CFI 7b88 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7bcc x25: x25 x26: x26
STACK CFI 7c60 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7c9c x25: x25 x26: x26
STACK CFI 7ca4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7ca8 x25: x25 x26: x26
STACK CFI 7cb4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 7ce4 x25: x25 x26: x26
STACK CFI 7d04 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 7d34 fc .cfa: sp 0 + .ra: x30
STACK CFI 7d3c .cfa: sp 352 +
STACK CFI 7d4c .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 7d5c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 7e1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7e24 .cfa: sp 352 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x29: .cfa -208 + ^
STACK CFI INIT 7e30 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 7e38 .cfa: sp 64 +
STACK CFI 7e3c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7e44 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7e58 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7efc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7f04 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7ff4 2b28 .cfa: sp 0 + .ra: x30
STACK CFI 7ffc .cfa: sp 480 +
STACK CFI 8010 .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 8028 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 8030 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 8048 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 87a0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 87a8 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT ab20 138 .cfa: sp 0 + .ra: x30
STACK CFI ab28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ac4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT ac60 180 .cfa: sp 0 + .ra: x30
STACK CFI ac68 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI ac7c .cfa: sp 1392 + x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^
STACK CFI adc0 .cfa: sp 240 +
STACK CFI add4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI addc .cfa: sp 1392 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x29: .cfa -240 + ^
STACK CFI INIT ade0 1308 .cfa: sp 0 + .ra: x30
STACK CFI ade8 .cfa: sp 144 +
STACK CFI adf4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI adfc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ae04 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ae18 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI afc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI afcc .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI afdc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b0c8 x25: x25 x26: x26
STACK CFI b194 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI b1e4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI b9e4 x25: x25 x26: x26
STACK CFI b9e8 x27: x27 x28: x28
STACK CFI ba70 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI ba7c x25: x25 x26: x26
STACK CFI bab0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI bad8 x25: x25 x26: x26
STACK CFI bae0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI bb18 x25: x25 x26: x26
STACK CFI bb1c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI bb60 x27: x27 x28: x28
STACK CFI bb68 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI bbe4 x25: x25 x26: x26
STACK CFI bbe8 x27: x27 x28: x28
STACK CFI bbec x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI bc50 x25: x25 x26: x26
STACK CFI bc54 x27: x27 x28: x28
STACK CFI bc88 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI bca0 x27: x27 x28: x28
STACK CFI bcf4 x25: x25 x26: x26
STACK CFI bcf8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI bd10 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI be98 x27: x27 x28: x28
STACK CFI beb4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c068 x27: x27 x28: x28
STACK CFI c080 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c0b0 x25: x25 x26: x26
STACK CFI c0b4 x27: x27 x28: x28
STACK CFI c0bc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c0c0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c0c4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI c0d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c0e0 x25: x25 x26: x26
STACK CFI c0e4 x27: x27 x28: x28
STACK CFI INIT c0f0 250 .cfa: sp 0 + .ra: x30
STACK CFI c0f8 .cfa: sp 144 +
STACK CFI c104 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c110 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c118 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI c128 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c130 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c13c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI c2d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI c2dc .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT c340 58 .cfa: sp 0 + .ra: x30
STACK CFI c348 .cfa: sp 32 +
STACK CFI c35c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c390 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c3a0 1dc .cfa: sp 0 + .ra: x30
STACK CFI c3a8 .cfa: sp 64 +
STACK CFI c3b4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c3bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI c4b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c4bc .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT c580 5f8 .cfa: sp 0 + .ra: x30
STACK CFI c588 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c59c .cfa: sp 1536 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI c908 .cfa: sp 80 +
STACK CFI c918 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI c920 .cfa: sp 1536 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI ca24 x25: .cfa -16 + ^
STACK CFI ca90 x25: x25
STACK CFI caa4 x25: .cfa -16 + ^
STACK CFI cae8 x25: x25
STACK CFI caf4 x25: .cfa -16 + ^
STACK CFI cafc x25: x25
STACK CFI cb58 x25: .cfa -16 + ^
STACK CFI cb60 x25: x25
STACK CFI cb74 x25: .cfa -16 + ^
STACK CFI INIT cb80 250 .cfa: sp 0 + .ra: x30
STACK CFI cb88 .cfa: sp 64 +
STACK CFI cb94 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI cb9c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cba4 x21: .cfa -16 + ^
STACK CFI cce0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI cce8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI cd7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI cd84 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT cdd0 668 .cfa: sp 0 + .ra: x30
STACK CFI cdd8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI cde8 .cfa: sp 2560 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ceb8 x23: .cfa -48 + ^
STACK CFI cebc x24: .cfa -40 + ^
STACK CFI cf08 x23: x23
STACK CFI cf0c x24: x24
STACK CFI cf34 .cfa: sp 96 +
STACK CFI cf44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cf4c .cfa: sp 2560 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI cfec x25: .cfa -32 + ^
STACK CFI d000 x26: .cfa -24 + ^
STACK CFI d010 x23: .cfa -48 + ^
STACK CFI d024 x24: .cfa -40 + ^
STACK CFI d064 x27: .cfa -16 + ^
STACK CFI d068 x28: .cfa -8 + ^
STACK CFI d368 x25: x25
STACK CFI d36c x26: x26
STACK CFI d370 x27: x27
STACK CFI d374 x28: x28
STACK CFI d378 x23: x23 x24: x24
STACK CFI d384 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d388 x23: x23
STACK CFI d38c x24: x24
STACK CFI d394 x25: x25
STACK CFI d398 x26: x26
STACK CFI d39c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d3a8 x23: x23
STACK CFI d3b0 x24: x24
STACK CFI d3b4 x27: x27
STACK CFI d3b8 x28: x28
STACK CFI d3bc x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI d408 x23: x23
STACK CFI d410 x24: x24
STACK CFI d414 x27: x27
STACK CFI d418 x28: x28
STACK CFI d41c x25: x25 x26: x26
STACK CFI d420 x23: .cfa -48 + ^
STACK CFI d424 x24: .cfa -40 + ^
STACK CFI d428 x25: .cfa -32 + ^
STACK CFI d42c x26: .cfa -24 + ^
STACK CFI d430 x27: .cfa -16 + ^
STACK CFI d434 x28: .cfa -8 + ^
STACK CFI INIT d440 9c .cfa: sp 0 + .ra: x30
STACK CFI d448 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d458 x19: .cfa -16 + ^
STACK CFI d47c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI d484 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI d4d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT d4e0 588 .cfa: sp 0 + .ra: x30
STACK CFI d4e8 .cfa: sp 208 +
STACK CFI d4f8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d53c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI d544 .cfa: sp 208 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d548 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d560 x19: x19 x20: x20
STACK CFI d590 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI d598 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI d59c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d5a8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d5f4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d608 x27: .cfa -16 + ^
STACK CFI d73c x19: x19 x20: x20
STACK CFI d740 x21: x21 x22: x22
STACK CFI d744 x23: x23 x24: x24
STACK CFI d748 x25: x25 x26: x26
STACK CFI d74c x27: x27
STACK CFI d750 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d774 x19: x19 x20: x20
STACK CFI d778 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI d780 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI d7d0 x19: x19 x20: x20
STACK CFI d7d4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d7d8 x19: x19 x20: x20
STACK CFI d7dc x21: x21 x22: x22
STACK CFI d7e0 x23: x23 x24: x24
STACK CFI d7e4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI d9d4 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI d9d8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d9dc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d9e0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d9e4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d9e8 x27: .cfa -16 + ^
STACK CFI INIT da70 120 .cfa: sp 0 + .ra: x30
STACK CFI da78 .cfa: sp 48 +
STACK CFI da84 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI da8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI dac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI dac8 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI db34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI db3c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI db88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT db90 410 .cfa: sp 0 + .ra: x30
STACK CFI db98 .cfa: sp 112 +
STACK CFI dba4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI dbac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI dbb8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI dbc0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI dd38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI dd40 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI dd98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI dda0 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI dec0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI df10 x25: x25 x26: x26
STACK CFI df18 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI df28 x25: x25 x26: x26
STACK CFI df9c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT dfa0 340 .cfa: sp 0 + .ra: x30
STACK CFI dfa8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e094 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e0a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e1d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e1e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e234 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e23c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e2b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e2bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT e2e0 50 .cfa: sp 0 + .ra: x30
STACK CFI e2e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e300 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e308 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e314 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI e31c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e328 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e330 50 .cfa: sp 0 + .ra: x30
STACK CFI e338 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e340 x19: .cfa -16 + ^
STACK CFI e378 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e380 128 .cfa: sp 0 + .ra: x30
STACK CFI e388 .cfa: sp 112 +
STACK CFI e39c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e3a4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e3b0 x21: .cfa -16 + ^
STACK CFI e49c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI e4a4 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT e4b0 114 .cfa: sp 0 + .ra: x30
STACK CFI e4b8 .cfa: sp 96 +
STACK CFI e4c8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e4d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI e5b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e5c0 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT e5c4 f4 .cfa: sp 0 + .ra: x30
STACK CFI e5cc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e5d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e5e0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI e5ec v8: .cfa -16 + ^
STACK CFI e6b0 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT e6c0 4fc .cfa: sp 0 + .ra: x30
STACK CFI e6c8 .cfa: sp 160 +
STACK CFI e6cc .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI e6d4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI e6e8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI e6f0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI e6f4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI e6f8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI e7bc v8: .cfa -48 + ^
STACK CFI e86c v8: v8
STACK CFI e8e0 x21: x21 x22: x22
STACK CFI e8e4 x23: x23 x24: x24
STACK CFI e8e8 x25: x25 x26: x26
STACK CFI e8ec x27: x27 x28: x28
STACK CFI e8f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e900 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI e9f0 v8: .cfa -48 + ^
STACK CFI ea40 v8: v8
STACK CFI ea70 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI eaa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI eaac .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI eac4 v8: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI eac8 v8: v8
STACK CFI eaf8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI eb0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI eb2c .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT ebc0 510 .cfa: sp 0 + .ra: x30
STACK CFI ebc8 .cfa: sp 160 +
STACK CFI ebcc .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI ebd4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI ebe8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI ebf0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI ebf4 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI ebf8 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI ecbc v8: .cfa -48 + ^
STACK CFI ed6c v8: v8
STACK CFI ede0 x21: x21 x22: x22
STACK CFI ede4 x23: x23 x24: x24
STACK CFI ede8 x25: x25 x26: x26
STACK CFI edec x27: x27 x28: x28
STACK CFI edf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ee00 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI eef4 v8: .cfa -48 + ^
STACK CFI ef48 v8: v8
STACK CFI ef78 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI efac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI efb4 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI efcc v8: .cfa -48 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI efd0 v8: v8
STACK CFI f000 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f038 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT f0d0 62c .cfa: sp 0 + .ra: x30
STACK CFI f0d8 .cfa: sp 288 +
STACK CFI f0e4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f0ec x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f10c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI f118 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI f11c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f120 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f418 x21: x21 x22: x22
STACK CFI f41c x23: x23 x24: x24
STACK CFI f420 x25: x25 x26: x26
STACK CFI f424 x27: x27 x28: x28
STACK CFI f44c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f454 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI f4c4 x21: x21 x22: x22
STACK CFI f4c8 x23: x23 x24: x24
STACK CFI f4cc x25: x25 x26: x26
STACK CFI f4d0 x27: x27 x28: x28
STACK CFI f4d4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f604 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f65c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f680 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI f698 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f6e8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f6ec x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI f6f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI f6f4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f6f8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT f700 76c .cfa: sp 0 + .ra: x30
STACK CFI f708 .cfa: sp 272 +
STACK CFI f714 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f71c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f73c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI f744 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI f74c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f7f4 x21: x21 x22: x22
STACK CFI f7f8 x23: x23 x24: x24
STACK CFI f7fc x25: x25 x26: x26
STACK CFI f808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f810 .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI f814 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI facc x27: x27 x28: x28
STACK CFI fad0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI fad4 x27: x27 x28: x28
STACK CFI fad8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI fb28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fb30 .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI fb48 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI fb54 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI fbd4 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI fc04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fc28 .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI fe58 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI fe5c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI fe60 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI fe64 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI fe68 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT fe70 788 .cfa: sp 0 + .ra: x30
STACK CFI fe78 .cfa: sp 288 +
STACK CFI fe84 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI fe8c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI feac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI feb4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI feb8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI febc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ffdc x21: x21 x22: x22
STACK CFI ffe0 x23: x23 x24: x24
STACK CFI ffe4 x25: x25 x26: x26
STACK CFI ffe8 x27: x27 x28: x28
STACK CFI 10010 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10018 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 10038 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1008c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 100b0 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI 100c8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 105d4 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 105d8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 105dc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 105e0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 105e4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 10600 360 .cfa: sp 0 + .ra: x30
STACK CFI 10608 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1061c .cfa: sp 1568 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 10684 .cfa: sp 96 +
STACK CFI 10694 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1069c .cfa: sp 1568 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 106a0 x25: .cfa -32 + ^
STACK CFI 106a8 x26: .cfa -24 + ^
STACK CFI 106b0 x27: .cfa -16 + ^
STACK CFI 106b8 x28: .cfa -8 + ^
STACK CFI 108a0 x25: x25
STACK CFI 108a4 x26: x26
STACK CFI 108a8 x27: x27
STACK CFI 108ac x28: x28
STACK CFI 108b0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1094c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 10950 x25: .cfa -32 + ^
STACK CFI 10954 x26: .cfa -24 + ^
STACK CFI 10958 x27: .cfa -16 + ^
STACK CFI 1095c x28: .cfa -8 + ^
STACK CFI INIT 10960 3c0 .cfa: sp 0 + .ra: x30
STACK CFI 10968 .cfa: sp 128 +
STACK CFI 1096c .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 10974 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 10988 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 10994 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1099c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 109a0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 10ac0 x21: x21 x22: x22
STACK CFI 10ac4 x23: x23 x24: x24
STACK CFI 10ac8 x25: x25 x26: x26
STACK CFI 10acc x27: x27 x28: x28
STACK CFI 10ad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10ad8 .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 10bf0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 10c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10c2c .cfa: sp 128 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 10c44 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 10ce8 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 10cfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10d20 30c .cfa: sp 0 + .ra: x30
STACK CFI 10d28 .cfa: sp 96 +
STACK CFI 10d34 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10d3c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10dbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10de0 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 10e28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10e30 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 11030 4c4 .cfa: sp 0 + .ra: x30
STACK CFI 11038 .cfa: sp 448 +
STACK CFI 11048 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1105c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 11064 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 11074 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 111e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 111e8 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 114f4 20c .cfa: sp 0 + .ra: x30
STACK CFI 114fc .cfa: sp 96 +
STACK CFI 11508 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 11514 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1151c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 11528 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 11690 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 11698 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 116e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 116ec .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 11700 178 .cfa: sp 0 + .ra: x30
STACK CFI 11708 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 11718 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 11724 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1172c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1173c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11748 .cfa: sp 624 + x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11804 .cfa: sp 96 +
STACK CFI 1181c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 11824 .cfa: sp 624 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 11880 6f0 .cfa: sp 0 + .ra: x30
STACK CFI 11888 .cfa: sp 160 +
STACK CFI 1188c .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 11894 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 118a4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 118ac x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 118b4 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 118f4 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 1198c v8: .cfa -32 + ^
STACK CFI 11a54 v8: v8
STACK CFI 11a94 x19: x19 x20: x20
STACK CFI 11a9c x21: x21 x22: x22
STACK CFI 11aa0 x23: x23 x24: x24
STACK CFI 11aa4 x27: x27 x28: x28
STACK CFI 11ab0 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 11ab8 .cfa: sp 160 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 11b04 v8: .cfa -32 + ^
STACK CFI 11b1c v8: v8
STACK CFI 11c7c x27: x27 x28: x28
STACK CFI 11ca8 x19: x19 x20: x20
STACK CFI 11cac x21: x21 x22: x22
STACK CFI 11cb0 x23: x23 x24: x24
STACK CFI 11cb8 v8: .cfa -32 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 11d1c v8: v8
STACK CFI 11da4 x27: x27 x28: x28
STACK CFI 11ddc x19: x19 x20: x20
STACK CFI 11de4 x21: x21 x22: x22
STACK CFI 11de8 x23: x23 x24: x24
STACK CFI 11dec x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 11e04 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 11e5c x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 11eb0 x19: x19 x20: x20
STACK CFI 11eb8 x21: x21 x22: x22
STACK CFI 11ebc x23: x23 x24: x24
STACK CFI 11ed8 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 11f60 x19: x19 x20: x20
STACK CFI 11f68 x21: x21 x22: x22
STACK CFI 11f6c x23: x23 x24: x24
STACK CFI INIT 11f70 e88 .cfa: sp 0 + .ra: x30
STACK CFI 11f78 .cfa: sp 320 +
STACK CFI 11f88 .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 11fa4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 11fac x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 11fc8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 11ff4 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 12000 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 124c0 x19: x19 x20: x20
STACK CFI 124c4 x21: x21 x22: x22
STACK CFI 124c8 x23: x23 x24: x24
STACK CFI 124cc x25: x25 x26: x26
STACK CFI 124d0 x27: x27 x28: x28
STACK CFI 124f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 124fc .cfa: sp 320 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 12668 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 1266c v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 12674 v12: .cfa -16 + ^ v13: .cfa -8 + ^
STACK CFI 127a8 v10: v10 v11: v11
STACK CFI 127b0 v8: v8 v9: v9
STACK CFI 127b4 v12: v12 v13: v13
STACK CFI 127bc v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 12868 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9
STACK CFI 12934 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 1296c v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 12998 x19: x19 x20: x20
STACK CFI 1299c x25: x25 x26: x26
STACK CFI 129a0 x27: x27 x28: x28
STACK CFI 129a8 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 129f8 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9
STACK CFI 12a9c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 12ad4 x19: x19 x20: x20
STACK CFI 12adc x27: x27 x28: x28
STACK CFI 12ae0 v10: .cfa -32 + ^ v11: .cfa -24 + ^ v12: .cfa -16 + ^ v13: .cfa -8 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 12b48 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 12b60 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 12b78 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 12bfc x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 12c50 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 12ccc x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 12d20 x19: x19 x20: x20
STACK CFI 12d28 x25: x25 x26: x26
STACK CFI 12d2c x27: x27 x28: x28
STACK CFI 12d30 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 12d48 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 12d64 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 12d90 x19: x19 x20: x20
STACK CFI 12d98 x25: x25 x26: x26
STACK CFI 12d9c x27: x27 x28: x28
STACK CFI 12da4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 12da8 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 12dac x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 12db0 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 12db4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 12db8 v8: .cfa -48 + ^ v9: .cfa -40 + ^
STACK CFI 12dbc v10: .cfa -32 + ^ v11: .cfa -24 + ^
STACK CFI 12dc0 v12: .cfa -16 + ^ v13: .cfa -8 + ^
STACK CFI 12dc4 v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI INIT 12e00 74c .cfa: sp 0 + .ra: x30
STACK CFI 12e08 .cfa: sp 176 +
STACK CFI 12e0c .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 12e18 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 12e28 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 12e30 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 12e38 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 12e3c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 12f04 v8: .cfa -48 + ^
STACK CFI 12fd4 v8: v8
STACK CFI 1301c x19: x19 x20: x20
STACK CFI 13024 x21: x21 x22: x22
STACK CFI 13028 x23: x23 x24: x24
STACK CFI 1302c x25: x25 x26: x26
STACK CFI 13038 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 13040 .cfa: sp 176 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 1308c v8: .cfa -48 + ^
STACK CFI 130a4 v8: v8
STACK CFI 132b0 x19: x19 x20: x20
STACK CFI 132b4 x21: x21 x22: x22
STACK CFI 132b8 x23: x23 x24: x24
STACK CFI 132bc x25: x25 x26: x26
STACK CFI 132c4 v8: .cfa -48 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 13334 v8: v8
STACK CFI 133ac x19: x19 x20: x20
STACK CFI 133b4 x21: x21 x22: x22
STACK CFI 133b8 x23: x23 x24: x24
STACK CFI 133bc x25: x25 x26: x26
STACK CFI 133c0 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 133d8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 13430 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 13484 x19: x19 x20: x20
STACK CFI 1348c x21: x21 x22: x22
STACK CFI 13490 x23: x23 x24: x24
STACK CFI 13494 x25: x25 x26: x26
STACK CFI 134b0 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 13538 x19: x19 x20: x20
STACK CFI 13540 x21: x21 x22: x22
STACK CFI 13544 x23: x23 x24: x24
STACK CFI 13548 x25: x25 x26: x26
STACK CFI INIT 13550 d6c .cfa: sp 0 + .ra: x30
STACK CFI 13558 .cfa: sp 128 +
STACK CFI 1355c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 13564 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1357c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1368c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 13694 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 142c0 fe4 .cfa: sp 0 + .ra: x30
STACK CFI 142c8 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 142d8 .cfa: sp 1344 + x19: .cfa -112 + ^ x20: .cfa -104 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 14314 x21: .cfa -96 + ^
STACK CFI 1431c x22: .cfa -88 + ^
STACK CFI 14320 x23: .cfa -80 + ^
STACK CFI 14328 x24: .cfa -72 + ^
STACK CFI 14448 x27: .cfa -48 + ^
STACK CFI 14450 x28: .cfa -40 + ^
STACK CFI 14454 v8: .cfa -32 + ^
STACK CFI 14458 v9: .cfa -24 + ^
STACK CFI 1445c v10: .cfa -16 + ^
STACK CFI 14460 v11: .cfa -8 + ^
STACK CFI 14a6c x21: x21
STACK CFI 14a70 x22: x22
STACK CFI 14a74 x23: x23
STACK CFI 14a78 x24: x24
STACK CFI 14a7c x27: x27
STACK CFI 14a80 x28: x28
STACK CFI 14a84 v8: v8
STACK CFI 14a88 v9: v9
STACK CFI 14a8c v10: v10
STACK CFI 14a90 v11: v11
STACK CFI 14ab0 .cfa: sp 128 +
STACK CFI 14ac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 14ac8 .cfa: sp 1344 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 14adc v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 14b04 v10: v10 v11: v11 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI 14bb4 x27: .cfa -48 + ^
STACK CFI 14bbc x28: .cfa -40 + ^
STACK CFI 14bc0 v8: .cfa -32 + ^
STACK CFI 14bc4 v9: .cfa -24 + ^
STACK CFI 14bc8 v10: .cfa -16 + ^
STACK CFI 14bcc v11: .cfa -8 + ^
STACK CFI 14be4 v10: v10 v11: v11 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI 14c5c x27: .cfa -48 + ^
STACK CFI 14c64 x28: .cfa -40 + ^
STACK CFI 14c6c v8: .cfa -32 + ^
STACK CFI 14c74 v9: .cfa -24 + ^
STACK CFI 14c7c v10: .cfa -16 + ^
STACK CFI 14c84 v11: .cfa -8 + ^
STACK CFI 14de4 x21: x21
STACK CFI 14de8 x22: x22
STACK CFI 14dec x23: x23
STACK CFI 14df0 x24: x24
STACK CFI 14df4 x27: x27
STACK CFI 14df8 x28: x28
STACK CFI 14dfc v8: v8
STACK CFI 14e00 v9: v9
STACK CFI 14e04 v10: v10
STACK CFI 14e08 v11: v11
STACK CFI 14e20 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 1507c v10: v10 v11: v11 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI 15084 x21: x21
STACK CFI 1508c x22: x22
STACK CFI 15090 x23: x23
STACK CFI 15094 x24: x24
STACK CFI 15098 v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 150e8 v10: v10 v11: v11 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI 151fc v10: .cfa -16 + ^ v11: .cfa -8 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 15224 v10: v10 v11: v11 v8: v8 v9: v9 x27: x27 x28: x28
STACK CFI 1526c x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 15270 x21: .cfa -96 + ^
STACK CFI 15274 x22: .cfa -88 + ^
STACK CFI 15278 x23: .cfa -80 + ^
STACK CFI 1527c x24: .cfa -72 + ^
STACK CFI 15280 x27: .cfa -48 + ^
STACK CFI 15284 x28: .cfa -40 + ^
STACK CFI 15288 v8: .cfa -32 + ^
STACK CFI 1528c v9: .cfa -24 + ^
STACK CFI 15290 v10: .cfa -16 + ^
STACK CFI 15294 v11: .cfa -8 + ^
STACK CFI 15298 v10: v10 v11: v11 v8: v8 v9: v9 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI INIT 152a4 d8 .cfa: sp 0 + .ra: x30
STACK CFI 152ac .cfa: sp 336 +
STACK CFI 152bc .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 15370 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 15378 .cfa: sp 336 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI INIT 15380 164 .cfa: sp 0 + .ra: x30
STACK CFI 15388 .cfa: sp 96 +
STACK CFI 1538c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15394 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 154d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 154e0 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 154e4 264 .cfa: sp 0 + .ra: x30
STACK CFI 154ec .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 154fc .cfa: sp 1920 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 15550 x23: .cfa -32 + ^
STACK CFI 15558 x24: .cfa -24 + ^
STACK CFI 15560 x25: .cfa -16 + ^
STACK CFI 155f0 x23: x23
STACK CFI 155f4 x24: x24
STACK CFI 155f8 x25: x25
STACK CFI 15638 .cfa: sp 80 +
STACK CFI 15644 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1564c .cfa: sp 1920 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 156e8 .cfa: sp 80 +
STACK CFI 1570c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 15724 .cfa: sp 1920 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 1573c x23: .cfa -32 + ^
STACK CFI 15740 x24: .cfa -24 + ^
STACK CFI 15744 x25: .cfa -16 + ^
STACK CFI INIT 15750 130 .cfa: sp 0 + .ra: x30
STACK CFI 15758 .cfa: sp 64 +
STACK CFI 1575c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 15764 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15784 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1578c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 15790 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 15860 x21: x21 x22: x22
STACK CFI 15870 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 15878 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 15880 150 .cfa: sp 0 + .ra: x30
STACK CFI 15888 .cfa: sp 272 +
STACK CFI 1588c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 15894 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 158b8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1598c x21: x21 x22: x22
STACK CFI 159b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 159bc .cfa: sp 272 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 159c8 x21: x21 x22: x22
STACK CFI 159cc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 159d0 7fc .cfa: sp 0 + .ra: x30
STACK CFI 159d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 159ec .cfa: sp 1696 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 15a88 .cfa: sp 96 +
STACK CFI 15a98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 15aa0 .cfa: sp 1696 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 15ab4 x23: .cfa -48 + ^
STACK CFI 15abc x24: .cfa -40 + ^
STACK CFI 15b28 x23: x23
STACK CFI 15b2c x24: x24
STACK CFI 15b30 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 15b50 x27: .cfa -16 + ^
STACK CFI 15b54 x28: .cfa -8 + ^
STACK CFI 15cb8 x27: x27
STACK CFI 15cbc x28: x28
STACK CFI 15d18 x27: .cfa -16 + ^
STACK CFI 15d1c x28: .cfa -8 + ^
STACK CFI 15d70 x27: x27
STACK CFI 15d78 x28: x28
STACK CFI 15d88 x27: .cfa -16 + ^
STACK CFI 15d8c x28: .cfa -8 + ^
STACK CFI 15edc x27: x27 x28: x28
STACK CFI 15ef8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 161b8 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 161bc x23: .cfa -48 + ^
STACK CFI 161c0 x24: .cfa -40 + ^
STACK CFI 161c4 x27: .cfa -16 + ^
STACK CFI 161c8 x28: .cfa -8 + ^
