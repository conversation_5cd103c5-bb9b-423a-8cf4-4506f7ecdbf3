MODULE Linux arm64 708CB18ABBBEA2558E0D85B227AFBE500 libboost_context.so.1.77.0
INFO CODE_ID 8AB18C70BEBB55A28E0D85B227AFBE50
PUBLIC 8e8 0 _init
PUBLIC 9a0 0 call_weak_fn
PUBLIC 9c0 0 deregister_tm_clones
PUBLIC 9f0 0 register_tm_clones
PUBLIC a30 0 __do_global_dtors_aux
PUBLIC a80 0 frame_dummy
PUBLIC a84 0 make_fcontext
PUBLIC aa4 0 jump_fcontext
PUBLIC b14 0 ontop_fcontext
PUBLIC b80 0 boost::context::stack_traits::is_unbounded()
PUBLIC c20 0 boost::context::stack_traits::page_size()
PUBLIC ca0 0 boost::context::stack_traits::default_size()
PUBLIC cb0 0 boost::context::stack_traits::minimum_size()
PUBLIC cc0 0 boost::context::stack_traits::maximum_size()
PUBLIC d50 0 _fini
STACK CFI INIT 9c0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9f0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT a30 48 .cfa: sp 0 + .ra: x30
STACK CFI a34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a3c x19: .cfa -16 + ^
STACK CFI a74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a80 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b80 98 .cfa: sp 0 + .ra: x30
STACK CFI b84 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b94 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI be4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI be8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT c20 78 .cfa: sp 0 + .ra: x30
STACK CFI c24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c30 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI c58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI c94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT ca0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT cb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT cc0 90 .cfa: sp 0 + .ra: x30
STACK CFI cc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI cd4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d20 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
