MODULE Linux arm64 39D2B4CC93B870D35B3636719BDD685C0 liblidds.so.3
INFO CODE_ID CCB4D239B893D3705B3636719BDD685C
PUBLIC 3840 0 _init
PUBLIC 3c50 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&) [clone .constprop.0]
PUBLIC 3d60 0 _GLOBAL__sub_I_lidds_dds_pro.cpp
PUBLIC 3f70 0 _GLOBAL__sub_I_lidds_logger.cpp
PUBLIC 3f90 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&) [clone .constprop.0]
PUBLIC 40a0 0 _GLOBAL__sub_I_system_publisher_listener.cpp
PUBLIC 4260 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string<std::allocator<char> >(char const*, std::allocator<char> const&) [clone .constprop.0]
PUBLIC 4370 0 _GLOBAL__sub_I_system_subscriber_listener.cpp
PUBLIC 4530 0 call_weak_fn
PUBLIC 4550 0 deregister_tm_clones
PUBLIC 4580 0 register_tm_clones
PUBLIC 45c0 0 __do_global_dtors_aux
PUBLIC 4610 0 frame_dummy
PUBLIC 4620 0 lios::lidds::(anonymous namespace)::ParticipantMap::~ParticipantMap()
PUBLIC 4720 0 lios::lidds::LiddsField::GetTopic(vbs::DomainParticipant*, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, evbs::edds::dds::TypeSupport const&)
PUBLIC 48b0 0 lios::lidds::LiddsField::GetReaderProfName(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 4980 0 lios::lidds::LiddsField::GetWriterProfName(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 4a50 0 lios::lidds::(anonymous namespace)::ParticipantMap::FindBy(int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&) [clone .constprop.0]
PUBLIC 5820 0 lios::lidds::LiddsField::GetParticipant(int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 5980 0 lios::lidds::LiddsField::CreateReader(int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, evbs::edds::dds::TypeSupport const&)
PUBLIC 5ac0 0 lios::lidds::LiddsField::CreateWriter(int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, evbs::edds::dds::TypeSupport const&)
PUBLIC 5c00 0 evbs::edds::dds::TypeSupport::get_type_name[abi:cxx11]() const
PUBLIC 5c10 0 std::once_flag::_Prepare_execution::_Prepare_execution<std::call_once<void (&)() noexcept>(std::once_flag&, void (&)() noexcept)::{lambda()#1}>(void (&)() noexcept)::{lambda()#1}::_FUN()
PUBLIC 5c40 0 std::filesystem::__cxx11::path::~path()
PUBLIC 5c90 0 std::_Hashtable<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, vbs::DomainParticipant*>, std::allocator<std::pair<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const, vbs::DomainParticipant*> >, std::__detail::_Select1st, std::equal_to<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::hash<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > >, std::__detail::_Mod_range_hashing, std::__detail::_Default_ranged_hash, std::__detail::_Prime_rehash_policy, std::__detail::_Hashtable_traits<true, false, true> >::_M_rehash(unsigned long, unsigned long const&)
PUBLIC 5dc0 0 lios::lidds::LiddsLogger::Enable()
PUBLIC 6040 0 lios::lidds::SystemPublisherListener::OnPublicationMatched(vbs::PublicationMatchedStatus const&)
PUBLIC 6070 0 lios::lidds::SystemPublisherListener::OnLivelinessLost(vbs::LivelinessLostStatus const&)
PUBLIC 6090 0 lios::lidds::SystemPublisherListener::~SystemPublisherListener()
PUBLIC 60d0 0 lios::lidds::SystemPublisherListener::~SystemPublisherListener()
PUBLIC 6100 0 lios::lidds::SystemPublisherListener::SystemPublisherListener(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >)
PUBLIC 61a0 0 lios::lidds::LiddsPublisherListener::OnOfferedDeadlineMissed(vbs::DeadlineMissedStatus const&)
PUBLIC 61b0 0 lios::lidds::SystemSubscriberListener::OnSampleLost(vbs::BaseStatus const&)
PUBLIC 61e0 0 lios::lidds::SystemSubscriberListener::OnSubscriptionMatched(vbs::SubscriptionMatchedStatus const&)
PUBLIC 6210 0 lios::lidds::SystemSubscriberListener::OnLivelinessChange(vbs::LivelinessChangedStatus const&)
PUBLIC 6240 0 lios::lidds::SystemSubscriberListener::~SystemSubscriberListener()
PUBLIC 6280 0 lios::lidds::SystemSubscriberListener::~SystemSubscriberListener()
PUBLIC 62b0 0 lios::lidds::SystemSubscriberListener::SystemSubscriberListener(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >)
PUBLIC 6350 0 lios::lidds::LiddsSubscriberListener::OnRequestedDeadlineMissed(vbs::DeadlineMissedStatus const&)
PUBLIC 6354 0 _fini
STACK CFI INIT 4550 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4580 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 45c0 48 .cfa: sp 0 + .ra: x30
STACK CFI 45c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 45cc x19: .cfa -16 + ^
STACK CFI 4604 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4610 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c00 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5c10 30 .cfa: sp 0 + .ra: x30
STACK CFI 5c18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5c34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3c50 104 .cfa: sp 0 + .ra: x30
STACK CFI 3c54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3c64 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3c6c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3cec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4620 fc .cfa: sp 0 + .ra: x30
STACK CFI 4624 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 462c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4634 x21: .cfa -16 + ^
STACK CFI 46fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4700 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4718 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5c40 50 .cfa: sp 0 + .ra: x30
STACK CFI 5c44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c50 x19: .cfa -16 + ^
STACK CFI 5c80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5c84 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5c8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4720 190 .cfa: sp 0 + .ra: x30
STACK CFI 4724 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 472c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 473c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 47d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 47d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x29: .cfa -96 + ^
STACK CFI INIT 48b0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 48b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 48c8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 48d4 x21: .cfa -16 + ^
STACK CFI 4944 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4948 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4980 c8 .cfa: sp 0 + .ra: x30
STACK CFI 4984 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4998 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 49a4 x21: .cfa -16 + ^
STACK CFI 4a14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4a18 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5c90 12c .cfa: sp 0 + .ra: x30
STACK CFI 5c94 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5ca0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ca8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5d50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4a50 dc8 .cfa: sp 0 + .ra: x30
STACK CFI 4a54 .cfa: sp 512 +
STACK CFI 4a60 .ra: .cfa -504 + ^ x29: .cfa -512 + ^
STACK CFI 4a6c x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^
STACK CFI 4a74 x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 4a84 x25: .cfa -448 + ^ x26: .cfa -440 + ^
STACK CFI 4a8c x27: .cfa -432 + ^ x28: .cfa -424 + ^
STACK CFI 4fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4fd8 .cfa: sp 512 + .ra: .cfa -504 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^ x28: .cfa -424 + ^ x29: .cfa -512 + ^
STACK CFI INIT 5820 154 .cfa: sp 0 + .ra: x30
STACK CFI 5824 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5834 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5840 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 590c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5910 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5980 134 .cfa: sp 0 + .ra: x30
STACK CFI 5984 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5994 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 59a0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 59ac x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 59b4 x25: .cfa -64 + ^
STACK CFI 5a58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5a5c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5ac0 134 .cfa: sp 0 + .ra: x30
STACK CFI 5ac4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5ad4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5ae0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5aec x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 5af4 x25: .cfa -64 + ^
STACK CFI 5b98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 5b9c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3d60 210 .cfa: sp 0 + .ra: x30
STACK CFI 3d64 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3d78 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3d88 x23: .cfa -16 + ^
STACK CFI 3f6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5dc0 274 .cfa: sp 0 + .ra: x30
STACK CFI 5dc4 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 5de8 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 5df0 x21: .cfa -160 + ^
STACK CFI 5f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5f1c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x29: .cfa -192 + ^
STACK CFI INIT 3f70 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 61a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6040 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6070 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f90 104 .cfa: sp 0 + .ra: x30
STACK CFI 3f94 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3fa4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3fac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4028 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 402c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6090 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 60d0 28 .cfa: sp 0 + .ra: x30
STACK CFI 60d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 60dc x19: .cfa -16 + ^
STACK CFI 60f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6100 a0 .cfa: sp 0 + .ra: x30
STACK CFI 6104 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6118 x21: .cfa -16 + ^
STACK CFI 6120 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 616c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 619c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 40a0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 40a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 40b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 40cc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 425c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6350 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 61b0 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 61e0 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6210 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4260 104 .cfa: sp 0 + .ra: x30
STACK CFI 4264 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4274 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 427c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 42f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 42fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6240 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6280 28 .cfa: sp 0 + .ra: x30
STACK CFI 6284 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 628c x19: .cfa -16 + ^
STACK CFI 62a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 62b0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 62b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 62c8 x21: .cfa -16 + ^
STACK CFI 62d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6318 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 631c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 634c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 4370 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 4374 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4384 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 439c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 452c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
