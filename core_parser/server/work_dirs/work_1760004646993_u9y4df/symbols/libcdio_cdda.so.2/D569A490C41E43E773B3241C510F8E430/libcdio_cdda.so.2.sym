MODULE Linux arm64 D569A490C41E43E773B3241C510F8E430 libcdio_cdda.so.2
INFO CODE_ID 90A469D51EC4E74373B3241C510F8E43DAB2DFCB
PUBLIC 3440 0 cdio_cddap_version
PUBLIC 3460 0 cdio_cddap_close_no_free_cdio
PUBLIC 34e4 0 cdio_cddap_close
PUBLIC 3530 0 cdio_cddap_speed_set
PUBLIC 3580 0 cdio_cddap_verbose_set
PUBLIC 35a0 0 cdio_cddap_messages
PUBLIC 35c4 0 cdio_cddap_errors
PUBLIC 35f0 0 cdio_cddap_free_messages
PUBLIC 3620 0 cdio_cddap_identify
PUBLIC 3730 0 cdio_cddap_find_a_cdrom
PUBLIC 3890 0 cdio_cddap_identify_cdio
PUBLIC 3910 0 cdio_cddap_track_firstsector
PUBLIC 3a50 0 cdio_cddap_track_lastsector
PUBLIC 3bc0 0 cdio_cddap_tracks
PUBLIC 3c00 0 cdio_cddap_sector_gettrack
PUBLIC 3c70 0 cdio_cddap_track_channels
PUBLIC 3c90 0 cdio_cddap_track_audiop
PUBLIC 3cc0 0 data_bigendianp
PUBLIC 41a0 0 cdio_cddap_read_timed
PUBLIC 42c4 0 cdio_cddap_read
PUBLIC 42e0 0 cdio_cddap_open
PUBLIC 4670 0 cdio_cddap_disc_firstsector
PUBLIC 4770 0 cdio_cddap_disc_lastsector
PUBLIC 4830 0 cdio_cddap_track_copyp
PUBLIC 4860 0 cdio_cddap_track_preemp
STACK CFI INIT 1950 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1980 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 19c0 48 .cfa: sp 0 + .ra: x30
STACK CFI 19c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 19cc x19: .cfa -16 + ^
STACK CFI 1a04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1a10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a20 1c .cfa: sp 0 + .ra: x30
STACK CFI 1a28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a40 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 1a48 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1a7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1af0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1bb4 x21: x21 x22: x22
STACK CFI 1c08 x19: x19 x20: x20
STACK CFI 1c0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c14 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 1c18 x19: x19 x20: x20
STACK CFI 1c1c x21: x21 x22: x22
STACK CFI 1c20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1c30 39c .cfa: sp 0 + .ra: x30
STACK CFI 1c3c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 1c54 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 1c74 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1c80 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 1c90 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 1d5c x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 1e94 x19: x19 x20: x20
STACK CFI 1e98 x21: x21 x22: x22
STACK CFI 1e9c x23: x23 x24: x24
STACK CFI 1ea0 x25: x25 x26: x26
STACK CFI 1f74 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 1f7c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 1f80 x19: x19 x20: x20
STACK CFI 1f84 x21: x21 x22: x22
STACK CFI 1f88 x25: x25 x26: x26
STACK CFI 1f90 .cfa: sp 0 + .ra: .ra x27: x27 x28: x28 x29: x29
STACK CFI 1f98 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 1fa4 x19: x19 x20: x20
STACK CFI 1fac x21: x21 x22: x22
STACK CFI 1fb4 x25: x25 x26: x26
STACK CFI INIT 1fd0 1c .cfa: sp 0 + .ra: x30
STACK CFI 1fd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1fe0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ff0 94 .cfa: sp 0 + .ra: x30
STACK CFI 1ff8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2000 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2014 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 205c x21: x21 x22: x22
STACK CFI 2068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2070 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2084 98 .cfa: sp 0 + .ra: x30
STACK CFI 2090 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2098 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 20bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 20c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 20d0 x21: .cfa -16 + ^
STACK CFI 20f8 x21: x21
STACK CFI 2114 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2120 254 .cfa: sp 0 + .ra: x30
STACK CFI 2128 .cfa: sp 432 +
STACK CFI 2138 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2144 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 214c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2158 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2164 v8: .cfa -16 + ^ v9: .cfa -8 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 22dc .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 22e4 .cfa: sp 432 + .ra: .cfa -104 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 2374 1cc .cfa: sp 0 + .ra: x30
STACK CFI 237c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2384 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2390 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 23b0 x27: .cfa -16 + ^
STACK CFI 23bc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 23d4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2498 x21: x21 x22: x22
STACK CFI 24a4 x23: x23 x24: x24
STACK CFI 24b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 24b8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 24cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 24d4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 2540 84 .cfa: sp 0 + .ra: x30
STACK CFI 2550 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2558 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2580 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 259c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 25bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 25c4 2c4 .cfa: sp 0 + .ra: x30
STACK CFI 25cc .cfa: sp 96 +
STACK CFI 25d8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 25e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2614 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2618 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 26a0 x25: .cfa -16 + ^
STACK CFI 26f4 x25: x25
STACK CFI 272c x21: x21 x22: x22
STACK CFI 2730 x23: x23 x24: x24
STACK CFI 275c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2764 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 27e0 x25: x25
STACK CFI 285c x21: x21 x22: x22
STACK CFI 2864 x23: x23 x24: x24
STACK CFI 2870 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2878 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 287c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2880 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2884 x25: .cfa -16 + ^
STACK CFI INIT 2890 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 2898 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 28a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 28a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 28b0 x23: .cfa -16 + ^
STACK CFI 2928 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2930 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2970 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2978 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2a54 e4 .cfa: sp 0 + .ra: x30
STACK CFI 2a5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a70 .cfa: sp 4288 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2ae0 .cfa: sp 48 +
STACK CFI 2aec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2af4 .cfa: sp 4288 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2b40 17c .cfa: sp 0 + .ra: x30
STACK CFI 2b48 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2b50 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2b58 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2b60 x23: .cfa -16 + ^
STACK CFI 2bf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2bfc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2c40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c48 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2c74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c7c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2cb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2cc0 3b0 .cfa: sp 0 + .ra: x30
STACK CFI 2cc8 .cfa: sp 288 +
STACK CFI 2cd4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2ce0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2ce8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2d10 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2da8 x27: .cfa -16 + ^
STACK CFI 2e34 x27: x27
STACK CFI 2e40 x25: x25 x26: x26
STACK CFI 2e74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2e7c .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 2eb4 x25: x25 x26: x26
STACK CFI 2eb8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2ec0 x25: x25 x26: x26
STACK CFI 2ec4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2ee8 x27: .cfa -16 + ^
STACK CFI 2f34 x25: x25 x26: x26
STACK CFI 2f38 x27: x27
STACK CFI 2f5c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3064 x25: x25 x26: x26
STACK CFI 3068 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 306c x27: .cfa -16 + ^
STACK CFI INIT 3070 3d0 .cfa: sp 0 + .ra: x30
STACK CFI 3078 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 3084 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 3090 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 309c x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 30b0 x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 31b4 v12: .cfa -128 + ^ v13: .cfa -120 + ^
STACK CFI 31c8 v8: .cfa -160 + ^ v9: .cfa -152 + ^
STACK CFI 31e0 v10: .cfa -144 + ^ v11: .cfa -136 + ^
STACK CFI 32f4 v8: v8 v9: v9
STACK CFI 32f8 v10: v10 v11: v11
STACK CFI 32fc v12: v12 v13: v13
STACK CFI 3414 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 341c .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT 3440 20 .cfa: sp 0 + .ra: x30
STACK CFI 3448 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3458 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3460 84 .cfa: sp 0 + .ra: x30
STACK CFI 3470 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 347c x19: .cfa -16 + ^
STACK CFI 34d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 34e4 44 .cfa: sp 0 + .ra: x30
STACK CFI 34fc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3504 x19: .cfa -16 + ^
STACK CFI 3520 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3530 4c .cfa: sp 0 + .ra: x30
STACK CFI 3538 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3544 x19: .cfa -16 + ^
STACK CFI 355c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3564 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3580 20 .cfa: sp 0 + .ra: x30
STACK CFI 3588 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3590 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 35a0 24 .cfa: sp 0 + .ra: x30
STACK CFI 35ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 35c4 24 .cfa: sp 0 + .ra: x30
STACK CFI 35d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 35f0 28 .cfa: sp 0 + .ra: x30
STACK CFI 35f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3604 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 360c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3610 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3620 110 .cfa: sp 0 + .ra: x30
STACK CFI 3628 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3630 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3638 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 36a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 36b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3708 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3710 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3730 15c .cfa: sp 0 + .ra: x30
STACK CFI 3738 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3740 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3758 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3768 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^
STACK CFI 384c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 3854 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 3890 78 .cfa: sp 0 + .ra: x30
STACK CFI 38a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 38a8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 38bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 38fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3910 13c .cfa: sp 0 + .ra: x30
STACK CFI 3918 .cfa: sp 160 +
STACK CFI 391c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3928 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3950 x21: .cfa -16 + ^
STACK CFI 398c x21: x21
STACK CFI 39bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 39c4 .cfa: sp 160 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 39d4 x21: x21
STACK CFI 39ec x21: .cfa -16 + ^
STACK CFI 3a20 x21: x21
STACK CFI 3a28 x21: .cfa -16 + ^
STACK CFI 3a3c x21: x21
STACK CFI 3a48 x21: .cfa -16 + ^
STACK CFI INIT 3a50 168 .cfa: sp 0 + .ra: x30
STACK CFI 3a58 .cfa: sp 160 +
STACK CFI 3a5c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3a68 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3a90 x21: .cfa -16 + ^
STACK CFI 3afc x21: x21
STACK CFI 3b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3b2c .cfa: sp 160 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3b4c x21: x21
STACK CFI 3b54 x21: .cfa -16 + ^
STACK CFI 3b58 x21: x21
STACK CFI 3b74 x21: .cfa -16 + ^
STACK CFI 3ba8 x21: x21
STACK CFI 3bb4 x21: .cfa -16 + ^
STACK CFI INIT 3bc0 3c .cfa: sp 0 + .ra: x30
STACK CFI 3bdc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3bf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3c00 68 .cfa: sp 0 + .ra: x30
STACK CFI 3c08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3c34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3c4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c5c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3c70 1c .cfa: sp 0 + .ra: x30
STACK CFI 3c78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3c80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3c90 28 .cfa: sp 0 + .ra: x30
STACK CFI 3c98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3cac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3cc0 4dc .cfa: sp 0 + .ra: x30
STACK CFI 3cc8 .cfa: sp 432 +
STACK CFI 3cd8 .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3ce0 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3cf8 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3d00 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3d1c v8: .cfa -32 + ^ v9: .cfa -24 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3e58 v10: .cfa -16 + ^
STACK CFI 3fc4 v10: v10
STACK CFI 4068 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4070 .cfa: sp 432 + .ra: .cfa -120 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 4108 v10: .cfa -16 + ^
STACK CFI 4118 v10: v10
STACK CFI 411c v10: .cfa -16 + ^
STACK CFI 4124 v10: v10
STACK CFI 4198 v10: .cfa -16 + ^
STACK CFI INIT 41a0 124 .cfa: sp 0 + .ra: x30
STACK CFI 41a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 41b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 41bc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 425c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 42c4 1c .cfa: sp 0 + .ra: x30
STACK CFI 42cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 42d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 42e0 390 .cfa: sp 0 + .ra: x30
STACK CFI 42e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 42f0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4304 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 430c .cfa: sp 80 + .ra: .cfa -72 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4314 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4384 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4390 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 4488 x21: x21 x22: x22
STACK CFI 448c x25: x25 x26: x26
STACK CFI 44dc x19: x19 x20: x20
STACK CFI 44e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4504 x19: x19 x20: x20
STACK CFI 450c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 4514 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 452c x19: x19 x20: x20
STACK CFI 4538 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 4540 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 4580 x19: x19 x20: x20
STACK CFI 4588 x21: x21 x22: x22
STACK CFI 4590 x25: x25 x26: x26
STACK CFI 4594 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 459c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 45b0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4610 x21: x21 x22: x22
STACK CFI 4618 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 461c x21: x21 x22: x22
STACK CFI 4620 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 464c x19: x19 x20: x20
STACK CFI 4654 x21: x21 x22: x22
STACK CFI 4658 x25: x25 x26: x26
STACK CFI 465c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 466c x19: x19 x20: x20
STACK CFI INIT 4670 fc .cfa: sp 0 + .ra: x30
STACK CFI 4678 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4680 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4698 x23: .cfa -16 + ^
STACK CFI 46ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 46f4 x19: x19 x20: x20
STACK CFI 46fc x23: x23
STACK CFI 4708 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4710 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4714 x19: x19 x20: x20
STACK CFI 4728 x23: x23
STACK CFI 4734 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 473c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4740 x19: x19 x20: x20
STACK CFI 4748 x23: x23
STACK CFI 474c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4754 .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4770 bc .cfa: sp 0 + .ra: x30
STACK CFI 4778 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4788 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4794 x21: .cfa -16 + ^
STACK CFI 47d8 x21: x21
STACK CFI 47e4 x19: x19 x20: x20
STACK CFI 47e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 47f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4804 x19: x19 x20: x20
STACK CFI 480c x21: x21
STACK CFI 4810 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4818 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4830 28 .cfa: sp 0 + .ra: x30
STACK CFI 4838 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 484c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4860 28 .cfa: sp 0 + .ra: x30
STACK CFI 4868 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 487c .cfa: sp 0 + .ra: .ra x29: x29
