MODULE Linux arm64 B1994C4EEA5E20188C5E1045F7B629FB0 libpipewire-module-spa-node-factory.so
INFO CODE_ID 4E4C99B15EEA18208C5E1045F7B629FBCE8572A5
PUBLIC 7b80 0 pipewire__module_init
STACK CFI INIT 5c90 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5cc0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d00 48 .cfa: sp 0 + .ra: x30
STACK CFI 5d04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5d0c x19: .cfa -16 + ^
STACK CFI 5d44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5d50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d60 98 .cfa: sp 0 + .ra: x30
STACK CFI 5d68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5d78 x19: .cfa -16 + ^
STACK CFI 5d9c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5da4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5df0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5e00 cc .cfa: sp 0 + .ra: x30
STACK CFI 5e08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e18 x19: .cfa -16 + ^
STACK CFI 5e6c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5e74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5ec4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5ed0 f4 .cfa: sp 0 + .ra: x30
STACK CFI 5ed8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ee8 x19: .cfa -16 + ^
STACK CFI 5f64 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5f6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5fbc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5fc4 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5fcc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5fdc x19: .cfa -16 + ^
STACK CFI 6038 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6040 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6090 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 60a0 104 .cfa: sp 0 + .ra: x30
STACK CFI 60a8 .cfa: sp 96 +
STACK CFI 60ac .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 60b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 60c0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 60fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6104 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 619c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 61a4 108 .cfa: sp 0 + .ra: x30
STACK CFI 61ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 61bc x19: .cfa -16 + ^
STACK CFI 625c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6264 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 62b0 cc .cfa: sp 0 + .ra: x30
STACK CFI 62b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 62c8 x19: .cfa -16 + ^
STACK CFI 6328 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6330 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6374 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6380 114 .cfa: sp 0 + .ra: x30
STACK CFI 6388 .cfa: sp 48 +
STACK CFI 638c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6394 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 63b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 63b8 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6410 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6474 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 647c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6494 98 .cfa: sp 0 + .ra: x30
STACK CFI 649c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 64a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6514 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 651c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 6524 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6530 68 .cfa: sp 0 + .ra: x30
STACK CFI 6538 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6540 x19: .cfa -16 + ^
STACK CFI 6580 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6588 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6590 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 65a0 13c .cfa: sp 0 + .ra: x30
STACK CFI 65a8 .cfa: sp 96 +
STACK CFI 65b8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 65c0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6664 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 666c .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 66e0 254 .cfa: sp 0 + .ra: x30
STACK CFI 66e8 .cfa: sp 96 +
STACK CFI 66ec .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 66f4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6700 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6708 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 681c .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 6934 2f0 .cfa: sp 0 + .ra: x30
STACK CFI 693c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6944 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 694c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6960 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6964 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6968 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6af0 x19: x19 x20: x20
STACK CFI 6b00 x25: x25 x26: x26
STACK CFI 6b04 x27: x27 x28: x28
STACK CFI 6b08 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6b10 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6b20 x19: x19 x20: x20
STACK CFI 6b24 x25: x25 x26: x26
STACK CFI 6b28 x27: x27 x28: x28
STACK CFI 6b34 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6b3c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6bd0 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6be8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6bec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6bf0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6bfc x19: x19 x20: x20
STACK CFI 6c04 x25: x25 x26: x26
STACK CFI 6c08 x27: x27 x28: x28
STACK CFI 6c14 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6c1c .cfa: sp 96 + .ra: .cfa -88 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6c24 7e0 .cfa: sp 0 + .ra: x30
STACK CFI 6c2c .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 6c44 .cfa: sp 4384 + x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 6d38 x28: .cfa -24 + ^
STACK CFI 6d58 x25: .cfa -48 + ^
STACK CFI 6d64 x26: .cfa -40 + ^
STACK CFI 6d6c x27: .cfa -32 + ^
STACK CFI 6e1c x25: x25
STACK CFI 6e20 x26: x26
STACK CFI 6e24 x27: x27
STACK CFI 6e28 x28: x28
STACK CFI 6e50 .cfa: sp 112 +
STACK CFI 6e64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6e6c .cfa: sp 4384 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 6f7c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6fc8 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7070 v8: .cfa -16 + ^
STACK CFI 70e0 v8: v8
STACK CFI 70e4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7100 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7130 x25: x25
STACK CFI 7134 x26: x26
STACK CFI 7138 x27: x27
STACK CFI 713c x28: x28
STACK CFI 7140 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7214 v8: .cfa -16 + ^
STACK CFI 7280 v8: v8
STACK CFI 72d8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7348 x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 738c x25: x25
STACK CFI 7390 x26: x26
STACK CFI 7394 x27: x27
STACK CFI 7398 x28: x28
STACK CFI 739c v8: .cfa -16 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 73e4 v8: v8
STACK CFI 73ec x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 73f0 x25: .cfa -48 + ^
STACK CFI 73f4 x26: .cfa -40 + ^
STACK CFI 73f8 x27: .cfa -32 + ^
STACK CFI 73fc x28: .cfa -24 + ^
STACK CFI 7400 v8: .cfa -16 + ^
STACK CFI INIT 7404 774 .cfa: sp 0 + .ra: x30
STACK CFI 740c .cfa: sp 144 +
STACK CFI 7418 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7420 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7428 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7434 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 744c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7470 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 76fc x25: x25 x26: x26
STACK CFI 7734 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 773c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 7774 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 78b0 x25: x25 x26: x26
STACK CFI 78b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7948 x25: x25 x26: x26
STACK CFI 794c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 79bc x25: x25 x26: x26
STACK CFI 79c0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7b70 x25: x25 x26: x26
STACK CFI 7b74 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 7b80 1c8 .cfa: sp 0 + .ra: x30
STACK CFI 7b88 .cfa: sp 96 +
STACK CFI 7b94 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7ba0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7ba8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 7cec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 7cf4 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
