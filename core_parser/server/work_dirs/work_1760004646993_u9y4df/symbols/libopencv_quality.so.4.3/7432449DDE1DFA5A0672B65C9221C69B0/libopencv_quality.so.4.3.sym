MODULE Linux arm64 7432449DDE1DFA5A0672B65C9221C69B0 libopencv_quality.so.4.3
INFO CODE_ID 9D4432741DDE5AFA0672B65C9221C69BA6CDACAC
PUBLIC 4e28 0 _init
PUBLIC 5360 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.39]
PUBLIC 5400 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.25]
PUBLIC 54a0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.22] [clone .constprop.23]
PUBLIC 5500 0 _GLOBAL__sub_I_qualitybrisque.cpp
PUBLIC 5530 0 call_weak_fn
PUBLIC 5548 0 deregister_tm_clones
PUBLIC 5580 0 register_tm_clones
PUBLIC 55c0 0 __do_global_dtors_aux
PUBLIC 5608 0 frame_dummy
PUBLIC 5640 0 cv::Algorithm::write(cv::FileStorage&) const
PUBLIC 5648 0 cv::Algorithm::read(cv::FileNode const&)
PUBLIC 5650 0 std::_Sp_counted_ptr<cv::quality::QualityBRISQUE*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 5658 0 std::_Sp_counted_ptr<cv::quality::QualityBRISQUE*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 5678 0 std::_Sp_counted_ptr<cv::quality::QualityBRISQUE*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 5680 0 std::_Sp_counted_ptr<cv::quality::QualityBRISQUE*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 5688 0 std::_Sp_counted_ptr<cv::quality::QualityBRISQUE*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 5690 0 (anonymous namespace)::AGGDfit(cv::Mat const&, double&, double&, double&)
PUBLIC 58b0 0 cv::quality::QualityBase::getQualityMap(cv::_OutputArray const&) const
PUBLIC 5940 0 cv::quality::QualityBase::clear()
PUBLIC 5ad0 0 virtual thunk to cv::quality::QualityBase::clear()
PUBLIC 5ae0 0 cv::quality::QualityBase::empty() const
PUBLIC 5b58 0 virtual thunk to cv::quality::QualityBase::empty() const
PUBLIC 5b68 0 cv::quality::QualityBRISQUE::~QualityBRISQUE()
PUBLIC 5ce0 0 virtual thunk to cv::quality::QualityBRISQUE::~QualityBRISQUE()
PUBLIC 5cf0 0 cv::quality::QualityBRISQUE::~QualityBRISQUE()
PUBLIC 5e60 0 virtual thunk to cv::quality::QualityBRISQUE::~QualityBRISQUE()
PUBLIC 5e70 0 cv::Mat::~Mat()
PUBLIC 5f00 0 (anonymous namespace)::mat_convert(cv::Mat const&)
PUBLIC 6140 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 6200 0 cv::quality::QualityBRISQUE::create(cv::Ptr<cv::ml::SVM> const&, cv::Mat const&)
PUBLIC 63f0 0 void std::vector<float, std::allocator<float> >::_M_emplace_back_aux<float>(float&&)
PUBLIC 64e0 0 (anonymous namespace)::ComputeBrisqueFeature(cv::Mat const&)
PUBLIC 7290 0 cv::quality::QualityBRISQUE::compute(cv::_InputArray const&)
PUBLIC 7f40 0 cv::quality::QualityBRISQUE::computeFeatures(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 8610 0 cv::quality::QualityBRISQUE::QualityBRISQUE(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 8990 0 cv::quality::QualityBRISQUE::QualityBRISQUE(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 8d10 0 cv::quality::QualityBRISQUE::create(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 8db8 0 cv::quality::QualityBRISQUE::compute(cv::_InputArray const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 8f60 0 std::_Sp_counted_ptr<cv::quality::QualityGMSD*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 8f68 0 std::_Sp_counted_ptr<cv::quality::QualityGMSD*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 8f88 0 std::_Sp_counted_ptr<cv::quality::QualityGMSD*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 8f90 0 std::_Sp_counted_ptr<cv::quality::QualityGMSD*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 8f98 0 std::_Sp_counted_ptr<cv::quality::QualityGMSD*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 8fa0 0 cv::quality::QualityGMSD::~QualityGMSD()
PUBLIC 8ff8 0 virtual thunk to cv::quality::QualityGMSD::~QualityGMSD()
PUBLIC 9008 0 cv::quality::QualityGMSD::~QualityGMSD()
PUBLIC 9068 0 virtual thunk to cv::quality::QualityGMSD::~QualityGMSD()
PUBLIC 9080 0 void (anonymous namespace)::conv2<cv::UMat, cv::UMat, cv::Matx<double, 3, 3> >(cv::UMat const&, cv::UMat&, cv::Matx<double, 3, 3> const&, (anonymous namespace)::ConvolutionType) [clone .constprop.27]
PUBLIC 9390 0 cv::quality::QualityGMSD::empty() const
PUBLIC 94e8 0 virtual thunk to cv::quality::QualityGMSD::empty() const
PUBLIC 9500 0 cv::quality::QualityGMSD::clear()
PUBLIC 9900 0 virtual thunk to cv::quality::QualityGMSD::clear()
PUBLIC 9910 0 cv::quality::QualityGMSD::_mat_data::_mat_data(cv::UMat const&)
PUBLIC 9ed0 0 cv::quality::QualityGMSD::_mat_data::_mat_data(cv::_InputArray const&)
PUBLIC a360 0 cv::quality::QualityGMSD::create(cv::_InputArray const&)
PUBLIC a580 0 cv::quality::QualityGMSD::_mat_data::compute(cv::quality::QualityGMSD::_mat_data const&, cv::quality::QualityGMSD::_mat_data const&)
PUBLIC aa38 0 cv::quality::QualityGMSD::compute(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC ab38 0 cv::quality::QualityGMSD::compute(cv::_InputArray const&)
PUBLIC ac00 0 std::_Sp_counted_ptr<cv::quality::QualityMSE*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC ac08 0 std::_Sp_counted_ptr<cv::quality::QualityMSE*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC ac28 0 std::_Sp_counted_ptr<cv::quality::QualityMSE*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC ac30 0 std::_Sp_counted_ptr<cv::quality::QualityMSE*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC ac38 0 std::_Sp_counted_ptr<cv::quality::QualityMSE*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC ac40 0 cv::quality::QualityMSE::~QualityMSE()
PUBLIC ac80 0 virtual thunk to cv::quality::QualityMSE::~QualityMSE()
PUBLIC ac90 0 (anonymous namespace)::compute(cv::UMat const&, cv::UMat const&)
PUBLIC adf0 0 cv::quality::QualityMSE::~QualityMSE()
PUBLIC ae38 0 virtual thunk to cv::quality::QualityMSE::~QualityMSE()
PUBLIC ae48 0 cv::quality::QualityMSE::empty() const
PUBLIC af30 0 virtual thunk to cv::quality::QualityMSE::empty() const
PUBLIC af40 0 cv::quality::QualityMSE::clear()
PUBLIC b210 0 virtual thunk to cv::quality::QualityMSE::clear()
PUBLIC b220 0 cv::quality::QualityMSE::compute(cv::_InputArray const&)
PUBLIC b710 0 cv::quality::QualityMSE::create(cv::_InputArray const&)
PUBLIC bcd0 0 cv::quality::QualityMSE::compute(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC c5f0 0 std::_Sp_counted_ptr<cv::quality::QualitySSIM*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC c5f8 0 std::_Sp_counted_ptr<cv::quality::QualitySSIM*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC c618 0 std::_Sp_counted_ptr<cv::quality::QualitySSIM*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC c620 0 std::_Sp_counted_ptr<cv::quality::QualitySSIM*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC c628 0 std::_Sp_counted_ptr<cv::quality::QualitySSIM*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC c630 0 cv::quality::QualitySSIM::~QualitySSIM()
PUBLIC c6a8 0 virtual thunk to cv::quality::QualitySSIM::~QualitySSIM()
PUBLIC c6b8 0 cv::quality::QualitySSIM::~QualitySSIM()
PUBLIC c728 0 virtual thunk to cv::quality::QualitySSIM::~QualitySSIM()
PUBLIC c738 0 cv::quality::QualitySSIM::empty() const
PUBLIC c9a0 0 virtual thunk to cv::quality::QualitySSIM::empty() const
PUBLIC c9b0 0 cv::quality::QualitySSIM::_mat_data::~_mat_data()
PUBLIC c9f0 0 cv::quality::QualitySSIM::clear()
PUBLIC d130 0 virtual thunk to cv::quality::QualitySSIM::clear()
PUBLIC d140 0 cv::quality::QualitySSIM::_mat_data::_mat_data(cv::UMat const&)
PUBLIC d7c0 0 cv::quality::QualitySSIM::_mat_data::_mat_data(cv::_InputArray const&)
PUBLIC dca0 0 cv::quality::QualitySSIM::create(cv::_InputArray const&)
PUBLIC e070 0 cv::quality::QualitySSIM::_mat_data::compute(cv::quality::QualitySSIM::_mat_data const&, cv::quality::QualitySSIM::_mat_data const&)
PUBLIC e880 0 cv::quality::QualitySSIM::compute(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC e9b0 0 cv::quality::QualitySSIM::compute(cv::_InputArray const&)
PUBLIC ea8c 0 _fini
STACK CFI INIT 5640 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5648 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5650 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5658 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5678 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5680 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5688 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5690 204 .cfa: sp 0 + .ra: x30
STACK CFI 5694 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 569c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 56ac .ra: .cfa -72 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x23: .cfa -80 + ^
STACK CFI 56bc v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^ v14: .cfa -16 + ^ v15: .cfa -8 + ^
STACK CFI 5878 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 587c .cfa: sp 112 + .ra: .cfa -72 + ^ v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v13: .cfa -24 + ^ v14: .cfa -16 + ^ v15: .cfa -8 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT 58b0 8c .cfa: sp 0 + .ra: x30
STACK CFI 58b4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 58c4 .ra: .cfa -16 + ^
STACK CFI 5904 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 5908 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 5918 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 5940 178 .cfa: sp 0 + .ra: x30
STACK CFI 5948 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 5958 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5960 .ra: .cfa -96 + ^
STACK CFI 5a60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 5a68 .cfa: sp 128 + .ra: .cfa -96 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI INIT 5ad0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5360 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5364 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5370 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 53f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 53f4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 5ae0 78 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b58 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b68 178 .cfa: sp 0 + .ra: x30
STACK CFI 5b6c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5b7c .ra: .cfa -16 + ^
STACK CFI 5c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 5c58 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 5ce0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5cf0 170 .cfa: sp 0 + .ra: x30
STACK CFI 5cf4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5d04 .ra: .cfa -16 + ^
STACK CFI 5dd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 5dd8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 5e60 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5e70 90 .cfa: sp 0 + .ra: x30
STACK CFI 5e74 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5ee8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 5ef0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5efc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 5f00 234 .cfa: sp 0 + .ra: x30
STACK CFI 5f04 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5f14 .ra: .cfa -72 + ^ x21: .cfa -80 + ^
STACK CFI 60ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 60b0 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^
STACK CFI INIT 6140 b4 .cfa: sp 0 + .ra: x30
STACK CFI 6148 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6154 .ra: .cfa -16 + ^
STACK CFI 617c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 6180 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 61c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 61d0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 6200 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 6204 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 620c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6214 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 6344 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 6348 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 63f0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 63f4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 63fc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6408 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6488 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 6490 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 64e0 d80 .cfa: sp 0 + .ra: x30
STACK CFI 64e8 .cfa: sp 1088 +
STACK CFI 64f8 x21: .cfa -1072 + ^ x22: .cfa -1064 + ^
STACK CFI 651c .ra: .cfa -1008 + ^ v10: .cfa -976 + ^ v11: .cfa -968 + ^ v12: .cfa -1000 + ^ v8: .cfa -992 + ^ v9: .cfa -984 + ^ x19: .cfa -1088 + ^ x20: .cfa -1080 + ^ x23: .cfa -1056 + ^ x24: .cfa -1048 + ^ x25: .cfa -1040 + ^ x26: .cfa -1032 + ^ x27: .cfa -1024 + ^ x28: .cfa -1016 + ^
STACK CFI 7130 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7134 .cfa: sp 1088 + .ra: .cfa -1008 + ^ v10: .cfa -976 + ^ v11: .cfa -968 + ^ v12: .cfa -1000 + ^ v8: .cfa -992 + ^ v9: .cfa -984 + ^ x19: .cfa -1088 + ^ x20: .cfa -1080 + ^ x21: .cfa -1072 + ^ x22: .cfa -1064 + ^ x23: .cfa -1056 + ^ x24: .cfa -1048 + ^ x25: .cfa -1040 + ^ x26: .cfa -1032 + ^ x27: .cfa -1024 + ^ x28: .cfa -1016 + ^
STACK CFI INIT 7290 c7c .cfa: sp 0 + .ra: x30
STACK CFI 7298 .cfa: sp 848 +
STACK CFI 72b8 x19: .cfa -848 + ^ x20: .cfa -840 + ^
STACK CFI 72f8 .ra: .cfa -768 + ^ v10: .cfa -760 + ^ v8: .cfa -752 + ^ v9: .cfa -744 + ^ x21: .cfa -832 + ^ x22: .cfa -824 + ^ x23: .cfa -816 + ^ x24: .cfa -808 + ^ x25: .cfa -800 + ^ x26: .cfa -792 + ^ x27: .cfa -784 + ^ x28: .cfa -776 + ^
STACK CFI 7b00 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7b04 .cfa: sp 848 + .ra: .cfa -768 + ^ v10: .cfa -760 + ^ v8: .cfa -752 + ^ v9: .cfa -744 + ^ x19: .cfa -848 + ^ x20: .cfa -840 + ^ x21: .cfa -832 + ^ x22: .cfa -824 + ^ x23: .cfa -816 + ^ x24: .cfa -808 + ^ x25: .cfa -800 + ^ x26: .cfa -792 + ^ x27: .cfa -784 + ^ x28: .cfa -776 + ^
STACK CFI INIT 7f40 6c0 .cfa: sp 0 + .ra: x30
STACK CFI 7f44 .cfa: sp 304 + x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 7f50 x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI 7f58 .ra: .cfa -272 + ^
STACK CFI 82a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 82b0 .cfa: sp 304 + .ra: .cfa -272 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^
STACK CFI INIT 5500 30 .cfa: sp 0 + .ra: x30
STACK CFI 5504 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5520 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8610 368 .cfa: sp 0 + .ra: x30
STACK CFI 8614 .cfa: sp 304 + x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 862c x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI 8638 .ra: .cfa -248 + ^ x25: .cfa -256 + ^
STACK CFI 884c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 8850 .cfa: sp 304 + .ra: .cfa -248 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^
STACK CFI INIT 8990 364 .cfa: sp 0 + .ra: x30
STACK CFI 8994 .cfa: sp 304 + x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 89ac x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI 89b4 .ra: .cfa -256 + ^
STACK CFI 8bb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 8bc0 .cfa: sp 304 + .ra: .cfa -256 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^
STACK CFI INIT 8d10 a4 .cfa: sp 0 + .ra: x30
STACK CFI 8d14 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8d1c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8d28 .ra: .cfa -16 + ^
STACK CFI 8d7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 8d80 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 8db8 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 8dbc .cfa: sp 240 + x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 8dc8 .ra: .cfa -216 + ^ x21: .cfa -224 + ^
STACK CFI 8ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 8ec0 .cfa: sp 240 + .ra: .cfa -216 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^
STACK CFI INIT 8f60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f68 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f88 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8f98 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8fa0 54 .cfa: sp 0 + .ra: x30
STACK CFI 8fa8 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8fbc .ra: .cfa -16 + ^
STACK CFI 8ff0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 8ff8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5400 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5404 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5410 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 5490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 5494 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 9008 5c .cfa: sp 0 + .ra: x30
STACK CFI 9010 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9024 .ra: .cfa -16 + ^
STACK CFI 9060 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 9068 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9080 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 9088 .cfa: sp 432 + x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 9090 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 90a0 .ra: .cfa -384 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 92c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 92d0 .cfa: sp 432 + .ra: .cfa -384 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 9338 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 9340 .cfa: sp 432 + .ra: .cfa -384 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI INIT 9390 158 .cfa: sp 0 + .ra: x30
STACK CFI INIT 94e8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9500 3ec .cfa: sp 0 + .ra: x30
STACK CFI 9508 .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 9518 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 953c .ra: .cfa -176 + ^
STACK CFI 9814 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 9818 .cfa: sp 208 + .ra: .cfa -176 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI INIT 9900 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9910 5a4 .cfa: sp 0 + .ra: x30
STACK CFI 9918 .cfa: sp 416 + x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI 993c x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI 9944 .ra: .cfa -336 + ^
STACK CFI 9d1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9d20 .cfa: sp 416 + .ra: .cfa -336 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^ x24: .cfa -376 + ^ x25: .cfa -368 + ^ x26: .cfa -360 + ^ x27: .cfa -352 + ^ x28: .cfa -344 + ^
STACK CFI INIT 9ed0 47c .cfa: sp 0 + .ra: x30
STACK CFI 9ed4 .cfa: sp 352 + x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 9ef0 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI 9ef8 .ra: .cfa -312 + ^ x23: .cfa -320 + ^
STACK CFI a000 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI a008 .cfa: sp 352 + .ra: .cfa -312 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^
STACK CFI INIT a360 20c .cfa: sp 0 + .ra: x30
STACK CFI a364 .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI a370 .ra: .cfa -168 + ^ x21: .cfa -176 + ^
STACK CFI a4ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI a4f0 .cfa: sp 192 + .ra: .cfa -168 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^
STACK CFI INIT a580 498 .cfa: sp 0 + .ra: x30
STACK CFI a588 .cfa: sp 432 + x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI a594 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI a5ac x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI a5b4 .ra: .cfa -352 + ^
STACK CFI a9c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a9c8 .cfa: sp 432 + .ra: .cfa -352 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI INIT aa38 100 .cfa: sp 0 + .ra: x30
STACK CFI aa3c .cfa: sp 480 + x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI aa48 .ra: .cfa -440 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^
STACK CFI aadc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI aae0 .cfa: sp 480 + .ra: .cfa -440 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^
STACK CFI INIT ab38 c4 .cfa: sp 0 + .ra: x30
STACK CFI ab3c .cfa: sp 320 + x19: .cfa -320 + ^ x20: .cfa -312 + ^
STACK CFI ab4c x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI ab58 .ra: .cfa -288 + ^
STACK CFI abc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI abcc .cfa: sp 320 + .ra: .cfa -288 + ^ x19: .cfa -320 + ^ x20: .cfa -312 + ^ x21: .cfa -304 + ^ x22: .cfa -296 + ^
STACK CFI INIT ac00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac08 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT ac28 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac38 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac40 3c .cfa: sp 0 + .ra: x30
STACK CFI ac48 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI ac78 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT ac80 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT ac90 14c .cfa: sp 0 + .ra: x30
STACK CFI ac98 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI acb0 .ra: .cfa -88 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^
STACK CFI adc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI adc8 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^
STACK CFI INIT 54a0 60 .cfa: sp 0 + .ra: x30
STACK CFI 54a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 54fc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT adf0 44 .cfa: sp 0 + .ra: x30
STACK CFI adf8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI ae30 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT ae38 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT ae48 e8 .cfa: sp 0 + .ra: x30
STACK CFI INIT af30 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT af40 2bc .cfa: sp 0 + .ra: x30
STACK CFI af48 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI af58 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI af60 .ra: .cfa -96 + ^
STACK CFI b160 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI b168 .cfa: sp 128 + .ra: .cfa -96 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI INIT b210 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT b220 4d8 .cfa: sp 0 + .ra: x30
STACK CFI b224 .cfa: sp 384 + x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI b244 x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI b264 .ra: .cfa -336 + ^
STACK CFI b3a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI b3a8 .cfa: sp 384 + .ra: .cfa -336 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^
STACK CFI INIT b710 5a4 .cfa: sp 0 + .ra: x30
STACK CFI b718 .cfa: sp 368 + x19: .cfa -368 + ^ x20: .cfa -360 + ^
STACK CFI b724 x21: .cfa -352 + ^ x22: .cfa -344 + ^
STACK CFI b738 x23: .cfa -336 + ^ x24: .cfa -328 + ^
STACK CFI b74c .ra: .cfa -320 + ^
STACK CFI b920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI b928 .cfa: sp 368 + .ra: .cfa -320 + ^ x19: .cfa -368 + ^ x20: .cfa -360 + ^ x21: .cfa -352 + ^ x22: .cfa -344 + ^ x23: .cfa -336 + ^ x24: .cfa -328 + ^
STACK CFI INIT bcd0 910 .cfa: sp 0 + .ra: x30
STACK CFI bcd8 .cfa: sp 464 + x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI bce0 x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI bcf4 .ra: .cfa -408 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^
STACK CFI bf38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI bf40 .cfa: sp 464 + .ra: .cfa -408 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^ x23: .cfa -432 + ^ x24: .cfa -424 + ^ x25: .cfa -416 + ^
STACK CFI INIT c5f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c5f8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT c618 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c620 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c628 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c630 74 .cfa: sp 0 + .ra: x30
STACK CFI c638 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c64c .ra: .cfa -16 + ^
STACK CFI c6a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT c6a8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT c6b8 6c .cfa: sp 0 + .ra: x30
STACK CFI c6c0 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c6d4 .ra: .cfa -16 + ^
STACK CFI c720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT c728 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT c738 268 .cfa: sp 0 + .ra: x30
STACK CFI INIT c9a0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT c9b0 34 .cfa: sp 0 + .ra: x30
STACK CFI c9b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI c9e0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT c9f0 724 .cfa: sp 0 + .ra: x30
STACK CFI c9f4 .cfa: sp 448 + x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI ca0c .ra: .cfa -408 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^
STACK CFI cfc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI cfd0 .cfa: sp 448 + .ra: .cfa -408 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^
STACK CFI INIT d130 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT d140 668 .cfa: sp 0 + .ra: x30
STACK CFI d148 .cfa: sp 288 + x19: .cfa -288 + ^ x20: .cfa -280 + ^
STACK CFI d154 x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI d15c x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI d174 .ra: .cfa -208 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI d698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI d6a0 .cfa: sp 288 + .ra: .cfa -208 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI INIT d7c0 4c4 .cfa: sp 0 + .ra: x30
STACK CFI d7c4 .cfa: sp 352 + x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI d7e0 x21: .cfa -336 + ^ x22: .cfa -328 + ^
STACK CFI d7e8 .ra: .cfa -312 + ^ x23: .cfa -320 + ^
STACK CFI d8f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI d8f8 .cfa: sp 352 + .ra: .cfa -312 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^
STACK CFI INIT dca0 3b4 .cfa: sp 0 + .ra: x30
STACK CFI dca4 .cfa: sp 432 + x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI dcb0 .ra: .cfa -408 + ^ x21: .cfa -416 + ^
STACK CFI df90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI df98 .cfa: sp 432 + .ra: .cfa -408 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^
STACK CFI INIT e070 7e8 .cfa: sp 0 + .ra: x30
STACK CFI e078 .cfa: sp 784 +
STACK CFI e08c x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI e09c x25: .cfa -736 + ^ x26: .cfa -728 + ^
STACK CFI e0c0 x19: .cfa -784 + ^ x20: .cfa -776 + ^ x21: .cfa -768 + ^ x22: .cfa -760 + ^
STACK CFI e0c8 x23: .cfa -752 + ^ x24: .cfa -744 + ^
STACK CFI e0f0 .ra: .cfa -704 + ^
STACK CFI e7ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI e7f0 .cfa: sp 784 + .ra: .cfa -704 + ^ x19: .cfa -784 + ^ x20: .cfa -776 + ^ x21: .cfa -768 + ^ x22: .cfa -760 + ^ x23: .cfa -752 + ^ x24: .cfa -744 + ^ x25: .cfa -736 + ^ x26: .cfa -728 + ^ x27: .cfa -720 + ^ x28: .cfa -712 + ^
STACK CFI INIT e880 12c .cfa: sp 0 + .ra: x30
STACK CFI e884 .cfa: sp 960 +
STACK CFI e888 x19: .cfa -960 + ^ x20: .cfa -952 + ^
STACK CFI e898 .ra: .cfa -920 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^
STACK CFI e95c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI e960 .cfa: sp 960 + .ra: .cfa -920 + ^ x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^
STACK CFI INIT e9b0 dc .cfa: sp 0 + .ra: x30
STACK CFI e9b4 .cfa: sp 560 +
STACK CFI e9b8 x19: .cfa -560 + ^ x20: .cfa -552 + ^
STACK CFI e9c4 x21: .cfa -544 + ^ x22: .cfa -536 + ^
STACK CFI e9d4 .ra: .cfa -528 + ^
STACK CFI ea60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI ea64 .cfa: sp 560 + .ra: .cfa -528 + ^ x19: .cfa -560 + ^ x20: .cfa -552 + ^ x21: .cfa -544 + ^ x22: .cfa -536 + ^
