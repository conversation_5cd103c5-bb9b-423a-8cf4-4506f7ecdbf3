MODULE Linux arm64 B03EA9517B7009744FC94B6173DB959D0 libposix-eadb-samba4.so.0
INFO CODE_ID 51A93EB0707B74094FC94B6173DB959D55251CB6
PUBLIC fd0 0 pull_xattr_blob_tdb_raw
PUBLIC 10b0 0 push_xattr_blob_tdb_raw
PUBLIC 1390 0 delete_posix_eadb_raw
PUBLIC 1440 0 unlink_posix_eadb_raw
PUBLIC 1580 0 list_posix_eadb_raw
STACK CFI INIT d20 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT d50 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT d90 48 .cfa: sp 0 + .ra: x30
STACK CFI d94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d9c x19: .cfa -16 + ^
STACK CFI dd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT de0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT df0 1d8 .cfa: sp 0 + .ra: x30
STACK CFI df8 .cfa: sp 208 +
STACK CFI e04 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e0c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e14 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI e20 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI f98 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT fd0 e0 .cfa: sp 0 + .ra: x30
STACK CFI fd8 .cfa: sp 80 +
STACK CFI fe4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI fec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ff8 x21: .cfa -16 + ^
STACK CFI 10a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 10ac .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 10b0 2e0 .cfa: sp 0 + .ra: x30
STACK CFI 10b8 .cfa: sp 176 +
STACK CFI 10c4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 10cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 10d4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 10e4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 10f0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1140 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1238 x27: x27 x28: x28
STACK CFI 128c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1294 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 12a0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1358 x27: x27 x28: x28
STACK CFI 136c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1388 x27: x27 x28: x28
STACK CFI 138c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 1390 ac .cfa: sp 0 + .ra: x30
STACK CFI 1398 .cfa: sp 64 +
STACK CFI 13a8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13b0 x19: .cfa -16 + ^
STACK CFI 1418 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1420 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1440 13c .cfa: sp 0 + .ra: x30
STACK CFI 1448 .cfa: sp 96 +
STACK CFI 1454 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 145c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1464 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 146c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1560 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1580 34 .cfa: sp 0 + .ra: x30
STACK CFI 1588 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1594 .cfa: sp 0 + .ra: .ra x29: x29
