MODULE Linux arm64 9A1F90ACE7D447585D986A30F9DBC0D20 librxe-rdmav34.so
INFO CODE_ID AC901F9AD4E758475D986A30F9DBC0D2C3377FF9
STACK CFI INIT 1900 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1930 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1970 48 .cfa: sp 0 + .ra: x30
STACK CFI 1974 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 197c x19: .cfa -16 + ^
STACK CFI 19b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 19c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19d0 20 .cfa: sp 0 + .ra: x30
STACK CFI 19d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 19e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 19f0 20 .cfa: sp 0 + .ra: x30
STACK CFI 19f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a10 20 .cfa: sp 0 + .ra: x30
STACK CFI 1a18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a30 20 .cfa: sp 0 + .ra: x30
STACK CFI 1a38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a50 20 .cfa: sp 0 + .ra: x30
STACK CFI 1a58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a70 20 .cfa: sp 0 + .ra: x30
STACK CFI 1a78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a90 20 .cfa: sp 0 + .ra: x30
STACK CFI 1a98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1aa4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ab0 20 .cfa: sp 0 + .ra: x30
STACK CFI 1ab8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ac4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ad0 20 .cfa: sp 0 + .ra: x30
STACK CFI 1ad8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ae4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1af0 20 .cfa: sp 0 + .ra: x30
STACK CFI 1af8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b10 68 .cfa: sp 0 + .ra: x30
STACK CFI 1b18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b30 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1b38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b70 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b80 ac .cfa: sp 0 + .ra: x30
STACK CFI 1b88 .cfa: sp 352 +
STACK CFI 1b98 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ba0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1c20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1c28 .cfa: sp 352 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1c30 60 .cfa: sp 0 + .ra: x30
STACK CFI 1c38 .cfa: sp 48 +
STACK CFI 1c48 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1c84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1c8c .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1c90 18 .cfa: sp 0 + .ra: x30
STACK CFI 1c98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ca0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1cb0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 1cb8 .cfa: sp 80 +
STACK CFI 1cc8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1cd0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1cd8 x21: .cfa -16 + ^
STACK CFI 1d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1d50 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1d64 3c .cfa: sp 0 + .ra: x30
STACK CFI 1d6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d78 x19: .cfa -16 + ^
STACK CFI 1d98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1da0 50 .cfa: sp 0 + .ra: x30
STACK CFI 1da8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1db0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1dcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1dd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1de8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1df0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 1df8 .cfa: sp 96 +
STACK CFI 1e04 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1e0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e14 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1e9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1ea4 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1ec0 3c .cfa: sp 0 + .ra: x30
STACK CFI 1ec8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ed0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1ef4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1f00 dc .cfa: sp 0 + .ra: x30
STACK CFI 1f08 .cfa: sp 160 +
STACK CFI 1f14 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1f1c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1f24 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1f30 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1fc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1fcc .cfa: sp 160 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1fe0 3c .cfa: sp 0 + .ra: x30
STACK CFI 1fe8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ff0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2020 1c .cfa: sp 0 + .ra: x30
STACK CFI 2028 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2034 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2040 a8 .cfa: sp 0 + .ra: x30
STACK CFI 2048 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2050 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 20c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 20c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 20f0 40 .cfa: sp 0 + .ra: x30
STACK CFI 20f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2100 x19: .cfa -16 + ^
STACK CFI 2128 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2130 ac .cfa: sp 0 + .ra: x30
STACK CFI 21a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 21d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 21e0 3c .cfa: sp 0 + .ra: x30
STACK CFI 21e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2210 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2220 54 .cfa: sp 0 + .ra: x30
STACK CFI 2228 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2230 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 225c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2264 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2274 174 .cfa: sp 0 + .ra: x30
STACK CFI 227c .cfa: sp 112 +
STACK CFI 2288 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2290 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2298 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 22a4 x23: .cfa -16 + ^
STACK CFI 2390 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2398 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 23f0 214 .cfa: sp 0 + .ra: x30
STACK CFI 23f8 .cfa: sp 96 +
STACK CFI 23fc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2404 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2414 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 25a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 25b0 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2604 118 .cfa: sp 0 + .ra: x30
STACK CFI 260c .cfa: sp 112 +
STACK CFI 2618 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2620 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 262c x21: .cfa -16 + ^
STACK CFI 26f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 26fc .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2720 c8 .cfa: sp 0 + .ra: x30
STACK CFI 2728 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2730 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 273c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 27d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 27e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 27f0 188 .cfa: sp 0 + .ra: x30
STACK CFI 27f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2804 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 280c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2814 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2940 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2948 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 295c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2964 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2980 88 .cfa: sp 0 + .ra: x30
STACK CFI 2988 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2990 x23: .cfa -16 + ^
STACK CFI 2998 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 29a0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2a10 bc .cfa: sp 0 + .ra: x30
STACK CFI 2a18 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2a20 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2a30 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2a40 x23: .cfa -16 + ^
STACK CFI 2a8c x21: x21 x22: x22
STACK CFI 2a98 x23: x23
STACK CFI 2a9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2aa4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2aa8 x21: x21 x22: x22
STACK CFI 2aac x23: x23
STACK CFI 2abc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ac4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2ac8 x21: x21 x22: x22
STACK CFI INIT 2ad0 84 .cfa: sp 0 + .ra: x30
STACK CFI 2ad8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b18 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2b20 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2b54 84 .cfa: sp 0 + .ra: x30
STACK CFI 2b68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2b74 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2b9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ba4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2bcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2be0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 2be8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2bf0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2c00 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2c14 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2c6c x19: x19 x20: x20
STACK CFI 2c74 x21: x21 x22: x22
STACK CFI 2c80 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 2c88 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2c90 x19: x19 x20: x20
STACK CFI 2c94 x21: x21 x22: x22
STACK CFI 2c9c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI INIT 2ca4 ac .cfa: sp 0 + .ra: x30
STACK CFI 2cac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2cb4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2cd0 x21: .cfa -16 + ^
STACK CFI 2ce4 x21: x21
STACK CFI 2cf4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2cfc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2d34 x21: x21
STACK CFI 2d40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2d48 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2d50 60 .cfa: sp 0 + .ra: x30
STACK CFI 2d58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2d60 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2d68 x21: .cfa -16 + ^
STACK CFI 2d98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2da0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2db0 11c .cfa: sp 0 + .ra: x30
STACK CFI 2db8 .cfa: sp 144 +
STACK CFI 2dc4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2dcc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2dd4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2e98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2ea0 .cfa: sp 144 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2ed0 118 .cfa: sp 0 + .ra: x30
STACK CFI 2ed8 .cfa: sp 160 +
STACK CFI 2ee4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2eec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2ef4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2fb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2fbc .cfa: sp 160 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2ff0 134 .cfa: sp 0 + .ra: x30
STACK CFI 2ff8 .cfa: sp 128 +
STACK CFI 3004 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 300c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3014 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3074 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 307c .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3080 x23: .cfa -16 + ^
STACK CFI 3104 x23: x23
STACK CFI 3108 x23: .cfa -16 + ^
STACK CFI 311c x23: x23
STACK CFI 3120 x23: .cfa -16 + ^
STACK CFI INIT 3124 60 .cfa: sp 0 + .ra: x30
STACK CFI 312c .cfa: sp 48 +
STACK CFI 313c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3178 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3180 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3184 d0 .cfa: sp 0 + .ra: x30
STACK CFI 318c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3238 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3240 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3244 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3254 cc .cfa: sp 0 + .ra: x30
STACK CFI 325c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3304 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 330c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3310 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3320 e0 .cfa: sp 0 + .ra: x30
STACK CFI 3328 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 33e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 33ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 33f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3400 c4 .cfa: sp 0 + .ra: x30
STACK CFI 3408 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 34b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 34c4 cc .cfa: sp 0 + .ra: x30
STACK CFI 34cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3574 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 357c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3580 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3590 c8 .cfa: sp 0 + .ra: x30
STACK CFI 3598 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 363c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3644 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3648 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3660 e0 .cfa: sp 0 + .ra: x30
STACK CFI 3668 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3724 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 372c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3730 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3740 dc .cfa: sp 0 + .ra: x30
STACK CFI 3748 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3800 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3808 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 380c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3820 d0 .cfa: sp 0 + .ra: x30
STACK CFI 3828 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 38d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 38dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 38e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 38f0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 38f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3994 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 399c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 39a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 39b0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 39b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3a58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3a60 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3a64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3a74 c4 .cfa: sp 0 + .ra: x30
STACK CFI 3a7c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3b24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3b40 e0 .cfa: sp 0 + .ra: x30
STACK CFI 3b48 .cfa: sp 80 +
STACK CFI 3b4c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3b54 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3bf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c00 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3c20 74 .cfa: sp 0 + .ra: x30
STACK CFI 3c28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c30 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3c64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3c94 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 3ca0 .cfa: sp 208 +
STACK CFI 3cac .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3cb4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3cc0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3ccc x23: .cfa -16 + ^
STACK CFI 3ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3de4 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3e74 384 .cfa: sp 0 + .ra: x30
STACK CFI 3e7c .cfa: sp 240 +
STACK CFI 3e88 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3e90 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3ea4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3eb0 x23: .cfa -16 + ^
STACK CFI 3fd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3fe0 .cfa: sp 240 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4200 68 .cfa: sp 0 + .ra: x30
STACK CFI 4208 .cfa: sp 48 +
STACK CFI 4218 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 425c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4264 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4270 78 .cfa: sp 0 + .ra: x30
STACK CFI 4278 .cfa: sp 144 +
STACK CFI 4288 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 42dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 42e4 .cfa: sp 144 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 42f0 50c .cfa: sp 0 + .ra: x30
STACK CFI 42f8 .cfa: sp 160 +
STACK CFI 4304 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 431c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4324 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 4348 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 434c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4358 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 44d4 x19: x19 x20: x20
STACK CFI 44d8 x21: x21 x22: x22
STACK CFI 44dc x23: x23 x24: x24
STACK CFI 44e0 x25: x25 x26: x26
STACK CFI 44e4 x27: x27 x28: x28
STACK CFI 4508 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4510 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 47a8 x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 47ac x21: x21 x22: x22
STACK CFI 47b0 x23: x23 x24: x24
STACK CFI 47b8 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 47d8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 47dc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 47e0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 47e4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 47e8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 47ec x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 4800 ec .cfa: sp 0 + .ra: x30
STACK CFI 4808 .cfa: sp 176 +
STACK CFI 480c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4818 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 486c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4874 .cfa: sp 176 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 48f0 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 48f8 .cfa: sp 96 +
STACK CFI 4904 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 490c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4918 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 499c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 49a4 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4ad0 50 .cfa: sp 0 + .ra: x30
STACK CFI 4ad8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ae0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4afc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4b04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4b18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4b20 30 .cfa: sp 0 + .ra: x30
STACK CFI 4b28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b30 x19: .cfa -16 + ^
STACK CFI 4b48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4b50 bc .cfa: sp 0 + .ra: x30
STACK CFI 4b58 .cfa: sp 64 +
STACK CFI 4b68 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b78 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4bf0 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 18c0 20 .cfa: sp 0 + .ra: x30
STACK CFI 18c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 18d4 .cfa: sp 0 + .ra: .ra x29: x29
