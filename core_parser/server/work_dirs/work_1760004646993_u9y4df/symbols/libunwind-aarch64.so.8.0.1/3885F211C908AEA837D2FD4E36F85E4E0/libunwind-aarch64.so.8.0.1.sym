MODULE Linux arm64 3885F211C908AEA837D2FD4E36F85E4E0 libunwind-aarch64.so.8
INFO CODE_ID 11F2853808C9A8AE37D2FD4E36F85E4EF3805359
PUBLIC 5440 0 _Uaarch64_get_elf_image
PUBLIC 5b40 0 _Uaarch64_get_exe_image_path
PUBLIC 5cb0 0 _Uaarch64_flush_cache
PUBLIC 7b80 0 _Uaarch64_strerror
PUBLIC 7c40 0 _Uaarch64_is_fpreg
PUBLIC 7d90 0 _Uaarch64_regname
PUBLIC 7dd4 0 _Uaarch64_get_accessors
PUBLIC 7e20 0 _Uaarch64_destroy_addr_space
PUBLIC 7e30 0 _Uaarch64_get_reg
PUBLIC 9024 0 _Uaarch64_set_reg
PUBLIC 9100 0 _Uaarch64_get_fpreg
PUBLIC 91a4 0 _Uaarch64_set_fpreg
PUBLIC 9250 0 _Uaarch64_set_caching_policy
PUBLIC 92c0 0 _Uaarch64_set_cache_size
PUBLIC 9374 0 _Uaarch64_apply_reg_state
PUBLIC 9384 0 _Uaarch64_create_addr_space
PUBLIC 9420 0 _Uaarch64_get_save_loc
PUBLIC 9490 0 _Uaarch64_init_local
PUBLIC 94f0 0 _Uaarch64_init_local2
PUBLIC 95b0 0 _Uaarch64_init_remote
PUBLIC 9620 0 _Uaarch64_is_signal_frame
PUBLIC 96a0 0 _Uaarch64_resume
PUBLIC 98a0 0 _Uaarch64_dwarf_search_unwind_table
PUBLIC a4d0 0 _Uaarch64_get_proc_info_by_ip
PUBLIC a570 0 _Uaarch64_get_proc_name
PUBLIC a7e0 0 _Uaarch64_reg_states_iterate
PUBLIC ae70 0 _Uaarch64_step
PUBLIC b3c0 0 _Uaarch64_get_proc_info
PUBLIC b400 0 _Uaarch64_dwarf_find_debug_frame
PUBLIC ba10 0 _Uaarch64_dwarf_find_unwind_table
STACK CFI INIT 1eb0 10 .cfa: sp 0 + .ra: x30
STACK CFI 1eb4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ebc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ec0 40 .cfa: sp 0 + .ra: x30
STACK CFI 1ed0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ed8 x19: .cfa -16 + ^
STACK CFI 1ef4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1f00 398 .cfa: sp 0 + .ra: x30
STACK CFI 1f04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2288 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 22a0 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 22a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 22ac x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 22b8 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 22d0 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 22d8 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 2434 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2438 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2454 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 2458 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 2460 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 246c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 2480 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 248c x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 2620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2624 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 2634 330 .cfa: sp 0 + .ra: x30
STACK CFI 2638 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 2644 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2658 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2664 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 26bc x23: x23 x24: x24
STACK CFI 26cc x21: x21 x22: x22
STACK CFI 26d4 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 26ec x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 26f4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 275c x21: x21 x22: x22
STACK CFI 2760 x23: x23 x24: x24
STACK CFI 2764 x25: x25 x26: x26
STACK CFI 2768 x27: x27 x28: x28
STACK CFI 276c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2770 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 2794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2798 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 27b4 x21: x21 x22: x22
STACK CFI 27b8 x23: x23 x24: x24
STACK CFI 27c0 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 27c8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 27d0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2830 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2834 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 2844 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 28e4 x21: x21 x22: x22
STACK CFI 28ec x23: x23 x24: x24
STACK CFI 28f0 x27: x27 x28: x28
STACK CFI 28f8 x25: x25 x26: x26
STACK CFI 28fc x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2908 x21: x21 x22: x22
STACK CFI 290c x23: x23 x24: x24
STACK CFI 2910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2914 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI 291c x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 2920 x21: x21 x22: x22
STACK CFI 2928 x23: x23 x24: x24
STACK CFI 292c x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 2950 x21: x21 x22: x22
STACK CFI 2958 x23: x23 x24: x24
STACK CFI 295c x25: x25 x26: x26
STACK CFI 2960 x27: x27 x28: x28
STACK CFI INIT 2964 44c .cfa: sp 0 + .ra: x30
STACK CFI 2968 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2974 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 2980 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 2998 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2aac x21: x21 x22: x22
STACK CFI 2ab0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2b34 x21: x21 x22: x22
STACK CFI 2b48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2b4c .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 2b50 x21: x21 x22: x22
STACK CFI 2b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2b74 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 2bb8 x21: x21 x22: x22
STACK CFI 2bc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2bcc .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 2c08 x21: x21 x22: x22
STACK CFI 2c10 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2d70 x21: x21 x22: x22
STACK CFI 2d74 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2d98 x21: x21 x22: x22
STACK CFI 2da0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI INIT 2db0 64 .cfa: sp 0 + .ra: x30
STACK CFI 2db4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2dbc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2dcc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2e04 x21: x21 x22: x22
STACK CFI 2e10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2e14 8c .cfa: sp 0 + .ra: x30
STACK CFI 2e18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2e20 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2e3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e40 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2e5c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2e68 x21: .cfa -16 + ^
STACK CFI 2e90 x21: x21
STACK CFI 2e9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2ea0 70 .cfa: sp 0 + .ra: x30
STACK CFI 2ee0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2eec x19: .cfa -16 + ^
STACK CFI 2f0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2f10 218 .cfa: sp 0 + .ra: x30
STACK CFI 2f14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f1c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 30c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 30c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3130 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 3134 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 314c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3158 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3178 x25: x25 x26: x26
STACK CFI 317c x27: x27 x28: x28
STACK CFI 3180 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3184 .cfa: sp 128 + .ra: .cfa -120 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 3194 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 3198 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 319c x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 31ac x19: x19 x20: x20
STACK CFI 31b0 x21: x21 x22: x22
STACK CFI 31b4 x23: x23 x24: x24
STACK CFI 31b8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 31c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 31c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 3320 10c .cfa: sp 0 + .ra: x30
STACK CFI 3324 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3334 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 33ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 33b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 33c0 x21: .cfa -16 + ^
STACK CFI 3400 x21: x21
STACK CFI 3404 x21: .cfa -16 + ^
STACK CFI 3424 x21: x21
STACK CFI INIT 3430 ac .cfa: sp 0 + .ra: x30
STACK CFI 3434 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 3444 x21: .cfa -144 + ^
STACK CFI 3454 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 34c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 34cc .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x29: .cfa -176 + ^
STACK CFI INIT 34e0 364 .cfa: sp 0 + .ra: x30
STACK CFI 34e4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 34ec x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3504 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 350c x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 3518 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3544 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 3774 x19: x19 x20: x20
STACK CFI 3778 x23: x23 x24: x24
STACK CFI 377c x25: x25 x26: x26
STACK CFI 3780 x27: x27 x28: x28
STACK CFI 3788 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 378c x19: x19 x20: x20
STACK CFI 3790 x25: x25 x26: x26
STACK CFI 3794 x27: x27 x28: x28
STACK CFI 37a0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 37a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 37a8 x19: x19 x20: x20
STACK CFI 37b4 x23: x23 x24: x24
STACK CFI 37b8 x25: x25 x26: x26
STACK CFI 37bc x27: x27 x28: x28
STACK CFI 37c0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 37c4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 37d0 x19: x19 x20: x20
STACK CFI 37d4 x23: x23 x24: x24
STACK CFI 37dc x25: x25 x26: x26
STACK CFI 37e0 x27: x27 x28: x28
STACK CFI 37e8 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3818 x19: x19 x20: x20
STACK CFI 3820 x23: x23 x24: x24
STACK CFI 3824 x25: x25 x26: x26
STACK CFI 3828 x27: x27 x28: x28
STACK CFI 382c x19: .cfa -112 + ^ x20: .cfa -104 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3830 x19: x19 x20: x20
STACK CFI 3838 x23: x23 x24: x24
STACK CFI 383c x25: x25 x26: x26
STACK CFI 3840 x27: x27 x28: x28
STACK CFI INIT 3844 9bc .cfa: sp 0 + .ra: x30
STACK CFI 384c .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 3854 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 3860 x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 3878 x21: .cfa -256 + ^ x22: .cfa -248 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 3884 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 38e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 38e4 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT 4200 b0 .cfa: sp 0 + .ra: x30
STACK CFI 4204 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 420c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4218 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 42a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 42a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 42b0 138 .cfa: sp 0 + .ra: x30
STACK CFI 42d0 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 4348 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 43f0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 43f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4400 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4410 x23: .cfa -32 + ^
STACK CFI 441c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4478 x19: x19 x20: x20
STACK CFI 447c x23: x23
STACK CFI 4484 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4488 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 44c0 x19: x19 x20: x20 x23: x23
STACK CFI INIT 44d0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 44d4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 44e0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 44f0 x23: .cfa -32 + ^
STACK CFI 44fc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4558 x19: x19 x20: x20
STACK CFI 455c x23: x23
STACK CFI 4564 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 4568 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 45a0 x19: x19 x20: x20 x23: x23
STACK CFI INIT 45b0 154 .cfa: sp 0 + .ra: x30
STACK CFI 45b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 45bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 45e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 45ec .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 45fc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 463c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4644 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 4688 x21: x21 x22: x22
STACK CFI 468c x23: x23 x24: x24
STACK CFI 4690 x25: x25 x26: x26
STACK CFI 4694 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4698 x21: x21 x22: x22
STACK CFI 469c x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 46a0 x23: x23 x24: x24
STACK CFI 46a4 x25: x25 x26: x26
STACK CFI 46ac x21: x21 x22: x22
STACK CFI 46b0 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 46f8 x21: x21 x22: x22
STACK CFI 46fc x23: x23 x24: x24
STACK CFI 4700 x25: x25 x26: x26
STACK CFI INIT 4704 164 .cfa: sp 0 + .ra: x30
STACK CFI 4708 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4710 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 4718 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 4728 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 474c x21: x21 x22: x22
STACK CFI 475c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 4760 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 4770 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4780 x21: x21 x22: x22
STACK CFI 478c x23: x23 x24: x24
STACK CFI 4794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 4798 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 4824 x21: x21 x22: x22
STACK CFI 4828 x23: x23 x24: x24
STACK CFI 4844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 4848 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 4858 x21: x21 x22: x22
STACK CFI 485c x23: x23 x24: x24
STACK CFI 4864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI INIT 4870 314 .cfa: sp 0 + .ra: x30
STACK CFI 4874 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 4878 .cfa: x29 240 +
STACK CFI 487c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 4888 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 489c x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 4900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4904 .cfa: x29 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI INIT 4b84 4d4 .cfa: sp 0 + .ra: x30
STACK CFI 4b88 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4b90 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4ba0 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 4bac x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4bbc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4bc0 x27: .cfa -32 + ^
STACK CFI 4f50 x25: x25 x26: x26
STACK CFI 4f54 x27: x27
STACK CFI 4f58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4f5c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI 5018 x25: x25 x26: x26
STACK CFI 501c x27: x27
STACK CFI 5030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5034 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x29: .cfa -112 + ^
STACK CFI INIT 5060 3dc .cfa: sp 0 + .ra: x30
STACK CFI 5068 .cfa: sp 4288 +
STACK CFI 506c .ra: .cfa -4280 + ^ x29: .cfa -4288 + ^
STACK CFI 5074 x21: .cfa -4256 + ^ x22: .cfa -4248 + ^
STACK CFI 5080 x19: .cfa -4272 + ^ x20: .cfa -4264 + ^
STACK CFI 5090 x23: .cfa -4240 + ^ x24: .cfa -4232 + ^
STACK CFI 50c4 x25: .cfa -4224 + ^ x26: .cfa -4216 + ^
STACK CFI 51d8 x27: .cfa -4208 + ^ x28: .cfa -4200 + ^
STACK CFI 525c x27: x27 x28: x28
STACK CFI 5264 x25: x25 x26: x26
STACK CFI 527c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5280 .cfa: sp 4288 + .ra: .cfa -4280 + ^ x19: .cfa -4272 + ^ x20: .cfa -4264 + ^ x21: .cfa -4256 + ^ x22: .cfa -4248 + ^ x23: .cfa -4240 + ^ x24: .cfa -4232 + ^ x25: .cfa -4224 + ^ x26: .cfa -4216 + ^ x27: .cfa -4208 + ^ x28: .cfa -4200 + ^ x29: .cfa -4288 + ^
STACK CFI 52a4 x27: x27 x28: x28
STACK CFI 53d8 x25: x25 x26: x26
STACK CFI 53f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 53f4 .cfa: sp 4288 + .ra: .cfa -4280 + ^ x19: .cfa -4272 + ^ x20: .cfa -4264 + ^ x21: .cfa -4256 + ^ x22: .cfa -4248 + ^ x23: .cfa -4240 + ^ x24: .cfa -4232 + ^ x25: .cfa -4224 + ^ x26: .cfa -4216 + ^ x29: .cfa -4288 + ^
STACK CFI 5400 x25: x25 x26: x26
STACK CFI 5408 x25: .cfa -4224 + ^ x26: .cfa -4216 + ^ x27: .cfa -4208 + ^ x28: .cfa -4200 + ^
STACK CFI 5410 x27: x27 x28: x28
STACK CFI 5428 x27: .cfa -4208 + ^ x28: .cfa -4200 + ^
STACK CFI INIT 5440 3d0 .cfa: sp 0 + .ra: x30
STACK CFI 5444 .cfa: sp 448 + .ra: .cfa -440 + ^ x29: .cfa -448 + ^
STACK CFI 544c x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI 5458 x21: .cfa -416 + ^ x22: .cfa -408 + ^
STACK CFI 546c x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI 548c x25: .cfa -384 + ^ x26: .cfa -376 + ^
STACK CFI 5498 x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI 576c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5770 .cfa: sp 448 + .ra: .cfa -440 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^ x29: .cfa -448 + ^
STACK CFI INIT 5810 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 5818 .cfa: sp 4368 +
STACK CFI 5820 .ra: .cfa -4344 + ^ x29: .cfa -4352 + ^
STACK CFI 5828 x19: .cfa -4336 + ^ x20: .cfa -4328 + ^
STACK CFI 5834 x23: .cfa -4304 + ^ x24: .cfa -4296 + ^
STACK CFI 5840 x25: .cfa -4288 + ^ x26: .cfa -4280 + ^
STACK CFI 5854 x21: .cfa -4320 + ^ x22: .cfa -4312 + ^
STACK CFI 5868 x27: .cfa -4272 + ^ x28: .cfa -4264 + ^
STACK CFI 59d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 59d8 .cfa: sp 4368 + .ra: .cfa -4344 + ^ x19: .cfa -4336 + ^ x20: .cfa -4328 + ^ x21: .cfa -4320 + ^ x22: .cfa -4312 + ^ x23: .cfa -4304 + ^ x24: .cfa -4296 + ^ x25: .cfa -4288 + ^ x26: .cfa -4280 + ^ x27: .cfa -4272 + ^ x28: .cfa -4264 + ^ x29: .cfa -4352 + ^
STACK CFI INIT 5af4 44 .cfa: sp 0 + .ra: x30
STACK CFI 5af8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5b00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5b0c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5b34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5b40 28 .cfa: sp 0 + .ra: x30
STACK CFI 5b4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5b64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5b70 13c .cfa: sp 0 + .ra: x30
STACK CFI 5b78 .cfa: sp 4288 +
STACK CFI 5b7c .ra: .cfa -4280 + ^ x29: .cfa -4288 + ^
STACK CFI 5b84 x21: .cfa -4256 + ^ x22: .cfa -4248 + ^
STACK CFI 5b90 x19: .cfa -4272 + ^ x20: .cfa -4264 + ^
STACK CFI 5c90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5c94 .cfa: sp 4288 + .ra: .cfa -4280 + ^ x19: .cfa -4272 + ^ x20: .cfa -4264 + ^ x21: .cfa -4256 + ^ x22: .cfa -4248 + ^ x29: .cfa -4288 + ^
STACK CFI INIT 5cb0 74 .cfa: sp 0 + .ra: x30
STACK CFI 5cb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5cbc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5cc8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5d24 29c .cfa: sp 0 + .ra: x30
STACK CFI 5d28 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 5d30 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 5d44 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 5dc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5dc4 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x29: .cfa -208 + ^
STACK CFI 5dc8 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 5f3c x23: x23 x24: x24
STACK CFI 5f40 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI INIT 5fc0 1284 .cfa: sp 0 + .ra: x30
STACK CFI 5fc4 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 5fd4 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 5fe0 x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 5fec x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 6038 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 615c x27: x27 x28: x28
STACK CFI 6174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6178 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x29: .cfa -288 + ^
STACK CFI 619c x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 61a8 x27: x27 x28: x28
STACK CFI 61b0 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 6308 x27: x27 x28: x28
STACK CFI 630c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 6310 .cfa: sp 288 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI 721c x27: x27 x28: x28
STACK CFI 7224 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 7228 x27: x27 x28: x28
STACK CFI 7230 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 7240 x27: x27 x28: x28
STACK CFI INIT 7244 2bc .cfa: sp 0 + .ra: x30
STACK CFI 7248 .cfa: sp 2128 +
STACK CFI 724c .ra: .cfa -2120 + ^ x29: .cfa -2128 + ^
STACK CFI 7254 x25: .cfa -2064 + ^ x26: .cfa -2056 + ^
STACK CFI 7260 x19: .cfa -2112 + ^ x20: .cfa -2104 + ^ x21: .cfa -2096 + ^ x22: .cfa -2088 + ^
STACK CFI 7270 x23: .cfa -2080 + ^ x24: .cfa -2072 + ^ x27: .cfa -2048 + ^ x28: .cfa -2040 + ^
STACK CFI 74d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 74d4 .cfa: sp 2128 + .ra: .cfa -2120 + ^ x19: .cfa -2112 + ^ x20: .cfa -2104 + ^ x21: .cfa -2096 + ^ x22: .cfa -2088 + ^ x23: .cfa -2080 + ^ x24: .cfa -2072 + ^ x25: .cfa -2064 + ^ x26: .cfa -2056 + ^ x27: .cfa -2048 + ^ x28: .cfa -2040 + ^ x29: .cfa -2128 + ^
STACK CFI INIT 7500 230 .cfa: sp 0 + .ra: x30
STACK CFI 7504 .cfa: sp 288 + .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 7514 x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^
STACK CFI 7520 x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 772c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI INIT 7730 450 .cfa: sp 0 + .ra: x30
STACK CFI 7734 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 773c x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 774c x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 7768 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 77a0 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 7a7c x25: x25 x26: x26
STACK CFI 7a94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 7a98 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^ x29: .cfa -272 + ^
STACK CFI 7b70 x25: x25 x26: x26
STACK CFI INIT 7b80 c0 .cfa: sp 0 + .ra: x30
STACK CFI 7b84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7ba0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7ba4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7c40 1c .cfa: sp 0 + .ra: x30
STACK CFI 7c44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7c58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7c60 9c .cfa: sp 0 + .ra: x30
STACK CFI 7c64 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7c70 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7c7c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7cc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7cc4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7cdc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7cf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 7d00 90 .cfa: sp 0 + .ra: x30
STACK CFI 7d04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7d10 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7d1c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7d68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7d6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7d90 44 .cfa: sp 0 + .ra: x30
STACK CFI 7d94 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7db4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7dc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7dc8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7dd4 48 .cfa: sp 0 + .ra: x30
STACK CFI 7dd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7de8 x19: .cfa -16 + ^
STACK CFI 7e04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7e08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7e18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7e20 10 .cfa: sp 0 + .ra: x30
STACK CFI 7e24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7e2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7e30 dc .cfa: sp 0 + .ra: x30
STACK CFI 7e34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7e80 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7e90 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7ea0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7ea4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7eb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7ebc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7eec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7efc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7f10 b50 .cfa: sp 0 + .ra: x30
STACK CFI 7f14 .cfa: sp 720 +
STACK CFI 7f18 .ra: .cfa -712 + ^ x29: .cfa -720 + ^
STACK CFI 7f20 x19: .cfa -704 + ^ x20: .cfa -696 + ^
STACK CFI 7f28 x21: .cfa -688 + ^ x22: .cfa -680 + ^
STACK CFI 7f34 x23: .cfa -672 + ^ x24: .cfa -664 + ^
STACK CFI 7f3c x27: .cfa -640 + ^ x28: .cfa -632 + ^
STACK CFI 7f84 x25: .cfa -656 + ^ x26: .cfa -648 + ^
STACK CFI 8004 x25: x25 x26: x26
STACK CFI 800c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 8010 .cfa: sp 720 + .ra: .cfa -712 + ^ x19: .cfa -704 + ^ x20: .cfa -696 + ^ x21: .cfa -688 + ^ x22: .cfa -680 + ^ x23: .cfa -672 + ^ x24: .cfa -664 + ^ x25: .cfa -656 + ^ x26: .cfa -648 + ^ x27: .cfa -640 + ^ x28: .cfa -632 + ^ x29: .cfa -720 + ^
STACK CFI 8098 x25: x25 x26: x26
STACK CFI 80a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 80a4 .cfa: sp 720 + .ra: .cfa -712 + ^ x19: .cfa -704 + ^ x20: .cfa -696 + ^ x21: .cfa -688 + ^ x22: .cfa -680 + ^ x23: .cfa -672 + ^ x24: .cfa -664 + ^ x25: .cfa -656 + ^ x26: .cfa -648 + ^ x27: .cfa -640 + ^ x28: .cfa -632 + ^ x29: .cfa -720 + ^
STACK CFI 8110 x25: x25 x26: x26
STACK CFI 8118 x25: .cfa -656 + ^ x26: .cfa -648 + ^
STACK CFI 817c x25: x25 x26: x26
STACK CFI 81a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 81a8 .cfa: sp 720 + .ra: .cfa -712 + ^ x19: .cfa -704 + ^ x20: .cfa -696 + ^ x21: .cfa -688 + ^ x22: .cfa -680 + ^ x23: .cfa -672 + ^ x24: .cfa -664 + ^ x25: .cfa -656 + ^ x26: .cfa -648 + ^ x27: .cfa -640 + ^ x28: .cfa -632 + ^ x29: .cfa -720 + ^
STACK CFI 8960 x25: x25 x26: x26
STACK CFI 8974 x25: .cfa -656 + ^ x26: .cfa -648 + ^
STACK CFI INIT 8a60 5c4 .cfa: sp 0 + .ra: x30
STACK CFI 8a64 .cfa: sp 1760 +
STACK CFI 8a6c .ra: .cfa -1752 + ^ x29: .cfa -1760 + ^
STACK CFI 8a90 x19: .cfa -1744 + ^ x20: .cfa -1736 + ^ x21: .cfa -1728 + ^ x22: .cfa -1720 + ^ x23: .cfa -1712 + ^ x24: .cfa -1704 + ^ x25: .cfa -1696 + ^ x26: .cfa -1688 + ^ x27: .cfa -1680 + ^ x28: .cfa -1672 + ^
STACK CFI 8c28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8c2c .cfa: sp 1760 + .ra: .cfa -1752 + ^ x19: .cfa -1744 + ^ x20: .cfa -1736 + ^ x21: .cfa -1728 + ^ x22: .cfa -1720 + ^ x23: .cfa -1712 + ^ x24: .cfa -1704 + ^ x25: .cfa -1696 + ^ x26: .cfa -1688 + ^ x27: .cfa -1680 + ^ x28: .cfa -1672 + ^ x29: .cfa -1760 + ^
STACK CFI INIT 9024 d8 .cfa: sp 0 + .ra: x30
STACK CFI 9094 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 90d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 90dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 90f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9100 a4 .cfa: sp 0 + .ra: x30
STACK CFI 9108 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9118 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9178 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9190 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9194 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 91a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 91a4 a8 .cfa: sp 0 + .ra: x30
STACK CFI 91ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 91bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 921c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 923c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 9248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9250 68 .cfa: sp 0 + .ra: x30
STACK CFI 9254 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 925c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 92ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 92b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 92c0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 92c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 92cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 92e0 x21: .cfa -16 + ^
STACK CFI 934c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9350 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 9368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 936c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9374 10 .cfa: sp 0 + .ra: x30
STACK CFI 9378 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9380 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9384 9c .cfa: sp 0 + .ra: x30
STACK CFI 9388 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9390 x19: .cfa -16 + ^
STACK CFI 93dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 93e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 93ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 93f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 941c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9420 68 .cfa: sp 0 + .ra: x30
STACK CFI 9424 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 943c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9440 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9464 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9470 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 947c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9490 5c .cfa: sp 0 + .ra: x30
STACK CFI 9494 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 949c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 94e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 94e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 94f0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 94f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 94fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 954c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9550 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9598 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 95ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 95b0 70 .cfa: sp 0 + .ra: x30
STACK CFI 95b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 95bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 95d0 x21: .cfa -16 + ^
STACK CFI 9614 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9618 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9620 78 .cfa: sp 0 + .ra: x30
STACK CFI 9624 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9634 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 968c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9690 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 96a0 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 96a4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 96ac x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 96c0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 96cc x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 96d8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 96e4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 9788 x21: x21 x22: x22
STACK CFI 978c x23: x23 x24: x24
STACK CFI 9794 x25: x25 x26: x26
STACK CFI 979c x27: x27 x28: x28
STACK CFI 97a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 97a4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 9888 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9894 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 98a0 478 .cfa: sp 0 + .ra: x30
STACK CFI 98a4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 98ac x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 98c0 x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 98dc x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 9944 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 9b50 x25: x25 x26: x26
STACK CFI 9b58 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 9b7c x25: x25 x26: x26
STACK CFI 9b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 9b94 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 9ba8 x25: x25 x26: x26
STACK CFI 9bdc x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 9be4 x25: x25 x26: x26
STACK CFI 9cec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 9cf0 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 9cf8 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 9cfc x25: x25 x26: x26
STACK CFI 9d14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI INIT 9d20 14c .cfa: sp 0 + .ra: x30
STACK CFI 9d24 .cfa: sp 416 + .ra: .cfa -408 + ^ x29: .cfa -416 + ^
STACK CFI 9d3c x19: .cfa -400 + ^ x20: .cfa -392 + ^
STACK CFI 9d48 x21: .cfa -384 + ^ x22: .cfa -376 + ^
STACK CFI 9d60 x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^
STACK CFI 9e24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9e28 .cfa: sp 416 + .ra: .cfa -408 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^ x29: .cfa -416 + ^
STACK CFI 9e60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9e64 .cfa: sp 416 + .ra: .cfa -408 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^ x29: .cfa -416 + ^
STACK CFI INIT 9e70 65c .cfa: sp 0 + .ra: x30
STACK CFI 9e7c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 9e84 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 9e90 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 9e98 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 9ecc x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 9ed0 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI a178 x23: x23 x24: x24
STACK CFI a17c x25: x25 x26: x26
STACK CFI a184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI a188 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI a1a8 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI a1ac x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI a1bc x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI a1cc x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI a28c x23: x23 x24: x24
STACK CFI a290 x25: x25 x26: x26
STACK CFI a2a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI a2ac .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI a2ec x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI a334 x23: x23 x24: x24
STACK CFI a338 x25: x25 x26: x26
STACK CFI a33c x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI a358 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI a3a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI a3a8 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI a3b0 x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI a490 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI a4c0 x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI INIT a4d0 98 .cfa: sp 0 + .ra: x30
STACK CFI a4d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a4e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a4f0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a534 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI a558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI a560 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT a570 1c4 .cfa: sp 0 + .ra: x30
STACK CFI a574 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI a57c x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI a584 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI a598 x25: .cfa -128 + ^ x26: .cfa -120 + ^
STACK CFI a5ac x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI a5b4 x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI a658 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a65c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^ x29: .cfa -192 + ^
STACK CFI INIT a734 ac .cfa: sp 0 + .ra: x30
STACK CFI a738 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a748 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a75c x21: .cfa -16 + ^
STACK CFI a7b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI a7b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT a7e0 150 .cfa: sp 0 + .ra: x30
STACK CFI a7e4 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI a7ec x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI a7f8 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI a800 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI a89c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a8a0 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x29: .cfa -208 + ^
STACK CFI a8b8 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI a928 x25: x25 x26: x26
STACK CFI INIT a930 540 .cfa: sp 0 + .ra: x30
STACK CFI a934 .cfa: sp 2176 +
STACK CFI a938 .ra: .cfa -2168 + ^ x29: .cfa -2176 + ^
STACK CFI a940 x19: .cfa -2160 + ^ x20: .cfa -2152 + ^
STACK CFI a948 x21: .cfa -2144 + ^ x22: .cfa -2136 + ^
STACK CFI a954 x23: .cfa -2128 + ^ x24: .cfa -2120 + ^
STACK CFI aa88 x25: .cfa -2112 + ^ x26: .cfa -2104 + ^
STACK CFI aaa0 x25: x25 x26: x26
STACK CFI ada4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI ada8 .cfa: sp 2176 + .ra: .cfa -2168 + ^ x19: .cfa -2160 + ^ x20: .cfa -2152 + ^ x21: .cfa -2144 + ^ x22: .cfa -2136 + ^ x23: .cfa -2128 + ^ x24: .cfa -2120 + ^ x29: .cfa -2176 + ^
STACK CFI ade0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI ade4 .cfa: sp 2176 + .ra: .cfa -2168 + ^ x19: .cfa -2160 + ^ x20: .cfa -2152 + ^ x21: .cfa -2144 + ^ x22: .cfa -2136 + ^ x23: .cfa -2128 + ^ x24: .cfa -2120 + ^ x25: .cfa -2112 + ^ x26: .cfa -2104 + ^ x29: .cfa -2176 + ^
STACK CFI adec x27: .cfa -2096 + ^ x28: .cfa -2088 + ^
STACK CFI ae5c x25: x25 x26: x26
STACK CFI ae60 x27: x27 x28: x28
STACK CFI INIT ae70 3fc .cfa: sp 0 + .ra: x30
STACK CFI ae74 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ae7c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ae88 x21: .cfa -32 + ^
STACK CFI aed0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI aed4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI b0e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI b0ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT b270 150 .cfa: sp 0 + .ra: x30
STACK CFI b274 .cfa: sp 2032 +
STACK CFI b278 .ra: .cfa -2024 + ^ x29: .cfa -2032 + ^
STACK CFI b284 x19: .cfa -2016 + ^ x20: .cfa -2008 + ^
STACK CFI b2a0 x21: .cfa -2000 + ^ x22: .cfa -1992 + ^
STACK CFI b2bc x21: x21 x22: x22
STACK CFI b2e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b2ec .cfa: sp 2032 + .ra: .cfa -2024 + ^ x19: .cfa -2016 + ^ x20: .cfa -2008 + ^ x29: .cfa -2032 + ^
STACK CFI b334 x21: .cfa -2000 + ^ x22: .cfa -1992 + ^
STACK CFI b338 x23: .cfa -1984 + ^ x24: .cfa -1976 + ^
STACK CFI b344 x25: .cfa -1968 + ^
STACK CFI b3b0 x23: x23 x24: x24
STACK CFI b3b4 x25: x25
STACK CFI b3bc x21: x21 x22: x22
STACK CFI INIT b3c0 40 .cfa: sp 0 + .ra: x30
STACK CFI b3c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b3cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b3fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b400 19c .cfa: sp 0 + .ra: x30
STACK CFI b40c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI b414 x23: .cfa -16 + ^
STACK CFI b420 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI b438 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI b498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI b49c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT b5a0 46c .cfa: sp 0 + .ra: x30
STACK CFI b5a4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI b5ac x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI b5b4 x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI b5cc x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI b5d0 x23: .cfa -128 + ^ x24: .cfa -120 + ^
STACK CFI b604 x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI b7cc x27: x27 x28: x28
STACK CFI b7d4 x21: x21 x22: x22
STACK CFI b7d8 x23: x23 x24: x24
STACK CFI b7e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI b7e8 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI b854 x21: x21 x22: x22
STACK CFI b858 x23: x23 x24: x24
STACK CFI b860 x27: x27 x28: x28
STACK CFI b864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI b868 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI b904 x21: x21 x22: x22
STACK CFI b908 x23: x23 x24: x24
STACK CFI b910 x27: x27 x28: x28
STACK CFI b914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI b918 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI b9b8 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI b9c0 x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI INIT ba10 2d4 .cfa: sp 0 + .ra: x30
STACK CFI ba14 .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI ba1c x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI ba54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ba58 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x29: .cfa -208 + ^
STACK CFI ba68 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI ba6c x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI ba70 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI ba74 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI ba84 x21: x21 x22: x22
STACK CFI ba88 x23: x23 x24: x24
STACK CFI ba8c x25: x25 x26: x26
STACK CFI ba90 x27: x27 x28: x28
STACK CFI ba94 x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI bc70 x25: x25 x26: x26
STACK CFI bc78 x27: x27 x28: x28
STACK CFI bc88 x21: x21 x22: x22
STACK CFI bc90 x23: x23 x24: x24
STACK CFI bc9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bca0 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI bcc4 x21: x21 x22: x22
STACK CFI bcc8 x23: x23 x24: x24
STACK CFI bccc x25: x25 x26: x26
STACK CFI bcd0 x27: x27 x28: x28
STACK CFI bcd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bcd8 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI INIT ccf0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT cd30 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT cd60 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e80 24 .cfa: sp 0 + .ra: x30
STACK CFI 1e84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1e9c .cfa: sp 0 + .ra: .ra x29: x29
