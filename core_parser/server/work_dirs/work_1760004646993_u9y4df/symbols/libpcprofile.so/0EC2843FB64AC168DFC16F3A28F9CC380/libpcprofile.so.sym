MODULE Linux arm64 0EC2843FB64AC168DFC16F3A28F9CC380 libpcprofile.so
INFO CODE_ID 3F84C20E4AB668C1DFC16F3A28F9CC38FBA2734D
PUBLIC 9d0 0 __cyg_profile_func_enter
STACK CFI INIT 900 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 930 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 970 48 .cfa: sp 0 + .ra: x30
STACK CFI 974 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 97c x19: .cfa -16 + ^
STACK CFI 9b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7d0 110 .cfa: sp 0 + .ra: x30
STACK CFI 7d4 .cfa: sp 64 +
STACK CFI 7e8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 82c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 830 .cfa: sp 64 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 834 x21: .cfa -16 + ^
STACK CFI 844 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8a8 x19: x19 x20: x20
STACK CFI 8ac x21: x21
STACK CFI 8b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 8c8 x21: x21
STACK CFI 8d0 x19: x19 x20: x20
STACK CFI 8d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8dc x21: .cfa -16 + ^
STACK CFI INIT 7a0 2c .cfa: sp 0 + .ra: x30
STACK CFI 7ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9d0 78 .cfa: sp 0 + .ra: x30
STACK CFI 9d4 .cfa: sp 48 +
STACK CFI 9e8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a44 .cfa: sp 48 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
