MODULE Linux arm64 299978C98E6CB55498A47966FB5143680 libutil-setid-samba4.so.0
INFO CODE_ID C97899296C8E54B598A47966FB514368CEE3B849
PUBLIC 8a0 0 samba_setresuid
PUBLIC 8d4 0 samba_setresgid
PUBLIC 910 0 samba_setreuid
PUBLIC 940 0 samba_setregid
PUBLIC 970 0 samba_seteuid
PUBLIC 9a0 0 samba_setegid
PUBLIC 9d0 0 samba_setuid
PUBLIC 9f4 0 samba_setgid
PUBLIC a20 0 samba_setuidx
PUBLIC a50 0 samba_setgidx
PUBLIC a80 0 samba_setgroups
STACK CFI INIT 7d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 800 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 840 48 .cfa: sp 0 + .ra: x30
STACK CFI 844 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 84c x19: .cfa -16 + ^
STACK CFI 884 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 890 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a0 34 .cfa: sp 0 + .ra: x30
STACK CFI 8b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8d4 34 .cfa: sp 0 + .ra: x30
STACK CFI 8e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 900 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 910 28 .cfa: sp 0 + .ra: x30
STACK CFI 918 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 930 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 940 28 .cfa: sp 0 + .ra: x30
STACK CFI 948 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 960 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 970 2c .cfa: sp 0 + .ra: x30
STACK CFI 978 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 994 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9a0 2c .cfa: sp 0 + .ra: x30
STACK CFI 9a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9d0 24 .cfa: sp 0 + .ra: x30
STACK CFI 9d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9f4 24 .cfa: sp 0 + .ra: x30
STACK CFI 9fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a20 2c .cfa: sp 0 + .ra: x30
STACK CFI a28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a50 2c .cfa: sp 0 + .ra: x30
STACK CFI a58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a80 28 .cfa: sp 0 + .ra: x30
STACK CFI a88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI aa0 .cfa: sp 0 + .ra: .ra x29: x29
