MODULE Linux arm64 A70C2557D9B61672E1A524D24C26CD160 libjpe_interface.so
INFO CODE_ID 57250CA7B6D97216E1A524D24C26CD16
PUBLIC 1d68 0 _init
PUBLIC 2090 0 call_weak_fn
PUBLIC 20b0 0 deregister_tm_clones
PUBLIC 20e0 0 register_tm_clones
PUBLIC 2120 0 __do_global_dtors_aux
PUBLIC 2170 0 frame_dummy
PUBLIC 2180 0 lios::jpe::JpeNvMedia::JpeNvMedia(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> >&)
PUBLIC 24b0 0 lios::jpe::JpeNvMedia::~JpeNvMedia()
PUBLIC 2500 0 lios::jpe::JpeNvMedia::GetImage(unsigned char**)
PUBLIC 2510 0 lios::jpe::JpeNvMedia::GetJpeBufAttrList(linvs::buf::BufAttrList*)
PUBLIC 2610 0 lios::jpe::JpeNvMedia::FeedFrame(NvSciBufObjRefRec*)
PUBLIC 2860 0 std::vector<unsigned char, std::allocator<unsigned char> >::_M_default_append(unsigned long)
PUBLIC 29cc 0 _fini
STACK CFI INIT 20b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 20e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2120 48 .cfa: sp 0 + .ra: x30
STACK CFI 2124 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 212c x19: .cfa -16 + ^
STACK CFI 2164 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2170 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2180 328 .cfa: sp 0 + .ra: x30
STACK CFI 2184 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 218c x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 2194 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI 21a8 x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI 21b0 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI 2410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2414 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x29: .cfa -240 + ^
STACK CFI INIT 24b0 50 .cfa: sp 0 + .ra: x30
STACK CFI 24b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 24bc x19: .cfa -16 + ^
STACK CFI 24fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2500 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2510 fc .cfa: sp 0 + .ra: x30
STACK CFI 2514 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2524 x19: .cfa -144 + ^
STACK CFI 25e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 25e4 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 2860 16c .cfa: sp 0 + .ra: x30
STACK CFI 2868 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2874 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 287c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 289c x25: .cfa -16 + ^
STACK CFI 2918 x25: x25
STACK CFI 2938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 293c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 2960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2968 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 2978 x25: .cfa -16 + ^
STACK CFI INIT 2610 24c .cfa: sp 0 + .ra: x30
STACK CFI 2614 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 262c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 2634 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 2780 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2784 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
