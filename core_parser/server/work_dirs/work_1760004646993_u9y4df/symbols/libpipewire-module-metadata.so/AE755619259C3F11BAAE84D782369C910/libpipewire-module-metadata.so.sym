MODULE Linux arm64 AE755619259C3F11BAAE84D782369C910 libpipewire-module-metadata.so
INFO CODE_ID 195675AE9C25113FBAAE84D782369C918AD7CE76
PUBLIC 8e90 0 pipewire__module_init
STACK CFI INIT 23d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2400 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2440 48 .cfa: sp 0 + .ra: x30
STACK CFI 2444 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 244c x19: .cfa -16 + ^
STACK CFI 2484 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2490 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 24a0 340 .cfa: sp 0 + .ra: x30
STACK CFI 24a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2594 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 25a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 26d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 26e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2734 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 273c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 27b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 27bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 27e0 ac .cfa: sp 0 + .ra: x30
STACK CFI 27e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 27f0 x19: .cfa -16 + ^
STACK CFI 2874 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 287c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2884 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2890 dc .cfa: sp 0 + .ra: x30
STACK CFI 2898 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 28a0 x19: .cfa -16 + ^
STACK CFI 2954 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 295c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2964 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2970 1c .cfa: sp 0 + .ra: x30
STACK CFI 2978 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2984 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2990 48 .cfa: sp 0 + .ra: x30
STACK CFI 29b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 29c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 29e0 68 .cfa: sp 0 + .ra: x30
STACK CFI 29e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29f0 x19: .cfa -16 + ^
STACK CFI 2a0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2a14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2a40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2a50 60 .cfa: sp 0 + .ra: x30
STACK CFI 2a58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2a60 x19: .cfa -16 + ^
STACK CFI 2a74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2a7c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2aa8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2ab0 130 .cfa: sp 0 + .ra: x30
STACK CFI 2ab8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2ac4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2adc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2af4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2bb4 x23: x23 x24: x24
STACK CFI 2bb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2bc0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 2bd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2be0 68 .cfa: sp 0 + .ra: x30
STACK CFI 2be8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2bf0 x19: .cfa -16 + ^
STACK CFI 2c30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2c38 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2c40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2c50 78 .cfa: sp 0 + .ra: x30
STACK CFI 2c58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c60 x19: .cfa -16 + ^
STACK CFI 2cb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2cb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2cc0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2cd0 104 .cfa: sp 0 + .ra: x30
STACK CFI 2cd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2ce8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2cf4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2d64 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2dcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2dd4 13c .cfa: sp 0 + .ra: x30
STACK CFI 2ddc .cfa: sp 96 +
STACK CFI 2dec .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2df4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2e98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ea0 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2f10 20 .cfa: sp 0 + .ra: x30
STACK CFI 2f18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2f24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2f30 110 .cfa: sp 0 + .ra: x30
STACK CFI 2f38 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2f44 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2f54 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2ff8 x21: x21 x22: x22
STACK CFI 3004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 300c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3010 x21: x21 x22: x22
STACK CFI 3020 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3028 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 302c x21: x21 x22: x22
STACK CFI 3038 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3040 33c .cfa: sp 0 + .ra: x30
STACK CFI 3048 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3054 x19: .cfa -16 + ^
STACK CFI 309c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 30a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3380 b8 .cfa: sp 0 + .ra: x30
STACK CFI 3388 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3390 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 339c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 33a8 x23: .cfa -16 + ^
STACK CFI 3418 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 3420 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3440 e0 .cfa: sp 0 + .ra: x30
STACK CFI 3448 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3450 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3458 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3464 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 346c x25: .cfa -16 + ^
STACK CFI 34d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 34d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 3518 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 3520 68 .cfa: sp 0 + .ra: x30
STACK CFI 3528 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3530 x19: .cfa -16 + ^
STACK CFI 3570 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3578 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3580 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3590 88 .cfa: sp 0 + .ra: x30
STACK CFI 3598 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 35a0 x19: .cfa -16 + ^
STACK CFI 3610 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3620 2c8c .cfa: sp 0 + .ra: x30
STACK CFI 3628 .cfa: sp 480 +
STACK CFI 363c .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 3654 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 365c x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 3674 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 3e20 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3e28 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 62b0 294 .cfa: sp 0 + .ra: x30
STACK CFI 62b8 .cfa: sp 128 +
STACK CFI 62bc .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 62c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 62d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 62dc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 6450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 6458 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6544 260 .cfa: sp 0 + .ra: x30
STACK CFI 654c .cfa: sp 96 +
STACK CFI 6550 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6558 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6568 x21: .cfa -16 + ^
STACK CFI 66b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 66b8 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 67a4 de4 .cfa: sp 0 + .ra: x30
STACK CFI 67ac .cfa: sp 352 +
STACK CFI 67c0 .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 67c8 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 67d8 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 67e4 x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 67ec x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 6bb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6bb8 .cfa: sp 352 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT 7590 168 .cfa: sp 0 + .ra: x30
STACK CFI 7598 .cfa: sp 80 +
STACK CFI 75a4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 75ac x19: .cfa -16 + ^
STACK CFI 76dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 76e4 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7700 70 .cfa: sp 0 + .ra: x30
STACK CFI 7708 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7710 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7760 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7770 9c .cfa: sp 0 + .ra: x30
STACK CFI 7778 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7780 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 778c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7798 x23: .cfa -16 + ^
STACK CFI 77fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 7810 6c .cfa: sp 0 + .ra: x30
STACK CFI 7818 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7820 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 786c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7880 9c .cfa: sp 0 + .ra: x30
STACK CFI 7888 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7890 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 789c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 78a8 x23: .cfa -16 + ^
STACK CFI 790c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 7920 180 .cfa: sp 0 + .ra: x30
STACK CFI 7928 .cfa: sp 160 +
STACK CFI 7938 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7944 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7a8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7a94 .cfa: sp 160 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7aa0 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 7aa8 .cfa: sp 128 +
STACK CFI 7ab4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7abc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7c6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7c74 .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7c80 174 .cfa: sp 0 + .ra: x30
STACK CFI 7c88 .cfa: sp 160 +
STACK CFI 7c98 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ca4 x19: .cfa -16 + ^
STACK CFI 7de0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7de8 .cfa: sp 160 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7df4 194 .cfa: sp 0 + .ra: x30
STACK CFI 7dfc .cfa: sp 176 +
STACK CFI 7e08 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7e10 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7e18 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7e20 x23: .cfa -16 + ^
STACK CFI 7f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7f7c .cfa: sp 176 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7f90 9c .cfa: sp 0 + .ra: x30
STACK CFI 7f98 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7fa0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7fac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7fb8 x23: .cfa -16 + ^
STACK CFI 801c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 8030 6c .cfa: sp 0 + .ra: x30
STACK CFI 8038 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8040 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 808c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 80a0 9c .cfa: sp 0 + .ra: x30
STACK CFI 80a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 80b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 80bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 80c8 x23: .cfa -16 + ^
STACK CFI 812c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 8140 180 .cfa: sp 0 + .ra: x30
STACK CFI 8148 .cfa: sp 160 +
STACK CFI 8158 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8164 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 82ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 82b4 .cfa: sp 160 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 82c0 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 82c8 .cfa: sp 128 +
STACK CFI 82d4 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 82dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 848c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8494 .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 84a0 174 .cfa: sp 0 + .ra: x30
STACK CFI 84a8 .cfa: sp 160 +
STACK CFI 84b8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 84c4 x19: .cfa -16 + ^
STACK CFI 8600 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8608 .cfa: sp 160 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8614 d8 .cfa: sp 0 + .ra: x30
STACK CFI 861c .cfa: sp 336 +
STACK CFI 862c .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 86e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 86e8 .cfa: sp 336 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI INIT 86f0 384 .cfa: sp 0 + .ra: x30
STACK CFI 86f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8700 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 870c .cfa: sp 560 + x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8770 x25: .cfa -32 + ^
STACK CFI 8778 x27: .cfa -16 + ^
STACK CFI 8780 x28: .cfa -8 + ^
STACK CFI 87a4 x23: .cfa -48 + ^
STACK CFI 87a8 x24: .cfa -40 + ^
STACK CFI 87ac x26: .cfa -24 + ^
STACK CFI 89b0 x23: x23
STACK CFI 89b4 x24: x24
STACK CFI 89b8 x25: x25
STACK CFI 89bc x26: x26
STACK CFI 89c0 x27: x27
STACK CFI 89c4 x28: x28
STACK CFI 89e4 .cfa: sp 96 +
STACK CFI 89f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 89f8 .cfa: sp 560 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8a58 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8a5c x23: .cfa -48 + ^
STACK CFI 8a60 x24: .cfa -40 + ^
STACK CFI 8a64 x25: .cfa -32 + ^
STACK CFI 8a68 x26: .cfa -24 + ^
STACK CFI 8a6c x27: .cfa -16 + ^
STACK CFI 8a70 x28: .cfa -8 + ^
STACK CFI INIT 8a74 418 .cfa: sp 0 + .ra: x30
STACK CFI 8a7c .cfa: sp 176 +
STACK CFI 8a88 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8a90 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8a9c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8aa8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8ab0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8ab8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8d04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8d0c .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8e90 250 .cfa: sp 0 + .ra: x30
STACK CFI 8e98 .cfa: sp 96 +
STACK CFI 8ea4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8eb0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8efc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9018 x23: x23 x24: x24
STACK CFI 9048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9050 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 906c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 90b8 x23: x23 x24: x24
STACK CFI 90c0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 90cc x23: x23 x24: x24
STACK CFI 90dc x23: .cfa -16 + ^ x24: .cfa -8 + ^
