MODULE Linux arm64 0A99563E86063F0B56BE8D61695BC6950 libunwind-ptrace.so.0
INFO CODE_ID 3E56990A06860B3F56BE8D61695BC695C37D96D7
PUBLIC 10e0 0 _UPT_get_dyn_info_list_addr
PUBLIC 1570 0 _UPT_resume
PUBLIC 15a0 0 _UPT_access_fpreg
PUBLIC 1690 0 _UPT_access_mem
PUBLIC 1730 0 _UPT_access_reg
PUBLIC 1800 0 _UPT_put_unwind_info
PUBLIC 1a00 0 _UPT_find_proc_info
PUBLIC 1fa4 0 _UPT_get_proc_name
PUBLIC 1fc0 0 _UPT_create
PUBLIC 2020 0 _UPT_destroy
STACK CFI INIT 1010 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1040 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1080 48 .cfa: sp 0 + .ra: x30
STACK CFI 1084 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 108c x19: .cfa -16 + ^
STACK CFI 10c4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 10d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 10e0 14 .cfa: sp 0 + .ra: x30
STACK CFI 10e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 10f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 10f4 164 .cfa: sp 0 + .ra: x30
STACK CFI 10f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1100 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1108 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1118 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 113c x21: x21 x22: x22
STACK CFI 114c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 1150 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 1160 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1170 x21: x21 x22: x22
STACK CFI 117c x23: x23 x24: x24
STACK CFI 1184 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 1188 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 1214 x21: x21 x22: x22
STACK CFI 1218 x23: x23 x24: x24
STACK CFI 1234 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 1238 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 1248 x21: x21 x22: x22
STACK CFI 124c x23: x23 x24: x24
STACK CFI 1254 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI INIT 1260 308 .cfa: sp 0 + .ra: x30
STACK CFI 1264 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 1268 .cfa: x29 224 +
STACK CFI 126c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 1274 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 1280 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 128c x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 12f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 12f4 .cfa: x29 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI INIT 1570 28 .cfa: sp 0 + .ra: x30
STACK CFI 1578 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1594 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 15a0 f0 .cfa: sp 0 + .ra: x30
STACK CFI 15a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 15ac x23: .cfa -16 + ^
STACK CFI 15bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 15c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 162c x19: x19 x20: x20
STACK CFI 1634 x21: x21 x22: x22
STACK CFI 163c .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 1640 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 167c x19: x19 x20: x20
STACK CFI 1680 x21: x21 x22: x22
STACK CFI 168c .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI INIT 1690 a0 .cfa: sp 0 + .ra: x30
STACK CFI 1698 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 16a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 16b0 x21: .cfa -32 + ^
STACK CFI 16f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 16fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 1724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1730 cc .cfa: sp 0 + .ra: x30
STACK CFI 1734 .cfa: sp 352 + .ra: .cfa -344 + ^ x29: .cfa -352 + ^
STACK CFI 173c x21: .cfa -320 + ^ x22: .cfa -312 + ^
STACK CFI 1758 x23: .cfa -304 + ^ x24: .cfa -296 + ^
STACK CFI 1770 x19: .cfa -336 + ^ x20: .cfa -328 + ^
STACK CFI 17a0 x19: x19 x20: x20
STACK CFI 17a8 x23: x23 x24: x24
STACK CFI 17b0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 17b4 .cfa: sp 352 + .ra: .cfa -344 + ^ x19: .cfa -336 + ^ x20: .cfa -328 + ^ x21: .cfa -320 + ^ x22: .cfa -312 + ^ x23: .cfa -304 + ^ x24: .cfa -296 + ^ x29: .cfa -352 + ^
STACK CFI 17dc x19: x19 x20: x20
STACK CFI 17e4 x23: x23 x24: x24
STACK CFI INIT 1800 30 .cfa: sp 0 + .ra: x30
STACK CFI 180c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1814 x19: .cfa -16 + ^
STACK CFI 1828 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1830 1cc .cfa: sp 0 + .ra: x30
STACK CFI 1838 .cfa: sp 4176 +
STACK CFI 183c .ra: .cfa -4168 + ^ x29: .cfa -4176 + ^
STACK CFI 1844 x19: .cfa -4160 + ^ x20: .cfa -4152 + ^
STACK CFI 1854 x21: .cfa -4144 + ^ x22: .cfa -4136 + ^
STACK CFI 185c x23: .cfa -4128 + ^ x24: .cfa -4120 + ^
STACK CFI 1974 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1978 .cfa: sp 4176 + .ra: .cfa -4168 + ^ x19: .cfa -4160 + ^ x20: .cfa -4152 + ^ x21: .cfa -4144 + ^ x22: .cfa -4136 + ^ x23: .cfa -4128 + ^ x24: .cfa -4120 + ^ x29: .cfa -4176 + ^
STACK CFI INIT 1a00 c4 .cfa: sp 0 + .ra: x30
STACK CFI 1a04 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1a0c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1a18 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1a30 x23: .cfa -16 + ^
STACK CFI 1a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1a84 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1ab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 1abc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1ac4 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 1ac8 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 1ae0 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 1aec x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 1b0c x25: x25 x26: x26
STACK CFI 1b10 x27: x27 x28: x28
STACK CFI 1b14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1b18 .cfa: sp 128 + .ra: .cfa -120 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI 1b28 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 1b2c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 1b30 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 1b44 x19: x19 x20: x20
STACK CFI 1b48 x21: x21 x22: x22
STACK CFI 1b4c x23: x23 x24: x24
STACK CFI 1b50 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1b58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1b5c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 1cc0 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 1cc8 .cfa: sp 4368 +
STACK CFI 1cd0 .ra: .cfa -4344 + ^ x29: .cfa -4352 + ^
STACK CFI 1cd8 x19: .cfa -4336 + ^ x20: .cfa -4328 + ^
STACK CFI 1ce4 x23: .cfa -4304 + ^ x24: .cfa -4296 + ^
STACK CFI 1cf0 x25: .cfa -4288 + ^ x26: .cfa -4280 + ^
STACK CFI 1d04 x21: .cfa -4320 + ^ x22: .cfa -4312 + ^
STACK CFI 1d18 x27: .cfa -4272 + ^ x28: .cfa -4264 + ^
STACK CFI 1e84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1e88 .cfa: sp 4368 + .ra: .cfa -4344 + ^ x19: .cfa -4336 + ^ x20: .cfa -4328 + ^ x21: .cfa -4320 + ^ x22: .cfa -4312 + ^ x23: .cfa -4304 + ^ x24: .cfa -4296 + ^ x25: .cfa -4288 + ^ x26: .cfa -4280 + ^ x27: .cfa -4272 + ^ x28: .cfa -4264 + ^ x29: .cfa -4352 + ^
STACK CFI INIT 1fa4 14 .cfa: sp 0 + .ra: x30
STACK CFI 1fa8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1fb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1fc0 5c .cfa: sp 0 + .ra: x30
STACK CFI 1fc4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1fcc x19: .cfa -16 + ^
STACK CFI 2018 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2020 30 .cfa: sp 0 + .ra: x30
STACK CFI 2024 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 202c x19: .cfa -16 + ^
STACK CFI 204c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
