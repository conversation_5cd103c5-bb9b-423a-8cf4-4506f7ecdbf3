MODULE Linux arm64 F1CED9372E6AADD7ED17FA917BFE970E0 libcrack.so.2
INFO CODE_ID 37D9CEF16A2ED7ADED17FA917BFE970EFFFD0ACB
PUBLIC 3820 0 GetDefaultCracklibDict
PUBLIC 3840 0 PWOpen
PUBLIC 3d10 0 PutPW
PUBLIC 3f20 0 PWClose
PUBLIC 4084 0 GetPW
PUBLIC 4284 0 FindPW
PUBLIC 4370 0 Suffix
PUBLIC 43e0 0 Reverse
PUBLIC 4450 0 Uppercase
PUBLIC 4510 0 Lowercase
PUBLIC 45d0 0 Capitalise
PUBLIC 4690 0 Pluralise
PUBLIC 4800 0 Substitute
PUBLIC 4880 0 Purge
PUBLIC 4900 0 MatchClass
PUBLIC 4b10 0 PolyStrchr
PUBLIC 4b74 0 PolySubst
PUBLIC 4c04 0 PolyPurge
PUBLIC 4cb0 0 Char2Int
PUBLIC 4d20 0 Mangle
PUBLIC 55f0 0 GTry
PUBLIC 56f0 0 FascistGecosUser
PUBLIC 5ae0 0 FascistGecos
PUBLIC 5c14 0 PMatch
PUBLIC 5c84 0 Chop
PUBLIC 5cc4 0 Trim
PUBLIC 5d40 0 FascistLookUser
PUBLIC 6130 0 FascistLook
PUBLIC 6150 0 __DEBIAN_SPECIFIC__SafeFascistCheck
PUBLIC 6244 0 FascistCheckUser
PUBLIC 6340 0 FascistCheck
PUBLIC 6360 0 Clone
STACK CFI INIT 3750 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3780 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 37c0 48 .cfa: sp 0 + .ra: x30
STACK CFI 37c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 37cc x19: .cfa -16 + ^
STACK CFI 3804 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3810 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3820 20 .cfa: sp 0 + .ra: x30
STACK CFI 3828 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3838 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3840 4cc .cfa: sp 0 + .ra: x30
STACK CFI 3848 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3858 .cfa: sp 3168 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 38a4 x23: .cfa -32 + ^
STACK CFI 38ac x24: .cfa -24 + ^
STACK CFI 38b4 x25: .cfa -16 + ^
STACK CFI 3ac4 x23: x23
STACK CFI 3acc x24: x24
STACK CFI 3ad0 x25: x25
STACK CFI 3af0 .cfa: sp 80 +
STACK CFI 3afc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3b04 .cfa: sp 3168 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 3bac x23: x23
STACK CFI 3bb4 x24: x24
STACK CFI 3bb8 x25: x25
STACK CFI 3be4 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 3c2c x23: x23
STACK CFI 3c34 x24: x24
STACK CFI 3c38 x25: x25
STACK CFI 3c3c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 3cbc x23: x23 x24: x24 x25: x25
STACK CFI 3cc0 x23: .cfa -32 + ^
STACK CFI 3cc4 x24: .cfa -24 + ^
STACK CFI 3cc8 x25: .cfa -16 + ^
STACK CFI 3ce0 x23: x23
STACK CFI 3ce8 x24: x24
STACK CFI 3cec x25: x25
STACK CFI 3cf0 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 3cfc x23: x23
STACK CFI 3d04 x24: x24
STACK CFI 3d08 x25: x25
STACK CFI INIT 3d10 20c .cfa: sp 0 + .ra: x30
STACK CFI 3d18 .cfa: sp 80 +
STACK CFI 3d1c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 3d24 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 3d48 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3dc0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3eac x23: x23 x24: x24
STACK CFI 3eb4 x19: x19 x20: x20
STACK CFI 3ee0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 3ee8 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3ef8 x23: x23 x24: x24
STACK CFI 3f00 x19: x19 x20: x20
STACK CFI 3f14 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 3f18 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 3f20 164 .cfa: sp 0 + .ra: x30
STACK CFI 3f28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f48 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3ff8 x19: x19 x20: x20
STACK CFI 3ffc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4004 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 402c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4050 x19: x19 x20: x20
STACK CFI 4058 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 407c x19: x19 x20: x20
STACK CFI INIT 4084 200 .cfa: sp 0 + .ra: x30
STACK CFI 408c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 409c .cfa: sp 3216 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 41e8 .cfa: sp 48 +
STACK CFI 41f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 41fc .cfa: sp 3216 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4284 ec .cfa: sp 0 + .ra: x30
STACK CFI 428c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4294 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 42a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 434c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4354 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4370 70 .cfa: sp 0 + .ra: x30
STACK CFI 4378 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4380 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4388 x21: .cfa -16 + ^
STACK CFI 43c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 43c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 43d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 43e0 68 .cfa: sp 0 + .ra: x30
STACK CFI 43e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 43f0 x19: .cfa -16 + ^
STACK CFI 4440 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4450 b8 .cfa: sp 0 + .ra: x30
STACK CFI 4458 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4464 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4480 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 44bc x23: x23 x24: x24
STACK CFI 44d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 44dc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4510 b8 .cfa: sp 0 + .ra: x30
STACK CFI 4518 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4524 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4540 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 457c x23: x23 x24: x24
STACK CFI 4594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 459c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 45c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 45d0 bc .cfa: sp 0 + .ra: x30
STACK CFI 45d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 45e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 45e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 45f4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4684 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 4690 16c .cfa: sp 0 + .ra: x30
STACK CFI 4698 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 46a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 46ac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 472c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4770 x23: .cfa -16 + ^
STACK CFI 47ac x23: x23
STACK CFI 47b0 x23: .cfa -16 + ^
STACK CFI 47b4 x23: x23
STACK CFI 47b8 x23: .cfa -16 + ^
STACK CFI 47f8 x23: x23
STACK CFI INIT 4800 78 .cfa: sp 0 + .ra: x30
STACK CFI 4808 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 484c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4854 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4870 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4880 80 .cfa: sp 0 + .ra: x30
STACK CFI 4888 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 48d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 48dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 48f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4900 20c .cfa: sp 0 + .ra: x30
STACK CFI 4908 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4910 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4930 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4948 x21: x21 x22: x22
STACK CFI 4958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4960 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4988 x21: x21 x22: x22
STACK CFI 499c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 49a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4b10 64 .cfa: sp 0 + .ra: x30
STACK CFI 4b18 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4b20 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4b54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4b5c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4b6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4b74 90 .cfa: sp 0 + .ra: x30
STACK CFI 4b7c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4b88 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4b90 x23: .cfa -16 + ^
STACK CFI 4bec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4bf4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4c04 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4c0c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4c18 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4c74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4c7c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 4ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 4cb0 70 .cfa: sp 0 + .ra: x30
STACK CFI 4cb8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4cc0 x19: .cfa -16 + ^
STACK CFI 4cf0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4cf8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4d04 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4d0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4d18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4d20 8cc .cfa: sp 0 + .ra: x30
STACK CFI 4d28 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4d38 .cfa: sp 2144 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4d98 x23: .cfa -32 + ^
STACK CFI 4d9c x24: .cfa -24 + ^
STACK CFI 4e00 x23: x23
STACK CFI 4e04 x24: x24
STACK CFI 4e28 .cfa: sp 80 +
STACK CFI 4e34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4e3c .cfa: sp 2144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 4e74 x23: x23
STACK CFI 4e78 x24: x24
STACK CFI 4e8c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4f3c x25: .cfa -16 + ^
STACK CFI 4f98 x25: x25
STACK CFI 5530 x23: x23
STACK CFI 5534 x24: x24
STACK CFI 5538 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5570 x25: .cfa -16 + ^
STACK CFI 5584 x25: x25
STACK CFI 55dc x23: x23 x24: x24
STACK CFI 55e0 x23: .cfa -32 + ^
STACK CFI 55e4 x24: .cfa -24 + ^
STACK CFI 55e8 x25: .cfa -16 + ^
STACK CFI INIT 55f0 fc .cfa: sp 0 + .ra: x30
STACK CFI 55f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5600 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5610 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 561c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 56c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 56cc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 56e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 56f0 3ec .cfa: sp 0 + .ra: x30
STACK CFI 56f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 570c .cfa: sp 11376 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5774 x23: .cfa -48 + ^
STACK CFI 5778 x24: .cfa -40 + ^
STACK CFI 57b4 x27: .cfa -16 + ^
STACK CFI 57bc x25: .cfa -32 + ^
STACK CFI 57c0 x26: .cfa -24 + ^
STACK CFI 57c8 x28: .cfa -8 + ^
STACK CFI 5890 x23: x23
STACK CFI 5894 x24: x24
STACK CFI 5898 x25: x25
STACK CFI 589c x26: x26
STACK CFI 58a0 x27: x27
STACK CFI 58a4 x28: x28
STACK CFI 58c8 .cfa: sp 96 +
STACK CFI 58d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 58dc .cfa: sp 11376 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 5a44 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5a60 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5a74 x25: x25
STACK CFI 5a78 x26: x26
STACK CFI 5a7c x27: x27
STACK CFI 5a80 x28: x28
STACK CFI 5a88 x23: x23
STACK CFI 5a8c x24: x24
STACK CFI 5a90 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5ac0 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 5ac4 x23: .cfa -48 + ^
STACK CFI 5ac8 x24: .cfa -40 + ^
STACK CFI 5acc x25: .cfa -32 + ^
STACK CFI 5ad0 x26: .cfa -24 + ^
STACK CFI 5ad4 x27: .cfa -16 + ^
STACK CFI 5ad8 x28: .cfa -8 + ^
STACK CFI INIT 5ae0 134 .cfa: sp 0 + .ra: x30
STACK CFI 5ae8 .cfa: sp 128 +
STACK CFI 5af4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5b00 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5b08 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5bd0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5bd8 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5c14 70 .cfa: sp 0 + .ra: x30
STACK CFI 5c1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5c24 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5c68 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5c78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5c84 40 .cfa: sp 0 + .ra: x30
STACK CFI 5c8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5cbc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5cc4 7c .cfa: sp 0 + .ra: x30
STACK CFI 5ccc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5cd4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5ce0 x21: .cfa -16 + ^
STACK CFI 5d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5d40 3f0 .cfa: sp 0 + .ra: x30
STACK CFI 5d48 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5d60 .cfa: sp 2160 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5dc4 x27: .cfa -16 + ^
STACK CFI 5f3c x27: x27
STACK CFI 5f40 x27: .cfa -16 + ^
STACK CFI 5f5c x27: x27
STACK CFI 5f7c .cfa: sp 96 +
STACK CFI 5f90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5f98 .cfa: sp 2160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 5fb4 x27: .cfa -16 + ^
STACK CFI 6050 x27: x27
STACK CFI 6070 x27: .cfa -16 + ^
STACK CFI 608c x27: x27
STACK CFI 6090 x27: .cfa -16 + ^
STACK CFI 60ac x27: x27
STACK CFI 60b0 x27: .cfa -16 + ^
STACK CFI 60e4 x27: x27
STACK CFI 60e8 x27: .cfa -16 + ^
STACK CFI 6104 x27: x27
STACK CFI 6108 x27: .cfa -16 + ^
STACK CFI 6124 x27: x27
STACK CFI 612c x27: .cfa -16 + ^
STACK CFI INIT 6130 20 .cfa: sp 0 + .ra: x30
STACK CFI 6138 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6144 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6150 f4 .cfa: sp 0 + .ra: x30
STACK CFI 6158 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6168 .cfa: sp 1088 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6220 .cfa: sp 48 +
STACK CFI 6230 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6238 .cfa: sp 1088 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6244 f8 .cfa: sp 0 + .ra: x30
STACK CFI 624c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 625c .cfa: sp 1088 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6300 .cfa: sp 48 +
STACK CFI 6310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6318 .cfa: sp 1088 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6340 20 .cfa: sp 0 + .ra: x30
STACK CFI 6348 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6354 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6360 58 .cfa: sp 0 + .ra: x30
STACK CFI 6368 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6374 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 63b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
