MODULE Linux arm64 A319CFF203FACC528EBF24C10A8A59BB0 libboost_type_erasure.so.1.77.0
INFO CODE_ID F2CF19A3FA0352CC8EBF24C10A8A59BB
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 50e0 24 0 init_have_lse_atomics
50e0 4 45 0
50e4 4 46 0
50e8 4 45 0
50ec 4 46 0
50f0 4 47 0
50f4 4 47 0
50f8 4 48 0
50fc 4 47 0
5100 4 48 0
PUBLIC 48a8 0 _init
PUBLIC 4ce0 0 void boost::throw_exception<boost::thread_resource_error>(boost::thread_resource_error const&)
PUBLIC 4d70 0 void boost::throw_exception<boost::lock_error>(boost::lock_error const&)
PUBLIC 4e00 0 boost::detail::interruption_checker::unlock_if_locked() [clone .part.0]
PUBLIC 4f54 0 void boost::throw_exception<boost::condition_error>(boost::condition_error const&)
PUBLIC 4fe4 0 boost::wrapexcept<boost::condition_error>::rethrow() const
PUBLIC 5038 0 boost::wrapexcept<boost::lock_error>::rethrow() const
PUBLIC 508c 0 boost::wrapexcept<boost::thread_resource_error>::rethrow() const
PUBLIC 5104 0 call_weak_fn
PUBLIC 5120 0 deregister_tm_clones
PUBLIC 5150 0 register_tm_clones
PUBLIC 5190 0 __do_global_dtors_aux
PUBLIC 51e0 0 frame_dummy
PUBLIC 51f0 0 std::_Rb_tree<std::vector<std::type_info const*, std::allocator<std::type_info const*> >, std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()>, std::_Select1st<std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()> >, std::less<std::vector<std::type_info const*, std::allocator<std::type_info const*> > >, std::allocator<std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()> > >::_M_erase(std::_Rb_tree_node<std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()> >*) [clone .isra.0]
PUBLIC 54d0 0 (anonymous namespace)::data_type::~data_type()
PUBLIC 5600 0 (anonymous namespace)::get_data()
PUBLIC 5bc0 0 boost::type_erasure::detail::lookup_function_impl(std::vector<std::type_info const*, std::allocator<std::type_info const*> > const&)
PUBLIC 6410 0 boost::type_erasure::detail::register_function_impl(std::vector<std::type_info const*, std::allocator<std::type_info const*> > const&, void (*)())
PUBLIC 6cc0 0 boost::system::error_category::failed(int) const
PUBLIC 6cd0 0 boost::system::detail::generic_error_category::name() const
PUBLIC 6ce0 0 boost::system::detail::system_error_category::name() const
PUBLIC 6cf0 0 boost::system::detail::system_error_category::default_error_condition(int) const
PUBLIC 6d10 0 boost::system::detail::interop_error_category::name() const
PUBLIC 6d20 0 boost::system::error_category::equivalent(boost::system::error_code const&, int) const
PUBLIC 6e00 0 boost::system::detail::std_category::name() const
PUBLIC 6e20 0 boost::system::detail::std_category::message[abi:cxx11](int) const
PUBLIC 6e90 0 boost::shared_mutex::state_data::can_lock() const
PUBLIC 6eb0 0 boost::shared_mutex::state_data::can_lock_shared() const
PUBLIC 6ed0 0 boost::system::detail::system_error_category::message(int, char*, unsigned long) const
PUBLIC 6ee0 0 boost::system::detail::generic_error_category::message(int, char*, unsigned long) const
PUBLIC 6ef0 0 boost::type_erasure::bad_any_cast::~bad_any_cast()
PUBLIC 6f00 0 boost::type_erasure::bad_any_cast::~bad_any_cast()
PUBLIC 6f40 0 boost::system::detail::std_category::~std_category()
PUBLIC 6f60 0 boost::system::detail::std_category::~std_category()
PUBLIC 6fa0 0 boost::system::error_category::equivalent(int, boost::system::error_condition const&) const
PUBLIC 7030 0 boost::system::detail::generic_error_category::message[abi:cxx11](int) const
PUBLIC 7150 0 boost::system::detail::system_error_category::message[abi:cxx11](int) const
PUBLIC 7270 0 boost::system::error_category::default_error_condition(int) const
PUBLIC 7310 0 boost::system::system_error::~system_error()
PUBLIC 7360 0 boost::thread_exception::~thread_exception()
PUBLIC 73b0 0 boost::condition_error::~condition_error()
PUBLIC 7400 0 boost::thread_resource_error::~thread_resource_error()
PUBLIC 7450 0 boost::lock_error::~lock_error()
PUBLIC 74a0 0 boost::thread_resource_error::~thread_resource_error()
PUBLIC 7500 0 boost::lock_error::~lock_error()
PUBLIC 7560 0 boost::condition_error::~condition_error()
PUBLIC 75c0 0 boost::system::system_error::~system_error()
PUBLIC 7620 0 boost::thread_exception::~thread_exception()
PUBLIC 7680 0 boost::wrapexcept<boost::condition_error>::clone() const
PUBLIC 79b0 0 non-virtual thunk to boost::wrapexcept<boost::lock_error>::~wrapexcept()
PUBLIC 7a40 0 non-virtual thunk to boost::wrapexcept<boost::lock_error>::~wrapexcept()
PUBLIC 7ad0 0 boost::wrapexcept<boost::lock_error>::~wrapexcept()
PUBLIC 7b60 0 non-virtual thunk to boost::wrapexcept<boost::thread_resource_error>::~wrapexcept()
PUBLIC 7bf0 0 non-virtual thunk to boost::wrapexcept<boost::thread_resource_error>::~wrapexcept()
PUBLIC 7c80 0 boost::wrapexcept<boost::thread_resource_error>::~wrapexcept()
PUBLIC 7d10 0 non-virtual thunk to boost::wrapexcept<boost::condition_error>::~wrapexcept()
PUBLIC 7da0 0 non-virtual thunk to boost::wrapexcept<boost::condition_error>::~wrapexcept()
PUBLIC 7e30 0 boost::wrapexcept<boost::condition_error>::~wrapexcept()
PUBLIC 7ec0 0 boost::wrapexcept<boost::thread_resource_error>::~wrapexcept()
PUBLIC 7f50 0 non-virtual thunk to boost::wrapexcept<boost::thread_resource_error>::~wrapexcept()
PUBLIC 7ff0 0 non-virtual thunk to boost::wrapexcept<boost::thread_resource_error>::~wrapexcept()
PUBLIC 8090 0 boost::wrapexcept<boost::condition_error>::~wrapexcept()
PUBLIC 8120 0 non-virtual thunk to boost::wrapexcept<boost::condition_error>::~wrapexcept()
PUBLIC 81c0 0 non-virtual thunk to boost::wrapexcept<boost::condition_error>::~wrapexcept()
PUBLIC 8260 0 boost::wrapexcept<boost::lock_error>::~wrapexcept()
PUBLIC 82f0 0 non-virtual thunk to boost::wrapexcept<boost::lock_error>::~wrapexcept()
PUBLIC 8390 0 non-virtual thunk to boost::wrapexcept<boost::lock_error>::~wrapexcept()
PUBLIC 8430 0 boost::system::detail::std_category::default_error_condition(int) const
PUBLIC 85f0 0 boost::wrapexcept<boost::lock_error>::clone() const
PUBLIC 8920 0 boost::wrapexcept<boost::thread_resource_error>::clone() const
PUBLIC 8c50 0 boost::system::detail::std_category::equivalent(std::error_code const&, int) const
PUBLIC 9180 0 boost::system::detail::std_category::equivalent(int, std::error_condition const&) const
PUBLIC 9760 0 boost::system::detail::snprintf(char*, unsigned long, char const*, ...)
PUBLIC 9810 0 boost::system::detail::interop_error_category::message(int, char*, unsigned long) const
PUBLIC 9850 0 boost::system::error_category::message(int, char*, unsigned long) const
PUBLIC 9950 0 boost::system::detail::interop_error_category::message[abi:cxx11](int) const
PUBLIC 9ab0 0 boost::thread_exception::thread_exception(int, char const*)
PUBLIC 9b20 0 boost::condition_variable::~condition_variable()
PUBLIC 9b70 0 boost::system::system_error::system_error(boost::system::system_error const&)
PUBLIC 9ca0 0 boost::shared_mutex::unlock()
PUBLIC 9e20 0 boost::shared_mutex::unlock_shared()
PUBLIC 9ff0 0 boost::unique_lock<boost::mutex>::lock()
PUBLIC a1b0 0 std::_Rb_tree<std::vector<std::type_info const*, std::allocator<std::type_info const*> >, std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()>, std::_Select1st<std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()> >, std::less<std::vector<std::type_info const*, std::allocator<std::type_info const*> > >, std::allocator<std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()> > >::_M_get_insert_unique_pos(std::vector<std::type_info const*, std::allocator<std::type_info const*> > const&)
PUBLIC a320 0 std::_Rb_tree<std::vector<std::type_info const*, std::allocator<std::type_info const*> >, std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()>, std::_Select1st<std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()> >, std::less<std::vector<std::type_info const*, std::allocator<std::type_info const*> > >, std::allocator<std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()> > >::_M_get_insert_hint_unique_pos(std::_Rb_tree_const_iterator<std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()> >, std::vector<std::type_info const*, std::allocator<std::type_info const*> > const&)
PUBLIC a5f0 0 std::_Rb_tree_iterator<std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()> > std::_Rb_tree<std::vector<std::type_info const*, std::allocator<std::type_info const*> >, std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()>, std::_Select1st<std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()> >, std::less<std::vector<std::type_info const*, std::allocator<std::type_info const*> > >, std::allocator<std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()> > >::_M_emplace_hint_unique<std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> >, void (*)()> >(std::_Rb_tree_const_iterator<std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> > const, void (*)()> >, std::pair<std::vector<std::type_info const*, std::allocator<std::type_info const*> >, void (*)()>&&)
PUBLIC a740 0 boost::wrapexcept<boost::condition_error>::wrapexcept(boost::wrapexcept<boost::condition_error> const&)
PUBLIC a910 0 boost::wrapexcept<boost::lock_error>::wrapexcept(boost::wrapexcept<boost::lock_error> const&)
PUBLIC aae0 0 boost::wrapexcept<boost::thread_resource_error>::wrapexcept(boost::wrapexcept<boost::thread_resource_error> const&)
PUBLIC acb0 0 boost::system::system_error::what() const
PUBLIC aee0 0 __aarch64_cas8_acq_rel
PUBLIC af14 0 _fini
STACK CFI INIT 5120 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5150 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 5190 48 .cfa: sp 0 + .ra: x30
STACK CFI 5194 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 519c x19: .cfa -16 + ^
STACK CFI 51d4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 51e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6cc0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6cd0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ce0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6cf0 20 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d10 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d20 d4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e00 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e20 64 .cfa: sp 0 + .ra: x30
STACK CFI 6e24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6e30 x19: .cfa -32 + ^
STACK CFI 6e7c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6e80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6e90 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6eb0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ed0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ee0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ef0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f00 34 .cfa: sp 0 + .ra: x30
STACK CFI 6f04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f14 x19: .cfa -16 + ^
STACK CFI 6f30 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6f40 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f60 38 .cfa: sp 0 + .ra: x30
STACK CFI 6f64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f74 x19: .cfa -16 + ^
STACK CFI 6f94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6fa0 88 .cfa: sp 0 + .ra: x30
STACK CFI 6fa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6fac x19: .cfa -16 + ^
STACK CFI 6fd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6fd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 700c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7010 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7024 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7030 114 .cfa: sp 0 + .ra: x30
STACK CFI 7034 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 7048 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 7050 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 70d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 70d4 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 7150 114 .cfa: sp 0 + .ra: x30
STACK CFI 7154 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 7168 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 7170 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 71f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 71f4 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x29: .cfa -192 + ^
STACK CFI INIT 7270 94 .cfa: sp 0 + .ra: x30
STACK CFI 7274 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7288 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 72e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 72e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7300 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7310 50 .cfa: sp 0 + .ra: x30
STACK CFI 7314 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 732c x19: .cfa -16 + ^
STACK CFI 735c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7360 50 .cfa: sp 0 + .ra: x30
STACK CFI 7364 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 737c x19: .cfa -16 + ^
STACK CFI 73ac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 73b0 50 .cfa: sp 0 + .ra: x30
STACK CFI 73b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 73cc x19: .cfa -16 + ^
STACK CFI 73fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7400 50 .cfa: sp 0 + .ra: x30
STACK CFI 7404 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 741c x19: .cfa -16 + ^
STACK CFI 744c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7450 50 .cfa: sp 0 + .ra: x30
STACK CFI 7454 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 746c x19: .cfa -16 + ^
STACK CFI 749c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 74a0 5c .cfa: sp 0 + .ra: x30
STACK CFI 74a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 74bc x19: .cfa -16 + ^
STACK CFI 74f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7500 5c .cfa: sp 0 + .ra: x30
STACK CFI 7504 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 751c x19: .cfa -16 + ^
STACK CFI 7558 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7560 5c .cfa: sp 0 + .ra: x30
STACK CFI 7564 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 757c x19: .cfa -16 + ^
STACK CFI 75b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 75c0 5c .cfa: sp 0 + .ra: x30
STACK CFI 75c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 75dc x19: .cfa -16 + ^
STACK CFI 7618 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7620 5c .cfa: sp 0 + .ra: x30
STACK CFI 7624 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 763c x19: .cfa -16 + ^
STACK CFI 7678 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7680 328 .cfa: sp 0 + .ra: x30
STACK CFI 7684 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7694 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 76a8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 7858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 785c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 79b0 88 .cfa: sp 0 + .ra: x30
STACK CFI 79b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 79c4 x19: .cfa -16 + ^
STACK CFI 7a34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7b60 84 .cfa: sp 0 + .ra: x30
STACK CFI 7b64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7b74 x19: .cfa -16 + ^
STACK CFI 7be0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7d10 84 .cfa: sp 0 + .ra: x30
STACK CFI 7d14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7d24 x19: .cfa -16 + ^
STACK CFI 7d90 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7da0 88 .cfa: sp 0 + .ra: x30
STACK CFI 7da4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7db4 x19: .cfa -16 + ^
STACK CFI 7e24 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7bf0 88 .cfa: sp 0 + .ra: x30
STACK CFI 7bf4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7c04 x19: .cfa -16 + ^
STACK CFI 7c74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7a40 84 .cfa: sp 0 + .ra: x30
STACK CFI 7a44 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7a54 x19: .cfa -16 + ^
STACK CFI 7ac0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7e30 84 .cfa: sp 0 + .ra: x30
STACK CFI 7e34 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7e44 x19: .cfa -16 + ^
STACK CFI 7eb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7c80 84 .cfa: sp 0 + .ra: x30
STACK CFI 7c84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7c94 x19: .cfa -16 + ^
STACK CFI 7d00 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7ad0 84 .cfa: sp 0 + .ra: x30
STACK CFI 7ad4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ae4 x19: .cfa -16 + ^
STACK CFI 7b50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7ec0 90 .cfa: sp 0 + .ra: x30
STACK CFI 7ec4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ed4 x19: .cfa -16 + ^
STACK CFI 7f4c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8090 90 .cfa: sp 0 + .ra: x30
STACK CFI 8094 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 80a4 x19: .cfa -16 + ^
STACK CFI 811c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8260 90 .cfa: sp 0 + .ra: x30
STACK CFI 8264 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8274 x19: .cfa -16 + ^
STACK CFI 82ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8120 98 .cfa: sp 0 + .ra: x30
STACK CFI 8124 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8134 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 81b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 81c0 9c .cfa: sp 0 + .ra: x30
STACK CFI 81c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 81d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8258 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 82f0 98 .cfa: sp 0 + .ra: x30
STACK CFI 82f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8304 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7f50 98 .cfa: sp 0 + .ra: x30
STACK CFI 7f54 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7f64 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7fe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7ff0 9c .cfa: sp 0 + .ra: x30
STACK CFI 7ff4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8004 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8390 9c .cfa: sp 0 + .ra: x30
STACK CFI 8394 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 83a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 51f0 2d4 .cfa: sp 0 + .ra: x30
STACK CFI 51f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5200 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 520c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5218 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 521c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5468 x21: x21 x22: x22
STACK CFI 546c x27: x27 x28: x28
STACK CFI 54bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT 54d0 130 .cfa: sp 0 + .ra: x30
STACK CFI 54d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 54dc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 55fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8430 1bc .cfa: sp 0 + .ra: x30
STACK CFI 8434 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 844c x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 84b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 84b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 8500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8504 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 852c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 8530 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 85f0 328 .cfa: sp 0 + .ra: x30
STACK CFI 85f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8604 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8618 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 87c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 87cc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8920 328 .cfa: sp 0 + .ra: x30
STACK CFI 8924 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8934 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8948 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^
STACK CFI 8af8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 8afc .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8c50 530 .cfa: sp 0 + .ra: x30
STACK CFI 8c54 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 8c5c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 8c74 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 8c94 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 8dc0 x25: x25 x26: x26
STACK CFI 8dc8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 8e1c x25: x25 x26: x26
STACK CFI 8e24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8e28 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 8ea0 x25: x25 x26: x26
STACK CFI 8ed8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8edc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x29: .cfa -112 + ^
STACK CFI 8ef0 x25: x25 x26: x26
STACK CFI 8ef4 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 8f80 x25: x25 x26: x26
STACK CFI 902c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 9040 x25: x25 x26: x26
STACK CFI 9054 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 9108 x25: x25 x26: x26
STACK CFI 911c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 9130 x25: x25 x26: x26
STACK CFI 914c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 9160 x25: x25 x26: x26
STACK CFI 9164 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI INIT 9180 5d4 .cfa: sp 0 + .ra: x30
STACK CFI 9184 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 918c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 91a4 x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 9464 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9468 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 9528 x25: .cfa -48 + ^
STACK CFI 955c x25: x25
STACK CFI 971c x25: .cfa -48 + ^
STACK CFI 9738 x25: x25
STACK CFI INIT 9760 a4 .cfa: sp 0 + .ra: x30
STACK CFI 9764 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 97fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9800 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI INIT 9810 3c .cfa: sp 0 + .ra: x30
STACK CFI 9814 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 982c x19: .cfa -16 + ^
STACK CFI 9848 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9850 f4 .cfa: sp 0 + .ra: x30
STACK CFI 9854 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9864 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9894 x21: .cfa -64 + ^
STACK CFI 98d8 x21: x21
STACK CFI 9900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9904 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x29: .cfa -96 + ^
STACK CFI 9908 x21: x21
STACK CFI 9918 x21: .cfa -64 + ^
STACK CFI 9940 x21: x21
STACK CFI INIT 9950 158 .cfa: sp 0 + .ra: x30
STACK CFI 9954 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9968 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 9970 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 9a0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9a10 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT 9ab0 64 .cfa: sp 0 + .ra: x30
STACK CFI 9ab4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9abc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9b20 44 .cfa: sp 0 + .ra: x30
STACK CFI 9b24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9b2c x19: .cfa -16 + ^
STACK CFI 9b60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9b70 12c .cfa: sp 0 + .ra: x30
STACK CFI 9b74 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9b84 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9b90 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9c24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9c28 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4ce0 90 .cfa: sp 0 + .ra: x30
STACK CFI 4ce4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4cec x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 5600 5b4 .cfa: sp 0 + .ra: x30
STACK CFI 5604 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 5614 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 565c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5660 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 5674 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 567c x23: .cfa -96 + ^
STACK CFI 58b0 x21: x21 x22: x22
STACK CFI 58b4 x23: x23
STACK CFI 58b8 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^
STACK CFI 593c x21: x21 x22: x22 x23: x23
STACK CFI 5940 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 5944 x23: .cfa -96 + ^
STACK CFI INIT 4d70 90 .cfa: sp 0 + .ra: x30
STACK CFI 4d74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4d7c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 9ca0 174 .cfa: sp 0 + .ra: x30
STACK CFI 9ca4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9cb4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 9cbc x21: .cfa -96 + ^
STACK CFI 9d80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9d84 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4e00 154 .cfa: sp 0 + .ra: x30
STACK CFI 4e04 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 4e0c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 4e24 x21: .cfa -96 + ^
STACK CFI 4f50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 9e20 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 9e24 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9e34 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 9e3c x21: .cfa -96 + ^
STACK CFI 9f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9f1c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x29: .cfa -128 + ^
STACK CFI INIT 4f54 90 .cfa: sp 0 + .ra: x30
STACK CFI 4f58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4f60 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI INIT 9ff0 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 9ff4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 9ffc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI a068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a06c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI a084 x21: .cfa -96 + ^
STACK CFI a0bc x21: x21
STACK CFI a0c0 x21: .cfa -96 + ^
STACK CFI a100 x21: x21
STACK CFI a118 x21: .cfa -96 + ^
STACK CFI a150 x21: x21
STACK CFI a168 x21: .cfa -96 + ^
STACK CFI INIT 5bc0 844 .cfa: sp 0 + .ra: x30
STACK CFI 5bc4 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI 5be4 x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 6050 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6054 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^ x29: .cfa -240 + ^
STACK CFI INIT a1b0 168 .cfa: sp 0 + .ra: x30
STACK CFI a1b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a1bc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a298 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a29c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a2e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a2ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a2fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a300 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a314 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a320 2cc .cfa: sp 0 + .ra: x30
STACK CFI a324 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a32c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a348 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a350 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a400 x23: x23 x24: x24
STACK CFI a410 x19: x19 x20: x20
STACK CFI a418 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI a41c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a4a0 x23: x23 x24: x24
STACK CFI a4b0 x19: x19 x20: x20
STACK CFI a4b4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a504 x19: x19 x20: x20 x23: x23 x24: x24
STACK CFI a564 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a570 x19: x19 x20: x20
STACK CFI a574 x23: x23 x24: x24
STACK CFI a584 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI a588 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a594 x19: x19 x20: x20
STACK CFI a59c x23: x23 x24: x24
STACK CFI a5a0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI a5a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a5b0 x19: x19 x20: x20
STACK CFI a5b4 x23: x23 x24: x24
STACK CFI a5b8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI a5bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a5c0 x23: x23 x24: x24
STACK CFI a5c8 x19: x19 x20: x20
STACK CFI a5e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT a5f0 144 .cfa: sp 0 + .ra: x30
STACK CFI a5f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI a5fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI a608 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI a614 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a68c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a690 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI a6c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a6c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6410 8a4 .cfa: sp 0 + .ra: x30
STACK CFI 6414 .cfa: sp 272 + .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 6434 x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 68ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 68b0 .cfa: sp 272 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^ x29: .cfa -272 + ^
STACK CFI INIT a740 1c4 .cfa: sp 0 + .ra: x30
STACK CFI a744 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a754 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a760 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a774 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a77c x25: .cfa -32 + ^
STACK CFI a878 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI a87c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4fe4 54 .cfa: sp 0 + .ra: x30
STACK CFI 4fe8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4ff0 x19: .cfa -16 + ^
STACK CFI INIT a910 1c4 .cfa: sp 0 + .ra: x30
STACK CFI a914 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a924 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a930 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a944 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a94c x25: .cfa -32 + ^
STACK CFI aa48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI aa4c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5038 54 .cfa: sp 0 + .ra: x30
STACK CFI 503c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5044 x19: .cfa -16 + ^
STACK CFI INIT aae0 1c4 .cfa: sp 0 + .ra: x30
STACK CFI aae4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI aaf4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ab00 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ab14 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ab1c x25: .cfa -32 + ^
STACK CFI ac18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI ac1c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI INIT 508c 54 .cfa: sp 0 + .ra: x30
STACK CFI 5090 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5098 x19: .cfa -16 + ^
STACK CFI INIT acb0 224 .cfa: sp 0 + .ra: x30
STACK CFI acb4 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI acbc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI accc x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI ad10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI ad14 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x29: .cfa -112 + ^
STACK CFI INIT aee0 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 50e0 24 .cfa: sp 0 + .ra: x30
STACK CFI 50e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 50fc .cfa: sp 0 + .ra: .ra x29: x29
