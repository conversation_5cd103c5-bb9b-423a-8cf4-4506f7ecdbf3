MODULE Linux arm64 8202E556FE69EC3EE458047213C3E4AE0 libgmock.so
INFO CODE_ID 56E5028269FE3EECE458047213C3E4AE
FILE 0 /builds/buildroot.org/toolchains-builder/build/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 86f0 24 0 init_have_lse_atomics
86f0 4 45 0
86f4 4 46 0
86f8 4 45 0
86fc 4 46 0
8700 4 47 0
8704 4 47 0
8708 4 48 0
870c 4 47 0
8710 4 48 0
PUBLIC 7c98 0 _init
PUBLIC 8510 0 _GLOBAL__sub_I_gmock_all.cc
PUBLIC 8714 0 call_weak_fn
PUBLIC 8730 0 deregister_tm_clones
PUBLIC 8760 0 register_tm_clones
PUBLIC 87a0 0 __do_global_dtors_aux
PUBLIC 87f0 0 frame_dummy
PUBLIC 8800 0 testing::(anonymous namespace)::BetweenCardinalityImpl::ConservativeLowerBound() const
PUBLIC 8810 0 testing::(anonymous namespace)::BetweenCardinalityImpl::ConservativeUpperBound() const
PUBLIC 8820 0 testing::(anonymous namespace)::BetweenCardinalityImpl::IsSatisfiedByCallCount(int) const
PUBLIC 8850 0 testing::(anonymous namespace)::BetweenCardinalityImpl::IsSaturatedByCallCount(int) const
PUBLIC 8860 0 testing::(anonymous namespace)::BetweenCardinalityImpl::~BetweenCardinalityImpl()
PUBLIC 8870 0 testing::(anonymous namespace)::BetweenCardinalityImpl::~BetweenCardinalityImpl()
PUBLIC 8880 0 std::pair<std::_Rb_tree_iterator<testing::Expectation>, bool> std::_Rb_tree<testing::Expectation, testing::Expectation, std::_Identity<testing::Expectation>, testing::Expectation::Less, std::allocator<testing::Expectation> >::_M_insert_unique<testing::Expectation const&>(testing::Expectation const&) [clone .isra.0]
PUBLIC 89d0 0 std::_Rb_tree<testing::internal::UntypedFunctionMockerBase*, testing::internal::UntypedFunctionMockerBase*, std::_Identity<testing::internal::UntypedFunctionMockerBase*>, std::less<testing::internal::UntypedFunctionMockerBase*>, std::allocator<testing::internal::UntypedFunctionMockerBase*> >::_M_erase(std::_Rb_tree_node<testing::internal::UntypedFunctionMockerBase*>*) [clone .isra.0]
PUBLIC 8b30 0 std::_Rb_tree<void const*, std::pair<void const* const, testing::internal::CallReaction>, std::_Select1st<std::pair<void const* const, testing::internal::CallReaction> >, std::less<void const*>, std::allocator<std::pair<void const* const, testing::internal::CallReaction> > >::_M_erase(std::_Rb_tree_node<std::pair<void const* const, testing::internal::CallReaction> >*) [clone .isra.0]
PUBLIC 8c90 0 testing::internal::LogElementMatcherPairVec(std::vector<std::pair<unsigned long, unsigned long>, std::allocator<std::pair<unsigned long, unsigned long> > > const&, std::ostream*)
PUBLIC 8db0 0 std::_Rb_tree<void const*, std::pair<void const* const, testing::(anonymous namespace)::MockObjectState>, std::_Select1st<std::pair<void const* const, testing::(anonymous namespace)::MockObjectState> >, std::less<void const*>, std::allocator<std::pair<void const* const, testing::(anonymous namespace)::MockObjectState> > >::_M_erase(std::_Rb_tree_node<std::pair<void const* const, testing::(anonymous namespace)::MockObjectState> >*) [clone .isra.0]
PUBLIC 8e60 0 testing::internal::ParseGoogleMockFlagValue(char const*, char const*, bool)
PUBLIC 9040 0 testing::internal::ParseGoogleMockIntFlag(char const*, char const*, int*) [clone .constprop.0]
PUBLIC 9170 0 testing::(anonymous namespace)::MockObjectRegistry::~MockObjectRegistry()
PUBLIC 9490 0 std::_Rb_tree_iterator<std::pair<void const* const, testing::internal::CallReaction> > std::_Rb_tree<void const*, std::pair<void const* const, testing::internal::CallReaction>, std::_Select1st<std::pair<void const* const, testing::internal::CallReaction> >, std::less<void const*>, std::allocator<std::pair<void const* const, testing::internal::CallReaction> > >::_M_emplace_hint_unique<std::piecewise_construct_t const&, std::tuple<void const* const&>, std::tuple<> >(std::_Rb_tree_const_iterator<std::pair<void const* const, testing::internal::CallReaction> >, std::piecewise_construct_t const&, std::tuple<void const* const&>&&, std::tuple<>&&) [clone .isra.0]
PUBLIC 9750 0 std::_Rb_tree_iterator<std::pair<void const* const, testing::(anonymous namespace)::MockObjectState> > std::_Rb_tree<void const*, std::pair<void const* const, testing::(anonymous namespace)::MockObjectState>, std::_Select1st<std::pair<void const* const, testing::(anonymous namespace)::MockObjectState> >, std::less<void const*>, std::allocator<std::pair<void const* const, testing::(anonymous namespace)::MockObjectState> > >::_M_emplace_hint_unique<std::piecewise_construct_t const&, std::tuple<void const* const&>, std::tuple<> >(std::_Rb_tree_const_iterator<std::pair<void const* const, testing::(anonymous namespace)::MockObjectState> >, std::piecewise_construct_t const&, std::tuple<void const* const&>&&, std::tuple<>&&) [clone .constprop.0] [clone .isra.0]
PUBLIC 9a60 0 testing::(anonymous namespace)::SetReactionOnUninterestingCalls(void const*, testing::internal::CallReaction)
PUBLIC 9d10 0 testing::internal::JoinAsTuple(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&)
PUBLIC a080 0 testing::internal::ConvertIdentifierNameToWords[abi:cxx11](char const*)
PUBLIC a250 0 testing::internal::GetFailureReporter()
PUBLIC a2f0 0 testing::internal::LogIsVisible(testing::internal::LogSeverity)
PUBLIC a370 0 testing::internal::Log(testing::internal::LogSeverity, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, int)
PUBLIC a670 0 testing::internal::GetWithoutMatchers()
PUBLIC a680 0 testing::internal::IllegalDoDefault(char const*, int)
PUBLIC a820 0 testing::internal::FormatMatcherDescription(bool, char const*, std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&)
PUBLIC ac20 0 testing::internal::MatchMatrix::NextGraph()
PUBLIC ac90 0 testing::internal::MatchMatrix::Randomize()
PUBLIC ad10 0 testing::internal::UnorderedElementsAreMatcherImplBase::DescribeToImpl(std::ostream*) const
PUBLIC b600 0 testing::internal::UnorderedElementsAreMatcherImplBase::DescribeNegationToImpl(std::ostream*) const
PUBLIC c210 0 testing::internal::ReportUninterestingCall(testing::internal::CallReaction, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC c540 0 testing::internal::UntypedFunctionMockerBase::UntypedFunctionMockerBase()
PUBLIC c570 0 testing::internal::UntypedFunctionMockerBase::SetOwnerAndName(void const*, char const*)
PUBLIC c730 0 testing::internal::UntypedFunctionMockerBase::MockObject() const
PUBLIC ca60 0 testing::internal::UntypedFunctionMockerBase::Name() const
PUBLIC cd90 0 testing::internal::intToCallReaction(int)
PUBLIC cda0 0 testing::Mock::AllowUninterestingCalls(void const*)
PUBLIC cdb0 0 testing::Mock::WarnUninterestingCalls(void const*)
PUBLIC cdc0 0 testing::Mock::FailUninterestingCalls(void const*)
PUBLIC cdd0 0 testing::Mock::UnregisterCallReaction(void const*)
PUBLIC d0e0 0 testing::Mock::GetReactionOnUninterestingCalls(void const*)
PUBLIC d400 0 testing::Mock::AllowLeak(void const*)
PUBLIC d690 0 testing::Mock::IsNaggy(void*)
PUBLIC d6b0 0 testing::Mock::IsNice(void*)
PUBLIC d6d0 0 testing::Mock::IsStrict(void*)
PUBLIC d6f0 0 testing::Mock::Register(void const*, testing::internal::UntypedFunctionMockerBase*)
PUBLIC da50 0 testing::internal::UntypedFunctionMockerBase::RegisterOwner(void const*)
PUBLIC dc00 0 testing::Mock::RegisterUseByOnCallOrExpectCall(void const*, char const*, int)
PUBLIC df10 0 testing::Mock::UnregisterLocked(testing::internal::UntypedFunctionMockerBase*)
PUBLIC e260 0 testing::Mock::ClearDefaultActionsLocked(void*)
PUBLIC e4b0 0 testing::Expectation::Expectation()
PUBLIC e4c0 0 testing::Expectation::Expectation(std::shared_ptr<testing::internal::ExpectationBase> const&)
PUBLIC e510 0 testing::internal::UntypedFunctionMockerBase::GetHandleOf(testing::internal::ExpectationBase*)
PUBLIC e700 0 testing::internal::UntypedFunctionMockerBase::~UntypedFunctionMockerBase()
PUBLIC e820 0 testing::internal::UntypedFunctionMockerBase::~UntypedFunctionMockerBase()
PUBLIC e850 0 testing::Expectation::~Expectation()
PUBLIC e900 0 std::_Rb_tree<testing::Expectation, testing::Expectation, std::_Identity<testing::Expectation>, testing::Expectation::Less, std::allocator<testing::Expectation> >::_M_erase(std::_Rb_tree_node<testing::Expectation>*) [clone .isra.0]
PUBLIC eab0 0 testing::internal::ExpectationBase::SpecifyCardinality(testing::Cardinality const&)
PUBLIC ebb0 0 testing::internal::ExpectationBase::UntypedTimes(testing::Cardinality const&)
PUBLIC ee70 0 testing::internal::ExpectationBase::~ExpectationBase()
PUBLIC f050 0 testing::internal::ExpectationBase::~ExpectationBase()
PUBLIC f080 0 testing::Sequence::AddExpectation(testing::Expectation const&) const
PUBLIC f1b0 0 testing::(anonymous namespace)::FormatTimes(int)
PUBLIC f5a0 0 testing::Cardinality::DescribeActualCallCountTo(int, std::ostream*)
PUBLIC f6c0 0 testing::internal::ExpectationBase::DescribeCallCountTo(std::ostream*) const
PUBLIC fcb0 0 testing::(anonymous namespace)::BetweenCardinalityImpl::DescribeTo(std::ostream*) const
PUBLIC ff00 0 testing::(anonymous namespace)::BetweenCardinalityImpl::BetweenCardinalityImpl(int, int)
PUBLIC 105f0 0 testing::Between(int, int)
PUBLIC 106a0 0 testing::AtLeast(int)
PUBLIC 10700 0 testing::AnyNumber()
PUBLIC 10760 0 testing::AtMost(int)
PUBLIC 107c0 0 testing::Exactly(int)
PUBLIC 10820 0 testing::internal::ExpectationBase::ExpectationBase(char const*, int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 10a60 0 testing::internal::MatchMatrix::DebugString[abi:cxx11]() const
PUBLIC 10e70 0 testing::internal::ExpectationBase::CheckActionCountIfNotDone() const
PUBLIC 115c0 0 testing::internal::UntypedFunctionMockerBase::UntypedInvokeWith(void*)
PUBLIC 12ac0 0 testing::internal::UnorderedElementsAreMatcherImplBase::VerifyMatchMatrix(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&, testing::internal::MatchMatrix const&, testing::MatchResultListener*) const
PUBLIC 12fb0 0 testing::internal::LogWithLocation(testing::internal::LogSeverity, char const*, int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 133b0 0 testing::internal::UntypedFunctionMockerBase::VerifyAndClearExpectationsLocked()
PUBLIC 13f00 0 testing::Mock::VerifyAndClearExpectationsLocked(void*)
PUBLIC 14170 0 testing::Mock::VerifyAndClearExpectations(void*)
PUBLIC 14370 0 testing::Mock::VerifyAndClear(void*)
PUBLIC 14580 0 testing::InitGoogleMock(int*, wchar_t**)
PUBLIC 14590 0 testing::internal::ExpectationBase::RetireAllPreRequisites()
PUBLIC 14920 0 testing::internal::ExpectationBase::FindUnsatisfiedPrerequisites(testing::ExpectationSet*) const
PUBLIC 14c90 0 testing::internal::ExpectationBase::AllPrerequisitesAreSatisfied() const
PUBLIC 14f90 0 testing::InitGoogleMock(int*, char**)
PUBLIC 14fa0 0 testing::InitGoogleMock()
PUBLIC 15010 0 testing::internal::FindMaxBipartiteMatching(testing::internal::MatchMatrix const&)
PUBLIC 15560 0 testing::internal::UnorderedElementsAreMatcherImplBase::FindPairing(testing::internal::MatchMatrix const&, testing::MatchResultListener*) const
PUBLIC 157c0 0 testing::InSequence::InSequence()
PUBLIC 15b20 0 testing::InSequence::~InSequence()
PUBLIC 15e00 0 std::ctype<char>::do_widen(char) const
PUBLIC 15e10 0 DeleteThreadLocalValue
PUBLIC 15e30 0 testing::internal::ActionResultHolder<void>::PrintAsActionResult(std::ostream*) const
PUBLIC 15e40 0 std::_Sp_counted_ptr<testing::Expectation*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 15e50 0 std::_Sp_counted_ptr<testing::CardinalityInterface const*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 15e60 0 testing::internal::ThreadLocal<testing::Sequence*>::DefaultValueHolderFactory::~DefaultValueHolderFactory()
PUBLIC 15e70 0 testing::internal::GoogleTestFailureReporter::~GoogleTestFailureReporter()
PUBLIC 15e80 0 std::_Sp_counted_ptr<testing::Expectation*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 15e90 0 std::_Sp_counted_ptr<testing::CardinalityInterface const*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 15eb0 0 std::_Sp_counted_ptr<testing::CardinalityInterface const*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 15ec0 0 testing::internal::ThreadLocal<testing::Sequence*>::ValueHolder::~ValueHolder()
PUBLIC 15ed0 0 testing::internal::ThreadLocal<testing::Sequence*>::ValueHolder::~ValueHolder()
PUBLIC 15ee0 0 testing::internal::GoogleTestFailureReporter::~GoogleTestFailureReporter()
PUBLIC 15ef0 0 std::_Sp_counted_ptr<testing::CardinalityInterface const*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 15f00 0 std::_Sp_counted_ptr<testing::CardinalityInterface const*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 15f10 0 std::_Sp_counted_ptr<testing::Expectation*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 15f20 0 std::_Sp_counted_ptr<testing::Expectation*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 15f30 0 testing::internal::ThreadLocal<testing::Sequence*>::DefaultValueHolderFactory::~DefaultValueHolderFactory()
PUBLIC 15f40 0 testing::internal::ThreadLocal<testing::Sequence*>::DefaultValueHolderFactory::MakeNewHolder() const
PUBLIC 15f70 0 testing::internal::GoogleTestFailureReporter::ReportFailure(testing::internal::FailureReporterInterface::FailureType, char const*, int, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 16080 0 testing::internal::ThreadLocal<testing::Sequence*>::~ThreadLocal()
PUBLIC 161b0 0 std::map<void const*, testing::internal::CallReaction, std::less<void const*>, std::allocator<std::pair<void const* const, testing::internal::CallReaction> > >::~map()
PUBLIC 161f0 0 testing::internal::MutexBase::Unlock()
PUBLIC 162f0 0 testing::internal::MaxBipartiteMatchState::TryAugment(unsigned long, std::vector<char, std::allocator<char> >*)
PUBLIC 167f0 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release_last_use_cold()
PUBLIC 16870 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 16910 0 std::_Sp_counted_ptr<testing::Expectation*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 16950 0 std::vector<char, std::allocator<char> >::~vector()
PUBLIC 16960 0 std::vector<unsigned long, std::allocator<unsigned long> >::~vector()
PUBLIC 16970 0 std::vector<std::pair<unsigned long, unsigned long>, std::allocator<std::pair<unsigned long, unsigned long> > >::~vector()
PUBLIC 16980 0 std::vector<testing::internal::ExpectationBase const*, std::allocator<testing::internal::ExpectationBase const*> >::~vector()
PUBLIC 16990 0 std::vector<std::shared_ptr<testing::internal::ExpectationBase>, std::allocator<std::shared_ptr<testing::internal::ExpectationBase> > >::~vector()
PUBLIC 16a80 0 void testing::internal::InitGoogleMockImpl<wchar_t>(int*, wchar_t**)
PUBLIC 16cc0 0 void std::vector<testing::internal::ExpectationBase*, std::allocator<testing::internal::ExpectationBase*> >::_M_realloc_insert<testing::internal::ExpectationBase* const&>(__gnu_cxx::__normal_iterator<testing::internal::ExpectationBase**, std::vector<testing::internal::ExpectationBase*, std::allocator<testing::internal::ExpectationBase*> > >, testing::internal::ExpectationBase* const&)
PUBLIC 16e30 0 void std::vector<testing::internal::ExpectationBase const*, std::allocator<testing::internal::ExpectationBase const*> >::_M_realloc_insert<testing::internal::ExpectationBase const* const&>(__gnu_cxx::__normal_iterator<testing::internal::ExpectationBase const**, std::vector<testing::internal::ExpectationBase const*, std::allocator<testing::internal::ExpectationBase const*> > >, testing::internal::ExpectationBase const* const&)
PUBLIC 16fa0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > testing::internal::StreamableToString<char*>(char* const&)
PUBLIC 170b0 0 void testing::internal::InitGoogleMockImpl<char>(int*, char**)
PUBLIC 172b0 0 void std::vector<std::pair<unsigned long, unsigned long>, std::allocator<std::pair<unsigned long, unsigned long> > >::_M_realloc_insert<std::pair<unsigned long, unsigned long> >(__gnu_cxx::__normal_iterator<std::pair<unsigned long, unsigned long>*, std::vector<std::pair<unsigned long, unsigned long>, std::allocator<std::pair<unsigned long, unsigned long> > > >, std::pair<unsigned long, unsigned long>&&)
PUBLIC 17400 0 testing::internal::ThreadLocal<testing::Sequence*>::ValueHolder* testing::internal::CheckedDowncastToActualType<testing::internal::ThreadLocal<testing::Sequence*>::ValueHolder, testing::internal::ThreadLocalValueHolderBase>(testing::internal::ThreadLocalValueHolderBase*)
PUBLIC 17550 0 __aarch64_ldadd4_acq_rel
PUBLIC 17580 0 _fini
STACK CFI INIT 8730 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8760 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 87a0 48 .cfa: sp 0 + .ra: x30
STACK CFI 87a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 87ac x19: .cfa -16 + ^
STACK CFI 87e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 87f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e00 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e10 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8800 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8810 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8820 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8850 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e40 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8860 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e90 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 15eb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15ec0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15ed0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15ee0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8870 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15ef0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15f00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15f10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15f20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15f30 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15f40 28 .cfa: sp 0 + .ra: x30
STACK CFI 15f44 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 15f64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 15f70 104 .cfa: sp 0 + .ra: x30
STACK CFI 15f74 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 15f8c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 15f94 x21: .cfa -48 + ^
STACK CFI 16018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1601c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 16080 12c .cfa: sp 0 + .ra: x30
STACK CFI 16084 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 16094 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 16100 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 16108 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 1610c x21: .cfa -32 + ^
STACK CFI 16178 x21: x21
STACK CFI 161a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 161a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 161a8 x21: .cfa -32 + ^
STACK CFI INIT 8880 150 .cfa: sp 0 + .ra: x30
STACK CFI 8884 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 888c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8894 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 889c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8954 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 89d0 15c .cfa: sp 0 + .ra: x30
STACK CFI 89d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 89e0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 89e8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 89f4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8a18 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8a1c x27: .cfa -16 + ^
STACK CFI 8a68 x21: x21 x22: x22
STACK CFI 8a6c x27: x27
STACK CFI 8a84 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 8a9c x21: x21 x22: x22 x27: x27
STACK CFI 8ab4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 8acc x21: x21 x22: x22 x27: x27
STACK CFI 8b00 x25: x25 x26: x26
STACK CFI 8b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 8b30 15c .cfa: sp 0 + .ra: x30
STACK CFI 8b38 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8b40 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8b48 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8b54 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8b78 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8b7c x27: .cfa -16 + ^
STACK CFI 8bc8 x21: x21 x22: x22
STACK CFI 8bcc x27: x27
STACK CFI 8be4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 8bfc x21: x21 x22: x22 x27: x27
STACK CFI 8c14 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 8c2c x21: x21 x22: x22 x27: x27
STACK CFI 8c60 x25: x25 x26: x26
STACK CFI 8c84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 161b0 38 .cfa: sp 0 + .ra: x30
STACK CFI 161b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 161bc x19: .cfa -16 + ^
STACK CFI 161e4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8c90 120 .cfa: sp 0 + .ra: x30
STACK CFI 8c94 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8ca0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8cb0 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8ce0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8cf8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8d88 x23: x23 x24: x24
STACK CFI 8d8c x25: x25 x26: x26
STACK CFI 8dac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI INIT 8db0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 8db8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8dc0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8dc8 x21: .cfa -16 + ^
STACK CFI 8e4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 8e60 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 8e64 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 8e74 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 8e98 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 8eb0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 8f7c x21: x21 x22: x22
STACK CFI 8f80 x23: x23 x24: x24
STACK CFI 8fa8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8fac .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x29: .cfa -144 + ^
STACK CFI 8fb4 x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 8fd0 x21: x21 x22: x22
STACK CFI 8fd4 x23: x23 x24: x24
STACK CFI 8fdc x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 8fe0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI INIT 9040 130 .cfa: sp 0 + .ra: x30
STACK CFI 9044 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9058 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9078 x21: .cfa -32 + ^
STACK CFI 90e8 x21: x21
STACK CFI 9110 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9114 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI 9118 x21: x21
STACK CFI 9128 x21: .cfa -32 + ^
STACK CFI INIT 9170 320 .cfa: sp 0 + .ra: x30
STACK CFI 9174 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 918c x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 91a8 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 91b0 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 91c8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 91e0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 92ec x25: x25 x26: x26
STACK CFI 92f0 x27: x27 x28: x28
STACK CFI 9390 x19: x19 x20: x20
STACK CFI 9394 x21: x21 x22: x22
STACK CFI 939c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 93a0 .cfa: sp 160 + .ra: .cfa -152 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 93c8 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 93cc .cfa: sp 160 + .ra: .cfa -152 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x29: .cfa -160 + ^
STACK CFI 93d0 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 93d4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 93d8 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 93dc x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 93e0 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 93e4 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 93e8 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 9490 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 9494 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 949c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 94ac x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 94b4 x25: .cfa -16 + ^
STACK CFI 95f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 95f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 9654 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9658 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9750 308 .cfa: sp 0 + .ra: x30
STACK CFI 9754 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 975c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9768 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9774 x25: .cfa -16 + ^
STACK CFI 9910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9914 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 9970 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9974 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 161f0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 161fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16238 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1623c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16240 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 162ac x19: x19 x20: x20
STACK CFI 162b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 9a60 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 9a64 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9a6c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9b68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9b6c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI 9bac x21: .cfa -48 + ^
STACK CFI 9c14 x21: x21
STACK CFI 9c1c x21: .cfa -48 + ^
STACK CFI 9c88 x21: x21
STACK CFI 9c90 x21: .cfa -48 + ^
STACK CFI 9cd0 x21: x21
STACK CFI 9cfc x21: .cfa -48 + ^
STACK CFI INIT 9d10 36c .cfa: sp 0 + .ra: x30
STACK CFI 9d14 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 9d1c x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 9d2c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 9d48 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 9d60 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9d6c x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 9ebc x23: x23 x24: x24
STACK CFI 9ec0 x27: x27 x28: x28
STACK CFI 9ef4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 9ef8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI 9f7c x23: .cfa -96 + ^ x24: .cfa -88 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 9fdc x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 9fe0 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 9fe4 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI INIT a080 1d0 .cfa: sp 0 + .ra: x30
STACK CFI a084 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a08c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a094 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a0ac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a0b8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a0c0 x27: .cfa -16 + ^
STACK CFI a198 x21: x21 x22: x22
STACK CFI a19c x25: x25 x26: x26
STACK CFI a1a0 x27: x27
STACK CFI a1b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI a1b4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT a250 94 .cfa: sp 0 + .ra: x30
STACK CFI a254 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a25c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a280 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a284 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a2c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a2cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT a2f0 7c .cfa: sp 0 + .ra: x30
STACK CFI INIT a370 2fc .cfa: sp 0 + .ra: x30
STACK CFI a374 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI a384 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI a38c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI a394 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI a3d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a3dc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT a670 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT a680 1a0 .cfa: sp 0 + .ra: x30
STACK CFI a684 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI a694 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI a6a0 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI a6ac x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI a7b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a7bc .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI INIT a820 3fc .cfa: sp 0 + .ra: x30
STACK CFI a824 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI a834 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI a840 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI a84c x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI a854 x25: .cfa -128 + ^
STACK CFI aa74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI aa78 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x29: .cfa -192 + ^
STACK CFI INIT 162f0 500 .cfa: sp 0 + .ra: x30
STACK CFI 162f4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 1630c x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 16318 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 16320 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI 16324 x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 16328 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI 166e0 x19: x19 x20: x20
STACK CFI 166e4 x21: x21 x22: x22
STACK CFI 166e8 x23: x23 x24: x24
STACK CFI 166ec x25: x25 x26: x26
STACK CFI 166f0 x27: x27 x28: x28
STACK CFI 166f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 166fc .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI 16790 x19: x19 x20: x20
STACK CFI 16794 x21: x21 x22: x22
STACK CFI 16798 x23: x23 x24: x24
STACK CFI 1679c x25: x25 x26: x26
STACK CFI 167a0 x27: x27 x28: x28
STACK CFI 167ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 167b0 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x27: .cfa -176 + ^ x28: .cfa -168 + ^ x29: .cfa -256 + ^
STACK CFI INIT ac20 6c .cfa: sp 0 + .ra: x30
STACK CFI INIT ac90 80 .cfa: sp 0 + .ra: x30
STACK CFI ac94 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ac9c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI acb0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI acb4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI ad00 x19: x19 x20: x20
STACK CFI ad04 x21: x21 x22: x22
STACK CFI ad0c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI INIT ad10 8ec .cfa: sp 0 + .ra: x30
STACK CFI ad14 .cfa: sp 240 + .ra: .cfa -232 + ^ x29: .cfa -240 + ^
STACK CFI ad24 x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI ad2c x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI ad4c x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI ae5c x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI b0d0 x27: x27 x28: x28
STACK CFI b108 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b10c .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x29: .cfa -240 + ^
STACK CFI b138 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI b14c x27: x27 x28: x28
STACK CFI b150 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI b3c8 x27: x27 x28: x28
STACK CFI b3cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b3d0 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x29: .cfa -240 + ^
STACK CFI b410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b414 .cfa: sp 240 + .ra: .cfa -232 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x29: .cfa -240 + ^
STACK CFI b418 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT b600 c08 .cfa: sp 0 + .ra: x30
STACK CFI b604 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI b614 x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI b61c x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI b63c x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI b74c x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI b9c0 x27: x27 x28: x28
STACK CFI b9f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI b9fc .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x29: .cfa -256 + ^
STACK CFI ba28 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI ba3c x27: x27 x28: x28
STACK CFI ba40 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI bea4 x27: x27 x28: x28
STACK CFI bea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI beac .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x29: .cfa -256 + ^
STACK CFI beec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI bef0 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x29: .cfa -256 + ^
STACK CFI bef4 x27: .cfa -176 + ^ x28: .cfa -168 + ^
STACK CFI INIT c210 328 .cfa: sp 0 + .ra: x30
STACK CFI c214 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI c22c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI c25c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI c268 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI c308 x21: x21 x22: x22
STACK CFI c30c x23: x23 x24: x24
STACK CFI c330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c334 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI c384 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c388 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x29: .cfa -128 + ^
STACK CFI c428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c434 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI c438 x21: x21 x22: x22
STACK CFI c43c x23: x23 x24: x24
STACK CFI c444 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI c448 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI c48c x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI c4b4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI c4b8 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI c4c4 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI c4e4 x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI INIT c540 2c .cfa: sp 0 + .ra: x30
STACK CFI INIT c570 1bc .cfa: sp 0 + .ra: x30
STACK CFI c574 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c584 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c58c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c604 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI c608 x23: .cfa -32 + ^
STACK CFI c674 x23: x23
STACK CFI c67c x23: .cfa -32 + ^
STACK CFI c6e4 x23: x23
STACK CFI c6ec x23: .cfa -32 + ^
STACK CFI INIT c730 32c .cfa: sp 0 + .ra: x30
STACK CFI c734 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI c744 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI c74c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI c754 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI c850 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI c854 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI INIT ca60 324 .cfa: sp 0 + .ra: x30
STACK CFI ca64 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI ca74 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI ca7c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI ca84 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI cb78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI cb7c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x29: .cfa -128 + ^
STACK CFI INIT cd90 c .cfa: sp 0 + .ra: x30
STACK CFI INIT cda0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT cdb0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT cdc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT cdd0 310 .cfa: sp 0 + .ra: x30
STACK CFI cdd4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI cde4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI ce04 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ce10 x25: .cfa -32 + ^
STACK CFI cee4 x25: x25
STACK CFI cee8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI ceec .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x29: .cfa -96 + ^
STACK CFI cf04 x25: x25
STACK CFI cf6c x25: .cfa -32 + ^
STACK CFI d09c x25: x25
STACK CFI d0c4 x25: .cfa -32 + ^
STACK CFI INIT d0e0 31c .cfa: sp 0 + .ra: x30
STACK CFI d0e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d10c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d1d8 x21: .cfa -48 + ^
STACK CFI d240 x21: x21
STACK CFI d2e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d2e8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI d30c x21: .cfa -48 + ^
STACK CFI d378 x21: x21
STACK CFI d388 x21: .cfa -48 + ^
STACK CFI d38c x21: x21
STACK CFI d3b8 x21: .cfa -48 + ^
STACK CFI INIT d400 290 .cfa: sp 0 + .ra: x30
STACK CFI d404 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI d414 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI d50c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI d510 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT d690 1c .cfa: sp 0 + .ra: x30
STACK CFI d694 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI d6a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d6b0 1c .cfa: sp 0 + .ra: x30
STACK CFI d6b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI d6c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d6d0 1c .cfa: sp 0 + .ra: x30
STACK CFI d6d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI d6e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT d6f0 35c .cfa: sp 0 + .ra: x30
STACK CFI d6f4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d6fc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d710 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI d848 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT da50 1ac .cfa: sp 0 + .ra: x30
STACK CFI da54 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI da64 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI da70 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI dae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI dae8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT dc00 310 .cfa: sp 0 + .ra: x30
STACK CFI dc04 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI dc18 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI dd1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI dd20 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT df10 348 .cfa: sp 0 + .ra: x30
STACK CFI df14 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI df2c x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI df60 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI df64 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI df80 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI df84 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI e018 x25: x25 x26: x26
STACK CFI e01c x27: x27 x28: x28
STACK CFI e040 x21: x21 x22: x22
STACK CFI e044 x23: x23 x24: x24
STACK CFI e048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e04c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI e178 x25: x25 x26: x26
STACK CFI e180 x27: x27 x28: x28
STACK CFI e18c x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI e194 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI e1fc x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI e200 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI e20c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI e210 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI e214 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI e240 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI e244 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI e248 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI e24c x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT e260 244 .cfa: sp 0 + .ra: x30
STACK CFI e264 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e27c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e398 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e39c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI INIT e4b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT e4c0 50 .cfa: sp 0 + .ra: x30
STACK CFI e4f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e50c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e510 1f0 .cfa: sp 0 + .ra: x30
STACK CFI e514 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI e524 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI e594 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e598 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x29: .cfa -112 + ^
STACK CFI e5a4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI e694 x21: x21 x22: x22
STACK CFI e69c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI INIT 167f0 78 .cfa: sp 0 + .ra: x30
STACK CFI 167f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16804 x19: .cfa -16 + ^
STACK CFI 16838 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1683c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1684c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 16858 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 16870 9c .cfa: sp 0 + .ra: x30
STACK CFI 16874 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16880 x19: .cfa -16 + ^
STACK CFI 168c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 168c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 168f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 168fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 16908 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e700 114 .cfa: sp 0 + .ra: x30
STACK CFI e704 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI e710 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI e720 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI e73c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI e7b4 x23: x23 x24: x24
STACK CFI e7d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI e7dc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI e804 x23: x23 x24: x24
STACK CFI e810 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT e820 24 .cfa: sp 0 + .ra: x30
STACK CFI e824 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e82c x19: .cfa -16 + ^
STACK CFI e840 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e850 a4 .cfa: sp 0 + .ra: x30
STACK CFI e854 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e85c x19: .cfa -16 + ^
STACK CFI e8a4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e8a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI e8cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI e8d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI e8f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 16910 38 .cfa: sp 0 + .ra: x30
STACK CFI 16914 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1691c x19: .cfa -16 + ^
STACK CFI 16938 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1693c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 16944 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT e900 1a8 .cfa: sp 0 + .ra: x30
STACK CFI e908 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e910 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e91c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI e928 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e92c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ea6c x21: x21 x22: x22
STACK CFI ea70 x27: x27 x28: x28
STACK CFI eaa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI INIT eab0 f8 .cfa: sp 0 + .ra: x30
STACK CFI eab4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI eac0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI eacc x21: .cfa -16 + ^
STACK CFI eb4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI eb50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT ebb0 2bc .cfa: sp 0 + .ra: x30
STACK CFI ebb4 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI ebc4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI ebd0 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI ebdc x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI ebe8 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI ecb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI ecb8 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x29: .cfa -144 + ^
STACK CFI INIT ee70 1e0 .cfa: sp 0 + .ra: x30
STACK CFI ee74 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ee8c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ee94 x21: .cfa -32 + ^
STACK CFI ef60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI ef64 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI f030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI f034 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT f050 24 .cfa: sp 0 + .ra: x30
STACK CFI f054 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f05c x19: .cfa -16 + ^
STACK CFI f070 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f080 128 .cfa: sp 0 + .ra: x30
STACK CFI f084 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f08c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f098 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI f148 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f14c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT f1b0 3f0 .cfa: sp 0 + .ra: x30
STACK CFI f1b4 .cfa: sp 560 +
STACK CFI f1c0 .ra: .cfa -552 + ^ x29: .cfa -560 + ^
STACK CFI f1c8 x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI f1f0 x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI f200 x25: .cfa -496 + ^ x26: .cfa -488 + ^
STACK CFI f20c x23: .cfa -512 + ^ x24: .cfa -504 + ^
STACK CFI f210 x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI f404 x21: x21 x22: x22
STACK CFI f408 x23: x23 x24: x24
STACK CFI f40c x25: x25 x26: x26
STACK CFI f410 x27: x27 x28: x28
STACK CFI f43c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f440 .cfa: sp 560 + .ra: .cfa -552 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x29: .cfa -560 + ^
STACK CFI f48c x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI f49c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI f4a0 x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI f4a4 x23: .cfa -512 + ^ x24: .cfa -504 + ^
STACK CFI f4a8 x25: .cfa -496 + ^ x26: .cfa -488 + ^
STACK CFI f4ac x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI INIT f5a0 118 .cfa: sp 0 + .ra: x30
STACK CFI f5a4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f5b4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f5e4 x21: .cfa -64 + ^
STACK CFI f63c x21: x21
STACK CFI f640 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f644 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI f674 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f67c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x29: .cfa -96 + ^
STACK CFI f680 x21: .cfa -64 + ^
STACK CFI INIT f6c0 5ec .cfa: sp 0 + .ra: x30
STACK CFI f6c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI f6cc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI f6d8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI f6f0 x23: .cfa -32 + ^
STACK CFI f890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI f894 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI INIT fcb0 24c .cfa: sp 0 + .ra: x30
STACK CFI fcb4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI fcbc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI fd58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fd5c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI fdd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fde4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI fe14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fe1c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI fe80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI fe88 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT ff00 6ec .cfa: sp 0 + .ra: x30
STACK CFI ff04 .cfa: sp 624 +
STACK CFI ff14 .ra: .cfa -616 + ^ x29: .cfa -624 + ^
STACK CFI ff1c x19: .cfa -608 + ^ x20: .cfa -600 + ^
STACK CFI ff24 x21: .cfa -592 + ^ x22: .cfa -584 + ^
STACK CFI ff48 x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x28: .cfa -536 + ^
STACK CFI 10154 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10158 .cfa: sp 624 + .ra: .cfa -616 + ^ x19: .cfa -608 + ^ x20: .cfa -600 + ^ x21: .cfa -592 + ^ x22: .cfa -584 + ^ x23: .cfa -576 + ^ x24: .cfa -568 + ^ x25: .cfa -560 + ^ x26: .cfa -552 + ^ x27: .cfa -544 + ^ x28: .cfa -536 + ^ x29: .cfa -624 + ^
STACK CFI INIT 105f0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 105f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 105fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10604 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 10660 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10664 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 106a0 5c .cfa: sp 0 + .ra: x30
STACK CFI 106a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 106b8 x19: .cfa -32 + ^
STACK CFI 106f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 106f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 10700 5c .cfa: sp 0 + .ra: x30
STACK CFI 10704 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10718 x19: .cfa -32 + ^
STACK CFI 10754 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10758 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 10760 5c .cfa: sp 0 + .ra: x30
STACK CFI 10764 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10778 x19: .cfa -32 + ^
STACK CFI 107b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 107b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 107c0 5c .cfa: sp 0 + .ra: x30
STACK CFI 107c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 107d8 x19: .cfa -32 + ^
STACK CFI 10814 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 10818 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x29: .cfa -48 + ^
STACK CFI INIT 10820 234 .cfa: sp 0 + .ra: x30
STACK CFI 10824 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 10834 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10844 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 10924 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 10964 x23: .cfa -32 + ^
STACK CFI 109cc x23: x23
STACK CFI 109d4 x23: .cfa -32 + ^
STACK CFI 10a10 x23: x23
STACK CFI 10a34 x23: .cfa -32 + ^
STACK CFI 10a40 x23: x23
STACK CFI 10a48 x23: .cfa -32 + ^
STACK CFI 10a4c x23: x23
STACK CFI 10a50 x23: .cfa -32 + ^
STACK CFI INIT 10a60 408 .cfa: sp 0 + .ra: x30
STACK CFI 10a64 .cfa: sp 592 +
STACK CFI 10a70 .ra: .cfa -584 + ^ x29: .cfa -592 + ^
STACK CFI 10a78 x19: .cfa -576 + ^ x20: .cfa -568 + ^
STACK CFI 10a80 x21: .cfa -560 + ^ x22: .cfa -552 + ^
STACK CFI 10a88 x25: .cfa -528 + ^ x26: .cfa -520 + ^
STACK CFI 10a90 x27: .cfa -512 + ^ x28: .cfa -504 + ^
STACK CFI 10bd8 x23: .cfa -544 + ^ x24: .cfa -536 + ^
STACK CFI 10c48 x23: x23 x24: x24
STACK CFI 10d48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10d4c .cfa: sp 592 + .ra: .cfa -584 + ^ x19: .cfa -576 + ^ x20: .cfa -568 + ^ x21: .cfa -560 + ^ x22: .cfa -552 + ^ x25: .cfa -528 + ^ x26: .cfa -520 + ^ x27: .cfa -512 + ^ x28: .cfa -504 + ^ x29: .cfa -592 + ^
STACK CFI 10d60 x23: .cfa -544 + ^ x24: .cfa -536 + ^
STACK CFI 10d64 x23: x23 x24: x24
STACK CFI 10d94 x23: .cfa -544 + ^ x24: .cfa -536 + ^
STACK CFI 10da4 x23: x23 x24: x24
STACK CFI 10df4 x23: .cfa -544 + ^ x24: .cfa -536 + ^
STACK CFI 10dfc x23: x23 x24: x24
STACK CFI INIT 10e70 74c .cfa: sp 0 + .ra: x30
STACK CFI 10e74 .cfa: sp 640 +
STACK CFI 10e80 .ra: .cfa -632 + ^ x29: .cfa -640 + ^
STACK CFI 10e88 x19: .cfa -624 + ^ x20: .cfa -616 + ^
STACK CFI 10ea4 x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI 10f14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10f18 .cfa: sp 640 + .ra: .cfa -632 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^ x29: .cfa -640 + ^
STACK CFI INIT 115c0 14f8 .cfa: sp 0 + .ra: x30
STACK CFI 115c4 .cfa: sp 1616 +
STACK CFI 115c8 .ra: .cfa -1608 + ^ x29: .cfa -1616 + ^
STACK CFI 115d0 x19: .cfa -1600 + ^ x20: .cfa -1592 + ^
STACK CFI 11600 x21: .cfa -1584 + ^ x22: .cfa -1576 + ^ x23: .cfa -1568 + ^ x24: .cfa -1560 + ^ x27: .cfa -1536 + ^ x28: .cfa -1528 + ^
STACK CFI 11608 x25: .cfa -1552 + ^ x26: .cfa -1544 + ^
STACK CFI 11b64 x25: x25 x26: x26
STACK CFI 11b80 x25: .cfa -1552 + ^ x26: .cfa -1544 + ^
STACK CFI 11e54 x25: x25 x26: x26
STACK CFI 11e58 x25: .cfa -1552 + ^ x26: .cfa -1544 + ^
STACK CFI 12194 x25: x25 x26: x26
STACK CFI 121cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 121d0 .cfa: sp 1616 + .ra: .cfa -1608 + ^ x19: .cfa -1600 + ^ x20: .cfa -1592 + ^ x21: .cfa -1584 + ^ x22: .cfa -1576 + ^ x23: .cfa -1568 + ^ x24: .cfa -1560 + ^ x25: .cfa -1552 + ^ x26: .cfa -1544 + ^ x27: .cfa -1536 + ^ x28: .cfa -1528 + ^ x29: .cfa -1616 + ^
STACK CFI 12214 x25: x25 x26: x26
STACK CFI 1231c x25: .cfa -1552 + ^ x26: .cfa -1544 + ^
STACK CFI 124dc x25: x25 x26: x26
STACK CFI 12510 x25: .cfa -1552 + ^ x26: .cfa -1544 + ^
STACK CFI 12588 x25: x25 x26: x26
STACK CFI 12594 x25: .cfa -1552 + ^ x26: .cfa -1544 + ^
STACK CFI 125ec x25: x25 x26: x26
STACK CFI 12604 x25: .cfa -1552 + ^ x26: .cfa -1544 + ^
STACK CFI 12628 x25: x25 x26: x26
STACK CFI 12644 x25: .cfa -1552 + ^ x26: .cfa -1544 + ^
STACK CFI 12748 x25: x25 x26: x26
STACK CFI 12770 x25: .cfa -1552 + ^ x26: .cfa -1544 + ^
STACK CFI 12aa0 x25: x25 x26: x26
STACK CFI 12ab0 x25: .cfa -1552 + ^ x26: .cfa -1544 + ^
STACK CFI INIT 16950 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12ac0 4e4 .cfa: sp 0 + .ra: x30
STACK CFI 12ac4 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 12acc x25: .cfa -112 + ^ x26: .cfa -104 + ^
STACK CFI 12ae8 x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^
STACK CFI 12d60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 12d64 .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x23: .cfa -128 + ^ x24: .cfa -120 + ^ x25: .cfa -112 + ^ x26: .cfa -104 + ^ x27: .cfa -96 + ^ x28: .cfa -88 + ^ x29: .cfa -176 + ^
STACK CFI INIT 16960 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16970 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 12fb0 400 .cfa: sp 0 + .ra: x30
STACK CFI 12fb4 .cfa: sp 544 +
STACK CFI 12fc0 .ra: .cfa -536 + ^ x29: .cfa -544 + ^
STACK CFI 12fc8 x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 12fd4 x21: .cfa -512 + ^ x22: .cfa -504 + ^
STACK CFI 12fe0 x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^
STACK CFI 12fe8 x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI 1326c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 13270 .cfa: sp 544 + .ra: .cfa -536 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^ x27: .cfa -464 + ^ x28: .cfa -456 + ^ x29: .cfa -544 + ^
STACK CFI INIT 16980 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16990 f0 .cfa: sp 0 + .ra: x30
STACK CFI 16994 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 169a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 169b4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 16a2c x23: x23 x24: x24
STACK CFI 16a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 16a48 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 16a70 x23: x23 x24: x24
STACK CFI 16a7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 133b0 b4c .cfa: sp 0 + .ra: x30
STACK CFI 133b4 .cfa: sp 704 +
STACK CFI 133c0 .ra: .cfa -696 + ^ x29: .cfa -704 + ^
STACK CFI 133c8 x21: .cfa -672 + ^ x22: .cfa -664 + ^
STACK CFI 133ec x19: .cfa -688 + ^ x20: .cfa -680 + ^ x23: .cfa -656 + ^ x24: .cfa -648 + ^
STACK CFI 1340c x25: .cfa -640 + ^ x26: .cfa -632 + ^
STACK CFI 13428 x27: .cfa -624 + ^ x28: .cfa -616 + ^
STACK CFI 13504 x27: x27 x28: x28
STACK CFI 1367c x25: x25 x26: x26
STACK CFI 13680 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 13684 .cfa: sp 704 + .ra: .cfa -696 + ^ x19: .cfa -688 + ^ x20: .cfa -680 + ^ x21: .cfa -672 + ^ x22: .cfa -664 + ^ x23: .cfa -656 + ^ x24: .cfa -648 + ^ x25: .cfa -640 + ^ x26: .cfa -632 + ^ x27: .cfa -624 + ^ x28: .cfa -616 + ^ x29: .cfa -704 + ^
STACK CFI 13a84 x27: x27 x28: x28
STACK CFI 13aac x27: .cfa -624 + ^ x28: .cfa -616 + ^
STACK CFI 13b40 x27: x27 x28: x28
STACK CFI 13c68 x25: x25 x26: x26
STACK CFI 13cd0 x25: .cfa -640 + ^ x26: .cfa -632 + ^
STACK CFI 13cd8 x27: .cfa -624 + ^ x28: .cfa -616 + ^
STACK CFI 13d18 x27: x27 x28: x28
STACK CFI 13d1c x27: .cfa -624 + ^ x28: .cfa -616 + ^
STACK CFI 13d84 x27: x27 x28: x28
STACK CFI 13db4 x27: .cfa -624 + ^ x28: .cfa -616 + ^
STACK CFI 13dcc x27: x27 x28: x28
STACK CFI 13dd4 x27: .cfa -624 + ^ x28: .cfa -616 + ^
STACK CFI 13e0c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 13e34 x25: .cfa -640 + ^ x26: .cfa -632 + ^
STACK CFI 13e38 x27: .cfa -624 + ^ x28: .cfa -616 + ^
STACK CFI 13e88 x27: x27 x28: x28
STACK CFI 13e8c x27: .cfa -624 + ^ x28: .cfa -616 + ^
STACK CFI INIT 13f00 26c .cfa: sp 0 + .ra: x30
STACK CFI 13f04 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 13f20 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI 14000 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 14004 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x29: .cfa -80 + ^
STACK CFI INIT 14170 1f8 .cfa: sp 0 + .ra: x30
STACK CFI 14174 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 14184 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 14208 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1420c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 14210 x21: .cfa -32 + ^
STACK CFI 1427c x21: x21
STACK CFI 14284 x21: .cfa -32 + ^
STACK CFI 142ec x21: x21
STACK CFI 142f4 x21: .cfa -32 + ^
STACK CFI 142f8 x21: x21
STACK CFI 14324 x21: .cfa -32 + ^
STACK CFI INIT 14370 208 .cfa: sp 0 + .ra: x30
STACK CFI 14374 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 14384 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 14418 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1441c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 14420 x21: .cfa -32 + ^
STACK CFI 1448c x21: x21
STACK CFI 14494 x21: .cfa -32 + ^
STACK CFI 144fc x21: x21
STACK CFI 14504 x21: .cfa -32 + ^
STACK CFI 14508 x21: x21
STACK CFI 14534 x21: .cfa -32 + ^
STACK CFI INIT 16a80 23c .cfa: sp 0 + .ra: x30
STACK CFI 16a84 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 16a94 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 16a9c x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 16ac4 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 16ad4 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 16ae4 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 16bb8 x23: x23 x24: x24
STACK CFI 16bbc x25: x25 x26: x26
STACK CFI 16bc0 x27: x27 x28: x28
STACK CFI 16be8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 16bec .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 16c5c x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 16c60 x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 16c64 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 16c68 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 14580 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 16cc0 170 .cfa: sp 0 + .ra: x30
STACK CFI 16cc4 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 16ccc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 16cdc x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 16ce8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 16d70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 16d74 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 14590 38c .cfa: sp 0 + .ra: x30
STACK CFI 14594 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 1459c x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 145c4 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 14610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14614 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI 1461c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 14624 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 14630 x27: .cfa -64 + ^
STACK CFI 147d8 x23: x23 x24: x24
STACK CFI 147dc x25: x25 x26: x26
STACK CFI 147e0 x27: x27
STACK CFI 14850 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^
STACK CFI 14878 x23: x23 x24: x24
STACK CFI 1487c x25: x25 x26: x26
STACK CFI 14880 x27: x27
STACK CFI 14884 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14888 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x29: .cfa -144 + ^
STACK CFI 1488c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 14890 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 14894 x27: .cfa -64 + ^
STACK CFI 148d4 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 14900 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 14904 x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 14908 x27: .cfa -64 + ^
STACK CFI INIT 16e30 170 .cfa: sp 0 + .ra: x30
STACK CFI 16e34 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 16e3c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 16e4c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 16e58 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 16ee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 16ee4 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 14920 36c .cfa: sp 0 + .ra: x30
STACK CFI 14924 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 1492c x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 1494c x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 14954 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 14b74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 14b78 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 14bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 14bb0 .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI INIT 14c90 300 .cfa: sp 0 + .ra: x30
STACK CFI 14c94 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 14c9c x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 14cb4 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 14cc4 x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 14df0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 14df4 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI INIT 16fa0 10c .cfa: sp 0 + .ra: x30
STACK CFI 16fa4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 16fb4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 16fc0 x21: .cfa -32 + ^
STACK CFI 17048 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1704c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 170b0 1fc .cfa: sp 0 + .ra: x30
STACK CFI 170b4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 170c4 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 170cc x23: .cfa -112 + ^ x24: .cfa -104 + ^
STACK CFI 170f0 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI 17104 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 17120 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 171c8 x19: x19 x20: x20
STACK CFI 171cc x25: x25 x26: x26
STACK CFI 171d0 x27: x27 x28: x28
STACK CFI 171f8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 171fc .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^ x23: .cfa -112 + ^ x24: .cfa -104 + ^ x25: .cfa -96 + ^ x26: .cfa -88 + ^ x27: .cfa -80 + ^ x28: .cfa -72 + ^ x29: .cfa -160 + ^
STACK CFI 1726c x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 17270 x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 17274 x25: .cfa -96 + ^ x26: .cfa -88 + ^
STACK CFI 17278 x27: .cfa -80 + ^ x28: .cfa -72 + ^
STACK CFI INIT 14f90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 14fa0 64 .cfa: sp 0 + .ra: x30
STACK CFI 14fac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 14ffc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 15000 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI INIT 172b0 14c .cfa: sp 0 + .ra: x30
STACK CFI 172b4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 172bc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 172cc x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 172d4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1738c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 17390 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 15010 550 .cfa: sp 0 + .ra: x30
STACK CFI 1501c .cfa: sp 208 + .ra: .cfa -200 + ^ x29: .cfa -208 + ^
STACK CFI 15044 x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 15048 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 1504c x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 150f8 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 1510c x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 15288 x25: x25 x26: x26
STACK CFI 1528c x27: x27 x28: x28
STACK CFI 1534c x19: x19 x20: x20
STACK CFI 15354 x21: x21 x22: x22
STACK CFI 15358 x23: x23 x24: x24
STACK CFI 1535c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 15360 .cfa: sp 208 + .ra: .cfa -200 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^ x29: .cfa -208 + ^
STACK CFI 15424 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 15470 x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 15490 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 154ac x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 154b0 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 154b4 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 154b8 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 154bc x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 154cc x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 154d0 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 154d4 x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 154d8 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 15518 x25: .cfa -144 + ^ x26: .cfa -136 + ^
STACK CFI 1551c x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 1552c x25: x25 x26: x26
STACK CFI 15534 x27: x27 x28: x28
STACK CFI 15538 x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 15550 x25: x25 x26: x26
STACK CFI 15554 x27: x27 x28: x28
STACK CFI INIT 15560 260 .cfa: sp 0 + .ra: x30
STACK CFI 15564 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 15574 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 15580 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1558c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 156b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 156b8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 17400 148 .cfa: sp 0 + .ra: x30
STACK CFI 17404 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17414 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 17498 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 174ac .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 157c0 360 .cfa: sp 0 + .ra: x30
STACK CFI 157c4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 157cc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 157d8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1583c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 15840 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 15908 x23: .cfa -32 + ^
STACK CFI 15974 x23: x23
STACK CFI 159dc x23: .cfa -32 + ^
STACK CFI 15a48 x23: x23
STACK CFI 15a8c x23: .cfa -32 + ^
STACK CFI 15ac0 x23: x23
STACK CFI 15af0 x23: .cfa -32 + ^
STACK CFI 15b00 x23: x23
STACK CFI INIT 15b20 2dc .cfa: sp 0 + .ra: x30
STACK CFI 15b24 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 15b64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 15b68 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 15b6c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 15b78 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 15bf4 x21: x21 x22: x22
STACK CFI 15bfc x19: x19 x20: x20
STACK CFI 15c00 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 15dd8 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 15ddc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 15de0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 8510 1d8 .cfa: sp 0 + .ra: x30
STACK CFI 8514 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8524 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 863c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8648 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 17550 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 86f0 24 .cfa: sp 0 + .ra: x30
STACK CFI 86f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 870c .cfa: sp 0 + .ra: .ra x29: x29
