MODULE Linux arm64 04D21B45A7335F80CFCCAF47D3D8461A0 libauth-samba4.so.0
INFO CODE_ID 451BD20433A7805FCFCCAF47D3D8461A3837A36C
PUBLIC 8e40 0 auth3_check_password_recv
PUBLIC a200 0 smb_register_auth
PUBLIC a424 0 auth_get_ntlm_challenge
PUBLIC a590 0 auth3_get_challenge
PUBLIC a5e0 0 auth3_context_set_challenge
PUBLIC a650 0 auth3_set_challenge
PUBLIC a760 0 auth_check_password_session_info
PUBLIC a930 0 netsamlogon_cache_init
PUBLIC ab70 0 netsamlogon_clear_cached_user
PUBLIC acd0 0 netsamlogon_cache_get
PUBLIC aff0 0 netsamlogon_cache_store
PUBLIC b3e4 0 netsamlogon_cache_have
PUBLIC b4f0 0 netsamlog_cache_for_all
PUBLIC b614 0 user_in_netgroup
PUBLIC b8d4 0 auth_unix_init
PUBLIC b900 0 auth_sam_init
PUBLIC b964 0 auth3_user_info_dc_add_hints
PUBLIC bb70 0 session_info_set_session_key
PUBLIC bbe0 0 make_session_info_guest
PUBLIC bc24 0 make_session_info_anonymous
PUBLIC bc80 0 make_session_info_system
PUBLIC bce0 0 get_session_info_system
PUBLIC bd00 0 smb_getpwnam
PUBLIC bf24 0 is_trusted_domain
PUBLIC c020 0 session_extract_session_key
PUBLIC c0a0 0 make_server_info
PUBLIC c370 0 make_server_info_guest
PUBLIC c3c0 0 do_map_to_guest_server_info
PUBLIC c510 0 make_server_info_anonymous
PUBLIC c580 0 serverinfo_to_SamInfo2
PUBLIC c670 0 serverinfo_to_SamInfo3
PUBLIC c764 0 serverinfo_to_SamInfo6
PUBLIC c910 0 create_info3_from_pac_logon_info
PUBLIC c9d4 0 create_info6_from_pac
PUBLIC cb50 0 samu_to_SamInfo3
PUBLIC d020 0 passwd_to_SamInfo3
PUBLIC d3a0 0 make_server_info_pw
PUBLIC d4d0 0 make_server_info_sam
PUBLIC d790 0 make_user_info
PUBLIC db74 0 make_user_info_for_reply
PUBLIC dd80 0 make_user_info_for_reply_enc
PUBLIC def0 0 make_user_info_guest
PUBLIC df54 0 nt_token_check_sid
PUBLIC df94 0 nt_token_check_domain_rid
PUBLIC e0b0 0 auth_check_ntlm_password
PUBLIC ed50 0 make_auth3_context_for_ntlm
PUBLIC eef0 0 make_auth4_context
PUBLIC f040 0 auth_generic_prepare
PUBLIC f424 0 make_auth3_context_for_netlogon
PUBLIC f4f0 0 make_auth3_context_for_winbind
PUBLIC f5c0 0 user_in_list
PUBLIC f7e0 0 map_username
PUBLIC 10060 0 get_user_from_kerberos_info
PUBLIC 10470 0 make_user_info_map
PUBLIC 105d0 0 auth3_check_password_send
PUBLIC 10a64 0 make_user_info_netlogon_network
PUBLIC 10be0 0 make_user_info_netlogon_interactive
PUBLIC 10ee4 0 make_server_info_info3
PUBLIC 115b4 0 make_server_info_wbcAuthUserInfo
PUBLIC 11690 0 check_sam_security
PUBLIC 12dc4 0 check_sam_security_info3
PUBLIC 13cf0 0 init_guest_session_info
PUBLIC 13fb0 0 reinit_guest_session_info
PUBLIC 14094 0 init_system_session_info
PUBLIC 14814 0 create_local_token
PUBLIC 14844 0 make_session_info_krb5
PUBLIC 14b60 0 auth3_generate_session_info
PUBLIC 15360 0 make_session_info_from_username
PUBLIC 16d30 0 add_aliases
PUBLIC 16ee4 0 get_user_sid_info3_and_extra
PUBLIC 16fc0 0 debug_unix_user_token
PUBLIC 17154 0 smb_pam_claim_session
PUBLIC 17260 0 smb_pam_close_session
PUBLIC 17360 0 smb_pam_accountcheck
PUBLIC 174a0 0 smb_pam_passcheck
PUBLIC 17bb0 0 smb_pam_passchange
PUBLIC 17fe0 0 pass_check
PUBLIC 18210 0 finalize_local_nt_token
PUBLIC 188f0 0 create_local_nt_token_from_info3
PUBLIC 18da0 0 create_local_nt_token
PUBLIC 19030 0 get_root_nt_token
PUBLIC 19a20 0 create_token_from_username
PUBLIC 19c10 0 user_in_group_sid
PUBLIC 19d40 0 user_in_group
PUBLIC 19e60 0 user_sid_in_group_sid
PUBLIC 1a180 0 auth_builtin_init
PUBLIC 1a1b4 0 auth_winbind_init
STACK CFI INIT 8c30 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8c60 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8ca0 48 .cfa: sp 0 + .ra: x30
STACK CFI 8ca4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8cac x19: .cfa -16 + ^
STACK CFI 8ce4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8cf0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8d00 60 .cfa: sp 0 + .ra: x30
STACK CFI 8d08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8d18 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8d60 70 .cfa: sp 0 + .ra: x30
STACK CFI 8d68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8d74 x19: .cfa -16 + ^
STACK CFI 8db0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8db8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8dc8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8dd0 70 .cfa: sp 0 + .ra: x30
STACK CFI 8dd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8de4 x19: .cfa -16 + ^
STACK CFI 8e20 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8e28 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8e38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8e40 114 .cfa: sp 0 + .ra: x30
STACK CFI 8e48 .cfa: sp 96 +
STACK CFI 8e54 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8e5c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8e64 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8e70 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8e7c x25: .cfa -16 + ^
STACK CFI 8f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 8f48 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8f54 d0 .cfa: sp 0 + .ra: x30
STACK CFI 8f5c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8f6c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8fe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8fe8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9024 20c .cfa: sp 0 + .ra: x30
STACK CFI 902c .cfa: sp 464 +
STACK CFI 9038 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 904c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 909c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 90a4 .cfa: sp 464 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 90ac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 90cc x21: x21 x22: x22
STACK CFI 90d0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 90e4 x23: .cfa -16 + ^
STACK CFI 9150 x21: x21 x22: x22
STACK CFI 9154 x23: x23
STACK CFI 9158 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: x23
STACK CFI 9168 x21: x21 x22: x22
STACK CFI 9170 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 9174 x23: x23
STACK CFI 9178 x23: .cfa -16 + ^
STACK CFI 91d0 x23: x23
STACK CFI 91d4 x21: x21 x22: x22
STACK CFI 91d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 91dc x23: .cfa -16 + ^
STACK CFI 91e0 x23: x23
STACK CFI INIT 9230 94 .cfa: sp 0 + .ra: x30
STACK CFI 9238 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9240 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9248 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9264 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 926c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 92b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 92c4 118 .cfa: sp 0 + .ra: x30
STACK CFI 92cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 92d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9328 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9330 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 9380 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9388 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 93e0 118 .cfa: sp 0 + .ra: x30
STACK CFI 93e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 93f0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9444 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 944c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 949c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 94a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 9500 24 .cfa: sp 0 + .ra: x30
STACK CFI 950c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 951c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9524 3c .cfa: sp 0 + .ra: x30
STACK CFI 952c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9534 x19: .cfa -16 + ^
STACK CFI 9558 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 9560 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 9568 .cfa: sp 288 +
STACK CFI 9574 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 957c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9598 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 9618 .cfa: sp 288 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 9628 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 9650 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9698 x21: x21 x22: x22
STACK CFI 972c x25: x25 x26: x26
STACK CFI 9730 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 974c x25: x25 x26: x26
STACK CFI 9754 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 9758 x25: x25 x26: x26
STACK CFI 975c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 9784 x25: x25 x26: x26
STACK CFI 978c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 97f8 x25: x25 x26: x26
STACK CFI 97fc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9800 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 9804 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 980c .cfa: sp 192 +
STACK CFI 9818 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9828 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9830 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 983c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9860 x27: .cfa -16 + ^
STACK CFI 98f8 x27: x27
STACK CFI 9930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 9938 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 99a0 x27: x27
STACK CFI 99c4 x27: .cfa -16 + ^
STACK CFI 99c8 x27: x27
STACK CFI 99cc x27: .cfa -16 + ^
STACK CFI 9a1c x27: x27
STACK CFI 9a70 x27: .cfa -16 + ^
STACK CFI 9ab8 x27: x27
STACK CFI 9af4 x27: .cfa -16 + ^
STACK CFI INIT 9b00 c4 .cfa: sp 0 + .ra: x30
STACK CFI 9b08 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9b10 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9b1c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9b28 x25: .cfa -16 + ^
STACK CFI 9b34 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9b98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 9ba0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 9bbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 9bc4 28c .cfa: sp 0 + .ra: x30
STACK CFI 9bcc .cfa: sp 352 +
STACK CFI 9bd8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9be0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 9be8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 9c24 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9c2c x25: .cfa -16 + ^
STACK CFI 9d10 x19: x19 x20: x20
STACK CFI 9d14 x25: x25
STACK CFI 9d40 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9d48 .cfa: sp 352 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 9d4c x19: x19 x20: x20
STACK CFI 9d50 x25: x25
STACK CFI 9db8 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^
STACK CFI 9e0c x19: x19 x20: x20 x25: x25
STACK CFI 9e48 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9e4c x25: .cfa -16 + ^
STACK CFI INIT 9e50 150 .cfa: sp 0 + .ra: x30
STACK CFI 9e58 .cfa: sp 112 +
STACK CFI 9e64 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9e6c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 9e74 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9e90 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 9eb8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9ec4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9f3c x25: x25 x26: x26
STACK CFI 9f40 x27: x27 x28: x28
STACK CFI 9f70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 9f78 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 9f7c x25: x25 x26: x26
STACK CFI 9f84 x27: x27 x28: x28
STACK CFI 9f98 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9f9c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 9fa0 25c .cfa: sp 0 + .ra: x30
STACK CFI 9fa8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9fb8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9fc4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9fd4 .cfa: sp 656 + x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI a078 .cfa: sp 64 +
STACK CFI a088 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a090 .cfa: sp 656 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT a200 224 .cfa: sp 0 + .ra: x30
STACK CFI a208 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a210 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a220 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI a244 x21: x21 x22: x22
STACK CFI a248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a250 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI a2a4 x21: x21 x22: x22
STACK CFI a2a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a2b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a2cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a2d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI a328 x21: x21 x22: x22
STACK CFI a32c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a334 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI a340 x21: x21 x22: x22
STACK CFI a37c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT a424 164 .cfa: sp 0 + .ra: x30
STACK CFI a42c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a438 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a48c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a494 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a4d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a4dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI a4e0 x21: .cfa -16 + ^
STACK CFI a50c x21: x21
STACK CFI a510 x21: .cfa -16 + ^
STACK CFI a538 x21: x21
STACK CFI INIT a590 48 .cfa: sp 0 + .ra: x30
STACK CFI a598 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a5ac x19: .cfa -16 + ^
STACK CFI a5d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT a5e0 6c .cfa: sp 0 + .ra: x30
STACK CFI a5e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a5f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a630 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a638 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a644 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT a650 10c .cfa: sp 0 + .ra: x30
STACK CFI a658 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a664 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI a6a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a6ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI a6e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI a6f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT a760 1c8 .cfa: sp 0 + .ra: x30
STACK CFI a768 .cfa: sp 128 +
STACK CFI a774 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI a77c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI a784 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI a78c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI a7f0 x25: .cfa -16 + ^
STACK CFI a8b0 x25: x25
STACK CFI a8e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI a8ec .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI a924 x25: .cfa -16 + ^
STACK CFI INIT a930 23c .cfa: sp 0 + .ra: x30
STACK CFI a938 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI a940 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a97c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI aa10 x21: x21 x22: x22
STACK CFI aa1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI aa24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI aa38 x21: x21 x22: x22
STACK CFI aa3c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ab64 x21: x21 x22: x22
STACK CFI INIT ab70 158 .cfa: sp 0 + .ra: x30
STACK CFI ab78 .cfa: sp 240 +
STACK CFI ab84 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ab8c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI abfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ac04 .cfa: sp 240 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI ac64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ac74 .cfa: sp 240 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT acd0 31c .cfa: sp 0 + .ra: x30
STACK CFI acd8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI acec .cfa: sp 592 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ad0c x23: .cfa -16 + ^
STACK CFI ad1c x21: .cfa -32 + ^
STACK CFI ad20 x22: .cfa -24 + ^
STACK CFI ad24 x24: .cfa -8 + ^
STACK CFI ade4 x21: x21
STACK CFI ade8 x22: x22
STACK CFI adec x23: x23
STACK CFI adf0 x24: x24
STACK CFI ae10 .cfa: sp 64 +
STACK CFI ae1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI ae24 .cfa: sp 592 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI ae5c x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI ae70 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI aed0 x21: x21
STACK CFI aed8 x22: x22
STACK CFI aedc x23: x23
STACK CFI aee0 x24: x24
STACK CFI af24 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI afc8 x21: x21
STACK CFI afcc x22: x22
STACK CFI afd0 x23: x23
STACK CFI afd4 x24: x24
STACK CFI afdc x21: .cfa -32 + ^
STACK CFI afe0 x22: .cfa -24 + ^
STACK CFI afe4 x23: .cfa -16 + ^
STACK CFI afe8 x24: .cfa -8 + ^
STACK CFI INIT aff0 3f4 .cfa: sp 0 + .ra: x30
STACK CFI aff8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b008 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b014 .cfa: sp 688 + x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b054 x23: .cfa -32 + ^
STACK CFI b05c x24: .cfa -24 + ^
STACK CFI b15c x23: x23
STACK CFI b164 x24: x24
STACK CFI b198 .cfa: sp 80 +
STACK CFI b1a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b1b0 .cfa: sp 688 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI b1c8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b1ec x23: x23
STACK CFI b1f4 x24: x24
STACK CFI b1f8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b264 x23: x23
STACK CFI b268 x24: x24
STACK CFI b26c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b2e4 x23: x23 x24: x24
STACK CFI b324 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b36c x23: x23
STACK CFI b370 x24: x24
STACK CFI b374 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b378 x25: .cfa -16 + ^
STACK CFI b3a4 x25: x25
STACK CFI b3a8 x25: .cfa -16 + ^
STACK CFI b3d0 x25: x25
STACK CFI b3d4 x23: x23 x24: x24
STACK CFI b3d8 x23: .cfa -32 + ^
STACK CFI b3dc x24: .cfa -24 + ^
STACK CFI b3e0 x25: .cfa -16 + ^
STACK CFI INIT b3e4 10c .cfa: sp 0 + .ra: x30
STACK CFI b3ec .cfa: sp 240 +
STACK CFI b3f8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b400 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b47c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b484 .cfa: sp 240 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT b4f0 124 .cfa: sp 0 + .ra: x30
STACK CFI b4f8 .cfa: sp 64 +
STACK CFI b504 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b50c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b5a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b5ac .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT b614 2c0 .cfa: sp 0 + .ra: x30
STACK CFI b61c .cfa: sp 320 +
STACK CFI b628 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b630 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b63c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI b70c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI b714 .cfa: sp 320 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT b8d4 2c .cfa: sp 0 + .ra: x30
STACK CFI b8dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b8e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b900 64 .cfa: sp 0 + .ra: x30
STACK CFI b908 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b958 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b964 208 .cfa: sp 0 + .ra: x30
STACK CFI b96c .cfa: sp 160 +
STACK CFI b980 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b990 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI b998 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI b9a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b9b0 x25: .cfa -16 + ^
STACK CFI ba94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI ba9c .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT bb70 70 .cfa: sp 0 + .ra: x30
STACK CFI bb78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI bb80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI bb90 x21: .cfa -16 + ^
STACK CFI bbd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT bbe0 44 .cfa: sp 0 + .ra: x30
STACK CFI bbe8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bbf4 x19: .cfa -16 + ^
STACK CFI bc18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT bc24 5c .cfa: sp 0 + .ra: x30
STACK CFI bc2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bc38 x19: .cfa -16 + ^
STACK CFI bc60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bc6c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI bc78 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT bc80 5c .cfa: sp 0 + .ra: x30
STACK CFI bc88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bc94 x19: .cfa -16 + ^
STACK CFI bcbc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bcc8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI bcd4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT bce0 20 .cfa: sp 0 + .ra: x30
STACK CFI bce8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bcf4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bd00 224 .cfa: sp 0 + .ra: x30
STACK CFI bd08 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI bd14 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI bd20 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI bdb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI bdbc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT bf24 fc .cfa: sp 0 + .ra: x30
STACK CFI bf2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bf34 x19: .cfa -16 + ^
STACK CFI bf60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bf68 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI bfe0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI bfe8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT c020 80 .cfa: sp 0 + .ra: x30
STACK CFI c028 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c054 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c060 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c068 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c074 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI c07c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI c088 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT c0a0 84 .cfa: sp 0 + .ra: x30
STACK CFI c0a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c0bc x19: .cfa -16 + ^
STACK CFI c0dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c0e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT c124 248 .cfa: sp 0 + .ra: x30
STACK CFI c12c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c134 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c13c x21: .cfa -16 + ^
STACK CFI c1f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c200 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT c370 4c .cfa: sp 0 + .ra: x30
STACK CFI c37c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c388 x19: .cfa -16 + ^
STACK CFI c3b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c3c0 150 .cfa: sp 0 + .ra: x30
STACK CFI c3c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c3d4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c3f0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI c430 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI c438 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI c478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI c480 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT c510 68 .cfa: sp 0 + .ra: x30
STACK CFI c524 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c52c x19: .cfa -16 + ^
STACK CFI c558 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c564 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI c570 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c580 ec .cfa: sp 0 + .ra: x30
STACK CFI c588 .cfa: sp 64 +
STACK CFI c594 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c59c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c5d0 x21: .cfa -16 + ^
STACK CFI c5f4 x21: x21
STACK CFI c620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c628 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI c664 x21: x21
STACK CFI c668 x21: .cfa -16 + ^
STACK CFI INIT c670 f4 .cfa: sp 0 + .ra: x30
STACK CFI c678 .cfa: sp 64 +
STACK CFI c684 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c68c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c6c0 x21: .cfa -16 + ^
STACK CFI c6e4 x21: x21
STACK CFI c718 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c720 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI c75c x21: x21
STACK CFI c760 x21: .cfa -16 + ^
STACK CFI INIT c764 1a4 .cfa: sp 0 + .ra: x30
STACK CFI c76c .cfa: sp 64 +
STACK CFI c778 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c780 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c7a8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c82c x21: x21 x22: x22
STACK CFI c858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI c860 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI c868 x21: x21 x22: x22
STACK CFI c888 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c8c0 x21: x21 x22: x22
STACK CFI c8f8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c8fc x21: x21 x22: x22
STACK CFI c904 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT c910 c4 .cfa: sp 0 + .ra: x30
STACK CFI c918 .cfa: sp 64 +
STACK CFI c924 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c92c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c934 x21: .cfa -16 + ^
STACK CFI c9ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c9b4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT c9d4 17c .cfa: sp 0 + .ra: x30
STACK CFI c9dc .cfa: sp 80 +
STACK CFI c9e8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c9f0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c9f8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI ca04 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI cae8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI caf0 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT cb50 4cc .cfa: sp 0 + .ra: x30
STACK CFI cb58 .cfa: sp 208 +
STACK CFI cb68 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI cb70 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI cb78 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI cb80 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI cb8c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI cb98 x27: .cfa -16 + ^
STACK CFI ce7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI ce84 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT d020 380 .cfa: sp 0 + .ra: x30
STACK CFI d028 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI d038 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI d040 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI d04c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI d05c .cfa: sp 944 + x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI d0ec .cfa: sp 96 +
STACK CFI d104 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI d10c .cfa: sp 944 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI d1e8 x27: .cfa -16 + ^
STACK CFI d288 x27: x27
STACK CFI d2a4 x27: .cfa -16 + ^
STACK CFI d2a8 x27: x27
STACK CFI d2f4 x27: .cfa -16 + ^
STACK CFI d30c x27: x27
STACK CFI d314 x27: .cfa -16 + ^
STACK CFI d318 x27: x27
STACK CFI d31c x27: .cfa -16 + ^
STACK CFI d320 x27: x27
STACK CFI d32c x27: .cfa -16 + ^
STACK CFI INIT d3a0 12c .cfa: sp 0 + .ra: x30
STACK CFI d3a8 .cfa: sp 96 +
STACK CFI d3b4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d3bc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d3c4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d3d0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d420 x25: .cfa -16 + ^
STACK CFI d458 x25: x25
STACK CFI d4a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI d4a8 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI d4ac x25: x25
STACK CFI d4c8 x25: .cfa -16 + ^
STACK CFI INIT d4d0 2c0 .cfa: sp 0 + .ra: x30
STACK CFI d4d8 .cfa: sp 96 +
STACK CFI d4e4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d4ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d4f8 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d504 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI d648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI d650 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT d790 3e4 .cfa: sp 0 + .ra: x30
STACK CFI d798 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI d7a0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d7b0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d7bc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d7c4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI da14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI da1c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI da48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI da50 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT db74 204 .cfa: sp 0 + .ra: x30
STACK CFI db7c .cfa: sp 224 +
STACK CFI db88 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI db90 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI db98 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI dba4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI dbb0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI dbbc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI dcf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI dd00 .cfa: sp 224 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT dd80 16c .cfa: sp 0 + .ra: x30
STACK CFI dd88 .cfa: sp 144 +
STACK CFI dd8c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI dd94 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI dda0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI ddac x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI ddb8 x25: .cfa -16 + ^
STACK CFI de60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI de68 .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI de98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI dea0 .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT def0 64 .cfa: sp 0 + .ra: x30
STACK CFI def8 .cfa: sp 80 +
STACK CFI df0c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI df4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT df54 40 .cfa: sp 0 + .ra: x30
STACK CFI df5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI df74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI df80 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI df84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT df94 11c .cfa: sp 0 + .ra: x30
STACK CFI df9c .cfa: sp 144 +
STACK CFI dfa8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI dfb4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI dfbc x23: .cfa -16 + ^
STACK CFI e03c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI e044 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT e0b0 84c .cfa: sp 0 + .ra: x30
STACK CFI e0b8 .cfa: sp 256 +
STACK CFI e0c8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e0d0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI e0dc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e0e8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI e14c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI e154 .cfa: sp 256 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI e168 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e204 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e2c0 x27: x27 x28: x28
STACK CFI e3b0 x23: x23 x24: x24
STACK CFI e3b4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e3f0 x23: x23 x24: x24
STACK CFI e3f4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e4b0 x23: x23 x24: x24
STACK CFI e4b4 x27: x27 x28: x28
STACK CFI e4b8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e4bc x23: x23 x24: x24
STACK CFI e4c0 x27: x27 x28: x28
STACK CFI e4c4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e4f4 x27: x27 x28: x28
STACK CFI e530 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e59c x27: x27 x28: x28
STACK CFI e67c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e724 x27: x27 x28: x28
STACK CFI e77c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e844 x27: x27 x28: x28
STACK CFI e878 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e898 x27: x27 x28: x28
STACK CFI e89c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI e8a4 x27: x27 x28: x28
STACK CFI e8ac x23: x23 x24: x24
STACK CFI e8b0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e8b4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT e900 450 .cfa: sp 0 + .ra: x30
STACK CFI e908 .cfa: sp 128 +
STACK CFI e914 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI e91c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI e928 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI e96c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI e9a0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI e9b4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ead4 x23: x23 x24: x24
STACK CFI ead8 x27: x27 x28: x28
STACK CFI eaf0 x21: x21 x22: x22
STACK CFI eb20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI eb28 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI ec44 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI ec50 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ec54 x23: x23 x24: x24
STACK CFI ec58 x27: x27 x28: x28
STACK CFI ec80 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ed0c x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI ed40 x21: x21 x22: x22
STACK CFI ed44 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI ed48 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI ed4c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT ed50 19c .cfa: sp 0 + .ra: x30
STACK CFI ed58 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI ed60 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI ed68 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI ed70 x23: .cfa -16 + ^
STACK CFI edd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ede0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI ee1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI ee24 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT eef0 150 .cfa: sp 0 + .ra: x30
STACK CFI eef8 .cfa: sp 64 +
STACK CFI ef08 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI ef14 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI ef38 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ef7c x19: x19 x20: x20
STACK CFI efa4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI efac .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI efe8 x19: x19 x20: x20
STACK CFI eff0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f008 x19: x19 x20: x20
STACK CFI f01c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f034 x19: x19 x20: x20
STACK CFI f03c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT f040 3e4 .cfa: sp 0 + .ra: x30
STACK CFI f048 .cfa: sp 144 +
STACK CFI f054 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f060 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI f06c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI f074 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI f168 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI f170 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI f17c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f2fc x27: x27 x28: x28
STACK CFI f310 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f350 x27: x27 x28: x28
STACK CFI f358 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f36c x27: x27 x28: x28
STACK CFI f374 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI f418 x27: x27 x28: x28
STACK CFI f420 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT f424 c4 .cfa: sp 0 + .ra: x30
STACK CFI f42c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f434 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI f46c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f474 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI f490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f49c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT f4f0 d0 .cfa: sp 0 + .ra: x30
STACK CFI f4f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f500 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI f53c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f544 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI f560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f56c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI f580 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI f58c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT f5c0 220 .cfa: sp 0 + .ra: x30
STACK CFI f5d0 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI f5d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI f5e4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI f618 x23: .cfa -16 + ^
STACK CFI f698 x23: x23
STACK CFI f69c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f6a4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI f6dc x23: x23
STACK CFI f6ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI f6f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI f7a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI f7a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT f7e0 87c .cfa: sp 0 + .ra: x30
STACK CFI f7e8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI f7f8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI f804 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI f810 .cfa: sp 672 + x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI f860 x25: .cfa -32 + ^
STACK CFI f86c x26: .cfa -24 + ^
STACK CFI f87c x27: .cfa -16 + ^
STACK CFI f894 x28: .cfa -8 + ^
STACK CFI fa74 x25: x25
STACK CFI fa78 x26: x26
STACK CFI fa7c x27: x27
STACK CFI fa80 x28: x28
STACK CFI faa8 .cfa: sp 96 +
STACK CFI fabc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI fac4 .cfa: sp 672 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI fbac x25: x25
STACK CFI fbb0 x26: x26
STACK CFI fbb4 x27: x27
STACK CFI fbb8 x28: x28
STACK CFI fbbc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI fc40 x25: x25
STACK CFI fc48 x26: x26
STACK CFI fc4c x27: x27
STACK CFI fc50 x28: x28
STACK CFI fc54 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI fc58 x25: x25
STACK CFI fc60 x26: x26
STACK CFI fc64 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI fca8 x25: x25
STACK CFI fcac x26: x26
STACK CFI fcb0 x27: x27
STACK CFI fcb4 x28: x28
STACK CFI fcb8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI fcbc x25: x25
STACK CFI fcc0 x26: x26
STACK CFI fcc4 x27: x27
STACK CFI fcc8 x28: x28
STACK CFI fccc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI fdf0 x25: x25
STACK CFI fdf8 x26: x26
STACK CFI fdfc x27: x27
STACK CFI fe00 x28: x28
STACK CFI fe04 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ff98 x25: x25
STACK CFI ff9c x26: x26
STACK CFI ffa0 x27: x27
STACK CFI ffa4 x28: x28
STACK CFI ffa8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI ffe4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI ffe8 x25: .cfa -32 + ^
STACK CFI ffec x26: .cfa -24 + ^
STACK CFI fff0 x27: .cfa -16 + ^
STACK CFI fff4 x28: .cfa -8 + ^
STACK CFI 1004c x25: x25
STACK CFI 10050 x26: x26
STACK CFI 10054 x27: x27
STACK CFI 10058 x28: x28
STACK CFI INIT 10060 40c .cfa: sp 0 + .ra: x30
STACK CFI 10068 .cfa: sp 144 +
STACK CFI 10074 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1007c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 10084 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 10090 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1009c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 100a8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10248 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10250 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 10470 160 .cfa: sp 0 + .ra: x30
STACK CFI 10478 .cfa: sp 208 +
STACK CFI 10484 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 10494 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 104a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 104a8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 104b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1057c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10584 .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 105d0 494 .cfa: sp 0 + .ra: x30
STACK CFI 105d8 .cfa: sp 160 +
STACK CFI 105e4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 105ec x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 105fc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10604 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 106fc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 10834 x25: x25 x26: x26
STACK CFI 10868 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 10870 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 10884 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 10910 x25: x25 x26: x26
STACK CFI 10914 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 10a5c x25: x25 x26: x26
STACK CFI 10a60 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 10a64 174 .cfa: sp 0 + .ra: x30
STACK CFI 10a6c .cfa: sp 208 +
STACK CFI 10a78 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 10a80 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 10a94 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 10aa0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 10aac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 10ab8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10bb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10bbc .cfa: sp 208 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 10be0 304 .cfa: sp 0 + .ra: x30
STACK CFI 10be8 .cfa: sp 288 +
STACK CFI 10bf4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 10bfc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 10c08 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 10c14 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 10c20 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 10c2c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 10db8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 10dc0 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 10ee4 6d0 .cfa: sp 0 + .ra: x30
STACK CFI 10eec .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 10efc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 10f04 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 10f10 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 10f1c .cfa: sp 560 + x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 10f88 .cfa: sp 96 +
STACK CFI 10fa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 10fa8 .cfa: sp 560 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 10fc8 x27: .cfa -16 + ^
STACK CFI 10fd0 x28: .cfa -8 + ^
STACK CFI 11030 x27: x27
STACK CFI 11034 x28: x28
STACK CFI 11038 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11184 x27: x27
STACK CFI 11188 x28: x28
STACK CFI 11190 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 111d8 x27: x27
STACK CFI 111e0 x28: x28
STACK CFI 111e4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11218 x27: x27
STACK CFI 11220 x28: x28
STACK CFI 11224 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11274 x27: x27
STACK CFI 11278 x28: x28
STACK CFI 1127c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 112f8 x27: x27
STACK CFI 11300 x28: x28
STACK CFI 11304 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1134c x27: x27
STACK CFI 11350 x28: x28
STACK CFI 11358 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11558 x27: x27 x28: x28
STACK CFI 1155c x27: .cfa -16 + ^
STACK CFI 11560 x28: .cfa -8 + ^
STACK CFI INIT 115b4 d4 .cfa: sp 0 + .ra: x30
STACK CFI 115bc .cfa: sp 368 +
STACK CFI 115c8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 115d0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 115d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 115e4 x23: .cfa -16 + ^
STACK CFI 11670 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 11678 .cfa: sp 368 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 11690 10a8 .cfa: sp 0 + .ra: x30
STACK CFI 11698 .cfa: sp 272 +
STACK CFI 116a4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 116ac x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 116b8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 116c0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 116f8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11758 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 119f4 x27: x27 x28: x28
STACK CFI 11a18 x25: x25 x26: x26
STACK CFI 11a54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 11a5c .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 11a60 x25: x25 x26: x26
STACK CFI 11a64 x27: x27 x28: x28
STACK CFI 11a70 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11aa4 x25: x25 x26: x26
STACK CFI 11aa8 x27: x27 x28: x28
STACK CFI 11aac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 11ad8 x25: x25 x26: x26
STACK CFI 11adc x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11b34 x25: x25 x26: x26
STACK CFI 11b38 x27: x27 x28: x28
STACK CFI 11b3c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11ba8 x25: x25 x26: x26
STACK CFI 11bac x27: x27 x28: x28
STACK CFI 11bb0 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11d08 x27: x27 x28: x28
STACK CFI 11d78 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 124fc x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 12500 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 12504 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 12740 f8 .cfa: sp 0 + .ra: x30
STACK CFI 12758 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12760 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 12788 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 12790 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 12798 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 127c4 x21: x21 x22: x22
STACK CFI 127c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 127d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 127dc x23: .cfa -16 + ^
STACK CFI 12808 x23: x23
STACK CFI 1280c x23: .cfa -16 + ^
STACK CFI 12834 x23: x23
STACK CFI INIT 12840 2cc .cfa: sp 0 + .ra: x30
STACK CFI 12858 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 12860 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1286c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 12890 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 12898 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 1289c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 128b4 x25: .cfa -16 + ^
STACK CFI 1293c x21: x21 x22: x22
STACK CFI 12944 x25: x25
STACK CFI 12948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 1295c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 129dc x21: x21 x22: x22
STACK CFI 129e0 x25: x25
STACK CFI 129e4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 129f8 x21: x21 x22: x22
STACK CFI 129fc x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI 12ab4 x21: x21 x22: x22
STACK CFI 12ab8 x25: x25
STACK CFI 12abc x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^
STACK CFI 12b04 x21: x21 x22: x22
STACK CFI 12b08 x25: x25
STACK CFI INIT 12b10 2b4 .cfa: sp 0 + .ra: x30
STACK CFI 12b28 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 12b30 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 12b3c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 12b64 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 12bec x25: .cfa -16 + ^
STACK CFI 12c0c x25: x25
STACK CFI 12c28 x21: x21 x22: x22
STACK CFI 12c30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 12c38 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 12c58 x21: x21 x22: x22
STACK CFI 12c6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 12c80 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 12ca4 x21: x21 x22: x22
STACK CFI 12cac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 12cb4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 12d18 x21: x21 x22: x22
STACK CFI 12d1c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 12dbc x25: .cfa -16 + ^
STACK CFI 12dc0 x25: x25
STACK CFI INIT 12dc4 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 12dcc .cfa: sp 80 +
STACK CFI 12dd8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12de0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 12dec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12df4 x23: .cfa -16 + ^
STACK CFI 12eb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 12ec0 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 12f74 1a4 .cfa: sp 0 + .ra: x30
STACK CFI 12f7c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 12f88 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 12f90 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 12f98 x23: .cfa -16 + ^
STACK CFI 1305c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 13064 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 130cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 130d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 13120 bc8 .cfa: sp 0 + .ra: x30
STACK CFI 13128 .cfa: sp 464 +
STACK CFI 13134 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1313c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 13144 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1314c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 13158 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 13160 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 13340 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 13348 .cfa: sp 464 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 13cf0 2c0 .cfa: sp 0 + .ra: x30
STACK CFI 13cf8 .cfa: sp 80 +
STACK CFI 13d04 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 13d0c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 13d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 13d60 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 13d64 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 13d7c x23: .cfa -16 + ^
STACK CFI 13e18 x21: x21 x22: x22
STACK CFI 13e1c x23: x23
STACK CFI 13e20 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 13e24 x21: x21 x22: x22
STACK CFI 13e28 x23: x23
STACK CFI 13e2c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 13e8c x23: x23
STACK CFI 13e94 x21: x21 x22: x22
STACK CFI 13e98 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 13fa4 x21: x21 x22: x22 x23: x23
STACK CFI 13fa8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 13fac x23: .cfa -16 + ^
STACK CFI INIT 13fb0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 13fb8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 13fc0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13fcc x21: .cfa -16 + ^
STACK CFI 14040 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 14048 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 14094 210 .cfa: sp 0 + .ra: x30
STACK CFI 1409c .cfa: sp 64 +
STACK CFI 140a8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 140b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 140fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14104 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 14108 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 141a0 x21: x21 x22: x22
STACK CFI 141a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 141a8 x21: x21 x22: x22
STACK CFI 141ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1429c x21: x21 x22: x22
STACK CFI 142a0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 142a4 570 .cfa: sp 0 + .ra: x30
STACK CFI 142ac .cfa: sp 288 +
STACK CFI 142b8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 142c0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 142cc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 142d8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 142e0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1439c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 143a4 .cfa: sp 288 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 14814 30 .cfa: sp 0 + .ra: x30
STACK CFI 1481c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14828 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 14830 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 14838 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 14844 318 .cfa: sp 0 + .ra: x30
STACK CFI 1484c .cfa: sp 96 +
STACK CFI 14858 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 14860 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 14868 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14874 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 14908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 14910 .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 14924 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 149a0 x25: x25 x26: x26
STACK CFI 149a8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 149d0 x25: x25 x26: x26
STACK CFI 14a20 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 14a28 x25: x25 x26: x26
STACK CFI 14a30 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 14a34 x25: x25 x26: x26
STACK CFI 14a88 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 14a9c x25: x25 x26: x26
STACK CFI 14aa0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 14ae0 x25: x25 x26: x26
STACK CFI 14ae4 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 14b54 x25: x25 x26: x26
STACK CFI 14b58 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 14b60 15c .cfa: sp 0 + .ra: x30
STACK CFI 14b68 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 14b74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 14b80 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 14bb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14bc0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 14c00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14c08 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 14c1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 14c24 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 14cc0 69c .cfa: sp 0 + .ra: x30
STACK CFI 14cc8 .cfa: sp 224 +
STACK CFI 14cd4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 14cdc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 14ce4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 14cf0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 14cfc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 14e38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 14e40 .cfa: sp 224 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 15360 110 .cfa: sp 0 + .ra: x30
STACK CFI 15368 .cfa: sp 80 +
STACK CFI 15374 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1537c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 15384 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1538c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1544c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 15454 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 15470 70 .cfa: sp 0 + .ra: x30
STACK CFI 15478 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15484 x19: .cfa -16 + ^
STACK CFI 154c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 154c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 154d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 154e0 70 .cfa: sp 0 + .ra: x30
STACK CFI 154e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 154f4 x19: .cfa -16 + ^
STACK CFI 15530 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 15538 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 15548 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 15550 2d8 .cfa: sp 0 + .ra: x30
STACK CFI 15558 .cfa: sp 288 +
STACK CFI 15564 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1556c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 15574 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1557c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 156a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 156a8 .cfa: sp 288 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 15830 16c .cfa: sp 0 + .ra: x30
STACK CFI 15848 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 15850 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1585c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 15870 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 158f4 x19: x19 x20: x20
STACK CFI 158fc x21: x21 x22: x22
STACK CFI 15908 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 15910 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1591c x19: x19 x20: x20
STACK CFI 15920 x21: x21 x22: x22
STACK CFI 1592c .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 15934 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1594c x19: x19 x20: x20
STACK CFI 15950 x21: x21 x22: x22
STACK CFI 15994 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI INIT 159a0 8c .cfa: sp 0 + .ra: x30
STACK CFI 159a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 159b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 15a18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 15a30 95c .cfa: sp 0 + .ra: x30
STACK CFI 15a38 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 15a40 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 15a4c .cfa: sp 656 + x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 15a9c x21: .cfa -64 + ^
STACK CFI 15aa4 x22: .cfa -56 + ^
STACK CFI 15aa8 x23: .cfa -48 + ^
STACK CFI 15aac x24: .cfa -40 + ^
STACK CFI 15ab0 x25: .cfa -32 + ^
STACK CFI 15ab8 x26: .cfa -24 + ^
STACK CFI 15de8 x21: x21
STACK CFI 15dec x23: x23
STACK CFI 15df0 x24: x24
STACK CFI 15df4 x25: x25
STACK CFI 15df8 x26: x26
STACK CFI 15e00 x22: x22
STACK CFI 15e04 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 15ef8 x21: x21
STACK CFI 15efc x22: x22
STACK CFI 15f00 x23: x23
STACK CFI 15f04 x24: x24
STACK CFI 15f08 x25: x25
STACK CFI 15f0c x26: x26
STACK CFI 15f30 .cfa: sp 96 +
STACK CFI 15f3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 15f44 .cfa: sp 656 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 160dc x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 16114 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 16370 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 16374 x21: .cfa -64 + ^
STACK CFI 16378 x22: .cfa -56 + ^
STACK CFI 1637c x23: .cfa -48 + ^
STACK CFI 16380 x24: .cfa -40 + ^
STACK CFI 16384 x25: .cfa -32 + ^
STACK CFI 16388 x26: .cfa -24 + ^
STACK CFI INIT 16390 94 .cfa: sp 0 + .ra: x30
STACK CFI 16398 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 163a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 163b0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 163fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 16404 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 16424 94 .cfa: sp 0 + .ra: x30
STACK CFI 1642c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16434 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 16440 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 16468 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 16470 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 164c0 108 .cfa: sp 0 + .ra: x30
STACK CFI 164c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 164d0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 16554 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1655c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 165d0 244 .cfa: sp 0 + .ra: x30
STACK CFI 165d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 165e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 165ec x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 165f8 x23: .cfa -16 + ^
STACK CFI 166a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 166a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 166dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 166e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 16814 c8 .cfa: sp 0 + .ra: x30
STACK CFI 1681c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16824 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 16830 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 16854 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1685c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 168a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 168a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 168e0 30c .cfa: sp 0 + .ra: x30
STACK CFI 168e8 .cfa: sp 64 +
STACK CFI 168f8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16904 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1690c x21: .cfa -16 + ^
STACK CFI 16a10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 16a18 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 16bf0 140 .cfa: sp 0 + .ra: x30
STACK CFI 16bf8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16c00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 16c10 x21: .cfa -16 + ^
STACK CFI 16c58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 16c60 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 16c98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 16ca4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 16cec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 16cf8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 16d30 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 16d38 .cfa: sp 160 +
STACK CFI 16d44 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 16d4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 16d54 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 16d90 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 16e14 x23: x23 x24: x24
STACK CFI 16e44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 16e4c .cfa: sp 160 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 16e58 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 16edc x23: x23 x24: x24
STACK CFI 16ee0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 16ee4 d4 .cfa: sp 0 + .ra: x30
STACK CFI 16eec .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16ef4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 16f2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 16f34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 16f3c x21: .cfa -16 + ^
STACK CFI 16f58 x21: x21
STACK CFI 16f5c x21: .cfa -16 + ^
STACK CFI 16f78 x21: x21
STACK CFI 16f7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 16f84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 16fc0 194 .cfa: sp 0 + .ra: x30
STACK CFI 16fc8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 16fd0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 16fd8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 16fe4 x25: .cfa -16 + ^
STACK CFI 17024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI 1702c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 17030 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 17090 x23: x23 x24: x24
STACK CFI 17098 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x29: x29
STACK CFI 170a0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 170a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 170d4 x23: x23 x24: x24
STACK CFI 170d8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 17118 x23: x23 x24: x24
STACK CFI 1711c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 17154 108 .cfa: sp 0 + .ra: x30
STACK CFI 1715c .cfa: sp 80 +
STACK CFI 17168 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 17170 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1717c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 17184 x23: .cfa -16 + ^
STACK CFI 171d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 171dc .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 17260 100 .cfa: sp 0 + .ra: x30
STACK CFI 17268 .cfa: sp 64 +
STACK CFI 17274 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1727c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 17284 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 172d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 172e0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 17360 138 .cfa: sp 0 + .ra: x30
STACK CFI 17368 .cfa: sp 64 +
STACK CFI 17374 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1737c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 17388 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 173d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 173dc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 174a0 70c .cfa: sp 0 + .ra: x30
STACK CFI 174a8 .cfa: sp 96 +
STACK CFI 174b4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 174bc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 174c4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 17544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1754c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 17564 x23: .cfa -16 + ^
STACK CFI 17680 x23: x23
STACK CFI 17684 x23: .cfa -16 + ^
STACK CFI 17ba4 x23: x23
STACK CFI 17ba8 x23: .cfa -16 + ^
STACK CFI INIT 17bb0 42c .cfa: sp 0 + .ra: x30
STACK CFI 17bb8 .cfa: sp 64 +
STACK CFI 17bc4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 17bcc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 17bd8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 17c48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 17c50 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 17fe0 184 .cfa: sp 0 + .ra: x30
STACK CFI 17fe8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 17ff0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 17ff8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 18004 x23: .cfa -16 + ^
STACK CFI 18040 x21: x21 x22: x22
STACK CFI 18048 x23: x23
STACK CFI 18054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1805c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 18068 x21: x21 x22: x22
STACK CFI 1806c x23: x23
STACK CFI 18080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18088 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 18134 x21: x21 x22: x22
STACK CFI 18138 x23: x23
STACK CFI 1813c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 18144 x21: x21 x22: x22
STACK CFI 1814c x23: x23
STACK CFI 18150 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 18158 x21: x21 x22: x22
STACK CFI 18160 x23: x23
STACK CFI INIT 18164 a4 .cfa: sp 0 + .ra: x30
STACK CFI 1816c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 18174 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 18180 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 18188 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 181dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 181e4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 18210 6d8 .cfa: sp 0 + .ra: x30
STACK CFI 18218 .cfa: sp 240 +
STACK CFI 18228 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 18230 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 18238 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 182e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 182e8 .cfa: sp 240 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 18318 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 183c0 x23: x23 x24: x24
STACK CFI 183e8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 18414 x23: x23 x24: x24
STACK CFI 18418 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1848c x23: x23 x24: x24
STACK CFI 18490 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 18494 x23: x23 x24: x24
STACK CFI 18498 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 18518 x23: x23 x24: x24
STACK CFI 18520 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 18610 x23: x23 x24: x24
STACK CFI 18614 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 18700 x23: x23 x24: x24
STACK CFI 18708 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 18720 x23: x23 x24: x24
STACK CFI 18728 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 18854 x23: x23 x24: x24
STACK CFI 18858 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 188f0 4b0 .cfa: sp 0 + .ra: x30
STACK CFI 188f8 .cfa: sp 160 +
STACK CFI 18904 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1890c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 18914 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 18920 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 189ac x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 18a50 x25: x25 x26: x26
STACK CFI 18a84 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 18a8c .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 18aa8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 18ab4 x25: x25 x26: x26
STACK CFI 18adc x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 18b48 x25: x25 x26: x26
STACK CFI 18bb0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 18be4 x25: x25 x26: x26
STACK CFI 18c08 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 18c3c x25: x25 x26: x26
STACK CFI 18c70 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 18ca4 x25: x25 x26: x26
STACK CFI 18cd8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 18cfc x25: x25 x26: x26
STACK CFI 18d04 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 18d28 x25: x25 x26: x26
STACK CFI 18d30 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 18d98 x25: x25 x26: x26
STACK CFI 18d9c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 18da0 290 .cfa: sp 0 + .ra: x30
STACK CFI 18da8 .cfa: sp 288 +
STACK CFI 18db4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 18dbc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 18dcc x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 18dd8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 18f30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 18f38 .cfa: sp 288 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 19030 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 19038 .cfa: sp 208 +
STACK CFI 19044 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1904c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 19054 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 190d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 190d8 .cfa: sp 208 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 191e4 83c .cfa: sp 0 + .ra: x30
STACK CFI 191ec .cfa: sp 496 +
STACK CFI 191f8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 19204 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1920c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 19220 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 19230 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 19510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 19518 .cfa: sp 496 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 19a20 1ec .cfa: sp 0 + .ra: x30
STACK CFI 19a28 .cfa: sp 192 +
STACK CFI 19a34 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 19a3c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 19a44 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 19a50 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 19a5c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 19a68 x27: .cfa -16 + ^
STACK CFI 19b20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 19b28 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 19c10 128 .cfa: sp 0 + .ra: x30
STACK CFI 19c18 .cfa: sp 80 +
STACK CFI 19c24 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 19c2c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 19c34 x21: .cfa -16 + ^
STACK CFI 19cc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 19cd0 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 19d40 11c .cfa: sp 0 + .ra: x30
STACK CFI 19d48 .cfa: sp 144 +
STACK CFI 19d54 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 19d60 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 19d6c x23: .cfa -16 + ^
STACK CFI 19e08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 19e10 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 19e60 20c .cfa: sp 0 + .ra: x30
STACK CFI 19e68 .cfa: sp 288 +
STACK CFI 19e74 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 19e7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 19e84 x21: .cfa -16 + ^
STACK CFI 19f40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 19f48 .cfa: sp 288 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1a070 108 .cfa: sp 0 + .ra: x30
STACK CFI 1a078 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a084 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1a0e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a0ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1a10c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a114 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1a180 34 .cfa: sp 0 + .ra: x30
STACK CFI 1a188 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a1a8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a1b4 2c .cfa: sp 0 + .ra: x30
STACK CFI 1a1bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a1c8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a1e0 520 .cfa: sp 0 + .ra: x30
STACK CFI 1a1e8 .cfa: sp 192 +
STACK CFI 1a1f4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1a1fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1a208 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1a214 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1a350 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1a358 .cfa: sp 192 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 1a700 3c4 .cfa: sp 0 + .ra: x30
STACK CFI 1a708 .cfa: sp 480 +
STACK CFI 1a714 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1a720 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1a738 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1a770 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1a8e0 x25: x25 x26: x26
STACK CFI 1a914 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1a91c .cfa: sp 480 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 1a970 x25: x25 x26: x26
STACK CFI 1a97c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1aabc x25: x25 x26: x26
STACK CFI 1aac0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
