MODULE Linux arm64 E7E0408DA3CF9BA6E45A38C0B8984DD90 libroken-samba4.so.0
INFO CODE_ID 8D40E0E7CFA3A69BE45A38C0B8984DD9E9B7F1D6
PUBLIC 5540 0 rk_base64_encode
PUBLIC 56a0 0 rk_base64_decode
PUBLIC 5834 0 ct_memcmp
PUBLIC 58a0 0 rk_hex_encode
PUBLIC 5950 0 rk_hex_decode
PUBLIC 5af0 0 rk_bswap64
PUBLIC 5b10 0 rk_bswap32
PUBLIC 5b30 0 rk_bswap16
PUBLIC 5b50 0 rk_undumpdata
PUBLIC 5cb0 0 rk_undumptext
PUBLIC 5e30 0 rk_emalloc
PUBLIC 5e80 0 rk_ecalloc
PUBLIC 5ed0 0 free_getarg_strings
PUBLIC 5ef0 0 rk_getauxv
PUBLIC 5f80 0 rk_getauxval
PUBLIC 5fd0 0 rk_injectauxv
PUBLIC 6060 0 get_window_size
PUBLIC 6150 0 issuid
PUBLIC 6344 0 net_read
PUBLIC 63f0 0 net_write
PUBLIC 64a0 0 rk_dumpdata
PUBLIC 6510 0 rk_parse_units
PUBLIC 67c0 0 rk_parse_time
PUBLIC 67e4 0 parse_time
PUBLIC 6810 0 rk_parse_flags
PUBLIC 6ae0 0 rk_unparse_units
PUBLIC 6b00 0 rk_unparse_time
PUBLIC 6b30 0 unparse_time
PUBLIC 6b60 0 rk_unparse_units_approx
PUBLIC 6b80 0 rk_unparse_time_approx
PUBLIC 6bb0 0 unparse_time_approx
PUBLIC 6be0 0 rk_print_units_table
PUBLIC 6d60 0 rk_print_time_table
PUBLIC 6d84 0 print_time_table
PUBLIC 6db0 0 rk_unparse_flags
PUBLIC 6f20 0 rk_print_flags_table
PUBLIC 6fb0 0 parse_units
PUBLIC 6fd0 0 unparse_units
PUBLIC 6ff0 0 unparse_units_approx
PUBLIC 7010 0 print_units_table
PUBLIC 7030 0 parse_flags
PUBLIC 7050 0 unparse_flags
PUBLIC 7070 0 print_flags_table
PUBLIC 7090 0 rk_svis
PUBLIC 7144 0 rk_strsvis
PUBLIC 7230 0 rk_strsvisx
PUBLIC 7344 0 rk_strrasvisx
PUBLIC 7440 0 rk_strrasvis
PUBLIC 74a4 0 rk_strasvisx
PUBLIC 7544 0 rk_strasvis
PUBLIC 75a0 0 rk_vis
PUBLIC 7660 0 rk_strvis
PUBLIC 76f0 0 rk_strvisx
PUBLIC 7794 0 rk_strlwr
PUBLIC 77e0 0 rk_strsep_copy
PUBLIC 7874 0 rk_strupr
PUBLIC 78c0 0 arg_printusage_i18n
PUBLIC 85a4 0 arg_printusage
PUBLIC 85d4 0 rk_strpoolfree
PUBLIC 8610 0 rk_strpoolprintf
PUBLIC 87c0 0 rk_strpoolcollect
PUBLIC 8800 0 rk_estrdup
PUBLIC 8830 0 rk_erealloc
PUBLIC 8880 0 rk_secure_getenv
PUBLIC 88c4 0 rk_wait_for_process_timed
PUBLIC 8a44 0 rk_wait_for_process
PUBLIC 8a70 0 rk_simple_execvp_timed
PUBLIC 8b10 0 rk_simple_execvp
PUBLIC 8b34 0 rk_simple_execve_timed
PUBLIC 8be0 0 rk_simple_execve
PUBLIC 8c04 0 rk_vstrcollect
PUBLIC 8c30 0 rk_pipe_execv
PUBLIC 9010 0 rk_simple_execlp
PUBLIC 90f0 0 rk_simple_execle
PUBLIC 9204 0 rk_strcollect
PUBLIC 92e0 0 rtbl_create
PUBLIC 9300 0 rtbl_set_flags
PUBLIC 9320 0 rtbl_get_flags
PUBLIC 9340 0 rtbl_destroy
PUBLIC 9404 0 rtbl_add_column_by_id
PUBLIC 94c0 0 rtbl_add_column
PUBLIC 94e4 0 rtbl_new_row
PUBLIC 9600 0 rtbl_set_prefix
PUBLIC 9650 0 rtbl_set_separator
PUBLIC 96a0 0 rtbl_set_column_prefix
PUBLIC 9754 0 rtbl_set_column_affix_by_id
PUBLIC 9834 0 rtbl_add_column_entry_by_id
PUBLIC 9894 0 rtbl_add_column_entryv_by_id
PUBLIC 99a0 0 rtbl_add_column_entry
PUBLIC 9a30 0 rtbl_add_column_entryv
PUBLIC 9b40 0 rtbl_format
PUBLIC a050 0 rk_random_init
PUBLIC a070 0 getarg
PUBLIC a7f4 0 rk_cloexec
PUBLIC a810 0 rk_cloexec_file
PUBLIC a830 0 rk_cloexec_dir
PUBLIC a850 0 rk_cloexec_socket
PUBLIC a870 0 rk_clzll
PUBLIC a8b0 0 rk_xfree
PUBLIC a8d0 0 rk_time_add
PUBLIC a940 0 rk_time_sub
PUBLIC a984 0 rk_timevalfix
PUBLIC aa10 0 rk_timevaladd
PUBLIC aa60 0 rk_timevalsub
PUBLIC aab0 0 rk_mergesort_r
PUBLIC b4a0 0 rk_mergesort
PUBLIC b4c4 0 rk_setprogname
PUBLIC b4e0 0 rk_dns_string_to_type
PUBLIC b550 0 rk_dns_type_to_string
PUBLIC b5a0 0 rk_dns_free_data
PUBLIC ba00 0 rk_dns_lookup
PUBLIC ba50 0 rk_dns_srv_order
PUBLIC bcc4 0 rk_socket_set_any
PUBLIC bd34 0 rk_socket_set_address_and_port
PUBLIC bdb0 0 rk_socket_addr_size
PUBLIC bdf0 0 rk_socket_sockaddr_size
PUBLIC be30 0 rk_socket_get_address
PUBLIC be70 0 rk_socket_get_port
PUBLIC beb0 0 rk_socket_set_port
PUBLIC bef0 0 rk_socket_set_portrange
PUBLIC bf10 0 rk_socket_set_debug
PUBLIC bf30 0 rk_socket_set_tos
PUBLIC bf50 0 rk_socket_set_nonblocking
PUBLIC bfb4 0 rk_socket_set_reuseaddr
PUBLIC bfd0 0 rk_socket_set_ipv6only
PUBLIC bff0 0 rk_socket_set_keepalive
PUBLIC c020 0 rk_socket
PUBLIC c330 0 roken_gethostby_setup
PUBLIC c534 0 roken_gethostbyname
PUBLIC c574 0 roken_gethostbyaddr
PUBLIC c5e0 0 roken_get_username
PUBLIC c790 0 roken_get_shell
PUBLIC c950 0 roken_get_homedir
PUBLIC cb00 0 roken_get_appdatadir
PUBLIC cb20 0 roken_get_loginname
STACK CFI INIT 3ef0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f20 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 3f60 48 .cfa: sp 0 + .ra: x30
STACK CFI 3f64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f6c x19: .cfa -16 + ^
STACK CFI 3fa4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3fb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 3fc0 18 .cfa: sp 0 + .ra: x30
STACK CFI 3fc8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3fd0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3fe0 1c .cfa: sp 0 + .ra: x30
STACK CFI 3fec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ff4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4000 24 .cfa: sp 0 + .ra: x30
STACK CFI 4010 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 401c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4024 38 .cfa: sp 0 + .ra: x30
STACK CFI 402c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4040 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 404c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4050 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4060 24 .cfa: sp 0 + .ra: x30
STACK CFI 4068 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 407c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4084 1c .cfa: sp 0 + .ra: x30
STACK CFI 408c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4098 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 40a0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 40a8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 40b0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 40c0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 40cc x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 40d8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 40e0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4154 x19: x19 x20: x20
STACK CFI 4158 x21: x21 x22: x22
STACK CFI 415c x25: x25 x26: x26
STACK CFI 4160 x27: x27 x28: x28
STACK CFI 4168 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI INIT 4170 54 .cfa: sp 0 + .ra: x30
STACK CFI 4178 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 41ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 41b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 41c4 40 .cfa: sp 0 + .ra: x30
STACK CFI 41cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 41d4 x19: .cfa -16 + ^
STACK CFI 41fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4204 e8 .cfa: sp 0 + .ra: x30
STACK CFI 420c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4214 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4220 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 422c x23: .cfa -16 + ^
STACK CFI 42ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 42b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 42f0 148 .cfa: sp 0 + .ra: x30
STACK CFI 42f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4304 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4310 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 431c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4324 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 43d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 43d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4440 68 .cfa: sp 0 + .ra: x30
STACK CFI 4448 .cfa: sp 32 +
STACK CFI 4460 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 44a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 44b0 17c .cfa: sp 0 + .ra: x30
STACK CFI 44b8 .cfa: sp 112 +
STACK CFI 44bc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 44c4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 44cc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 44d8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 44e4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 44ec x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4548 x19: x19 x20: x20
STACK CFI 4554 x25: x25 x26: x26
STACK CFI 4558 x27: x27 x28: x28
STACK CFI 455c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4564 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 45ec x19: x19 x20: x20 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4608 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 461c .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 4630 164 .cfa: sp 0 + .ra: x30
STACK CFI 4638 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4644 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4654 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 4720 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4728 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 475c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4764 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4794 2c8 .cfa: sp 0 + .ra: x30
STACK CFI 479c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 47a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 47ac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 47bc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 486c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 48b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 48c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4a60 d8 .cfa: sp 0 + .ra: x30
STACK CFI 4a68 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4a70 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4a84 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4a90 x23: .cfa -16 + ^
STACK CFI 4aa8 x21: x21 x22: x22
STACK CFI 4aac x23: x23
STACK CFI 4ae0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4ae8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 4b18 x21: x21 x22: x22
STACK CFI 4b20 x23: x23
STACK CFI 4b30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 4b40 138 .cfa: sp 0 + .ra: x30
STACK CFI 4b48 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4b50 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4b60 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4bfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4c04 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4c80 70 .cfa: sp 0 + .ra: x30
STACK CFI 4c88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c90 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4ce0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4cf0 c8 .cfa: sp 0 + .ra: x30
STACK CFI 4cf8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4d00 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4d0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4d90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4d98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4dc0 5d4 .cfa: sp 0 + .ra: x30
STACK CFI 4dc8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4de0 .cfa: sp 1184 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4e40 x27: .cfa -16 + ^
STACK CFI 4e48 x28: .cfa -8 + ^
STACK CFI 4f58 x27: x27
STACK CFI 4f68 x28: x28
STACK CFI 4f88 .cfa: sp 96 +
STACK CFI 4f9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4fa4 .cfa: sp 1184 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 531c x27: x27
STACK CFI 5320 x28: x28
STACK CFI 5328 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5354 x27: x27
STACK CFI 535c x28: x28
STACK CFI 5360 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 537c x27: x27
STACK CFI 5384 x28: x28
STACK CFI 538c x27: .cfa -16 + ^
STACK CFI 5390 x28: .cfa -8 + ^
STACK CFI INIT 5394 6c .cfa: sp 0 + .ra: x30
STACK CFI 539c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 53ac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 53cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 53d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5400 13c .cfa: sp 0 + .ra: x30
STACK CFI 5408 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5410 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 541c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5478 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5480 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5540 160 .cfa: sp 0 + .ra: x30
STACK CFI 5548 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5550 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 555c x21: .cfa -16 + ^
STACK CFI 5668 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5670 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 56a0 194 .cfa: sp 0 + .ra: x30
STACK CFI 56c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5704 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 570c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5818 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5820 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5834 6c .cfa: sp 0 + .ra: x30
STACK CFI 583c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5888 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5890 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5898 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 58a0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 58a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 58b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 58c4 x21: .cfa -16 + ^
STACK CFI 5934 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 593c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5950 19c .cfa: sp 0 + .ra: x30
STACK CFI 5958 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5960 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 596c x21: .cfa -16 + ^
STACK CFI 5a78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5a80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5ad8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5ae0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5af0 1c .cfa: sp 0 + .ra: x30
STACK CFI 5af8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5b04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5b10 1c .cfa: sp 0 + .ra: x30
STACK CFI 5b18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5b24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5b30 1c .cfa: sp 0 + .ra: x30
STACK CFI 5b38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5b44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5b50 158 .cfa: sp 0 + .ra: x30
STACK CFI 5b58 .cfa: sp 192 +
STACK CFI 5b64 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5b6c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5b78 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5c3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5c44 .cfa: sp 192 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5cb0 17c .cfa: sp 0 + .ra: x30
STACK CFI 5cb8 .cfa: sp 208 +
STACK CFI 5cc4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5ccc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5cd8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5d1c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5d7c x23: x23 x24: x24
STACK CFI 5dc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5dc8 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5df8 x23: x23 x24: x24
STACK CFI 5e20 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 5e30 48 .cfa: sp 0 + .ra: x30
STACK CFI 5e38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e40 x19: .cfa -16 + ^
STACK CFI 5e5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 5e64 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5e80 48 .cfa: sp 0 + .ra: x30
STACK CFI 5e88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5e90 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5ea8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5eb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5ed0 1c .cfa: sp 0 + .ra: x30
STACK CFI 5ed8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5ee0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5ef0 8c .cfa: sp 0 + .ra: x30
STACK CFI 5f00 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5f0c x19: .cfa -16 + ^
STACK CFI 5f74 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5f80 50 .cfa: sp 0 + .ra: x30
STACK CFI 5f9c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5fb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 5fb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 5fd0 8c .cfa: sp 0 + .ra: x30
STACK CFI 5fd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5fe0 x19: .cfa -16 + ^
STACK CFI 604c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6054 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6060 f0 .cfa: sp 0 + .ra: x30
STACK CFI 6068 .cfa: sp 64 +
STACK CFI 6074 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 607c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6088 x21: .cfa -16 + ^
STACK CFI 60f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 60fc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6150 1f4 .cfa: sp 0 + .ra: x30
STACK CFI 6158 .cfa: sp 208 +
STACK CFI 6164 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 616c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6198 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 61cc x21: x21 x22: x22
STACK CFI 61f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6200 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 6214 x21: x21 x22: x22
STACK CFI 621c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6220 x23: .cfa -16 + ^
STACK CFI 626c x23: x23
STACK CFI 6278 x23: .cfa -16 + ^
STACK CFI 62d8 x23: x23
STACK CFI 62e4 x23: .cfa -16 + ^
STACK CFI 6300 x23: x23
STACK CFI 6308 x23: .cfa -16 + ^
STACK CFI 6320 x23: x23
STACK CFI 6330 x21: x21 x22: x22
STACK CFI 633c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6340 x23: .cfa -16 + ^
STACK CFI INIT 6344 a4 .cfa: sp 0 + .ra: x30
STACK CFI 634c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6358 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI 6368 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 63a0 x21: x21 x22: x22
STACK CFI 63b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI 63bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 63d0 x21: x21 x22: x22
STACK CFI 63e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x29: x29
STACK CFI INIT 63f0 a8 .cfa: sp 0 + .ra: x30
STACK CFI 63f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6404 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6418 x23: .cfa -16 + ^
STACK CFI 6444 x23: x23
STACK CFI 6458 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6460 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 648c x23: x23
STACK CFI 6490 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 64a0 68 .cfa: sp 0 + .ra: x30
STACK CFI 64a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 64b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 64bc x21: .cfa -16 + ^
STACK CFI 64ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 64f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6510 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 6518 .cfa: sp 144 +
STACK CFI 6524 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 652c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6538 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 658c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6594 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6718 x23: x23 x24: x24
STACK CFI 671c x27: x27 x28: x28
STACK CFI 6754 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 675c .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6760 x23: x23 x24: x24
STACK CFI 6768 x27: x27 x28: x28
STACK CFI 676c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 678c x23: x23 x24: x24 x27: x27 x28: x28
STACK CFI 67a0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 67a8 x27: x27 x28: x28
STACK CFI 67b0 x23: x23 x24: x24
STACK CFI 67b8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 67bc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 67c0 24 .cfa: sp 0 + .ra: x30
STACK CFI 67c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 67d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 67e4 24 .cfa: sp 0 + .ra: x30
STACK CFI 67ec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 67f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6810 2cc .cfa: sp 0 + .ra: x30
STACK CFI 6818 .cfa: sp 144 +
STACK CFI 681c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6824 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6838 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6858 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 685c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6860 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 69e0 x21: x21 x22: x22
STACK CFI 69e4 x23: x23 x24: x24
STACK CFI 69e8 x27: x27 x28: x28
STACK CFI 69ec x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6a24 x21: x21 x22: x22
STACK CFI 6a2c x23: x23 x24: x24
STACK CFI 6a30 x27: x27 x28: x28
STACK CFI 6a5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x26: x26 x29: x29
STACK CFI 6a64 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6a8c x21: x21 x22: x22
STACK CFI 6a94 x23: x23 x24: x24
STACK CFI 6a98 x27: x27 x28: x28
STACK CFI 6a9c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6aa4 x21: x21 x22: x22
STACK CFI 6aac x23: x23 x24: x24
STACK CFI 6ab0 x27: x27 x28: x28
STACK CFI 6ab4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6abc x21: x21 x22: x22
STACK CFI 6ac4 x23: x23 x24: x24
STACK CFI 6ac8 x27: x27 x28: x28
STACK CFI 6ad0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6ad4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6ad8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 6ae0 20 .cfa: sp 0 + .ra: x30
STACK CFI 6ae8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6af4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6b00 30 .cfa: sp 0 + .ra: x30
STACK CFI 6b08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6b24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6b30 30 .cfa: sp 0 + .ra: x30
STACK CFI 6b38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6b54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6b60 20 .cfa: sp 0 + .ra: x30
STACK CFI 6b68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6b74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6b80 30 .cfa: sp 0 + .ra: x30
STACK CFI 6b88 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6ba4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6bb0 30 .cfa: sp 0 + .ra: x30
STACK CFI 6bb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6bd4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6be0 178 .cfa: sp 0 + .ra: x30
STACK CFI 6be8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6bfc .cfa: sp 1120 + x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 6c2c x19: .cfa -64 + ^
STACK CFI 6c34 x20: .cfa -56 + ^
STACK CFI 6cec x19: x19
STACK CFI 6cf0 x20: x20
STACK CFI 6d10 .cfa: sp 80 +
STACK CFI 6d20 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6d28 .cfa: sp 1120 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 6d4c x19: x19 x20: x20
STACK CFI 6d50 x19: .cfa -64 + ^
STACK CFI 6d54 x20: .cfa -56 + ^
STACK CFI INIT 6d60 24 .cfa: sp 0 + .ra: x30
STACK CFI 6d68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6d74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6d84 24 .cfa: sp 0 + .ra: x30
STACK CFI 6d8c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6d98 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6db0 168 .cfa: sp 0 + .ra: x30
STACK CFI 6db8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6dc0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6dd0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6ddc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6de0 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 6e4c x19: x19 x20: x20
STACK CFI 6e58 x23: x23 x24: x24
STACK CFI 6e5c x25: x25 x26: x26
STACK CFI 6e60 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6e68 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 6e9c x19: x19 x20: x20
STACK CFI 6ea8 x23: x23 x24: x24
STACK CFI 6eac x25: x25 x26: x26
STACK CFI 6eb0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6eb8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 6ecc x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 6ed8 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6ef4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 6efc x19: x19 x20: x20
STACK CFI 6f08 x23: x23 x24: x24
STACK CFI 6f0c x25: x25 x26: x26
STACK CFI 6f10 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 6f20 88 .cfa: sp 0 + .ra: x30
STACK CFI 6f34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6f3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6f48 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6f5c x23: .cfa -16 + ^
STACK CFI 6f9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 6fb0 18 .cfa: sp 0 + .ra: x30
STACK CFI 6fb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6fc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6fd0 18 .cfa: sp 0 + .ra: x30
STACK CFI 6fd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6fe0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6ff0 18 .cfa: sp 0 + .ra: x30
STACK CFI 6ff8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7000 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7010 18 .cfa: sp 0 + .ra: x30
STACK CFI 7018 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7020 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7030 1c .cfa: sp 0 + .ra: x30
STACK CFI 7038 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7044 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7050 18 .cfa: sp 0 + .ra: x30
STACK CFI 7058 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7060 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7070 18 .cfa: sp 0 + .ra: x30
STACK CFI 7078 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7080 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7090 b4 .cfa: sp 0 + .ra: x30
STACK CFI 7098 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 70a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 70a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 70bc x23: .cfa -16 + ^
STACK CFI 710c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7114 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 713c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 7144 ec .cfa: sp 0 + .ra: x30
STACK CFI 714c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7154 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7160 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7168 x23: .cfa -32 + ^
STACK CFI 71dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 71e4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x29: .cfa -80 + ^
STACK CFI 7228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 7230 114 .cfa: sp 0 + .ra: x30
STACK CFI 7238 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7240 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 724c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7254 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7264 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 72d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 72dc .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 7330 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7338 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7344 f4 .cfa: sp 0 + .ra: x30
STACK CFI 734c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7354 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 736c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7388 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7394 x25: .cfa -16 + ^
STACK CFI 73c8 x23: x23 x24: x24
STACK CFI 73cc x25: x25
STACK CFI 73d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 73d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 73f0 x23: x23 x24: x24 x25: x25
STACK CFI 740c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7414 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 741c x23: x23 x24: x24
STACK CFI 7424 x25: x25
STACK CFI 742c x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 7430 x23: x23 x24: x24
STACK CFI 7434 x25: x25
STACK CFI INIT 7440 64 .cfa: sp 0 + .ra: x30
STACK CFI 7448 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7450 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7460 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 746c x23: .cfa -16 + ^
STACK CFI 749c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 74a4 a0 .cfa: sp 0 + .ra: x30
STACK CFI 74ac .cfa: sp 64 +
STACK CFI 74b0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 74b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 74cc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7538 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7540 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7544 54 .cfa: sp 0 + .ra: x30
STACK CFI 754c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7554 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7564 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7590 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 75a0 b8 .cfa: sp 0 + .ra: x30
STACK CFI 75a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 75b0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 75b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 75d0 x23: .cfa -16 + ^
STACK CFI 7620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7628 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 7660 90 .cfa: sp 0 + .ra: x30
STACK CFI 7668 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7670 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7678 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 76c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 76d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 76e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 76f0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 76f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7700 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7708 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7720 x23: .cfa -16 + ^
STACK CFI 776c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7774 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 778c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 7794 4c .cfa: sp 0 + .ra: x30
STACK CFI 779c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 77a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 77d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 77e0 94 .cfa: sp 0 + .ra: x30
STACK CFI 77e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 77f0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 77fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7838 x19: x19 x20: x20
STACK CFI 7840 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7848 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 786c x19: x19 x20: x20
STACK CFI INIT 7874 4c .cfa: sp 0 + .ra: x30
STACK CFI 787c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7884 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 78b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 78c0 ce4 .cfa: sp 0 + .ra: x30
STACK CFI 78c8 .cfa: sp 400 +
STACK CFI 78d4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 78dc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 78e4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 78ec x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 78f4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 78fc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7e90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7e98 .cfa: sp 400 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 85a4 30 .cfa: sp 0 + .ra: x30
STACK CFI 85ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 85b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 85d4 38 .cfa: sp 0 + .ra: x30
STACK CFI 85dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 85e4 x19: .cfa -16 + ^
STACK CFI 8604 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8610 1a8 .cfa: sp 0 + .ra: x30
STACK CFI 8618 .cfa: sp 352 +
STACK CFI 8628 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 8648 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 86cc x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 870c x21: x21 x22: x22
STACK CFI 8738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8740 .cfa: sp 352 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x29: .cfa -224 + ^
STACK CFI 876c x21: x21 x22: x22
STACK CFI 878c x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 8790 x21: x21 x22: x22
STACK CFI 87a4 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 87a8 x21: x21 x22: x22
STACK CFI INIT 87c0 40 .cfa: sp 0 + .ra: x30
STACK CFI 87d0 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 87d8 x19: .cfa -16 + ^
STACK CFI 87ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8800 30 .cfa: sp 0 + .ra: x30
STACK CFI 8808 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8818 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8820 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 8830 48 .cfa: sp 0 + .ra: x30
STACK CFI 8838 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8840 x19: .cfa -16 + ^
STACK CFI 885c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8864 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8880 44 .cfa: sp 0 + .ra: x30
STACK CFI 8888 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8890 x19: .cfa -16 + ^
STACK CFI 88a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 88b0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 88bc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 88c4 180 .cfa: sp 0 + .ra: x30
STACK CFI 88cc .cfa: sp 96 +
STACK CFI 88d8 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 88e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 88e8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 88f4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 88fc x25: .cfa -16 + ^
STACK CFI 89b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 89bc .cfa: sp 96 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 8a44 24 .cfa: sp 0 + .ra: x30
STACK CFI 8a4c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8a58 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8a70 a0 .cfa: sp 0 + .ra: x30
STACK CFI 8a78 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8a80 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8a8c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8a98 x23: .cfa -16 + ^
STACK CFI 8ac8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8ad0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 8ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8aec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8b10 24 .cfa: sp 0 + .ra: x30
STACK CFI 8b18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8b24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8b34 a8 .cfa: sp 0 + .ra: x30
STACK CFI 8b3c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8b44 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8b50 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8b5c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8b98 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 8bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8bb4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8be0 24 .cfa: sp 0 + .ra: x30
STACK CFI 8be8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8bf4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8c04 28 .cfa: sp 0 + .ra: x30
STACK CFI 8c0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8c18 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8c30 3d8 .cfa: sp 0 + .ra: x30
STACK CFI 8c38 .cfa: sp 288 +
STACK CFI 8c48 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 8c50 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 8c58 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 8c64 x23: .cfa -176 + ^
STACK CFI 8e44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8e4c .cfa: sp 288 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x29: .cfa -224 + ^
STACK CFI INIT 9010 e0 .cfa: sp 0 + .ra: x30
STACK CFI 9018 .cfa: sp 272 +
STACK CFI 9028 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 9038 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 90dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 90e4 .cfa: sp 272 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x29: .cfa -224 + ^
STACK CFI INIT 90f0 114 .cfa: sp 0 + .ra: x30
STACK CFI 90f8 .cfa: sp 272 +
STACK CFI 9108 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 9118 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 91dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 91e4 .cfa: sp 272 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x29: .cfa -224 + ^
STACK CFI INIT 9204 d8 .cfa: sp 0 + .ra: x30
STACK CFI 920c .cfa: sp 272 +
STACK CFI 9218 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 9220 x19: .cfa -208 + ^
STACK CFI 92d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 92d8 .cfa: sp 272 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x29: .cfa -224 + ^
STACK CFI INIT 92e0 20 .cfa: sp 0 + .ra: x30
STACK CFI 92e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 92f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9300 1c .cfa: sp 0 + .ra: x30
STACK CFI 9308 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9314 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9320 1c .cfa: sp 0 + .ra: x30
STACK CFI 9328 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9330 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9340 c4 .cfa: sp 0 + .ra: x30
STACK CFI 9348 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9350 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9364 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 93d8 x19: x19 x20: x20
STACK CFI 93fc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 9404 b4 .cfa: sp 0 + .ra: x30
STACK CFI 940c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9414 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9420 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9428 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 94a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 94a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 94c0 24 .cfa: sp 0 + .ra: x30
STACK CFI 94c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 94d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 94e4 114 .cfa: sp 0 + .ra: x30
STACK CFI 94f8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 9500 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 950c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 9520 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 95e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 95f0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 9600 4c .cfa: sp 0 + .ra: x30
STACK CFI 9608 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9610 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9640 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9650 4c .cfa: sp 0 + .ra: x30
STACK CFI 9658 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9660 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 9690 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 96a0 b4 .cfa: sp 0 + .ra: x30
STACK CFI 96a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 96b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 96c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 96c8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 970c x23: x23 x24: x24
STACK CFI 9714 x19: x19 x20: x20
STACK CFI 9728 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 9730 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9734 x19: x19 x20: x20
STACK CFI 9740 x23: x23 x24: x24
STACK CFI 9744 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 974c .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI INIT 9754 e0 .cfa: sp 0 + .ra: x30
STACK CFI 9768 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9778 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9780 x21: .cfa -16 + ^
STACK CFI 97f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 97fc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 981c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 982c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9834 60 .cfa: sp 0 + .ra: x30
STACK CFI 983c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 987c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 9884 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9888 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9894 10c .cfa: sp 0 + .ra: x30
STACK CFI 989c .cfa: sp 336 +
STACK CFI 98ac .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 98c0 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 98c8 x21: .cfa -192 + ^
STACK CFI 998c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9994 .cfa: sp 336 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x29: .cfa -224 + ^
STACK CFI INIT 99a0 90 .cfa: sp 0 + .ra: x30
STACK CFI 99a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 99b0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 99c0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 99c8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9a00 x23: x23 x24: x24
STACK CFI 9a08 x19: x19 x20: x20
STACK CFI 9a0c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 9a14 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 9a18 x19: x19 x20: x20
STACK CFI 9a1c x23: x23 x24: x24
STACK CFI 9a28 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 9a30 10c .cfa: sp 0 + .ra: x30
STACK CFI 9a38 .cfa: sp 336 +
STACK CFI 9a48 .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 9a5c x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 9a64 x21: .cfa -192 + ^
STACK CFI 9b28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9b30 .cfa: sp 336 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x29: .cfa -224 + ^
STACK CFI INIT 9b40 508 .cfa: sp 0 + .ra: x30
STACK CFI 9b48 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 9b50 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 9b68 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 9e68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 9e70 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI a024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a02c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT a050 20 .cfa: sp 0 + .ra: x30
STACK CFI a058 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a068 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a070 784 .cfa: sp 0 + .ra: x30
STACK CFI a078 .cfa: sp 192 +
STACK CFI a084 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI a094 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI a0a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI a0ac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI a0c0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI a0f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a160 x23: x23 x24: x24
STACK CFI a168 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a1e0 x23: x23 x24: x24
STACK CFI a1e8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a474 x23: x23 x24: x24
STACK CFI a4b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI a4bc .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI a4c0 x23: x23 x24: x24
STACK CFI a4cc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a538 x23: x23 x24: x24
STACK CFI a53c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a698 x23: x23 x24: x24
STACK CFI a6a4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a718 x23: x23 x24: x24
STACK CFI a720 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a72c x23: x23 x24: x24
STACK CFI a73c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a770 x23: x23 x24: x24
STACK CFI a778 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a7c0 x23: x23 x24: x24
STACK CFI a7cc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI a7e0 x23: x23 x24: x24
STACK CFI a7f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI INIT a7f4 18 .cfa: sp 0 + .ra: x30
STACK CFI a7fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a804 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a810 18 .cfa: sp 0 + .ra: x30
STACK CFI a818 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a820 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a830 1c .cfa: sp 0 + .ra: x30
STACK CFI a838 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a844 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a850 18 .cfa: sp 0 + .ra: x30
STACK CFI a858 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a860 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a870 40 .cfa: sp 0 + .ra: x30
STACK CFI a888 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT a8b0 18 .cfa: sp 0 + .ra: x30
STACK CFI a8b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a8c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a8d0 6c .cfa: sp 0 + .ra: x30
STACK CFI a8d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a91c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a924 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT a940 44 .cfa: sp 0 + .ra: x30
STACK CFI a948 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a96c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI a974 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI a978 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT a984 88 .cfa: sp 0 + .ra: x30
STACK CFI a98c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI a994 x19: .cfa -16 + ^
STACK CFI a9b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI a9c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI a9fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI aa04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT aa10 48 .cfa: sp 0 + .ra: x30
STACK CFI aa18 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aa20 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI aa50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT aa60 48 .cfa: sp 0 + .ra: x30
STACK CFI aa68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI aa70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI aaa0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT aab0 9f0 .cfa: sp 0 + .ra: x30
STACK CFI aab8 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI aacc x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI aadc x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI aae8 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI aaf8 x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI ab18 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI ad10 x19: x19 x20: x20
STACK CFI ad14 x21: x21 x22: x22
STACK CFI ad18 x25: x25 x26: x26
STACK CFI ad1c x27: x27 x28: x28
STACK CFI ad24 x23: x23 x24: x24
STACK CFI ad28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI ad30 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^ x29: .cfa -224 + ^
STACK CFI b474 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI b488 x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI b48c x19: x19 x20: x20
STACK CFI b494 x21: x21 x22: x22
STACK CFI b498 x23: x23 x24: x24
STACK CFI b49c x25: x25 x26: x26
STACK CFI INIT b4a0 24 .cfa: sp 0 + .ra: x30
STACK CFI b4a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b4b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b4c4 18 .cfa: sp 0 + .ra: x30
STACK CFI b4cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b4d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b4e0 6c .cfa: sp 0 + .ra: x30
STACK CFI b4f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI b4fc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI b530 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI b538 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI b544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT b550 4c .cfa: sp 0 + .ra: x30
STACK CFI b564 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI b594 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT b5a0 74 .cfa: sp 0 + .ra: x30
STACK CFI b5a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI b5b0 x21: .cfa -16 + ^
STACK CFI b5bc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b60c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT b614 3e4 .cfa: sp 0 + .ra: x30
STACK CFI b61c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI b62c .cfa: sp 1696 + x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI b674 x19: .cfa -64 + ^
STACK CFI b678 x20: .cfa -56 + ^
STACK CFI b67c x25: .cfa -16 + ^
STACK CFI b684 x26: .cfa -8 + ^
STACK CFI b8b4 x19: x19
STACK CFI b8b8 x20: x20
STACK CFI b8bc x25: x25
STACK CFI b8c0 x26: x26
STACK CFI b8e0 .cfa: sp 80 +
STACK CFI b8f0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI b8f8 .cfa: sp 1696 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI b90c x19: x19
STACK CFI b910 x20: x20
STACK CFI b914 x25: x25
STACK CFI b918 x26: x26
STACK CFI b920 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI b9d4 x19: x19
STACK CFI b9d8 x20: x20
STACK CFI b9dc x25: x25
STACK CFI b9e0 x26: x26
STACK CFI b9e8 x19: .cfa -64 + ^
STACK CFI b9ec x20: .cfa -56 + ^
STACK CFI b9f0 x25: .cfa -16 + ^
STACK CFI b9f4 x26: .cfa -8 + ^
STACK CFI INIT ba00 50 .cfa: sp 0 + .ra: x30
STACK CFI ba08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ba10 x19: .cfa -16 + ^
STACK CFI ba34 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI ba3c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI ba48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ba50 274 .cfa: sp 0 + .ra: x30
STACK CFI ba58 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI ba60 x25: .cfa -16 + ^
STACK CFI ba68 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI baa4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI bacc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI bc14 x23: x23 x24: x24
STACK CFI bc18 x21: x21 x22: x22
STACK CFI bc24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI bc2c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI bc8c x21: x21 x22: x22
STACK CFI bc90 x23: x23 x24: x24
STACK CFI bc98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x25: x25 x29: x29
STACK CFI bca0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT bcc4 70 .cfa: sp 0 + .ra: x30
STACK CFI bd1c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT bd34 74 .cfa: sp 0 + .ra: x30
STACK CFI bd90 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT bdb0 40 .cfa: sp 0 + .ra: x30
STACK CFI bdb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bdd4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bde0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bde4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bdf0 40 .cfa: sp 0 + .ra: x30
STACK CFI bdf8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI be14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI be20 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI be24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT be30 40 .cfa: sp 0 + .ra: x30
STACK CFI be38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI be54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI be60 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI be64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT be70 40 .cfa: sp 0 + .ra: x30
STACK CFI be78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI be94 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI bea0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bea4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT beb0 3c .cfa: sp 0 + .ra: x30
STACK CFI bed8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT bef0 18 .cfa: sp 0 + .ra: x30
STACK CFI bef8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bf00 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bf10 18 .cfa: sp 0 + .ra: x30
STACK CFI bf18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bf20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bf30 18 .cfa: sp 0 + .ra: x30
STACK CFI bf38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bf40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bf50 64 .cfa: sp 0 + .ra: x30
STACK CFI bf58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI bf64 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI bf98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI bfa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI bfac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT bfb4 18 .cfa: sp 0 + .ra: x30
STACK CFI bfbc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bfc4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bfd0 18 .cfa: sp 0 + .ra: x30
STACK CFI bfd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI bfe0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT bff0 30 .cfa: sp 0 + .ra: x30
STACK CFI bff8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c018 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT c020 7c .cfa: sp 0 + .ra: x30
STACK CFI c028 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c030 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c038 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI c064 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI c06c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI c094 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT c0a0 28c .cfa: sp 0 + .ra: x30
STACK CFI c0a8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI c0b4 .cfa: sp 1152 + x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI c124 x19: .cfa -64 + ^
STACK CFI c128 x20: .cfa -56 + ^
STACK CFI c17c x23: .cfa -32 + ^
STACK CFI c184 x24: .cfa -24 + ^
STACK CFI c208 x25: .cfa -16 + ^
STACK CFI c298 x19: x19
STACK CFI c29c x20: x20
STACK CFI c2a0 x23: x23
STACK CFI c2a4 x24: x24
STACK CFI c2a8 x25: x25
STACK CFI c2cc .cfa: sp 80 +
STACK CFI c2d4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI c2dc .cfa: sp 1152 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI c2f0 x19: x19
STACK CFI c2f4 x20: x20
STACK CFI c2fc x19: .cfa -64 + ^ x20: .cfa -56 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI c300 x19: x19
STACK CFI c308 x20: x20
STACK CFI c30c x23: x23
STACK CFI c310 x24: x24
STACK CFI c318 x19: .cfa -64 + ^
STACK CFI c31c x20: .cfa -56 + ^
STACK CFI c320 x23: .cfa -32 + ^
STACK CFI c324 x24: .cfa -24 + ^
STACK CFI c328 x25: .cfa -16 + ^
STACK CFI INIT c330 204 .cfa: sp 0 + .ra: x30
STACK CFI c338 .cfa: sp 144 +
STACK CFI c348 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI c350 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI c380 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI c390 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c3c8 x27: .cfa -16 + ^
STACK CFI c424 x27: x27
STACK CFI c42c x25: x25 x26: x26
STACK CFI c480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI c488 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI c494 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c4f8 x25: x25 x26: x26
STACK CFI c500 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c50c x25: x25 x26: x26
STACK CFI c510 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI c514 x27: x27
STACK CFI c518 x27: .cfa -16 + ^
STACK CFI c51c x25: x25 x26: x26
STACK CFI c524 x27: x27
STACK CFI c52c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI c530 x27: .cfa -16 + ^
STACK CFI INIT c534 40 .cfa: sp 0 + .ra: x30
STACK CFI c53c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI c544 x19: .cfa -16 + ^
STACK CFI c558 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI c560 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI c56c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT c574 64 .cfa: sp 0 + .ra: x30
STACK CFI c57c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c584 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c590 x21: .cfa -16 + ^
STACK CFI c5a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI c5b0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI c5d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT c5e0 1ac .cfa: sp 0 + .ra: x30
STACK CFI c5e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c5f4 .cfa: x29 64 +
STACK CFI c5f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c604 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c610 x23: .cfa -16 + ^
STACK CFI c69c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI c6a4 .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT c790 1c0 .cfa: sp 0 + .ra: x30
STACK CFI c798 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI c7a4 .cfa: x29 64 +
STACK CFI c7a8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI c7b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI c7c0 x23: .cfa -16 + ^
STACK CFI c828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI c830 .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT c950 1a8 .cfa: sp 0 + .ra: x30
STACK CFI c958 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI c964 .cfa: x29 48 +
STACK CFI c968 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c978 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI cad8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI cae0 .cfa: x29 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT cb00 18 .cfa: sp 0 + .ra: x30
STACK CFI cb08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cb10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT cb20 28 .cfa: sp 0 + .ra: x30
STACK CFI cb28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI cb40 .cfa: sp 0 + .ra: .ra x29: x29
