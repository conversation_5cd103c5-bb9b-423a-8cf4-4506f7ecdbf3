MODULE Linux arm64 DD94AFD0658ADF1941A96E8A81664D820 libpipewire-module-spa-device-factory.so
INFO CODE_ID D0AF94DD8A6519DF41A96E8A81664D82DF1021AC
PUBLIC 1f40 0 pipewire__module_init
STACK CFI INIT 1560 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1590 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 15d0 48 .cfa: sp 0 + .ra: x30
STACK CFI 15d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15dc x19: .cfa -16 + ^
STACK CFI 1614 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1620 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1630 60 .cfa: sp 0 + .ra: x30
STACK CFI 1638 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1640 x19: .cfa -16 + ^
STACK CFI 1688 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1690 c4 .cfa: sp 0 + .ra: x30
STACK CFI 1698 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 16a8 x19: .cfa -16 + ^
STACK CFI 16f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1700 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 174c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1754 cc .cfa: sp 0 + .ra: x30
STACK CFI 175c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 176c x19: .cfa -16 + ^
STACK CFI 17cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 17d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1818 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1820 4dc .cfa: sp 0 + .ra: x30
STACK CFI 1828 .cfa: sp 128 +
STACK CFI 1834 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 183c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1844 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 184c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1864 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 187c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1a30 x25: x25 x26: x26
STACK CFI 1a68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 1a70 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1a74 x25: x25 x26: x26
STACK CFI 1ab4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1b48 x25: x25 x26: x26
STACK CFI 1b4c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1bdc x25: x25 x26: x26
STACK CFI 1be0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1cf4 x25: x25 x26: x26
STACK CFI 1cf8 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 1d00 90 .cfa: sp 0 + .ra: x30
STACK CFI 1d08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d10 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1d78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1d80 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1d88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1d90 68 .cfa: sp 0 + .ra: x30
STACK CFI 1d98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1da0 x19: .cfa -16 + ^
STACK CFI 1de0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1de8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 1df0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1e00 13c .cfa: sp 0 + .ra: x30
STACK CFI 1e08 .cfa: sp 96 +
STACK CFI 1e18 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e20 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1ec4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1ecc .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1f40 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 1f48 .cfa: sp 96 +
STACK CFI 1f54 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1f60 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1fd8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2080 x23: x23 x24: x24
STACK CFI 20b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 20b8 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 20f8 x23: x23 x24: x24
STACK CFI 210c x23: .cfa -16 + ^ x24: .cfa -8 + ^
