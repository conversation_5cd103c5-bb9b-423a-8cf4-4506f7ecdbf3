MODULE Linux arm64 3FC9E1410CC6FF9D72DCA780D53D54F50 libopencv_structured_light.so.4.3
INFO CODE_ID 41E1C93FC60C9DFF72DCA780D53D54F5894A7A63
PUBLIC 56b8 0 _init
PUBLIC 5c50 0 call_weak_fn
PUBLIC 5c68 0 deregister_tm_clones
PUBLIC 5ca0 0 register_tm_clones
PUBLIC 5ce0 0 __do_global_dtors_aux
PUBLIC 5d28 0 frame_dummy
PUBLIC 5d60 0 cv::Algorithm::clear()
PUBLIC 5d68 0 cv::Algorithm::write(cv::FileStorage&) const
PUBLIC 5d70 0 cv::Algorithm::read(cv::FileNode const&)
PUBLIC 5d78 0 cv::Algorithm::empty() const
PUBLIC 5d80 0 cv::structured_light::GrayCodePattern_Impl::getNumberOfPatternImages() const
PUBLIC 5d88 0 cv::structured_light::GrayCodePattern_Impl::setBlackThreshold(unsigned long)
PUBLIC 5d90 0 cv::structured_light::GrayCodePattern_Impl::setWhiteThreshold(unsigned long)
PUBLIC 5d98 0 std::_Sp_counted_ptr_inplace<cv::structured_light::GrayCodePattern_Impl, std::allocator<cv::structured_light::GrayCodePattern_Impl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 5da0 0 cv::structured_light::GrayCodePattern_Impl::~GrayCodePattern_Impl()
PUBLIC 5db0 0 virtual thunk to cv::structured_light::GrayCodePattern_Impl::~GrayCodePattern_Impl()
PUBLIC 5dc0 0 std::_Sp_counted_ptr_inplace<cv::structured_light::GrayCodePattern_Impl, std::allocator<cv::structured_light::GrayCodePattern_Impl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 5dd8 0 cv::structured_light::GrayCodePattern_Impl::~GrayCodePattern_Impl()
PUBLIC 5e00 0 virtual thunk to cv::structured_light::GrayCodePattern_Impl::~GrayCodePattern_Impl()
PUBLIC 5e10 0 std::_Sp_counted_ptr_inplace<cv::structured_light::GrayCodePattern_Impl, std::allocator<cv::structured_light::GrayCodePattern_Impl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 5e18 0 std::_Sp_counted_ptr_inplace<cv::structured_light::GrayCodePattern_Impl, std::allocator<cv::structured_light::GrayCodePattern_Impl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 5e20 0 std::_Sp_counted_ptr_inplace<cv::structured_light::GrayCodePattern_Impl, std::allocator<cv::structured_light::GrayCodePattern_Impl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 5e70 0 cv::Mat::~Mat()
PUBLIC 5f00 0 cv::structured_light::GrayCodePattern_Impl::getImagesForShadowMasks(cv::_InputOutputArray const&, cv::_InputOutputArray const&) const
PUBLIC 6380 0 cv::structured_light::GrayCodePattern::Params::Params()
PUBLIC 6398 0 cv::structured_light::GrayCodePattern_Impl::computeNumberOfPatternImages()
PUBLIC 6400 0 cv::structured_light::GrayCodePattern_Impl::GrayCodePattern_Impl(cv::structured_light::GrayCodePattern::Params const&)
PUBLIC 64b0 0 cv::structured_light::GrayCodePattern_Impl::GrayCodePattern_Impl(cv::structured_light::GrayCodePattern::Params const&)
PUBLIC 6530 0 cv::structured_light::GrayCodePattern_Impl::grayToDec(std::vector<unsigned char, std::allocator<unsigned char> > const&) const
PUBLIC 65e8 0 cv::structured_light::GrayCodePattern_Impl::getProjPixel(cv::_InputArray const&, int, int, cv::Point_<int>&) const
PUBLIC 6ac8 0 cv::structured_light::GrayCodePattern::create(cv::structured_light::GrayCodePattern::Params const&)
PUBLIC 6b48 0 cv::structured_light::GrayCodePattern::create(int, int)
PUBLIC 6be0 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::~vector()
PUBLIC 6ca0 0 std::vector<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >, std::allocator<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > > > >::~vector()
PUBLIC 6d30 0 std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >::operator=(std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > const&)
PUBLIC 6f40 0 std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_default_append(unsigned long)
PUBLIC 72d0 0 cv::structured_light::GrayCodePattern_Impl::generate(cv::_OutputArray const&)
PUBLIC 7810 0 cv::structured_light::GrayCodePattern_Impl::computeShadowMasks(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&) const
PUBLIC 7c90 0 std::vector<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >, std::allocator<std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > > > >::_M_default_append(unsigned long)
PUBLIC 7e80 0 std::vector<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >, std::allocator<std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > > > >::_M_default_append(unsigned long)
PUBLIC 8040 0 void std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >::_M_emplace_back_aux<cv::Point_<int> >(cv::Point_<int>&&)
PUBLIC 8140 0 cv::structured_light::GrayCodePattern_Impl::decode(std::vector<std::vector<cv::Mat, std::allocator<cv::Mat> >, std::allocator<std::vector<cv::Mat, std::allocator<cv::Mat> > > > const&, cv::_OutputArray const&, cv::_InputArray const&, cv::_InputArray const&, int) const
PUBLIC 8780 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::decode(std::vector<std::vector<cv::Mat, std::allocator<cv::Mat> >, std::allocator<std::vector<cv::Mat, std::allocator<cv::Mat> > > > const&, cv::_OutputArray const&, cv::_InputArray const&, cv::_InputArray const&, int) const
PUBLIC 8788 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::findProCamMatches(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 8790 0 std::_Sp_counted_ptr_inplace<cv::structured_light::SinusoidalPatternProfilometry_Impl, std::allocator<cv::structured_light::SinusoidalPatternProfilometry_Impl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 8798 0 std::_Sp_counted_ptr_inplace<cv::structured_light::SinusoidalPatternProfilometry_Impl, std::allocator<cv::structured_light::SinusoidalPatternProfilometry_Impl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 87a0 0 std::_Sp_counted_ptr_inplace<cv::structured_light::SinusoidalPatternProfilometry_Impl, std::allocator<cv::structured_light::SinusoidalPatternProfilometry_Impl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 87a8 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::~SinusoidalPatternProfilometry_Impl()
PUBLIC 87e8 0 virtual thunk to cv::structured_light::SinusoidalPatternProfilometry_Impl::~SinusoidalPatternProfilometry_Impl()
PUBLIC 87f8 0 std::_Sp_counted_ptr_inplace<cv::structured_light::SinusoidalPatternProfilometry_Impl, std::allocator<cv::structured_light::SinusoidalPatternProfilometry_Impl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 8848 0 std::_Sp_counted_ptr_inplace<cv::structured_light::SinusoidalPatternProfilometry_Impl, std::allocator<cv::structured_light::SinusoidalPatternProfilometry_Impl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 8890 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::~SinusoidalPatternProfilometry_Impl()
PUBLIC 88d8 0 virtual thunk to cv::structured_light::SinusoidalPatternProfilometry_Impl::~SinusoidalPatternProfilometry_Impl()
PUBLIC 88f0 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::computeDataModulationTerm(cv::_InputArray const&, cv::_OutputArray const&, cv::_InputArray const&)
PUBLIC 9130 0 cv::structured_light::SinusoidalPattern::Params::Params()
PUBLIC 9178 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::Marker::Marker()
PUBLIC 9190 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::Marker::Marker(cv::Point_<int>)
PUBLIC 91c8 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::Marker::drawMarker(cv::_OutputArray const&)
PUBLIC 9250 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::SinusoidalPatternProfilometry_Impl(cv::structured_light::SinusoidalPattern::Params const&)
PUBLIC 93c8 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::SinusoidalPatternProfilometry_Impl(cv::structured_light::SinusoidalPattern::Params const&)
PUBLIC 9500 0 cv::MatExpr::~MatExpr()
PUBLIC 96b0 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::computeDft(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC a2d0 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::computeInverseDft(cv::_InputArray const&, cv::_OutputArray const&, bool)
PUBLIC a330 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::computeDftMagnitude(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC a870 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::computeFtPhaseMap(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC ab50 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::swapQuadrants(cv::_InputOutputArray const&, int, int)
PUBLIC aff0 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::frequencyFiltering(cv::_InputOutputArray const&, int, int, int, int, bool, int, int)
PUBLIC b8f0 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::findMaxInHalvesTransform(cv::_InputArray const&, cv::Point_<int>&, cv::Point_<int>&)
PUBLIC c040 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::computePsPhaseMap(cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC c308 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::computeFapsPhaseMap(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC c5a0 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::computeShadowMask(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC ca80 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::convertToAbsolutePhaseMap(cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&, cv::_InputArray const&)
PUBLIC cc30 0 cv::structured_light::SinusoidalPattern::create(cv::Ptr<cv::structured_light::SinusoidalPattern::Params>)
PUBLIC ccb0 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC cd70 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::unwrapPhaseMap(cv::_InputArray const&, cv::_OutputArray const&, cv::Size_<int>, cv::_InputArray const&)
PUBLIC d060 0 void std::vector<cv::Mat, std::allocator<cv::Mat> >::_M_emplace_back_aux<cv::Mat const&>(cv::Mat const&)
PUBLIC d3b0 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::computePhaseMap(cv::_InputArray const&, cv::_OutputArray const&, cv::_OutputArray const&, cv::_InputArray const&)
PUBLIC f650 0 void std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >::_M_emplace_back_aux<cv::Point_<int> const&>(cv::Point_<int> const&)
PUBLIC f750 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::extractMarkersLocation(cv::_InputArray const&, std::vector<cv::Point_<int>, std::allocator<cv::Point_<int> > >&)
PUBLIC f888 0 void std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >::_M_emplace_back_aux<cv::Point_<float> >(cv::Point_<float>&&)
PUBLIC f990 0 cv::structured_light::SinusoidalPatternProfilometry_Impl::generate(cv::_OutputArray const&)
PUBLIC 10630 0 _fini
STACK CFI INIT 5d60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d68 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d70 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d78 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d88 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5d98 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5da0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5db0 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5dc0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5dd8 24 .cfa: sp 0 + .ra: x30
STACK CFI 5ddc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5df8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 5e00 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5e10 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5e18 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5e20 50 .cfa: sp 0 + .ra: x30
STACK CFI 5e24 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5e30 .ra: .cfa -16 + ^
STACK CFI 5e6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 5e70 90 .cfa: sp 0 + .ra: x30
STACK CFI 5e74 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5ee8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 5ef0 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5efc .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 5f00 454 .cfa: sp 0 + .ra: x30
STACK CFI 5f08 .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 5f14 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 5f1c x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 5f24 .ra: .cfa -152 + ^ x25: .cfa -160 + ^
STACK CFI 61dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 61e0 .cfa: sp 208 + .ra: .cfa -152 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^
STACK CFI INIT 6380 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6398 58 .cfa: sp 0 + .ra: x30
STACK CFI 639c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -32 + ^
STACK CFI 63a4 v8: .cfa -16 + ^
STACK CFI 63ec .cfa: sp 0 + .ra: .ra v8: v8 x19: x19
STACK CFI INIT 6400 98 .cfa: sp 0 + .ra: x30
STACK CFI 6404 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6410 .ra: .cfa -16 + ^
STACK CFI 6468 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 646c .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 64b0 68 .cfa: sp 0 + .ra: x30
STACK CFI 64b4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 64c0 .ra: .cfa -16 + ^
STACK CFI 64f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 64f8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 6530 b4 .cfa: sp 0 + .ra: x30
STACK CFI 6534 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 653c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6544 .ra: .cfa -16 + ^
STACK CFI 65b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 65c0 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 65e8 4e0 .cfa: sp 0 + .ra: x30
STACK CFI 65ec .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 6604 .ra: .cfa -112 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI 69e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 69e8 .cfa: sp 192 + .ra: .cfa -112 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT 6ac8 7c .cfa: sp 0 + .ra: x30
STACK CFI 6acc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6ad4 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 6b2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 6b30 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 6b48 98 .cfa: sp 0 + .ra: x30
STACK CFI 6b4c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6b5c .ra: .cfa -32 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6bc8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 6bcc .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 6be0 bc .cfa: sp 0 + .ra: x30
STACK CFI 6be4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6be8 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 6c8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 6c90 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 6c98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 6ca0 8c .cfa: sp 0 + .ra: x30
STACK CFI 6ca4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6cac .ra: .cfa -8 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 6d18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 6d1c .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 6d28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI INIT 6d30 210 .cfa: sp 0 + .ra: x30
STACK CFI 6d34 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6d3c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6d44 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 6df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 6df8 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 6f40 384 .cfa: sp 0 + .ra: x30
STACK CFI 6f48 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6f60 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 7198 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 719c .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 71fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 7200 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 7218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 721c .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 72d0 52c .cfa: sp 0 + .ra: x30
STACK CFI 72d4 .cfa: sp 224 + x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI 72f4 .ra: .cfa -144 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI 771c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7720 .cfa: sp 224 + .ra: .cfa -144 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT 7810 464 .cfa: sp 0 + .ra: x30
STACK CFI 7814 .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 7820 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 7834 .ra: .cfa -176 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 7b68 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 7b70 .cfa: sp 256 + .ra: .cfa -176 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI INIT 7c90 1ec .cfa: sp 0 + .ra: x30
STACK CFI 7cf0 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7cf4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7d0c .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 7e50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 7e54 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 7e80 1bc .cfa: sp 0 + .ra: x30
STACK CFI 7ee4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7ee8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7ef8 .ra: .cfa -8 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 8014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 8018 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI INIT 8040 100 .cfa: sp 0 + .ra: x30
STACK CFI 8044 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 804c .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 8054 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8108 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 8110 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 8140 630 .cfa: sp 0 + .ra: x30
STACK CFI 8150 .cfa: sp 400 + x19: .cfa -400 + ^ x20: .cfa -392 + ^
STACK CFI 8164 x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI 8178 x25: .cfa -352 + ^ x26: .cfa -344 + ^
STACK CFI 8194 .ra: .cfa -320 + ^ v8: .cfa -312 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^
STACK CFI 84d8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 84e0 .cfa: sp 400 + .ra: .cfa -320 + ^ v8: .cfa -312 + ^ x19: .cfa -400 + ^ x20: .cfa -392 + ^ x21: .cfa -384 + ^ x22: .cfa -376 + ^ x23: .cfa -368 + ^ x24: .cfa -360 + ^ x25: .cfa -352 + ^ x26: .cfa -344 + ^ x27: .cfa -336 + ^ x28: .cfa -328 + ^
STACK CFI INIT 8780 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8788 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8790 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8798 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 87a0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 87a8 3c .cfa: sp 0 + .ra: x30
STACK CFI 87ac .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 87e0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 87e8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 87f8 50 .cfa: sp 0 + .ra: x30
STACK CFI 87fc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8808 .ra: .cfa -16 + ^
STACK CFI 8844 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 8848 48 .cfa: sp 0 + .ra: x30
STACK CFI 884c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 885c .ra: .cfa -16 + ^
STACK CFI 888c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 8890 44 .cfa: sp 0 + .ra: x30
STACK CFI 8894 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 88d0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 88d8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 88f0 800 .cfa: sp 0 + .ra: x30
STACK CFI 88f8 .cfa: sp 592 +
STACK CFI 8904 x19: .cfa -592 + ^ x20: .cfa -584 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^
STACK CFI 8924 .ra: .cfa -512 + ^ v10: .cfa -480 + ^ v11: .cfa -472 + ^ v12: .cfa -464 + ^ v13: .cfa -456 + ^ v8: .cfa -496 + ^ v9: .cfa -488 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^ x27: .cfa -528 + ^ x28: .cfa -520 + ^
STACK CFI 8e8c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8e90 .cfa: sp 592 + .ra: .cfa -512 + ^ v10: .cfa -480 + ^ v11: .cfa -472 + ^ v12: .cfa -464 + ^ v13: .cfa -456 + ^ v8: .cfa -496 + ^ v9: .cfa -488 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^ x27: .cfa -528 + ^ x28: .cfa -520 + ^
STACK CFI INIT 9130 38 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9178 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9190 34 .cfa: sp 0 + .ra: x30
STACK CFI INIT 91c8 88 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9250 174 .cfa: sp 0 + .ra: x30
STACK CFI 9254 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9260 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9268 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 9374 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 9378 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 93c8 138 .cfa: sp 0 + .ra: x30
STACK CFI 93cc .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 93d8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 93e0 .ra: .cfa -16 + ^
STACK CFI 94c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 94c4 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 9500 1ac .cfa: sp 0 + .ra: x30
STACK CFI 9504 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9510 .ra: .cfa -16 + ^
STACK CFI 966c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 9670 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 96a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 96b0 c0c .cfa: sp 0 + .ra: x30
STACK CFI 96b8 .cfa: sp 960 +
STACK CFI 96c0 x19: .cfa -960 + ^ x20: .cfa -952 + ^
STACK CFI 96d4 .ra: .cfa -904 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^ x25: .cfa -912 + ^
STACK CFI 9bc0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 9bc8 .cfa: sp 960 + .ra: .cfa -904 + ^ x19: .cfa -960 + ^ x20: .cfa -952 + ^ x21: .cfa -944 + ^ x22: .cfa -936 + ^ x23: .cfa -928 + ^ x24: .cfa -920 + ^ x25: .cfa -912 + ^
STACK CFI INIT a2d0 58 .cfa: sp 0 + .ra: x30
STACK CFI a2d4 .cfa: sp 64 + .ra: .cfa -64 + ^
STACK CFI a324 .cfa: sp 0 + .ra: .ra
STACK CFI INIT a330 530 .cfa: sp 0 + .ra: x30
STACK CFI a338 .cfa: sp 416 + x19: .cfa -416 + ^ x20: .cfa -408 + ^
STACK CFI a344 .ra: .cfa -376 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^
STACK CFI a74c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI a750 .cfa: sp 416 + .ra: .cfa -376 + ^ x19: .cfa -416 + ^ x20: .cfa -408 + ^ x21: .cfa -400 + ^ x22: .cfa -392 + ^ x23: .cfa -384 + ^
STACK CFI INIT a870 2c4 .cfa: sp 0 + .ra: x30
STACK CFI a878 .cfa: sp 304 + x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI a884 x21: .cfa -288 + ^ x22: .cfa -280 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^
STACK CFI a89c .ra: .cfa -224 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI aa48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI aa4c .cfa: sp 304 + .ra: .cfa -224 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^ x22: .cfa -280 + ^ x23: .cfa -272 + ^ x24: .cfa -264 + ^ x25: .cfa -256 + ^ x26: .cfa -248 + ^ x27: .cfa -240 + ^ x28: .cfa -232 + ^
STACK CFI INIT ab50 490 .cfa: sp 0 + .ra: x30
STACK CFI ab54 .cfa: sp 592 +
STACK CFI ab5c x19: .cfa -592 + ^ x20: .cfa -584 + ^
STACK CFI ab64 x23: .cfa -560 + ^ x24: .cfa -552 + ^
STACK CFI ab6c x25: .cfa -544 + ^ x26: .cfa -536 + ^
STACK CFI ab7c .ra: .cfa -528 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^
STACK CFI af38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI af40 .cfa: sp 592 + .ra: .cfa -528 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^
STACK CFI INIT aff0 8f0 .cfa: sp 0 + .ra: x30
STACK CFI aff4 .cfa: sp 624 +
STACK CFI b008 x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^
STACK CFI b028 .ra: .cfa -544 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI b4b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI b4b8 .cfa: sp 624 + .ra: .cfa -544 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI b724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI b728 .cfa: sp 624 + .ra: .cfa -544 + ^ x19: .cfa -624 + ^ x20: .cfa -616 + ^ x21: .cfa -608 + ^ x22: .cfa -600 + ^ x23: .cfa -592 + ^ x24: .cfa -584 + ^ x25: .cfa -576 + ^ x26: .cfa -568 + ^ x27: .cfa -560 + ^ x28: .cfa -552 + ^
STACK CFI INIT b8f0 738 .cfa: sp 0 + .ra: x30
STACK CFI b8f4 .cfa: sp 432 + x19: .cfa -432 + ^ x20: .cfa -424 + ^
STACK CFI b910 x21: .cfa -416 + ^ x22: .cfa -408 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI b920 .ra: .cfa -352 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^
STACK CFI bd1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI bd20 .cfa: sp 432 + .ra: .cfa -352 + ^ x19: .cfa -432 + ^ x20: .cfa -424 + ^ x21: .cfa -416 + ^ x22: .cfa -408 + ^ x23: .cfa -400 + ^ x24: .cfa -392 + ^ x25: .cfa -384 + ^ x26: .cfa -376 + ^ x27: .cfa -368 + ^ x28: .cfa -360 + ^
STACK CFI INIT c040 2c4 .cfa: sp 0 + .ra: x30
STACK CFI c044 .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI c048 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI c068 .ra: .cfa -112 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v12: .cfa -104 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI c254 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI c258 .cfa: sp 192 + .ra: .cfa -112 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v12: .cfa -104 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT c308 28c .cfa: sp 0 + .ra: x30
STACK CFI c30c .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI c328 .ra: .cfa -112 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI c438 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI c43c .cfa: sp 192 + .ra: .cfa -112 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT c5a0 4bc .cfa: sp 0 + .ra: x30
STACK CFI c5a4 .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI c5b0 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI c5c8 .ra: .cfa -176 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI c9fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI ca00 .cfa: sp 256 + .ra: .cfa -176 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI INIT ca80 1a0 .cfa: sp 0 + .ra: x30
STACK CFI ca84 .cfa: sp 240 + x19: .cfa -240 + ^ x20: .cfa -232 + ^
STACK CFI caa0 .ra: .cfa -200 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^
STACK CFI cbe4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI cbe8 .cfa: sp 240 + .ra: .cfa -200 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^
STACK CFI INIT cc30 7c .cfa: sp 0 + .ra: x30
STACK CFI cc34 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cc3c .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI cc94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI cc98 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT ccb0 b4 .cfa: sp 0 + .ra: x30
STACK CFI ccb8 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ccc4 .ra: .cfa -16 + ^
STACK CFI ccec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI ccf0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI cd34 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI cd40 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT cd70 2cc .cfa: sp 0 + .ra: x30
STACK CFI cd78 .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI cd8c .ra: .cfa -200 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^
STACK CFI cf4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI cf50 .cfa: sp 256 + .ra: .cfa -200 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^
STACK CFI INIT d060 344 .cfa: sp 0 + .ra: x30
STACK CFI d064 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI d070 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI d080 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI d2dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI d2e0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT d3b0 2288 .cfa: sp 0 + .ra: x30
STACK CFI d3b4 .cfa: sp 1616 +
STACK CFI d3b8 x19: .cfa -1600 + ^ x20: .cfa -1592 + ^
STACK CFI d3d4 .ra: .cfa -1520 + ^ v8: .cfa -1512 + ^ x21: .cfa -1584 + ^ x22: .cfa -1576 + ^ x23: .cfa -1568 + ^ x24: .cfa -1560 + ^ x25: .cfa -1552 + ^ x26: .cfa -1544 + ^ x27: .cfa -1536 + ^ x28: .cfa -1528 + ^
STACK CFI d428 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI d42c .cfa: sp 1616 + .ra: .cfa -1520 + ^ v8: .cfa -1512 + ^ x19: .cfa -1600 + ^ x20: .cfa -1592 + ^ x21: .cfa -1584 + ^ x22: .cfa -1576 + ^ x23: .cfa -1568 + ^ x24: .cfa -1560 + ^ x25: .cfa -1552 + ^ x26: .cfa -1544 + ^ x27: .cfa -1536 + ^ x28: .cfa -1528 + ^
STACK CFI INIT f650 100 .cfa: sp 0 + .ra: x30
STACK CFI f654 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI f65c .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI f664 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI f718 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI f720 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT f750 138 .cfa: sp 0 + .ra: x30
STACK CFI f754 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI f764 .ra: .cfa -24 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI f84c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI f850 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI INIT f888 100 .cfa: sp 0 + .ra: x30
STACK CFI f88c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI f894 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI f89c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI f950 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI f958 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT f990 c64 .cfa: sp 0 + .ra: x30
STACK CFI f994 .cfa: sp 800 +
STACK CFI f9c4 .ra: .cfa -720 + ^ v10: .cfa -688 + ^ v11: .cfa -680 + ^ v12: .cfa -672 + ^ v13: .cfa -664 + ^ v14: .cfa -712 + ^ v8: .cfa -704 + ^ v9: .cfa -696 + ^ x19: .cfa -800 + ^ x20: .cfa -792 + ^ x21: .cfa -784 + ^ x22: .cfa -776 + ^ x23: .cfa -768 + ^ x24: .cfa -760 + ^ x25: .cfa -752 + ^ x26: .cfa -744 + ^ x27: .cfa -736 + ^ x28: .cfa -728 + ^
STACK CFI 102f0 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 102f8 .cfa: sp 800 + .ra: .cfa -720 + ^ v10: .cfa -688 + ^ v11: .cfa -680 + ^ v12: .cfa -672 + ^ v13: .cfa -664 + ^ v14: .cfa -712 + ^ v8: .cfa -704 + ^ v9: .cfa -696 + ^ x19: .cfa -800 + ^ x20: .cfa -792 + ^ x21: .cfa -784 + ^ x22: .cfa -776 + ^ x23: .cfa -768 + ^ x24: .cfa -760 + ^ x25: .cfa -752 + ^ x26: .cfa -744 + ^ x27: .cfa -736 + ^ x28: .cfa -728 + ^
