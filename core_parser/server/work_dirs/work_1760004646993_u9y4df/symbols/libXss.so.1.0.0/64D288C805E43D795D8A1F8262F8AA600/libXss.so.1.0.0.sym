MODULE Linux arm64 64D288C805E43D795D8A1F8262F8AA600 libXss.so.1
INFO CODE_ID C888D264E405793D5D8A1F8262F8AA60C0C0E69C
PUBLIC 1050 0 XScreenSaverQueryExtension
PUBLIC 10c0 0 XScreenSaverQueryVersion
PUBLIC 1220 0 XScreenSaverAllocInfo
PUBLIC 1240 0 XScreenSaverQueryInfo
PUBLIC 13b0 0 XScreenSaverSelectInput
PUBLIC 1494 0 XScreenSaverSetAttributes
PUBLIC 1760 0 XScreenSaverUnsetAttributes
PUBLIC 1840 0 XScreenSaverRegister
PUBLIC 1904 0 XScreenSaverUnregister
PUBLIC 1974 0 XScreenSaverGetRegistered
PUBLIC 1a94 0 XScreenSaverSuspend
STACK CFI INIT d00 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT d30 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT d70 48 .cfa: sp 0 + .ra: x30
STACK CFI d74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d7c x19: .cfa -16 + ^
STACK CFI db4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT dc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT dd0 84 .cfa: sp 0 + .ra: x30
STACK CFI dd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI de0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI e04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI e0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI e40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT e54 24 .cfa: sp 0 + .ra: x30
STACK CFI e5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI e68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT e80 e8 .cfa: sp 0 + .ra: x30
STACK CFI e88 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI e90 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e9c x21: .cfa -16 + ^
STACK CFI ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI edc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI f60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT f70 e0 .cfa: sp 0 + .ra: x30
STACK CFI f78 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI f80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f8c x21: .cfa -16 + ^
STACK CFI fc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI fcc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1034 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 103c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1050 6c .cfa: sp 0 + .ra: x30
STACK CFI 1058 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1060 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 10b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10c0 158 .cfa: sp 0 + .ra: x30
STACK CFI 10c8 .cfa: sp 96 +
STACK CFI 10d4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 10dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10e4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 11c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 11d0 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1220 1c .cfa: sp 0 + .ra: x30
STACK CFI 1228 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1234 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1240 170 .cfa: sp 0 + .ra: x30
STACK CFI 1248 .cfa: sp 96 +
STACK CFI 1254 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 125c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1264 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1368 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 13b0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 13b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 13c0 v8: .cfa -16 + ^ v9: .cfa -8 + ^
STACK CFI 13cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1458 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 1460 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1474 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI 1480 .cfa: sp 48 + .ra: .cfa -40 + ^ v8: .cfa -16 + ^ v9: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 148c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x29: x29
STACK CFI INIT 1494 2c8 .cfa: sp 0 + .ra: x30
STACK CFI 149c .cfa: sp 384 +
STACK CFI 14a8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 14b8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 14c0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 14c8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 14d4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 14dc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 15e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 15e8 .cfa: sp 384 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 170c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1714 .cfa: sp 384 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 1744 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 174c .cfa: sp 384 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 1760 d8 .cfa: sp 0 + .ra: x30
STACK CFI 1768 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1770 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1778 x21: .cfa -16 + ^
STACK CFI 17fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1804 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1818 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1824 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1830 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1840 c4 .cfa: sp 0 + .ra: x30
STACK CFI 1848 .cfa: sp 64 +
STACK CFI 1854 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 185c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1868 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 18f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1900 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1904 70 .cfa: sp 0 + .ra: x30
STACK CFI 190c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1918 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 193c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1944 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 196c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1974 120 .cfa: sp 0 + .ra: x30
STACK CFI 197c .cfa: sp 128 +
STACK CFI 1988 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1990 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 199c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1a08 .cfa: sp 128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1a94 d8 .cfa: sp 0 + .ra: x30
STACK CFI 1a9c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1aa4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1aac x21: .cfa -16 + ^
STACK CFI 1b30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1b38 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1b4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1b58 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 1b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
