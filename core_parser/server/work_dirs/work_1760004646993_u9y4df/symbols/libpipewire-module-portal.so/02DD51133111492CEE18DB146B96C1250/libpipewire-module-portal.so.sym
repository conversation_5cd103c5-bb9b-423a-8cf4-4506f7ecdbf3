MODULE Linux arm64 02DD51133111492CEE18DB146B96C1250 libpipewire-module-portal.so
INFO CODE_ID 1351DD0211312C49EE18DB146B96C12509A91326
PUBLIC 1bd0 0 pipewire__module_init
STACK CFI INIT 1370 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13a0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 13e0 48 .cfa: sp 0 + .ra: x30
STACK CFI 13e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13ec x19: .cfa -16 + ^
STACK CFI 1424 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1430 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1440 154 .cfa: sp 0 + .ra: x30
STACK CFI 1448 .cfa: sp 96 +
STACK CFI 1454 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 145c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 149c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 14a4 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1594 a0 .cfa: sp 0 + .ra: x30
STACK CFI 159c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 15a4 x19: .cfa -16 + ^
STACK CFI 162c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1634 340 .cfa: sp 0 + .ra: x30
STACK CFI 163c .cfa: sp 96 +
STACK CFI 1648 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1650 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1658 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 16e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 16ec .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1974 ec .cfa: sp 0 + .ra: x30
STACK CFI 197c .cfa: sp 64 +
STACK CFI 198c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 199c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1a54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1a5c .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1a60 168 .cfa: sp 0 + .ra: x30
STACK CFI 1a68 .cfa: sp 80 +
STACK CFI 1a78 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a80 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1b4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1b54 .cfa: sp 80 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1bd0 388 .cfa: sp 0 + .ra: x30
STACK CFI 1bd8 .cfa: sp 128 +
STACK CFI 1be4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1bf0 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1bf8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1c48 x25: .cfa -16 + ^
STACK CFI 1d84 x25: x25
STACK CFI 1db8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 1dc0 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 1dd8 x25: x25
STACK CFI 1df4 x25: .cfa -16 + ^
STACK CFI 1df8 x25: x25
STACK CFI 1e00 x25: .cfa -16 + ^
STACK CFI 1e34 x25: x25
STACK CFI 1e38 x25: .cfa -16 + ^
STACK CFI 1ed0 x25: x25
STACK CFI 1edc x25: .cfa -16 + ^
STACK CFI 1f48 x25: x25
STACK CFI 1f54 x25: .cfa -16 + ^
