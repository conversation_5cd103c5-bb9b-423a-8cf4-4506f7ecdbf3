MODULE Linux arm64 2D5FCFB1C2A3161AB4D10D2D1245307E0 librygel-media-engine-simple.so
INFO CODE_ID B1CF5F2DA3C21A16B4D10D2D1245307E423FC6E0
PUBLIC 3af4 0 rygel_simple_media_engine_construct
PUBLIC 3b10 0 rygel_simple_media_engine_get_type
PUBLIC 3b90 0 rygel_simple_media_engine_new
PUBLIC 3bb0 0 module_get_instance
PUBLIC 3bd0 0 rygel_simple_data_source_construct
PUBLIC 3c80 0 rygel_simple_data_source_pool_func
PUBLIC 3cf0 0 rygel_simple_data_source_get_type
PUBLIC 3d70 0 rygel_simple_data_source_new
STACK CFI INIT 2680 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 26b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 26f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 26f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 26fc x19: .cfa -16 + ^
STACK CFI 2734 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2740 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2750 20 .cfa: sp 0 + .ra: x30
STACK CFI 2758 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2764 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2770 28 .cfa: sp 0 + .ra: x30
STACK CFI 277c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2788 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 27a0 28 .cfa: sp 0 + .ra: x30
STACK CFI 27a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 27b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 27bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 27c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 27d0 15c .cfa: sp 0 + .ra: x30
STACK CFI 27d8 .cfa: sp 64 +
STACK CFI 27dc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 27e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 27f8 x21: .cfa -16 + ^
STACK CFI 28ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 28f4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2930 74 .cfa: sp 0 + .ra: x30
STACK CFI 2938 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2944 x19: .cfa -16 + ^
STACK CFI 2994 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 29a4 5c .cfa: sp 0 + .ra: x30
STACK CFI 29ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 29b4 x19: .cfa -16 + ^
STACK CFI 29f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2a00 3e4 .cfa: sp 0 + .ra: x30
STACK CFI 2a10 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2a18 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2a24 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2a30 x23: .cfa -16 + ^
STACK CFI 2c10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c18 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2ca0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2ca8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2ce0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2d98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2db4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2de4 30 .cfa: sp 0 + .ra: x30
STACK CFI 2dec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2e0c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2e14 8c .cfa: sp 0 + .ra: x30
STACK CFI 2e1c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e24 x19: .cfa -16 + ^
STACK CFI 2e98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2ea0 50 .cfa: sp 0 + .ra: x30
STACK CFI 2ea8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2eb0 x19: .cfa -16 + ^
STACK CFI 2ee8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2ef0 58 .cfa: sp 0 + .ra: x30
STACK CFI 2ef8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f00 x19: .cfa -16 + ^
STACK CFI 2f40 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2f50 1d4 .cfa: sp 0 + .ra: x30
STACK CFI 2f58 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2f60 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2f6c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2f74 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 3080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3088 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3124 108 .cfa: sp 0 + .ra: x30
STACK CFI 312c .cfa: sp 64 +
STACK CFI 313c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 314c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3154 x21: .cfa -16 + ^
STACK CFI 31d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 31dc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3230 48 .cfa: sp 0 + .ra: x30
STACK CFI 3238 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3240 x19: .cfa -16 + ^
STACK CFI 3270 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3280 64 .cfa: sp 0 + .ra: x30
STACK CFI 3288 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3290 x19: .cfa -16 + ^
STACK CFI 32b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 32c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 32dc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 32e4 6c .cfa: sp 0 + .ra: x30
STACK CFI 32ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 32f4 x19: .cfa -16 + ^
STACK CFI 331c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3324 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3348 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3350 68 .cfa: sp 0 + .ra: x30
STACK CFI 3358 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3360 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3380 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3388 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 33b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 33c0 50 .cfa: sp 0 + .ra: x30
STACK CFI 33e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3404 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3410 28 .cfa: sp 0 + .ra: x30
STACK CFI 3418 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 342c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3440 420 .cfa: sp 0 + .ra: x30
STACK CFI 3448 .cfa: sp 112 +
STACK CFI 3454 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 345c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 347c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3480 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3488 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3500 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 35f8 x27: x27 x28: x28
STACK CFI 3728 x21: x21 x22: x22
STACK CFI 372c x23: x23 x24: x24
STACK CFI 3730 x25: x25 x26: x26
STACK CFI 3758 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3760 .cfa: sp 112 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 3780 x27: x27 x28: x28
STACK CFI 37c4 x21: x21 x22: x22
STACK CFI 37c8 x23: x23 x24: x24
STACK CFI 37cc x25: x25 x26: x26
STACK CFI 37d0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3824 x27: x27 x28: x28
STACK CFI 3828 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 3850 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3854 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3858 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 385c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 3860 74 .cfa: sp 0 + .ra: x30
STACK CFI 3868 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3870 x19: .cfa -16 + ^
STACK CFI 3890 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3898 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 38cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 38d4 5c .cfa: sp 0 + .ra: x30
STACK CFI 38dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 38e4 x19: .cfa -16 + ^
STACK CFI 3928 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3930 54 .cfa: sp 0 + .ra: x30
STACK CFI 3938 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3944 x19: .cfa -16 + ^
STACK CFI 397c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3984 f4 .cfa: sp 0 + .ra: x30
STACK CFI 398c .cfa: sp 80 +
STACK CFI 3998 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 39a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 39a8 x21: .cfa -16 + ^
STACK CFI 3a44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3a4c .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3a80 74 .cfa: sp 0 + .ra: x30
STACK CFI 3a88 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a94 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3aec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3af4 18 .cfa: sp 0 + .ra: x30
STACK CFI 3afc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3b10 78 .cfa: sp 0 + .ra: x30
STACK CFI 3b18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3b20 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3b4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3b54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3b80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3b90 1c .cfa: sp 0 + .ra: x30
STACK CFI 3b98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ba4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3bb0 18 .cfa: sp 0 + .ra: x30
STACK CFI 3bb8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3bc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3bd0 ac .cfa: sp 0 + .ra: x30
STACK CFI 3bd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3be0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3bec x21: .cfa -16 + ^
STACK CFI 3c3c x21: x21
STACK CFI 3c4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3c54 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3c80 54 .cfa: sp 0 + .ra: x30
STACK CFI 3c90 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3c98 x19: .cfa -16 + ^
STACK CFI 3cac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3cd4 18 .cfa: sp 0 + .ra: x30
STACK CFI 3cdc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3ce4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3cf0 78 .cfa: sp 0 + .ra: x30
STACK CFI 3cf8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3d00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3d2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3d34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3d60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3d70 34 .cfa: sp 0 + .ra: x30
STACK CFI 3d78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3d80 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3d9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 3da4 160 .cfa: sp 0 + .ra: x30
STACK CFI 3dac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3db4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3dcc x21: .cfa -16 + ^
STACK CFI 3e3c x21: x21
STACK CFI 3e48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3e50 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3e88 x21: x21
STACK CFI INIT 3f04 cc .cfa: sp 0 + .ra: x30
STACK CFI 3f0c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3f18 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3f48 x19: x19 x20: x20
STACK CFI 3f50 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3f58 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 3f9c x19: x19 x20: x20
STACK CFI 3fa0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3fac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3fd0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2640 24 .cfa: sp 0 + .ra: x30
STACK CFI 2644 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 265c .cfa: sp 0 + .ra: .ra x29: x29
