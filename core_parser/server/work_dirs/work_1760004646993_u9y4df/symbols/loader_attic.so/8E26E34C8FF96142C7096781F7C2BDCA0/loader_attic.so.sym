MODULE Linux arm64 8E26E34C8FF96142C7096781F7C2BDCA0 loader_attic.so
INFO CODE_ID 4CE3268EF98F4261C7096781F7C2BDCA7B4F91C4
PUBLIC 9480 0 v_check
PUBLIC 94b0 0 bind_engine
STACK CFI INIT 42d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4300 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4340 48 .cfa: sp 0 + .ra: x30
STACK CFI 4344 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 434c x19: .cfa -16 + ^
STACK CFI 4384 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4390 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 43a0 174 .cfa: sp 0 + .ra: x30
STACK CFI 43a8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 43b0 x21: .cfa -16 + ^
STACK CFI 43b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 43f8 x19: x19 x20: x20
STACK CFI 4404 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 441c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 445c x19: x19 x20: x20
STACK CFI 446c .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 4484 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 4498 x19: x19 x20: x20
STACK CFI 44a4 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 44bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4514 110 .cfa: sp 0 + .ra: x30
STACK CFI 451c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4524 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 452c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4538 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4544 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 45b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 45e0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4624 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 4638 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4700 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 472c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 48b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 48e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 4920 94 .cfa: sp 0 + .ra: x30
STACK CFI 4928 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4950 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4968 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4970 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4988 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4994 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 49b4 60c .cfa: sp 0 + .ra: x30
STACK CFI 49bc .cfa: sp 176 +
STACK CFI 49c8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 49d0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 49e0 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 49e8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4a90 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4bd8 x25: x25 x26: x26
STACK CFI 4c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 4c3c .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 4dbc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4dc8 x25: x25 x26: x26
STACK CFI 4df4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4e04 x25: x25 x26: x26
STACK CFI 4e08 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4f4c x25: x25 x26: x26
STACK CFI 4f64 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4f70 x25: x25 x26: x26
STACK CFI 4f74 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4fb0 x25: x25 x26: x26
STACK CFI 4fb4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4fb8 x25: x25 x26: x26
STACK CFI INIT 4fc0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 4fc8 .cfa: sp 64 +
STACK CFI 4fd4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4fdc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5000 x21: .cfa -16 + ^
STACK CFI 5034 x21: x21
STACK CFI 5068 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5088 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5094 x21: .cfa -16 + ^
STACK CFI INIT 50a0 3e4 .cfa: sp 0 + .ra: x30
STACK CFI 50a8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 50c0 .cfa: sp 1200 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 513c x27: .cfa -16 + ^
STACK CFI 5144 x28: .cfa -8 + ^
STACK CFI 52d4 x27: x27
STACK CFI 52dc x28: x28
STACK CFI 5308 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 530c x27: x27
STACK CFI 5310 x28: x28
STACK CFI 5350 .cfa: sp 96 +
STACK CFI 5368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 5394 .cfa: sp 1200 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 539c x27: x27
STACK CFI 53a0 x28: x28
STACK CFI 53a8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 53e0 x27: x27
STACK CFI 53e8 x28: x28
STACK CFI 5448 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 5478 x27: x27 x28: x28
STACK CFI 547c x27: .cfa -16 + ^
STACK CFI 5480 x28: .cfa -8 + ^
STACK CFI INIT 5484 250 .cfa: sp 0 + .ra: x30
STACK CFI 548c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5494 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 549c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 54a4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 54b4 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 5658 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5678 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 56d4 330 .cfa: sp 0 + .ra: x30
STACK CFI 56dc .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 56e4 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 56ec x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 56f8 x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 5700 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 5920 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 593c .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5a04 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 5a0c .cfa: sp 128 +
STACK CFI 5a1c .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5a24 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5a30 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5a5c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5aac x25: .cfa -16 + ^
STACK CFI 5b34 x23: x23 x24: x24
STACK CFI 5b38 x25: x25
STACK CFI 5ba0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5bc4 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 5bd0 x25: x25
STACK CFI 5c04 x23: x23 x24: x24
STACK CFI 5c08 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5c0c x23: x23 x24: x24
STACK CFI 5c10 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 5cb0 x23: x23 x24: x24 x25: x25
STACK CFI 5cb4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5cb8 x25: .cfa -16 + ^
STACK CFI INIT 5cc0 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 5cc8 .cfa: sp 80 +
STACK CFI 5cd8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5ce4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5cf0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5d90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5db8 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5e70 480 .cfa: sp 0 + .ra: x30
STACK CFI 5e78 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 5e90 .cfa: sp 1200 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5edc x27: .cfa -16 + ^
STACK CFI 5ee4 x28: .cfa -8 + ^
STACK CFI 6048 x27: x27
STACK CFI 604c x28: x28
STACK CFI 6094 .cfa: sp 96 +
STACK CFI 60ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 60dc .cfa: sp 1200 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6160 x27: x27
STACK CFI 6164 x28: x28
STACK CFI 6168 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 61d8 x27: x27 x28: x28
STACK CFI 6224 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 62e4 x27: x27 x28: x28
STACK CFI 62e8 x27: .cfa -16 + ^
STACK CFI 62ec x28: .cfa -8 + ^
STACK CFI INIT 62f0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 62f8 .cfa: sp 32 +
STACK CFI 6308 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 636c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 638c .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6390 78 .cfa: sp 0 + .ra: x30
STACK CFI 6398 .cfa: sp 32 +
STACK CFI 63a8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 63e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6404 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6410 7c .cfa: sp 0 + .ra: x30
STACK CFI 6418 .cfa: sp 32 +
STACK CFI 6428 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6468 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6488 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6490 74 .cfa: sp 0 + .ra: x30
STACK CFI 6498 .cfa: sp 32 +
STACK CFI 64a8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 64e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6500 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6504 7c .cfa: sp 0 + .ra: x30
STACK CFI 650c .cfa: sp 32 +
STACK CFI 651c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 655c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 657c .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6580 1c .cfa: sp 0 + .ra: x30
STACK CFI 6588 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6594 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 65a0 1c .cfa: sp 0 + .ra: x30
STACK CFI 65a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 65b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 65c0 198 .cfa: sp 0 + .ra: x30
STACK CFI 65c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6624 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 664c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 66a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 66cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6760 228 .cfa: sp 0 + .ra: x30
STACK CFI 6768 .cfa: sp 176 +
STACK CFI 6774 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6780 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 678c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 679c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 67a4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 67f4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 687c x27: x27 x28: x28
STACK CFI 68b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 68e0 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 6918 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 697c x27: x27 x28: x28
STACK CFI 6984 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 6990 94 .cfa: sp 0 + .ra: x30
STACK CFI 6998 .cfa: sp 32 +
STACK CFI 69a8 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 69f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6a20 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6a24 20 .cfa: sp 0 + .ra: x30
STACK CFI 6a2c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6a38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6a44 90 .cfa: sp 0 + .ra: x30
STACK CFI 6a4c .cfa: sp 32 +
STACK CFI 6a5c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6aa4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6ad0 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6ad4 20 .cfa: sp 0 + .ra: x30
STACK CFI 6adc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6ae8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6af4 b0 .cfa: sp 0 + .ra: x30
STACK CFI 6afc .cfa: sp 32 +
STACK CFI 6b0c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6b74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6ba0 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6ba4 20 .cfa: sp 0 + .ra: x30
STACK CFI 6bac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6bb8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6bc4 11c .cfa: sp 0 + .ra: x30
STACK CFI 6bcc .cfa: sp 64 +
STACK CFI 6bd8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6be0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6c04 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6c44 x21: x21 x22: x22
STACK CFI 6c70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6ca0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6cd0 x21: x21 x22: x22
STACK CFI 6cdc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 6ce0 20 .cfa: sp 0 + .ra: x30
STACK CFI 6ce8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6cf4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6d00 2c .cfa: sp 0 + .ra: x30
STACK CFI 6d0c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6d1c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6d30 24 .cfa: sp 0 + .ra: x30
STACK CFI 6d38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6d44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6d54 1c .cfa: sp 0 + .ra: x30
STACK CFI 6d5c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6d68 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6d70 78 .cfa: sp 0 + .ra: x30
STACK CFI 6d78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6d88 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6dbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6dd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6df0 90 .cfa: sp 0 + .ra: x30
STACK CFI 6df8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6e00 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6e08 x21: .cfa -16 + ^
STACK CFI 6e60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6e78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6e80 390 .cfa: sp 0 + .ra: x30
STACK CFI 6e88 .cfa: sp 192 +
STACK CFI 6e94 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6ea4 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6eac x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6eb4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 6ebc x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6fc8 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7210 ac .cfa: sp 0 + .ra: x30
STACK CFI 7218 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7220 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7270 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7290 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 729c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 72c0 204 .cfa: sp 0 + .ra: x30
STACK CFI 72c8 .cfa: sp 128 +
STACK CFI 72d4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 72e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 72e8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 73ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 73d0 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 73dc x23: x23 x24: x24
STACK CFI 73e0 x25: x25
STACK CFI 73f0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 73fc x25: .cfa -16 + ^
STACK CFI 749c x23: x23 x24: x24
STACK CFI 74a0 x25: x25
STACK CFI 74a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 74ac x23: x23 x24: x24 x25: x25
STACK CFI 74bc x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 74c0 x25: .cfa -16 + ^
STACK CFI INIT 74c4 bc .cfa: sp 0 + .ra: x30
STACK CFI 74cc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 74d4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 752c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 754c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 7560 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7580 16c .cfa: sp 0 + .ra: x30
STACK CFI 7588 .cfa: sp 96 +
STACK CFI 7594 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 75a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 75a8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 75b0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 765c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 767c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 76f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 76f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7704 x19: .cfa -16 + ^
STACK CFI 7720 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7740 44 .cfa: sp 0 + .ra: x30
STACK CFI 7750 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7760 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7784 78 .cfa: sp 0 + .ra: x30
STACK CFI 778c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7794 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 77b8 x21: .cfa -16 + ^
STACK CFI 77e8 x21: x21
STACK CFI 77f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7800 f8 .cfa: sp 0 + .ra: x30
STACK CFI 7808 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7818 .cfa: sp 4144 + x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 78c8 .cfa: sp 32 +
STACK CFI 78d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 78f4 .cfa: sp 4144 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7900 84 .cfa: sp 0 + .ra: x30
STACK CFI 7908 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7914 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7924 x21: .cfa -16 + ^
STACK CFI 797c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 7984 bc .cfa: sp 0 + .ra: x30
STACK CFI 798c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7994 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 79c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 79d8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 79f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7a04 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7a30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7a40 8c .cfa: sp 0 + .ra: x30
STACK CFI 7a48 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7a50 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7a5c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7ab0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7ac0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7ad0 378 .cfa: sp 0 + .ra: x30
STACK CFI 7ad8 .cfa: sp 272 +
STACK CFI 7ae4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7afc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7b04 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7b0c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7c90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7cb0 .cfa: sp 272 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 7e50 28 .cfa: sp 0 + .ra: x30
STACK CFI 7e58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7e64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7e80 fc .cfa: sp 0 + .ra: x30
STACK CFI 7e88 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 7e94 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 7ea0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 7eb0 x23: .cfa -16 + ^
STACK CFI 7f14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7f30 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 7f80 ec .cfa: sp 0 + .ra: x30
STACK CFI 7fd0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7fe8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8070 c4 .cfa: sp 0 + .ra: x30
STACK CFI 8078 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8080 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 808c x21: .cfa -16 + ^
STACK CFI 80c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 80dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8134 168 .cfa: sp 0 + .ra: x30
STACK CFI 813c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8144 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 814c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8158 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8164 x25: .cfa -16 + ^
STACK CFI 8228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 824c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 82a0 84 .cfa: sp 0 + .ra: x30
STACK CFI 82a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 82d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 82f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 82fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8324 260 .cfa: sp 0 + .ra: x30
STACK CFI 832c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8340 .cfa: sp 1152 + x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 84b4 .cfa: sp 64 +
STACK CFI 84c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 84f4 .cfa: sp 1152 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8584 38c .cfa: sp 0 + .ra: x30
STACK CFI 858c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 85a0 .cfa: sp 1168 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8600 .cfa: sp 96 +
STACK CFI 8614 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 8640 .cfa: sp 1168 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 8658 x21: .cfa -64 + ^
STACK CFI 8660 x22: .cfa -56 + ^
STACK CFI 8674 x27: .cfa -16 + ^
STACK CFI 86b8 x21: x21
STACK CFI 86bc x22: x22
STACK CFI 86c0 x27: x27
STACK CFI 86c4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 86cc x21: x21
STACK CFI 86d0 x22: x22
STACK CFI 86d4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 8844 x21: x21
STACK CFI 8848 x22: x22
STACK CFI 884c x27: x27
STACK CFI 8850 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^
STACK CFI 8900 x21: x21 x22: x22 x27: x27
STACK CFI 8904 x21: .cfa -64 + ^
STACK CFI 8908 x22: .cfa -56 + ^
STACK CFI 890c x27: .cfa -16 + ^
STACK CFI INIT 8910 98 .cfa: sp 0 + .ra: x30
STACK CFI 8918 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8920 x19: .cfa -16 + ^
STACK CFI 8964 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8980 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 898c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 89b0 1c .cfa: sp 0 + .ra: x30
STACK CFI 89b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 89c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 89d0 aa8 .cfa: sp 0 + .ra: x30
STACK CFI 89d8 .cfa: sp 368 +
STACK CFI 89dc .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 89e4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8a18 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8a20 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8a78 x25: x25 x26: x26
STACK CFI 8ab0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 8ae0 .cfa: sp 368 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8c84 x25: x25 x26: x26
STACK CFI 8c8c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 91b4 x25: x25 x26: x26
STACK CFI 93f0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 93f4 x25: x25 x26: x26
STACK CFI 93fc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9434 x25: x25 x26: x26
STACK CFI 9468 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 946c x25: x25 x26: x26
STACK CFI INIT 9480 2c .cfa: sp 0 + .ra: x30
STACK CFI 9488 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 949c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 94b0 264 .cfa: sp 0 + .ra: x30
STACK CFI 94b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 94c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 94cc x21: .cfa -16 + ^
STACK CFI 958c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 95a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
