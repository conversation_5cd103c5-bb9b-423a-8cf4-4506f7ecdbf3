MODULE Linux arm64 B6462D82292512EB1D3F652AD37258050 libcolord_sensor_colorhug.so
INFO CODE_ID 822D46B62529EB121D3F652AD3725805F5007100
PUBLIC 2504 0 cd_sensor_get_sample_async
PUBLIC 26f0 0 cd_sensor_get_sample_finish
PUBLIC 2764 0 cd_sensor_lock_async
PUBLIC 2980 0 cd_sensor_lock_finish
PUBLIC 29f4 0 cd_sensor_unlock_async
PUBLIC 2ad0 0 cd_sensor_unlock_finish
PUBLIC 2b44 0 cd_sensor_set_options_async
PUBLIC 2c50 0 cd_sensor_set_options_finish
PUBLIC 2cc4 0 cd_sensor_coldplug
STACK CFI INIT 1b60 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1b90 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1bd0 48 .cfa: sp 0 + .ra: x30
STACK CFI 1bd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1bdc x19: .cfa -16 + ^
STACK CFI 1c14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1c20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1c30 3c .cfa: sp 0 + .ra: x30
STACK CFI 1c40 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c48 x19: .cfa -16 + ^
STACK CFI 1c60 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1c70 3c .cfa: sp 0 + .ra: x30
STACK CFI 1c78 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1c80 x19: .cfa -16 + ^
STACK CFI 1ca4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1cb0 124 .cfa: sp 0 + .ra: x30
STACK CFI 1cb8 .cfa: sp 64 +
STACK CFI 1cc4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1ccc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1cd4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1d94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d9c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1dd4 b8 .cfa: sp 0 + .ra: x30
STACK CFI 1ddc .cfa: sp 48 +
STACK CFI 1de8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1df0 x19: .cfa -16 + ^
STACK CFI 1e50 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1e58 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1e90 100 .cfa: sp 0 + .ra: x30
STACK CFI 1e98 .cfa: sp 64 +
STACK CFI 1ea4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1eac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1eb8 x21: .cfa -16 + ^
STACK CFI 1f54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1f5c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1f90 170 .cfa: sp 0 + .ra: x30
STACK CFI 1f98 .cfa: sp 80 +
STACK CFI 1fa4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1fac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1fb4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1fc0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 20bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 20c4 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2100 e4 .cfa: sp 0 + .ra: x30
STACK CFI 2108 .cfa: sp 48 +
STACK CFI 2114 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 211c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 21a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 21b0 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 21e4 268 .cfa: sp 0 + .ra: x30
STACK CFI 21ec .cfa: sp 144 +
STACK CFI 21f8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2200 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2218 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2250 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 22a4 x27: .cfa -16 + ^
STACK CFI 233c x21: x21 x22: x22
STACK CFI 2340 x27: x27
STACK CFI 2344 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 237c x21: x21 x22: x22
STACK CFI 23b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 23c0 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 23f4 x27: x27
STACK CFI 23f8 x27: .cfa -16 + ^
STACK CFI 23fc x21: x21 x22: x22
STACK CFI 2400 x27: x27
STACK CFI 2444 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2448 x27: .cfa -16 + ^
STACK CFI INIT 2450 b4 .cfa: sp 0 + .ra: x30
STACK CFI 2458 .cfa: sp 48 +
STACK CFI 2464 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 246c x19: .cfa -16 + ^
STACK CFI 24c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 24d0 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2504 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 250c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2514 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2528 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2530 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2648 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2650 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2690 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2698 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 26b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 26c8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 26f0 74 .cfa: sp 0 + .ra: x30
STACK CFI 26f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2700 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2728 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2730 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 275c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2764 218 .cfa: sp 0 + .ra: x30
STACK CFI 276c .cfa: sp 80 +
STACK CFI 2778 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2780 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2788 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2794 x23: .cfa -16 + ^
STACK CFI 2904 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 290c .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2980 74 .cfa: sp 0 + .ra: x30
STACK CFI 2988 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2990 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 29b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 29c0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 29ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 29f4 d4 .cfa: sp 0 + .ra: x30
STACK CFI 29fc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a10 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2a7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2a84 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2a90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2a98 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2aac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2ad0 74 .cfa: sp 0 + .ra: x30
STACK CFI 2ad8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ae0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2b08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2b10 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2b3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2b44 10c .cfa: sp 0 + .ra: x30
STACK CFI 2b4c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2b54 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2b60 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2b6c x23: .cfa -16 + ^
STACK CFI 2bfc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c04 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 2c1c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2c38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2c50 74 .cfa: sp 0 + .ra: x30
STACK CFI 2c58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c60 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2c88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2c90 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2cbc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2cc4 90 .cfa: sp 0 + .ra: x30
STACK CFI 2ccc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2cdc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2d4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
