MODULE Linux arm64 AF53175CB3D0AB9594D8DF1B238F437F0 libunity-extras.so.9
INFO CODE_ID 5C1753AFD0B395AB94D8DF1B238F437FAE9C379E
PUBLIC 4640 0 unity_extras_preview_player_service_register_object
PUBLIC 4714 0 unity_extras_file_manager_interface_register_object
PUBLIC 5e04 0 unity_extras_preview_player_service_get_type
PUBLIC 5f30 0 unity_extras_preview_player_service_play
PUBLIC 5fc0 0 unity_extras_preview_player_service_play_finish
PUBLIC 6140 0 unity_extras_preview_player_service_pause
PUBLIC 61b4 0 unity_extras_preview_player_service_pause_finish
PUBLIC 6330 0 unity_extras_preview_player_service_pause_resume
PUBLIC 63a4 0 unity_extras_preview_player_service_pause_resume_finish
PUBLIC 6520 0 unity_extras_preview_player_service_resume
PUBLIC 6594 0 unity_extras_preview_player_service_resume_finish
PUBLIC 6710 0 unity_extras_preview_player_service_stop
PUBLIC 6784 0 unity_extras_preview_player_service_stop_finish
PUBLIC 6900 0 unity_extras_preview_player_service_close
PUBLIC 6974 0 unity_extras_preview_player_service_close_finish
PUBLIC 6af0 0 unity_extras_preview_player_service_video_properties
PUBLIC 6e64 0 unity_extras_preview_player_service_video_properties_finish
PUBLIC 71e4 0 unity_extras_preview_player_service_proxy_get_type
PUBLIC 7f34 0 unity_extras_preview_player_play
PUBLIC 8034 0 unity_extras_preview_player_play_finish
PUBLIC 8054 0 unity_extras_preview_player_pause
PUBLIC 8104 0 unity_extras_preview_player_pause_finish
PUBLIC 8124 0 unity_extras_preview_player_pause_resume
PUBLIC 81d4 0 unity_extras_preview_player_pause_resume_finish
PUBLIC 81f4 0 unity_extras_preview_player_resume
PUBLIC 82a4 0 unity_extras_preview_player_resume_finish
PUBLIC 82c4 0 unity_extras_preview_player_stop
PUBLIC 8374 0 unity_extras_preview_player_stop_finish
PUBLIC 8394 0 unity_extras_preview_player_close
PUBLIC 8444 0 unity_extras_preview_player_close_finish
PUBLIC 8464 0 unity_extras_preview_player_video_properties
PUBLIC 8564 0 unity_extras_preview_player_video_properties_finish
PUBLIC 85a0 0 unity_extras_preview_player_on_progress_signal
PUBLIC 8640 0 unity_extras_preview_player_construct
PUBLIC 8660 0 unity_extras_preview_player_get_type
PUBLIC 8714 0 unity_extras_preview_player_new
PUBLIC 87e0 0 unity_extras_file_manager_interface_get_type
PUBLIC 8904 0 unity_extras_file_manager_interface_show_items
PUBLIC 8ba0 0 unity_extras_file_manager_interface_show_items_finish
PUBLIC 8e24 0 unity_extras_file_manager_interface_proxy_get_type
PUBLIC 94b4 0 unity_extras_show_in_folder
PUBLIC 9570 0 unity_extras_show_in_folder_finish
PUBLIC 9590 0 unity_extras_dbus_name_has_owner
PUBLIC 9770 0 unity_extras_dbus_own_name
STACK CFI INIT 4260 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4290 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 42d0 48 .cfa: sp 0 + .ra: x30
STACK CFI 42d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 42dc x19: .cfa -16 + ^
STACK CFI 4314 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4320 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4330 a4 .cfa: sp 0 + .ra: x30
STACK CFI 4338 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 434c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 43d4 1c .cfa: sp 0 + .ra: x30
STACK CFI 43dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 43e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 43f0 1c .cfa: sp 0 + .ra: x30
STACK CFI 43f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4404 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4410 50 .cfa: sp 0 + .ra: x30
STACK CFI 4418 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4420 x19: .cfa -16 + ^
STACK CFI 4450 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4460 28 .cfa: sp 0 + .ra: x30
STACK CFI 446c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4478 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4490 18 .cfa: sp 0 + .ra: x30
STACK CFI 4498 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 44a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 44b0 18 .cfa: sp 0 + .ra: x30
STACK CFI 44b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 44c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 44d0 2c .cfa: sp 0 + .ra: x30
STACK CFI 44d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 44ec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4500 1c .cfa: sp 0 + .ra: x30
STACK CFI 4508 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4514 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4520 1c .cfa: sp 0 + .ra: x30
STACK CFI 4528 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4534 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4540 40 .cfa: sp 0 + .ra: x30
STACK CFI 4548 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4550 x19: .cfa -16 + ^
STACK CFI 4578 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4580 bc .cfa: sp 0 + .ra: x30
STACK CFI 4594 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 45a4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 45ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 45b8 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4610 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4618 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 4620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 4640 d4 .cfa: sp 0 + .ra: x30
STACK CFI 4648 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4650 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4658 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4668 x23: .cfa -16 + ^
STACK CFI 470c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 4714 a4 .cfa: sp 0 + .ra: x30
STACK CFI 471c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4724 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 472c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4738 x23: .cfa -16 + ^
STACK CFI 47a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 47c0 6c .cfa: sp 0 + .ra: x30
STACK CFI 47c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 47d0 x19: .cfa -16 + ^
STACK CFI 47fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4804 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4824 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4830 6c .cfa: sp 0 + .ra: x30
STACK CFI 4838 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4840 x19: .cfa -16 + ^
STACK CFI 486c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4874 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 4894 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 48a0 24 .cfa: sp 0 + .ra: x30
STACK CFI 48a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 48b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 48c4 24 .cfa: sp 0 + .ra: x30
STACK CFI 48cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 48d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 48f0 130 .cfa: sp 0 + .ra: x30
STACK CFI 48f8 .cfa: sp 208 +
STACK CFI 4904 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 490c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 495c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4964 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 4978 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 497c x23: .cfa -16 + ^
STACK CFI 4980 v8: .cfa -8 + ^
STACK CFI 4a04 x21: x21 x22: x22
STACK CFI 4a08 x23: x23
STACK CFI 4a0c v8: v8
STACK CFI 4a14 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4a18 x23: .cfa -16 + ^
STACK CFI 4a1c v8: .cfa -8 + ^
STACK CFI INIT 4a20 44 .cfa: sp 0 + .ra: x30
STACK CFI 4a28 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a34 x19: .cfa -16 + ^
STACK CFI 4a5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4a64 44 .cfa: sp 0 + .ra: x30
STACK CFI 4a6c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4a78 x19: .cfa -16 + ^
STACK CFI 4aa0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4ab0 178 .cfa: sp 0 + .ra: x30
STACK CFI 4ab8 .cfa: sp 208 +
STACK CFI 4ac4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4acc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4ad4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4ae0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 4bf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4bf8 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4c30 160 .cfa: sp 0 + .ra: x30
STACK CFI 4c38 .cfa: sp 208 +
STACK CFI 4c44 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4c4c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4c54 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4c60 x23: .cfa -16 + ^
STACK CFI 4d58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4d60 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4d90 160 .cfa: sp 0 + .ra: x30
STACK CFI 4d98 .cfa: sp 208 +
STACK CFI 4da4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4dac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4db4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4dc0 x23: .cfa -16 + ^
STACK CFI 4eb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4ec0 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4ef0 160 .cfa: sp 0 + .ra: x30
STACK CFI 4ef8 .cfa: sp 208 +
STACK CFI 4f04 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4f0c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4f14 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4f20 x23: .cfa -16 + ^
STACK CFI 5018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5020 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5050 160 .cfa: sp 0 + .ra: x30
STACK CFI 5058 .cfa: sp 208 +
STACK CFI 5064 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 506c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5074 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5080 x23: .cfa -16 + ^
STACK CFI 5178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5180 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 51b0 160 .cfa: sp 0 + .ra: x30
STACK CFI 51b8 .cfa: sp 208 +
STACK CFI 51c4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 51cc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 51d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 51e0 x23: .cfa -16 + ^
STACK CFI 52d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 52e0 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5310 178 .cfa: sp 0 + .ra: x30
STACK CFI 5318 .cfa: sp 208 +
STACK CFI 5324 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 532c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5334 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5340 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5450 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5458 .cfa: sp 208 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5490 88 .cfa: sp 0 + .ra: x30
STACK CFI 5498 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 54a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 54b0 x21: .cfa -16 + ^
STACK CFI 54fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5504 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5520 18 .cfa: sp 0 + .ra: x30
STACK CFI 5528 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5530 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5540 18 .cfa: sp 0 + .ra: x30
STACK CFI 5548 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5550 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5560 18 .cfa: sp 0 + .ra: x30
STACK CFI 5568 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5570 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5580 18 .cfa: sp 0 + .ra: x30
STACK CFI 5588 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5590 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 55a0 18 .cfa: sp 0 + .ra: x30
STACK CFI 55a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 55b0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 55c0 18 .cfa: sp 0 + .ra: x30
STACK CFI 55c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 55d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 55e0 194 .cfa: sp 0 + .ra: x30
STACK CFI 55e8 .cfa: sp 368 +
STACK CFI 55f4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 55fc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5604 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 561c x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 5660 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5720 x23: x23 x24: x24
STACK CFI 5754 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 575c .cfa: sp 368 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 5770 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 5774 3c .cfa: sp 0 + .ra: x30
STACK CFI 577c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5784 x19: .cfa -16 + ^
STACK CFI 57a8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 57b0 48 .cfa: sp 0 + .ra: x30
STACK CFI 57b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 57c0 x19: .cfa -16 + ^
STACK CFI 57f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5800 3c .cfa: sp 0 + .ra: x30
STACK CFI 5808 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5810 x19: .cfa -16 + ^
STACK CFI 5834 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5840 3c .cfa: sp 0 + .ra: x30
STACK CFI 5848 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5850 x19: .cfa -16 + ^
STACK CFI 5874 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5880 3c .cfa: sp 0 + .ra: x30
STACK CFI 5888 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5890 x19: .cfa -16 + ^
STACK CFI 58b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 58c0 3c .cfa: sp 0 + .ra: x30
STACK CFI 58c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 58d0 x19: .cfa -16 + ^
STACK CFI 58f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5900 3c .cfa: sp 0 + .ra: x30
STACK CFI 5908 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5910 x19: .cfa -16 + ^
STACK CFI 5934 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5940 38 .cfa: sp 0 + .ra: x30
STACK CFI 5948 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5950 x19: .cfa -16 + ^
STACK CFI 5970 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5980 58 .cfa: sp 0 + .ra: x30
STACK CFI 5988 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5990 x19: .cfa -16 + ^
STACK CFI 59d0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 59e0 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 59e8 .cfa: sp 368 +
STACK CFI 59f4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 59fc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 5a08 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 5a10 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 5a18 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 5a24 x27: .cfa -16 + ^
STACK CFI 5b90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 5b98 .cfa: sp 368 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5bd0 108 .cfa: sp 0 + .ra: x30
STACK CFI 5bd8 .cfa: sp 208 +
STACK CFI 5be4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5bec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5bf8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5c08 v8: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 5ccc .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5cd4 .cfa: sp 208 + .ra: .cfa -56 + ^ v8: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5ce0 64 .cfa: sp 0 + .ra: x30
STACK CFI 5ce8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5cf4 x19: .cfa -16 + ^
STACK CFI 5d3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5d44 c0 .cfa: sp 0 + .ra: x30
STACK CFI 5d58 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5d68 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5d70 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5d7c x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5dd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5ddc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 5de4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 5e04 124 .cfa: sp 0 + .ra: x30
STACK CFI 5e0c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5e14 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5e40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5e48 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 5e58 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5f1c x21: x21 x22: x22
STACK CFI 5f20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5f30 88 .cfa: sp 0 + .ra: x30
STACK CFI 5f38 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5f40 x23: .cfa -16 + ^
STACK CFI 5f48 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5f54 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5f98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 5fa0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 5fb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 5fc0 74 .cfa: sp 0 + .ra: x30
STACK CFI 5fc8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5fd0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5fdc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6020 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 602c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6034 104 .cfa: sp 0 + .ra: x30
STACK CFI 603c .cfa: sp 192 +
STACK CFI 6048 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6050 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 605c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 60c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 60d0 .cfa: sp 192 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6140 74 .cfa: sp 0 + .ra: x30
STACK CFI 6148 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6150 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 615c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6198 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 61a0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 61ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 61b4 74 .cfa: sp 0 + .ra: x30
STACK CFI 61bc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 61c4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 61d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 620c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6214 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6230 f8 .cfa: sp 0 + .ra: x30
STACK CFI 6238 .cfa: sp 192 +
STACK CFI 6244 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 624c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6258 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 62b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 62c0 .cfa: sp 192 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6330 74 .cfa: sp 0 + .ra: x30
STACK CFI 6338 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6340 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 634c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6388 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6390 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 639c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 63a4 74 .cfa: sp 0 + .ra: x30
STACK CFI 63ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 63b4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 63c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 63fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6404 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6410 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6420 f8 .cfa: sp 0 + .ra: x30
STACK CFI 6428 .cfa: sp 192 +
STACK CFI 6434 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 643c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6448 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 64a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 64b0 .cfa: sp 192 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6520 74 .cfa: sp 0 + .ra: x30
STACK CFI 6528 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6530 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 653c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6578 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6580 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 658c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6594 74 .cfa: sp 0 + .ra: x30
STACK CFI 659c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 65a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 65b0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 65ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 65f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6600 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6610 f8 .cfa: sp 0 + .ra: x30
STACK CFI 6618 .cfa: sp 192 +
STACK CFI 6624 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 662c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6638 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6698 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 66a0 .cfa: sp 192 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6710 74 .cfa: sp 0 + .ra: x30
STACK CFI 6718 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6720 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 672c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6768 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6770 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 677c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6784 74 .cfa: sp 0 + .ra: x30
STACK CFI 678c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6794 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 67a0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 67dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 67e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 67f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6800 f8 .cfa: sp 0 + .ra: x30
STACK CFI 6808 .cfa: sp 192 +
STACK CFI 6814 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 681c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6828 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6888 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6890 .cfa: sp 192 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6900 74 .cfa: sp 0 + .ra: x30
STACK CFI 6908 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6910 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 691c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6958 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6960 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 696c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6974 74 .cfa: sp 0 + .ra: x30
STACK CFI 697c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6984 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6990 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 69cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 69d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 69e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 69f0 f8 .cfa: sp 0 + .ra: x30
STACK CFI 69f8 .cfa: sp 192 +
STACK CFI 6a04 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6a0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6a18 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6a78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6a80 .cfa: sp 192 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6af0 88 .cfa: sp 0 + .ra: x30
STACK CFI 6af8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6b00 x23: .cfa -16 + ^
STACK CFI 6b08 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6b14 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6b58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6b60 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 6b80 2e4 .cfa: sp 0 + .ra: x30
STACK CFI 6b88 .cfa: sp 192 +
STACK CFI 6b98 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6ba0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6bac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6c78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6c80 .cfa: sp 192 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6cd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6ce0 .cfa: sp 192 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6e64 78 .cfa: sp 0 + .ra: x30
STACK CFI 6e6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6e74 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6e80 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6ec4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 6ee0 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 6ee8 .cfa: sp 448 +
STACK CFI 6ef4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6efc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6f18 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x28: x28 x29: x29
STACK CFI 6f7c .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 6f80 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 6f8c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 6f98 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7094 x21: x21 x22: x22
STACK CFI 7098 x23: x23 x24: x24
STACK CFI 709c x25: x25 x26: x26
STACK CFI 70a4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 70a8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 70ac x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 70b0 6c .cfa: sp 0 + .ra: x30
STACK CFI 70b8 .cfa: sp 48 +
STACK CFI 70bc .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7114 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7120 c4 .cfa: sp 0 + .ra: x30
STACK CFI 7128 .cfa: sp 64 +
STACK CFI 7134 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 713c x19: .cfa -16 + ^
STACK CFI 71d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 71e0 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 71e4 70 .cfa: sp 0 + .ra: x30
STACK CFI 71ec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 71f4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7218 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7220 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 724c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7254 1dc .cfa: sp 0 + .ra: x30
STACK CFI 725c .cfa: sp 96 +
STACK CFI 7260 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7268 x19: .cfa -16 + ^
STACK CFI 7320 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7328 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7354 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 735c .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 73e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 73e8 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7400 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7408 .cfa: sp 96 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7430 b0 .cfa: sp 0 + .ra: x30
STACK CFI 743c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7444 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7454 x21: .cfa -16 + ^
STACK CFI 74b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 74e0 160 .cfa: sp 0 + .ra: x30
STACK CFI 74e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 74f0 x19: .cfa -16 + ^
STACK CFI 7544 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 754c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7584 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 758c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 760c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7618 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7640 24 .cfa: sp 0 + .ra: x30
STACK CFI 7648 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7654 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7664 14c .cfa: sp 0 + .ra: x30
STACK CFI 766c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7674 x19: .cfa -16 + ^
STACK CFI 76c0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 76cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 76f8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7700 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 777c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7788 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 77b0 24 .cfa: sp 0 + .ra: x30
STACK CFI 77b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 77c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 77d4 14c .cfa: sp 0 + .ra: x30
STACK CFI 77dc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 77e4 x19: .cfa -16 + ^
STACK CFI 7830 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 783c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7868 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7870 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 78ec .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 78f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7920 24 .cfa: sp 0 + .ra: x30
STACK CFI 7928 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7934 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7944 14c .cfa: sp 0 + .ra: x30
STACK CFI 794c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7954 x19: .cfa -16 + ^
STACK CFI 79a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 79ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 79d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 79e0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7a5c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7a68 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7a90 24 .cfa: sp 0 + .ra: x30
STACK CFI 7a98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7aa4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7ab4 14c .cfa: sp 0 + .ra: x30
STACK CFI 7abc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ac4 x19: .cfa -16 + ^
STACK CFI 7b10 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7b1c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7b48 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7b50 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7bcc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7bd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7c00 24 .cfa: sp 0 + .ra: x30
STACK CFI 7c08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7c14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7c24 14c .cfa: sp 0 + .ra: x30
STACK CFI 7c2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7c34 x19: .cfa -16 + ^
STACK CFI 7c80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7c8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7cb8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7cc0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7d3c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7d48 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7d70 24 .cfa: sp 0 + .ra: x30
STACK CFI 7d78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7d84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7d94 150 .cfa: sp 0 + .ra: x30
STACK CFI 7d9c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7da4 x19: .cfa -16 + ^
STACK CFI 7df8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7e00 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7e2c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7e34 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 7eb0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 7ebc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7ee4 24 .cfa: sp 0 + .ra: x30
STACK CFI 7eec .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7ef8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7f10 24 .cfa: sp 0 + .ra: x30
STACK CFI 7f18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7f24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7f34 100 .cfa: sp 0 + .ra: x30
STACK CFI 7f44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7f4c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7f5c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7fdc x19: x19 x20: x20
STACK CFI 7fe0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 7fe8 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 7ff4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 8034 20 .cfa: sp 0 + .ra: x30
STACK CFI 803c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8048 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8054 b0 .cfa: sp 0 + .ra: x30
STACK CFI 8064 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 806c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 807c x21: .cfa -16 + ^
STACK CFI 80dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 8104 20 .cfa: sp 0 + .ra: x30
STACK CFI 810c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8118 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8124 b0 .cfa: sp 0 + .ra: x30
STACK CFI 8134 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 813c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 814c x21: .cfa -16 + ^
STACK CFI 81ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 81d4 20 .cfa: sp 0 + .ra: x30
STACK CFI 81dc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 81e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 81f4 b0 .cfa: sp 0 + .ra: x30
STACK CFI 8204 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 820c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 821c x21: .cfa -16 + ^
STACK CFI 827c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 82a4 20 .cfa: sp 0 + .ra: x30
STACK CFI 82ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 82b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 82c4 b0 .cfa: sp 0 + .ra: x30
STACK CFI 82d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 82dc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 82ec x21: .cfa -16 + ^
STACK CFI 834c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 8374 20 .cfa: sp 0 + .ra: x30
STACK CFI 837c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8388 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8394 b0 .cfa: sp 0 + .ra: x30
STACK CFI 83a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 83ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 83bc x21: .cfa -16 + ^
STACK CFI 841c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 8444 20 .cfa: sp 0 + .ra: x30
STACK CFI 844c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8458 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8464 100 .cfa: sp 0 + .ra: x30
STACK CFI 8474 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 847c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 848c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 850c x19: x19 x20: x20
STACK CFI 8510 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 8518 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8524 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 8564 3c .cfa: sp 0 + .ra: x30
STACK CFI 856c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8590 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8598 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 85a0 84 .cfa: sp 0 + .ra: x30
STACK CFI 85a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 85bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 85d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 85d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 85fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8600 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8624 1c .cfa: sp 0 + .ra: x30
STACK CFI 862c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8638 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8640 1c .cfa: sp 0 + .ra: x30
STACK CFI 8648 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8654 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8660 b4 .cfa: sp 0 + .ra: x30
STACK CFI 8668 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8670 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8678 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 86a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 86ac .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 870c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8714 1c .cfa: sp 0 + .ra: x30
STACK CFI 871c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8728 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 8730 ac .cfa: sp 0 + .ra: x30
STACK CFI 8738 .cfa: sp 64 +
STACK CFI 873c .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8744 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 87d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 87e0 124 .cfa: sp 0 + .ra: x30
STACK CFI 87e8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 87f0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 881c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8824 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 8834 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 88f0 x21: x21 x22: x22
STACK CFI 88fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8904 a4 .cfa: sp 0 + .ra: x30
STACK CFI 890c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 8914 x25: .cfa -16 + ^
STACK CFI 891c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8928 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8934 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 8984 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 898c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 89a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI INIT 89b0 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 89b8 .cfa: sp 368 +
STACK CFI 89c8 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 89d0 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 89dc x27: .cfa -16 + ^
STACK CFI 8a04 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8a10 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8a18 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8b34 x21: x21 x22: x22
STACK CFI 8b38 x23: x23 x24: x24
STACK CFI 8b3c x25: x25 x26: x26
STACK CFI 8b44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x29: x29
STACK CFI 8b4c .cfa: sp 368 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 8b7c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x27: x27 x29: x29
STACK CFI 8b84 .cfa: sp 368 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 8b88 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 8b8c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8b90 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8b94 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 8ba0 74 .cfa: sp 0 + .ra: x30
STACK CFI 8ba8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8bb0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8bbc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8bf8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8c00 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 8c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 8c14 144 .cfa: sp 0 + .ra: x30
STACK CFI 8c1c .cfa: sp 192 +
STACK CFI 8c28 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 8c30 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8c3c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8ce8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8cf0 .cfa: sp 192 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8d60 c4 .cfa: sp 0 + .ra: x30
STACK CFI 8d68 .cfa: sp 64 +
STACK CFI 8d74 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8d7c x19: .cfa -16 + ^
STACK CFI 8e18 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8e20 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8e24 70 .cfa: sp 0 + .ra: x30
STACK CFI 8e2c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8e34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8e60 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8e8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 8e94 5f8 .cfa: sp 0 + .ra: x30
STACK CFI 8e9c .cfa: sp 128 +
STACK CFI 8ea0 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8ea8 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 8ecc v8: .cfa -16 + ^
STACK CFI 8fa0 v8: v8
STACK CFI 8fa4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8fac .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 8fc0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9094 x21: x21 x22: x22
STACK CFI 90a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 90a8 .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 90c8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 91e4 x21: x21 x22: x22
STACK CFI 91e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 91f0 .cfa: sp 128 + .ra: .cfa -56 + ^ v8: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 9200 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 92a4 x21: x21 x22: x22
STACK CFI 92a8 v8: v8
STACK CFI 92b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 92bc .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 9388 v8: .cfa -16 + ^ x21: x21 x22: x22
STACK CFI 9398 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 941c v8: v8 x21: x21 x22: x22
STACK CFI 9454 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 945c .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 9484 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9488 v8: .cfa -16 + ^
STACK CFI INIT 9490 24 .cfa: sp 0 + .ra: x30
STACK CFI 9498 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 94a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 94b4 b8 .cfa: sp 0 + .ra: x30
STACK CFI 94c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 94cc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 94d8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9544 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 9570 18 .cfa: sp 0 + .ra: x30
STACK CFI 9578 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9580 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9590 1dc .cfa: sp 0 + .ra: x30
STACK CFI 9598 .cfa: sp 128 +
STACK CFI 95a4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 95ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 95cc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 95dc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9600 x21: x21 x22: x22
STACK CFI 9604 x23: x23 x24: x24
STACK CFI 9634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 963c .cfa: sp 128 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 96e0 x21: x21 x22: x22
STACK CFI 96e4 x23: x23 x24: x24
STACK CFI 96e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 96ec x21: x21 x22: x22
STACK CFI 96f0 x23: x23 x24: x24
STACK CFI 9718 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 974c x21: x21 x22: x22
STACK CFI 9750 x23: x23 x24: x24
STACK CFI 9754 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 9758 x21: x21 x22: x22
STACK CFI 975c x23: x23 x24: x24
STACK CFI 9764 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9768 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 9770 154 .cfa: sp 0 + .ra: x30
STACK CFI 9778 .cfa: sp 80 +
STACK CFI 9784 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 979c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 97a4 x23: .cfa -16 + ^
STACK CFI 97b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9814 x21: x21 x22: x22
STACK CFI 9818 x23: x23
STACK CFI 981c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 9824 x21: x21 x22: x22
STACK CFI 9828 x23: x23
STACK CFI 9858 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 9860 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 9878 x21: x21 x22: x22
STACK CFI 987c x23: x23
STACK CFI 9880 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 988c x21: x21 x22: x22
STACK CFI 9890 x23: x23
STACK CFI 98bc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 98c0 x23: .cfa -16 + ^
