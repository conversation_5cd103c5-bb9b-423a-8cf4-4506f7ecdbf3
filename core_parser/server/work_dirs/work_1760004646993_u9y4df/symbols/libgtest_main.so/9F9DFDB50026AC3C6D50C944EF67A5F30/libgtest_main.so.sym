MODULE Linux arm64 9F9DFDB50026AC3C6D50C944EF67A5F30 libgtest_main.so
INFO CODE_ID B5FD9D9F26003CAC6D50C944EF67A5F3
PUBLIC 678 0 _init
PUBLIC 710 0 main
PUBLIC 758 0 call_weak_fn
PUBLIC 770 0 deregister_tm_clones
PUBLIC 7a0 0 register_tm_clones
PUBLIC 7e0 0 __do_global_dtors_aux
PUBLIC 830 0 frame_dummy
PUBLIC 834 0 _fini
STACK CFI INIT 770 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7a0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 7e0 48 .cfa: sp 0 + .ra: x30
STACK CFI 7e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7ec x19: .cfa -16 + ^
STACK CFI 824 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 830 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 710 48 .cfa: sp 0 + .ra: x30
STACK CFI 714 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 720 x19: .cfa -32 + ^
STACK CFI 754 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
