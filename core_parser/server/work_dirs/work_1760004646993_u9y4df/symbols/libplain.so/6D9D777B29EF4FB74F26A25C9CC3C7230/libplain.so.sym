MODULE Linux arm64 6D9D777B29EF4FB74F26A25C9CC3C7230 libplain.so.2
INFO CODE_ID 7B779D6DEF29B74F4F26A25C9CC3C723F6AB7A0D
PUBLIC 1124 0 plain_server_plug_init
PUBLIC 1190 0 plain_client_plug_init
PUBLIC 1200 0 sasl_client_plug_init
PUBLIC 1220 0 sasl_server_plug_init
PUBLIC 1240 0 _plug_ipfromstring
PUBLIC 1504 0 _plug_buf_alloc
PUBLIC 1634 0 _plug_iovec_to_buf
PUBLIC 17e4 0 _plug_strdup
PUBLIC 18d0 0 _plug_free_string
PUBLIC 1cd0 0 _plug_free_secret
PUBLIC 1d30 0 _plug_find_prompt
PUBLIC 1d84 0 _plug_get_simple
PUBLIC 1eb4 0 _plug_get_password
PUBLIC 2040 0 _plug_challenge_prompt
PUBLIC 2174 0 _plug_get_realm
PUBLIC 2290 0 _plug_make_prompts
PUBLIC 2950 0 _plug_decode_init
PUBLIC 2984 0 _plug_decode
PUBLIC 2c40 0 _plug_decode_free
PUBLIC 2c80 0 _plug_parseuser
PUBLIC 2e10 0 _plug_make_fulluser
PUBLIC 2f10 0 _plug_get_error_message
PUBLIC 2fa0 0 _plug_snprintf_os_info
STACK CFI INIT f40 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT f70 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT fb0 48 .cfa: sp 0 + .ra: x30
STACK CFI fb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI fbc x19: .cfa -16 + ^
STACK CFI ff4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1000 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1010 50 .cfa: sp 0 + .ra: x30
STACK CFI 102c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1054 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1060 50 .cfa: sp 0 + .ra: x30
STACK CFI 1070 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1078 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 10b0 74 .cfa: sp 0 + .ra: x30
STACK CFI 10b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 10c4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 10f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 10fc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1124 68 .cfa: sp 0 + .ra: x30
STACK CFI 1160 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1180 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1190 6c .cfa: sp 0 + .ra: x30
STACK CFI 11d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 11f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1200 18 .cfa: sp 0 + .ra: x30
STACK CFI 1208 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1210 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1220 18 .cfa: sp 0 + .ra: x30
STACK CFI 1228 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1230 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1240 2c4 .cfa: sp 0 + .ra: x30
STACK CFI 1248 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1254 .cfa: sp 1312 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1284 x21: .cfa -48 + ^
STACK CFI 128c x22: .cfa -40 + ^
STACK CFI 1294 x23: .cfa -32 + ^
STACK CFI 129c x24: .cfa -24 + ^
STACK CFI 133c x21: x21
STACK CFI 1340 x22: x22
STACK CFI 1344 x23: x23
STACK CFI 1348 x24: x24
STACK CFI 1350 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 13a0 x25: .cfa -16 + ^
STACK CFI 13fc x21: x21
STACK CFI 1400 x22: x22
STACK CFI 1404 x23: x23
STACK CFI 1408 x24: x24
STACK CFI 140c x25: x25
STACK CFI 142c .cfa: sp 80 +
STACK CFI 1438 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1440 .cfa: sp 1312 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 1460 x25: .cfa -16 + ^
STACK CFI 147c x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 14a4 x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 14bc x25: .cfa -16 + ^
STACK CFI 14e0 x23: x23 x24: x24 x25: x25
STACK CFI 14e4 x21: x21
STACK CFI 14e8 x22: x22
STACK CFI 14f0 x21: .cfa -48 + ^
STACK CFI 14f4 x22: .cfa -40 + ^
STACK CFI 14f8 x23: .cfa -32 + ^
STACK CFI 14fc x24: .cfa -24 + ^
STACK CFI 1500 x25: .cfa -16 + ^
STACK CFI INIT 1504 130 .cfa: sp 0 + .ra: x30
STACK CFI 150c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 151c x23: .cfa -16 + ^
STACK CFI 1528 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1538 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1578 x19: x19 x20: x20
STACK CFI 1580 x21: x21 x22: x22
STACK CFI 1588 .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 1590 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 15ac x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 15b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 15bc x21: x21 x22: x22
STACK CFI 15e0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1604 x19: x19 x20: x20
STACK CFI 160c x21: x21 x22: x22
STACK CFI 1610 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 1634 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 163c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 164c x23: .cfa -16 + ^
STACK CFI 1658 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1664 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 16fc x19: x19 x20: x20
STACK CFI 1704 x21: x21 x22: x22
STACK CFI 170c .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 1714 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 1784 x19: x19 x20: x20
STACK CFI 178c x21: x21 x22: x22
STACK CFI 179c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 17a0 x19: x19 x20: x20
STACK CFI 17c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 17e4 e4 .cfa: sp 0 + .ra: x30
STACK CFI 17ec .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 17fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1808 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1820 x23: .cfa -16 + ^
STACK CFI 1854 x21: x21 x22: x22
STACK CFI 185c x23: x23
STACK CFI 1864 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 186c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 1878 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 187c x21: x21 x22: x22
STACK CFI 18a0 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 18bc x21: x21 x22: x22
STACK CFI 18c4 x23: x23
STACK CFI INIT 18d0 74 .cfa: sp 0 + .ra: x30
STACK CFI 18e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 18f0 x21: .cfa -16 + ^
STACK CFI 18f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1938 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 1944 38c .cfa: sp 0 + .ra: x30
STACK CFI 194c .cfa: sp 80 +
STACK CFI 1958 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1964 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 19d0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1b64 x23: x23 x24: x24
STACK CFI 1bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1bb4 .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 1bc4 x23: x23 x24: x24
STACK CFI 1bcc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1bd0 x23: x23 x24: x24
STACK CFI 1bf4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1c20 x23: x23 x24: x24
STACK CFI 1c24 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1c28 x23: x23 x24: x24
STACK CFI 1c30 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1c40 x23: x23 x24: x24
STACK CFI 1c44 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1c6c x23: x23 x24: x24
STACK CFI 1c94 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1cb8 x23: x23 x24: x24
STACK CFI 1cbc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1cc0 x23: x23 x24: x24
STACK CFI 1ccc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI INIT 1cd0 60 .cfa: sp 0 + .ra: x30
STACK CFI 1ce8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1cf0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1d24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1d30 54 .cfa: sp 0 + .ra: x30
STACK CFI 1d38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1d6c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1d74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1d78 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1d84 130 .cfa: sp 0 + .ra: x30
STACK CFI 1d8c .cfa: sp 80 +
STACK CFI 1d90 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1d98 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1dac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1e10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1e18 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1eb4 184 .cfa: sp 0 + .ra: x30
STACK CFI 1ebc .cfa: sp 80 +
STACK CFI 1ec0 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1ec8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1ed8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1f88 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2040 134 .cfa: sp 0 + .ra: x30
STACK CFI 2048 .cfa: sp 96 +
STACK CFI 204c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2054 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2070 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 20d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 20dc .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2174 11c .cfa: sp 0 + .ra: x30
STACK CFI 217c .cfa: sp 80 +
STACK CFI 2188 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2190 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 219c x21: .cfa -16 + ^
STACK CFI 21fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2204 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2290 21c .cfa: sp 0 + .ra: x30
STACK CFI 2298 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 22a4 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 22ac x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 22bc x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 22c8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 22d0 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 23fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2404 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 24b0 4a0 .cfa: sp 0 + .ra: x30
STACK CFI 24b8 .cfa: sp 192 +
STACK CFI 24c4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 24cc x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 24d4 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2510 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2520 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2528 x27: .cfa -16 + ^
STACK CFI 25f4 x23: x23 x24: x24
STACK CFI 25f8 x25: x25 x26: x26
STACK CFI 25fc x27: x27
STACK CFI 262c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 2634 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 27e0 x23: x23 x24: x24
STACK CFI 27e8 x25: x25 x26: x26
STACK CFI 27ec x27: x27
STACK CFI 27f0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2800 x23: x23 x24: x24
STACK CFI 2804 x25: x25 x26: x26
STACK CFI 2808 x27: x27
STACK CFI 280c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2810 x23: x23 x24: x24
STACK CFI 2818 x25: x25 x26: x26
STACK CFI 281c x27: x27
STACK CFI 2820 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 287c x23: x23 x24: x24
STACK CFI 2884 x25: x25 x26: x26
STACK CFI 2888 x27: x27
STACK CFI 288c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 28d8 x23: x23 x24: x24
STACK CFI 28dc x25: x25 x26: x26
STACK CFI 28e0 x27: x27
STACK CFI 28e4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2910 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 2934 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2938 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 293c x27: .cfa -16 + ^
STACK CFI INIT 2950 34 .cfa: sp 0 + .ra: x30
STACK CFI 295c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 296c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2984 2bc .cfa: sp 0 + .ra: x30
STACK CFI 298c .cfa: sp 144 +
STACK CFI 2998 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 29a0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 29c8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 29d4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 29e0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 29e8 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2b10 x19: x19 x20: x20
STACK CFI 2b18 x23: x23 x24: x24
STACK CFI 2b1c x25: x25 x26: x26
STACK CFI 2b20 x27: x27 x28: x28
STACK CFI 2b24 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2b68 x23: x23 x24: x24
STACK CFI 2b70 x25: x25 x26: x26
STACK CFI 2b74 x27: x27 x28: x28
STACK CFI 2b7c x19: x19 x20: x20
STACK CFI 2b84 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2b88 x19: x19 x20: x20
STACK CFI 2b8c x23: x23 x24: x24
STACK CFI 2b90 x25: x25 x26: x26
STACK CFI 2b94 x27: x27 x28: x28
STACK CFI 2bbc .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 2bc4 .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2bc8 x19: x19 x20: x20
STACK CFI 2bd0 x23: x23 x24: x24
STACK CFI 2bd4 x25: x25 x26: x26
STACK CFI 2bd8 x27: x27 x28: x28
STACK CFI 2bdc x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2c00 x19: x19 x20: x20
STACK CFI 2c08 x23: x23 x24: x24
STACK CFI 2c0c x25: x25 x26: x26
STACK CFI 2c10 x27: x27 x28: x28
STACK CFI 2c14 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2c18 x19: x19 x20: x20
STACK CFI 2c20 x23: x23 x24: x24
STACK CFI 2c24 x25: x25 x26: x26
STACK CFI 2c28 x27: x27 x28: x28
STACK CFI 2c30 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2c34 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2c38 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2c3c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 2c40 3c .cfa: sp 0 + .ra: x30
STACK CFI 2c48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2c70 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2c74 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2c80 190 .cfa: sp 0 + .ra: x30
STACK CFI 2c88 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2c98 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 2ca0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 2cb4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2cc4 x25: .cfa -16 + ^
STACK CFI 2d24 x21: x21 x22: x22
STACK CFI 2d28 x25: x25
STACK CFI 2d38 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 2d40 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2d94 x21: x21 x22: x22
STACK CFI 2d9c x25: x25
STACK CFI 2da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 2da8 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2db4 x25: x25
STACK CFI 2dbc x21: x21 x22: x22
STACK CFI 2dcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 2dd4 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2dec x21: x21 x22: x22 x25: x25
STACK CFI INIT 2e10 f8 .cfa: sp 0 + .ra: x30
STACK CFI 2e18 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2e2c x23: .cfa -16 + ^
STACK CFI 2e3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2e48 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2ea4 x19: x19 x20: x20
STACK CFI 2eac x21: x21 x22: x22
STACK CFI 2eb4 .cfa: sp 0 + .ra: .ra x23: x23 x29: x29
STACK CFI 2ebc .cfa: sp 64 + .ra: .cfa -56 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2ee0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2efc x19: x19 x20: x20
STACK CFI 2f04 x21: x21 x22: x22
STACK CFI INIT 2f10 88 .cfa: sp 0 + .ra: x30
STACK CFI 2f18 .cfa: sp 48 +
STACK CFI 2f24 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f2c x19: .cfa -16 + ^
STACK CFI 2f8c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2f94 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2fa0 9c .cfa: sp 0 + .ra: x30
STACK CFI 2fa8 .cfa: sp 448 +
STACK CFI 2fb4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2fbc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2fc8 x21: .cfa -16 + ^
STACK CFI 3030 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3038 .cfa: sp 448 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
