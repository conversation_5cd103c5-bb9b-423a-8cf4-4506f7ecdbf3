MODULE Linux arm64 E43AE813A53A948E26C7E6124030CECF0 libpipewire-module-rtkit.so
INFO CODE_ID 13E83AE43AA58E9426C7E6124030CECFDDED4317
PUBLIC 3734 0 pipewire__module_init
STACK CFI INIT 1d40 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1d70 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1db0 48 .cfa: sp 0 + .ra: x30
STACK CFI 1db4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1dbc x19: .cfa -16 + ^
STACK CFI 1df4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1e00 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1e10 174 .cfa: sp 0 + .ra: x30
STACK CFI 1e18 .cfa: sp 96 +
STACK CFI 1e28 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1e30 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1e50 x21: .cfa -16 + ^
STACK CFI 1e90 x21: x21
STACK CFI 1ebc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1ec4 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1ed8 x21: .cfa -16 + ^
STACK CFI 1f14 x21: x21
STACK CFI 1f18 x21: .cfa -16 + ^
STACK CFI 1f70 x21: x21
STACK CFI 1f74 x21: .cfa -16 + ^
STACK CFI 1f7c x21: x21
STACK CFI INIT 1f84 180 .cfa: sp 0 + .ra: x30
STACK CFI 1f8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1f9c x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 209c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 20a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 20fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2104 214 .cfa: sp 0 + .ra: x30
STACK CFI 210c .cfa: sp 320 +
STACK CFI 2110 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2118 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 212c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 214c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2184 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2204 x21: x21 x22: x22
STACK CFI 2240 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2248 .cfa: sp 320 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 225c x27: .cfa -16 + ^
STACK CFI 22c0 x27: x27
STACK CFI 22f4 x21: x21 x22: x22
STACK CFI 22f8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2304 x21: x21 x22: x22
STACK CFI 2310 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2314 x27: .cfa -16 + ^
STACK CFI INIT 2320 90 .cfa: sp 0 + .ra: x30
STACK CFI 2328 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2334 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 23a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 23b0 80 .cfa: sp 0 + .ra: x30
STACK CFI 23b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 23c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 23c8 x21: .cfa -16 + ^
STACK CFI 2408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2410 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2428 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2430 58 .cfa: sp 0 + .ra: x30
STACK CFI 2438 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 246c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2478 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 247c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2490 390 .cfa: sp 0 + .ra: x30
STACK CFI 2498 .cfa: sp 96 +
STACK CFI 249c .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 24a4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 24b8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 24d4 x23: .cfa -16 + ^
STACK CFI 2550 x23: x23
STACK CFI 255c x23: .cfa -16 + ^
STACK CFI 2580 x23: x23
STACK CFI 25b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 25b8 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 25dc x23: .cfa -16 + ^
STACK CFI 2600 x23: x23
STACK CFI 2608 x23: .cfa -16 + ^
STACK CFI 2694 x23: x23
STACK CFI 2698 x23: .cfa -16 + ^
STACK CFI 26cc x23: x23
STACK CFI 26e4 x23: .cfa -16 + ^
STACK CFI 2748 x23: x23
STACK CFI 274c x23: .cfa -16 + ^
STACK CFI 278c x23: x23
STACK CFI 2790 x23: .cfa -16 + ^
STACK CFI 2818 x23: x23
STACK CFI 281c x23: .cfa -16 + ^
STACK CFI INIT 2820 31c .cfa: sp 0 + .ra: x30
STACK CFI 2828 .cfa: sp 144 +
STACK CFI 2834 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 283c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2848 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2850 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2a28 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2a30 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 2b40 178 .cfa: sp 0 + .ra: x30
STACK CFI 2b48 .cfa: sp 64 +
STACK CFI 2b58 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2b64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2b6c x21: .cfa -16 + ^
STACK CFI 2bdc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2be4 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 2cc0 5c .cfa: sp 0 + .ra: x30
STACK CFI 2cc8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2cd0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2cd8 x21: .cfa -16 + ^
STACK CFI 2d10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2d20 104 .cfa: sp 0 + .ra: x30
STACK CFI 2d28 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2d30 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2d3c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2d48 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2dec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2df4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 2e1c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI INIT 2e24 98 .cfa: sp 0 + .ra: x30
STACK CFI 2e2c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2e34 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2e40 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2eb4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 2ec0 394 .cfa: sp 0 + .ra: x30
STACK CFI 2ec8 .cfa: sp 112 +
STACK CFI 2ed4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2edc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ee8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2f28 x23: .cfa -16 + ^
STACK CFI 2f4c x23: x23
STACK CFI 301c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3024 .cfa: sp 112 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 3040 x23: .cfa -16 + ^
STACK CFI 3098 x23: x23
STACK CFI 309c x23: .cfa -16 + ^
STACK CFI 3118 x23: x23
STACK CFI 3120 x23: .cfa -16 + ^
STACK CFI 3138 x23: x23
STACK CFI 3140 x23: .cfa -16 + ^
STACK CFI 31cc x23: x23
STACK CFI 31d0 x23: .cfa -16 + ^
STACK CFI 3208 x23: x23
STACK CFI 320c x23: .cfa -16 + ^
STACK CFI 324c x23: x23
STACK CFI 3250 x23: .cfa -16 + ^
STACK CFI INIT 3254 17c .cfa: sp 0 + .ra: x30
STACK CFI 325c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3264 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 32b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 32c0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 32dc x21: .cfa -16 + ^
STACK CFI 3320 x21: x21
STACK CFI 3324 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 332c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3348 x21: x21
STACK CFI 3360 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3388 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3394 x21: x21
STACK CFI 33a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 33d0 364 .cfa: sp 0 + .ra: x30
STACK CFI 33d8 .cfa: sp 64 +
STACK CFI 33e4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 33ec x21: .cfa -16 + ^
STACK CFI 33f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3510 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 3518 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3734 121c .cfa: sp 0 + .ra: x30
STACK CFI 373c .cfa: sp 288 +
STACK CFI 3748 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3750 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 375c x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3770 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 37f4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3a34 x27: x27 x28: x28
STACK CFI 3aac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 3ab4 .cfa: sp 288 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 3ad0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3c0c x27: x27 x28: x28
STACK CFI 3c68 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 3e00 x27: x27 x28: x28
STACK CFI 3e04 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4484 x27: x27 x28: x28
STACK CFI 448c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 452c x27: x27 x28: x28
STACK CFI 4530 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4770 x27: x27 x28: x28
STACK CFI 4774 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 491c x27: x27 x28: x28
STACK CFI 4920 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4924 x27: x27 x28: x28
STACK CFI 492c x27: .cfa -16 + ^ x28: .cfa -8 + ^
