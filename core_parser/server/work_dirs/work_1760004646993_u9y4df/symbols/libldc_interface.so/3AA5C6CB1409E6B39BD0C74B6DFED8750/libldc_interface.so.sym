MODULE Linux arm64 3AA5C6CB1409E6B39BD0C74B6DFED8750 libldc_interface.so
INFO CODE_ID CBC6A53A0914B3E69BD0C74B6DFED875
PUBLIC 1f08 0 _init
PUBLIC 2280 0 call_weak_fn
PUBLIC 22a0 0 deregister_tm_clones
PUBLIC 22d0 0 register_tm_clones
PUBLIC 2310 0 __do_global_dtors_aux
PUBLIC 2360 0 frame_dummy
PUBLIC 2370 0 lios::ldc::LdcNvMedia::~LdcNvMedia()
PUBLIC 23a0 0 lios::ldc::LdcNvMedia::SetParameters(NvMedia2DLdcCameraIntrinsic const&, NvMedia2DLdcCameraIntrinsic const&, NvMedia2DLdcLensDistortion const&, NvMedia2DLdcRegionParameters const&)
PUBLIC 2610 0 lios::ldc::LdcNvMedia::TransformSurface(NvSciBufObjRefRec*, NvSciBufObjRefRec*)
PUBLIC 2980 0 lios::ldc::LdcNvMedia::RegisterBufs(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> > const&)
PUBLIC 2a20 0 lios::ldc::LdcNvMedia::UnregisterBufs(std::vector<NvSciBufObjRefRec*, std::allocator<NvSciBufObjRefRec*> > const&)
PUBLIC 2ac0 0 lios::ldc::LdcNvMedia::GetLdcBufAttrList(linvs::buf::BufAttrList*)
PUBLIC 2be0 0 lios::ldc::LdcNvMedia::Init()
PUBLIC 2f00 0 lios::ldc::LdcNvMedia::LdcNvMedia(unsigned int, unsigned int)
PUBLIC 2f70 0 _fini
STACK CFI INIT 22a0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 22d0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2310 48 .cfa: sp 0 + .ra: x30
STACK CFI 2314 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 231c x19: .cfa -16 + ^
STACK CFI 2354 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2360 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2370 30 .cfa: sp 0 + .ra: x30
STACK CFI 2374 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 237c x19: .cfa -16 + ^
STACK CFI 239c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 23a0 26c .cfa: sp 0 + .ra: x30
STACK CFI 23a4 .cfa: sp 256 + .ra: .cfa -248 + ^ x29: .cfa -256 + ^
STACK CFI 23b8 x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^
STACK CFI 23c4 x23: .cfa -208 + ^ x24: .cfa -200 + ^
STACK CFI 23d0 x25: .cfa -192 + ^ x26: .cfa -184 + ^
STACK CFI 253c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 2540 .cfa: sp 256 + .ra: .cfa -248 + ^ x19: .cfa -240 + ^ x20: .cfa -232 + ^ x21: .cfa -224 + ^ x22: .cfa -216 + ^ x23: .cfa -208 + ^ x24: .cfa -200 + ^ x25: .cfa -192 + ^ x26: .cfa -184 + ^ x29: .cfa -256 + ^
STACK CFI INIT 2610 370 .cfa: sp 0 + .ra: x30
STACK CFI 2614 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 2628 x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI 2634 x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 2670 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 268c x25: .cfa -80 + ^
STACK CFI 279c x23: x23 x24: x24
STACK CFI 27a0 x25: x25
STACK CFI 27cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 27d0 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x29: .cfa -144 + ^
STACK CFI 2808 x23: x23 x24: x24 x25: x25
STACK CFI 282c x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^
STACK CFI 284c x23: x23 x24: x24
STACK CFI 2850 x25: x25
STACK CFI 2854 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^
STACK CFI 2874 x23: x23 x24: x24
STACK CFI 2878 x25: x25
STACK CFI 287c x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^
STACK CFI 289c x23: x23 x24: x24
STACK CFI 28a0 x25: x25
STACK CFI 28a4 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^
STACK CFI 28c4 x23: x23 x24: x24
STACK CFI 28c8 x25: x25
STACK CFI 28cc x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^
STACK CFI 28ec x23: x23 x24: x24
STACK CFI 28f0 x25: x25
STACK CFI 28f4 x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^
STACK CFI 2914 x23: x23 x24: x24
STACK CFI 2918 x25: x25
STACK CFI 291c x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^
STACK CFI 293c x23: x23 x24: x24
STACK CFI 2940 x25: x25
STACK CFI 2948 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 294c x25: .cfa -80 + ^
STACK CFI INIT 2980 9c .cfa: sp 0 + .ra: x30
STACK CFI 2984 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 298c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2994 x21: .cfa -16 + ^
STACK CFI 29f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 29f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2a18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2a20 9c .cfa: sp 0 + .ra: x30
STACK CFI 2a24 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2a2c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2a34 x21: .cfa -16 + ^
STACK CFI 2a90 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 2a94 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 2ab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 2ac0 114 .cfa: sp 0 + .ra: x30
STACK CFI 2ac4 .cfa: sp 160 + .ra: .cfa -152 + ^ x29: .cfa -160 + ^
STACK CFI 2ad4 x19: .cfa -144 + ^
STACK CFI 2ba8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2bac .cfa: sp 160 + .ra: .cfa -152 + ^ x19: .cfa -144 + ^ x29: .cfa -160 + ^
STACK CFI INIT 2be0 31c .cfa: sp 0 + .ra: x30
STACK CFI 2be4 .cfa: sp 224 + .ra: .cfa -216 + ^ x29: .cfa -224 + ^
STACK CFI 2bf8 x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 2c24 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 2c30 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 2d2c x25: .cfa -160 + ^
STACK CFI 2d64 x25: x25
STACK CFI 2db4 x21: x21 x22: x22
STACK CFI 2db8 x23: x23 x24: x24
STACK CFI 2de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2de4 .cfa: sp 224 + .ra: .cfa -216 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x29: .cfa -224 + ^
STACK CFI 2e60 x25: .cfa -160 + ^
STACK CFI 2e84 x25: x25
STACK CFI 2e88 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2eac x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^
STACK CFI 2ecc x25: x25
STACK CFI 2ed0 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 2ed4 x21: .cfa -192 + ^ x22: .cfa -184 + ^
STACK CFI 2ed8 x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI 2edc x25: .cfa -160 + ^
STACK CFI 2ee0 x25: x25
STACK CFI 2ee8 x25: .cfa -160 + ^
STACK CFI INIT 2f00 70 .cfa: sp 0 + .ra: x30
STACK CFI 2f04 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2f0c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2f18 x21: .cfa -16 + ^
STACK CFI 2f6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
