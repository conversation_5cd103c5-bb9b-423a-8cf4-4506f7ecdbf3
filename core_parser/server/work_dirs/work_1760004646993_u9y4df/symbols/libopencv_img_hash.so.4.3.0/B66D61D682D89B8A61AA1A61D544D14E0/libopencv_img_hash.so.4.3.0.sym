MODULE Linux arm64 B66D61D682D89B8A61AA1A61D544D14E0 libopencv_img_hash.so.4.3
INFO CODE_ID D6616DB6D8828A9B61AA1A61D544D14E838842F7
PUBLIC 5d70 0 _init
PUBLIC 61e0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.64]
PUBLIC 6280 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.57]
PUBLIC 6320 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.66]
PUBLIC 63c0 0 _GLOBAL__sub_I_average_hash.cpp
PUBLIC 63f0 0 _GLOBAL__sub_I_block_mean_hash.cpp
PUBLIC 6420 0 _GLOBAL__sub_I_color_moment_hash.cpp
PUBLIC 6450 0 _GLOBAL__sub_I_img_hash_base.cpp
PUBLIC 6480 0 _GLOBAL__sub_I_marr_hildreth_hash.cpp
PUBLIC 64b0 0 _GLOBAL__sub_I_phash.cpp
PUBLIC 64e0 0 _GLOBAL__sub_I_radial_variance_hash.cpp
PUBLIC 6510 0 call_weak_fn
PUBLIC 6528 0 deregister_tm_clones
PUBLIC 6560 0 register_tm_clones
PUBLIC 65a0 0 __do_global_dtors_aux
PUBLIC 65e8 0 frame_dummy
PUBLIC 6620 0 cv::Algorithm::clear()
PUBLIC 6628 0 cv::Algorithm::write(cv::FileStorage&) const
PUBLIC 6630 0 cv::Algorithm::read(cv::FileNode const&)
PUBLIC 6638 0 cv::Algorithm::empty() const
PUBLIC 6640 0 std::_Sp_counted_ptr<cv::img_hash::AverageHash*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 6648 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::AverageHashImpl, std::allocator<(anonymous namespace)::AverageHashImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 6650 0 std::_Sp_counted_ptr<cv::img_hash::AverageHash*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 6670 0 std::_Sp_counted_ptr<cv::img_hash::AverageHash*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 6678 0 std::_Sp_counted_ptr<cv::img_hash::AverageHash*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 6680 0 std::_Sp_counted_ptr<cv::img_hash::AverageHash*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 6688 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::AverageHashImpl, std::allocator<(anonymous namespace)::AverageHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 6690 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::AverageHashImpl, std::allocator<(anonymous namespace)::AverageHashImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 6698 0 cv::img_hash::AverageHash::~AverageHash()
PUBLIC 66b0 0 cv::img_hash::AverageHash::~AverageHash()
PUBLIC 66d8 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::AverageHashImpl, std::allocator<(anonymous namespace)::AverageHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 6728 0 (anonymous namespace)::AverageHashImpl::compare(cv::_InputArray const&, cv::_InputArray const&) const
PUBLIC 6758 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::AverageHashImpl, std::allocator<(anonymous namespace)::AverageHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 6918 0 (anonymous namespace)::AverageHashImpl::~AverageHashImpl()
PUBLIC 6ad0 0 (anonymous namespace)::AverageHashImpl::~AverageHashImpl()
PUBLIC 6c90 0 cv::Mat::~Mat()
PUBLIC 6d20 0 (anonymous namespace)::AverageHashImpl::compute(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 7430 0 cv::img_hash::averageHash(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 7d58 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 7e10 0 cv::img_hash::AverageHash::create()
PUBLIC 8110 0 std::_Sp_counted_ptr<cv::img_hash::BlockMeanHash*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 8118 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::BlockMeanHashImpl, std::allocator<(anonymous namespace)::BlockMeanHashImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 8120 0 std::_Sp_counted_ptr<cv::img_hash::BlockMeanHash*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 8140 0 std::_Sp_counted_ptr<cv::img_hash::BlockMeanHash*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 8148 0 std::_Sp_counted_ptr<cv::img_hash::BlockMeanHash*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 8150 0 std::_Sp_counted_ptr<cv::img_hash::BlockMeanHash*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 8158 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::BlockMeanHashImpl, std::allocator<(anonymous namespace)::BlockMeanHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 8160 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::BlockMeanHashImpl, std::allocator<(anonymous namespace)::BlockMeanHashImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 8168 0 cv::img_hash::BlockMeanHash::~BlockMeanHash()
PUBLIC 8180 0 cv::img_hash::BlockMeanHash::~BlockMeanHash()
PUBLIC 81a8 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::BlockMeanHashImpl, std::allocator<(anonymous namespace)::BlockMeanHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 81f8 0 (anonymous namespace)::BlockMeanHashImpl::compare(cv::_InputArray const&, cv::_InputArray const&) const
PUBLIC 8228 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::BlockMeanHashImpl, std::allocator<(anonymous namespace)::BlockMeanHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 8370 0 (anonymous namespace)::BlockMeanHashImpl::~BlockMeanHashImpl()
PUBLIC 84b0 0 (anonymous namespace)::BlockMeanHashImpl::~BlockMeanHashImpl()
PUBLIC 85f8 0 cv::img_hash::BlockMeanHash::setMode(int)
PUBLIC 86b0 0 cv::img_hash::BlockMeanHash::getMean() const
PUBLIC 87a0 0 cv::img_hash::BlockMeanHash::create(int)
PUBLIC 8b20 0 std::vector<double, std::allocator<double> >::_M_default_append(unsigned long)
PUBLIC 8c70 0 (anonymous namespace)::BlockMeanHashImpl::compute(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 9420 0 cv::img_hash::blockMeanHash(cv::_InputArray const&, cv::_OutputArray const&, int)
PUBLIC 9670 0 std::_Sp_counted_ptr<cv::img_hash::ColorMomentHash*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 9678 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::ColorMomentHashImpl, std::allocator<(anonymous namespace)::ColorMomentHashImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 9680 0 std::_Sp_counted_ptr<cv::img_hash::ColorMomentHash*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 96a0 0 std::_Sp_counted_ptr<cv::img_hash::ColorMomentHash*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 96a8 0 std::_Sp_counted_ptr<cv::img_hash::ColorMomentHash*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC 96b0 0 std::_Sp_counted_ptr<cv::img_hash::ColorMomentHash*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 96b8 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::ColorMomentHashImpl, std::allocator<(anonymous namespace)::ColorMomentHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 96c0 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::ColorMomentHashImpl, std::allocator<(anonymous namespace)::ColorMomentHashImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 96c8 0 cv::img_hash::ColorMomentHash::~ColorMomentHash()
PUBLIC 96e0 0 cv::img_hash::ColorMomentHash::~ColorMomentHash()
PUBLIC 9708 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::ColorMomentHashImpl, std::allocator<(anonymous namespace)::ColorMomentHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 9758 0 (anonymous namespace)::ColorMomentHashImpl::compare(cv::_InputArray const&, cv::_InputArray const&) const
PUBLIC 97a0 0 (anonymous namespace)::ColorMomentHashImpl::~ColorMomentHashImpl()
PUBLIC 9a60 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::ColorMomentHashImpl, std::allocator<(anonymous namespace)::ColorMomentHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 9d20 0 (anonymous namespace)::ColorMomentHashImpl::~ColorMomentHashImpl()
PUBLIC 9fd8 0 (anonymous namespace)::ColorMomentHashImpl::compute(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC a800 0 cv::img_hash::colorMomentHash(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC ab50 0 cv::img_hash::ColorMomentHash::create()
PUBLIC ae60 0 std::_Sp_counted_ptr<decltype(nullptr), (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC ae68 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC ae78 0 cv::img_hash::ImgHashBase::~ImgHashBase()
PUBLIC af78 0 cv::img_hash::ImgHashBase::~ImgHashBase()
PUBLIC af90 0 cv::img_hash::ImgHashBase::ImgHashBase()
PUBLIC afb8 0 cv::img_hash::ImgHashBase::compute(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC afd0 0 cv::img_hash::ImgHashBase::compare(cv::_InputArray const&, cv::_InputArray const&) const
PUBLIC afe8 0 std::_Sp_counted_ptr<cv::img_hash::MarrHildrethHash*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC aff0 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::MarrHildrethHashImpl, std::allocator<(anonymous namespace)::MarrHildrethHashImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC aff8 0 std::_Sp_counted_ptr<cv::img_hash::MarrHildrethHash*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC b018 0 std::_Sp_counted_ptr<cv::img_hash::MarrHildrethHash*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC b020 0 std::_Sp_counted_ptr<cv::img_hash::MarrHildrethHash*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC b028 0 std::_Sp_counted_ptr<cv::img_hash::MarrHildrethHash*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC b030 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::MarrHildrethHashImpl, std::allocator<(anonymous namespace)::MarrHildrethHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC b038 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::MarrHildrethHashImpl, std::allocator<(anonymous namespace)::MarrHildrethHashImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC b040 0 cv::img_hash::MarrHildrethHash::~MarrHildrethHash()
PUBLIC b058 0 cv::img_hash::MarrHildrethHash::~MarrHildrethHash()
PUBLIC b080 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::MarrHildrethHashImpl, std::allocator<(anonymous namespace)::MarrHildrethHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC b0d0 0 (anonymous namespace)::MarrHildrethHashImpl::compare(cv::_InputArray const&, cv::_InputArray const&) const
PUBLIC b100 0 (anonymous namespace)::getMHKernel(float, float, cv::Mat&)
PUBLIC b258 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::MarrHildrethHashImpl, std::allocator<(anonymous namespace)::MarrHildrethHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC b628 0 (anonymous namespace)::MarrHildrethHashImpl::~MarrHildrethHashImpl()
PUBLIC b9f0 0 (anonymous namespace)::MarrHildrethHashImpl::~MarrHildrethHashImpl()
PUBLIC bdc0 0 (anonymous namespace)::MarrHildrethHashImpl::compute(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC c720 0 cv::img_hash::MarrHildrethHash::getAlpha() const
PUBLIC c790 0 cv::img_hash::MarrHildrethHash::getScale() const
PUBLIC c800 0 cv::img_hash::MarrHildrethHash::setKernelParam(float, float)
PUBLIC c890 0 cv::img_hash::marrHildrethHash(cv::_InputArray const&, cv::_OutputArray const&, float, float)
PUBLIC ce50 0 cv::img_hash::MarrHildrethHash::create(float, float)
PUBLIC d2e0 0 std::_Sp_counted_ptr<cv::img_hash::PHash*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC d2e8 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::PHashImpl, std::allocator<(anonymous namespace)::PHashImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC d2f0 0 std::_Sp_counted_ptr<cv::img_hash::PHash*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC d310 0 std::_Sp_counted_ptr<cv::img_hash::PHash*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC d318 0 std::_Sp_counted_ptr<cv::img_hash::PHash*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC d320 0 std::_Sp_counted_ptr<cv::img_hash::PHash*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC d328 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::PHashImpl, std::allocator<(anonymous namespace)::PHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC d330 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::PHashImpl, std::allocator<(anonymous namespace)::PHashImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC d338 0 cv::img_hash::PHash::~PHash()
PUBLIC d350 0 cv::img_hash::PHash::~PHash()
PUBLIC d378 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::PHashImpl, std::allocator<(anonymous namespace)::PHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC d3c8 0 (anonymous namespace)::PHashImpl::compare(cv::_InputArray const&, cv::_InputArray const&) const
PUBLIC d3f8 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::PHashImpl, std::allocator<(anonymous namespace)::PHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC d748 0 (anonymous namespace)::PHashImpl::~PHashImpl()
PUBLIC da90 0 (anonymous namespace)::PHashImpl::~PHashImpl()
PUBLIC dde0 0 (anonymous namespace)::PHashImpl::compute(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC e650 0 cv::img_hash::pHash(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC ea60 0 cv::img_hash::PHash::create()
PUBLIC ed90 0 std::_Sp_counted_ptr<cv::img_hash::RadialVarianceHash*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC ed98 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::RadialVarianceHashImpl, std::allocator<(anonymous namespace)::RadialVarianceHashImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC eda0 0 std::_Sp_counted_ptr<cv::img_hash::RadialVarianceHash*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC edc0 0 std::_Sp_counted_ptr<cv::img_hash::RadialVarianceHash*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC edc8 0 std::_Sp_counted_ptr<cv::img_hash::RadialVarianceHash*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC edd0 0 std::_Sp_counted_ptr<cv::img_hash::RadialVarianceHash*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC edd8 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::RadialVarianceHashImpl, std::allocator<(anonymous namespace)::RadialVarianceHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC ede0 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::RadialVarianceHashImpl, std::allocator<(anonymous namespace)::RadialVarianceHashImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC ede8 0 cv::img_hash::RadialVarianceHash::~RadialVarianceHash()
PUBLIC ee00 0 cv::img_hash::RadialVarianceHash::~RadialVarianceHash()
PUBLIC ee28 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::RadialVarianceHashImpl, std::allocator<(anonymous namespace)::RadialVarianceHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC ee78 0 (anonymous namespace)::RadialVarianceHashImpl::hashCalculate(cv::Mat&)
PUBLIC f070 0 std::_Sp_counted_ptr_inplace<(anonymous namespace)::RadialVarianceHashImpl, std::allocator<(anonymous namespace)::RadialVarianceHashImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC f2c8 0 (anonymous namespace)::RadialVarianceHashImpl::~RadialVarianceHashImpl()
PUBLIC f518 0 (anonymous namespace)::RadialVarianceHashImpl::~RadialVarianceHashImpl()
PUBLIC f770 0 (anonymous namespace)::RadialVarianceHashImpl::compare(cv::_InputArray const&, cv::_InputArray const&) const
PUBLIC ff20 0 cv::img_hash::RadialVarianceHash::getNumOfAngleLine() const
PUBLIC ff90 0 cv::img_hash::RadialVarianceHash::getSigma() const
PUBLIC 10000 0 cv::img_hash::RadialVarianceHash::setNumOfAngleLine(int)
PUBLIC 100b8 0 cv::img_hash::RadialVarianceHash::setSigma(double)
PUBLIC 10180 0 cv::img_hash::RadialVarianceHash::getHash()
PUBLIC 10260 0 cv::img_hash::RadialVarianceHash::getPixPerLine(cv::Mat const&)
PUBLIC 10830 0 cv::img_hash::RadialVarianceHash::getProjection()
PUBLIC 10950 0 cv::img_hash::RadialVarianceHash::create(double, int)
PUBLIC 10cb0 0 (anonymous namespace)::RadialVarianceHashImpl::compute(cv::_InputArray const&, cv::_OutputArray const&)
PUBLIC 11a80 0 cv::img_hash::radialVarianceHash(cv::_InputArray const&, cv::_OutputArray const&, double, int)
PUBLIC 11db0 0 cv::img_hash::RadialVarianceHash::getFeatures()
PUBLIC 12128 0 _fini
STACK CFI INIT 6620 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6628 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6630 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6638 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6640 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6648 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6650 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6670 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6678 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6680 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6688 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6690 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6698 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 66b0 28 .cfa: sp 0 + .ra: x30
STACK CFI 66b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 66d4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 66d8 50 .cfa: sp 0 + .ra: x30
STACK CFI 66dc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 66e8 .ra: .cfa -16 + ^
STACK CFI 6724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 6728 30 .cfa: sp 0 + .ra: x30
STACK CFI 672c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6738 .ra: .cfa -16 + ^
STACK CFI 6750 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 6758 1bc .cfa: sp 0 + .ra: x30
STACK CFI 675c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 676c .ra: .cfa -16 + ^
STACK CFI 68d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 68d8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6910 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 6918 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 691c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 692c .ra: .cfa -16 + ^
STACK CFI 6a9c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 6aa0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 6ad0 1bc .cfa: sp 0 + .ra: x30
STACK CFI 6ad4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6ae4 .ra: .cfa -16 + ^
STACK CFI 6c4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 6c50 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6c88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 6c90 90 .cfa: sp 0 + .ra: x30
STACK CFI 6c94 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6d08 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 6d10 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6d1c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 6d20 704 .cfa: sp 0 + .ra: x30
STACK CFI 6d24 .cfa: sp 352 + x19: .cfa -352 + ^ x20: .cfa -344 + ^
STACK CFI 6d34 x25: .cfa -304 + ^ x26: .cfa -296 + ^
STACK CFI 6d48 .ra: .cfa -272 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI 71cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 71d0 .cfa: sp 352 + .ra: .cfa -272 + ^ x19: .cfa -352 + ^ x20: .cfa -344 + ^ x21: .cfa -336 + ^ x22: .cfa -328 + ^ x23: .cfa -320 + ^ x24: .cfa -312 + ^ x25: .cfa -304 + ^ x26: .cfa -296 + ^ x27: .cfa -288 + ^ x28: .cfa -280 + ^
STACK CFI INIT 7430 90c .cfa: sp 0 + .ra: x30
STACK CFI 7434 .cfa: sp 640 +
STACK CFI 743c x19: .cfa -640 + ^ x20: .cfa -632 + ^
STACK CFI 7454 x21: .cfa -624 + ^ x22: .cfa -616 + ^ x23: .cfa -608 + ^ x24: .cfa -600 + ^ x25: .cfa -592 + ^ x26: .cfa -584 + ^
STACK CFI 745c .ra: .cfa -576 + ^
STACK CFI 7ad0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 7ad4 .cfa: sp 640 + .ra: .cfa -576 + ^ x19: .cfa -640 + ^ x20: .cfa -632 + ^ x21: .cfa -624 + ^ x22: .cfa -616 + ^ x23: .cfa -608 + ^ x24: .cfa -600 + ^ x25: .cfa -592 + ^ x26: .cfa -584 + ^
STACK CFI INIT 7d58 b4 .cfa: sp 0 + .ra: x30
STACK CFI 7d60 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7d6c .ra: .cfa -16 + ^
STACK CFI 7d94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 7d98 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7ddc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 7de8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 7e10 2e8 .cfa: sp 0 + .ra: x30
STACK CFI 7e14 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 7e28 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7f94 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 7f98 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 63c0 30 .cfa: sp 0 + .ra: x30
STACK CFI 63c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 63e0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 8110 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8118 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8120 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8140 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8148 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8150 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8158 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8160 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8168 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8180 28 .cfa: sp 0 + .ra: x30
STACK CFI 8188 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 81a4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 81a8 50 .cfa: sp 0 + .ra: x30
STACK CFI 81ac .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 81b8 .ra: .cfa -16 + ^
STACK CFI 81f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 81f8 30 .cfa: sp 0 + .ra: x30
STACK CFI 81fc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8208 .ra: .cfa -16 + ^
STACK CFI 8220 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 61e0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 61e4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 61f0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 6270 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 6274 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 8228 144 .cfa: sp 0 + .ra: x30
STACK CFI 822c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 823c .ra: .cfa -16 + ^
STACK CFI 833c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 8340 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 8370 13c .cfa: sp 0 + .ra: x30
STACK CFI 8374 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8384 .ra: .cfa -16 + ^
STACK CFI 848c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 8490 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 84b0 144 .cfa: sp 0 + .ra: x30
STACK CFI 84b4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 84c4 .ra: .cfa -16 + ^
STACK CFI 85c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 85c8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 85f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 85f8 b8 .cfa: sp 0 + .ra: x30
STACK CFI 85fc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8600 .ra: .cfa -48 + ^
STACK CFI 861c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 8620 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 86b0 e8 .cfa: sp 0 + .ra: x30
STACK CFI 86b4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 86b8 .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 8738 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 873c .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT 87a0 36c .cfa: sp 0 + .ra: x30
STACK CFI 87a4 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 87ac x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 87bc .ra: .cfa -48 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 893c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 8940 .cfa: sp 96 + .ra: .cfa -48 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI INIT 8b20 14c .cfa: sp 0 + .ra: x30
STACK CFI 8b28 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8b40 .ra: .cfa -8 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8b80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 8b90 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 8c2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 8c30 .cfa: sp 80 + .ra: .cfa -8 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI INIT 8c70 7a4 .cfa: sp 0 + .ra: x30
STACK CFI 8c74 .cfa: sp 528 +
STACK CFI 8c78 x19: .cfa -528 + ^ x20: .cfa -520 + ^
STACK CFI 8c80 x25: .cfa -480 + ^ x26: .cfa -472 + ^
STACK CFI 8c9c .ra: .cfa -448 + ^ v8: .cfa -440 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI 9134 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9138 .cfa: sp 528 + .ra: .cfa -448 + ^ v8: .cfa -440 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^ x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI INIT 9420 23c .cfa: sp 0 + .ra: x30
STACK CFI 9424 .cfa: sp 304 + x19: .cfa -304 + ^ x20: .cfa -296 + ^
STACK CFI 9448 .ra: .cfa -280 + ^ x21: .cfa -288 + ^
STACK CFI 95ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 95b0 .cfa: sp 304 + .ra: .cfa -280 + ^ x19: .cfa -304 + ^ x20: .cfa -296 + ^ x21: .cfa -288 + ^
STACK CFI INIT 63f0 30 .cfa: sp 0 + .ra: x30
STACK CFI 63f4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6410 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 9670 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9678 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9680 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT 96a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96a8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96b0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96c8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 96e0 28 .cfa: sp 0 + .ra: x30
STACK CFI 96e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 9704 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 9708 50 .cfa: sp 0 + .ra: x30
STACK CFI 970c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9718 .ra: .cfa -16 + ^
STACK CFI 9754 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 9758 3c .cfa: sp 0 + .ra: x30
STACK CFI 975c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9768 .ra: .cfa -16 + ^
STACK CFI 978c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 97a0 2bc .cfa: sp 0 + .ra: x30
STACK CFI 97a4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 97b4 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 9a4c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 9a50 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 9a58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 9a60 2bc .cfa: sp 0 + .ra: x30
STACK CFI 9a64 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9a74 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 9d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 9d10 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 9d18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 9d20 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 9d24 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9d34 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 9fd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT 9fd8 81c .cfa: sp 0 + .ra: x30
STACK CFI 9fdc .cfa: sp 544 +
STACK CFI 9fe0 x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI 9ff0 x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^
STACK CFI a008 .ra: .cfa -464 + ^ v8: .cfa -456 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI a538 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI a53c .cfa: sp 544 + .ra: .cfa -464 + ^ v8: .cfa -456 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^ x24: .cfa -504 + ^ x25: .cfa -496 + ^ x26: .cfa -488 + ^ x27: .cfa -480 + ^ x28: .cfa -472 + ^
STACK CFI INIT a800 338 .cfa: sp 0 + .ra: x30
STACK CFI a804 .cfa: sp 464 + x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI a818 .ra: .cfa -432 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI ab20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI ab24 .cfa: sp 464 + .ra: .cfa -432 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^ x21: .cfa -448 + ^ x22: .cfa -440 + ^
STACK CFI INIT ab50 300 .cfa: sp 0 + .ra: x30
STACK CFI ab54 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ab68 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI ace8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI acf0 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 6420 30 .cfa: sp 0 + .ra: x30
STACK CFI 6424 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6440 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT ae60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ae68 c .cfa: sp 0 + .ra: x30
STACK CFI INIT ae78 100 .cfa: sp 0 + .ra: x30
STACK CFI ae7c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ae88 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI aed0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI aed8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT af78 18 .cfa: sp 0 + .ra: x30
STACK CFI af7c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI af8c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT af90 28 .cfa: sp 0 + .ra: x30
STACK CFI af94 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI afb4 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT afb8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT afd0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6450 30 .cfa: sp 0 + .ra: x30
STACK CFI 6454 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6470 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT afe8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT aff0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT aff8 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT b018 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT b020 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b028 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b030 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b038 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT b040 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT b058 28 .cfa: sp 0 + .ra: x30
STACK CFI b060 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI b07c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT b080 50 .cfa: sp 0 + .ra: x30
STACK CFI b084 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b090 .ra: .cfa -16 + ^
STACK CFI b0cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT b0d0 30 .cfa: sp 0 + .ra: x30
STACK CFI b0d4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b0e0 .ra: .cfa -16 + ^
STACK CFI b0f8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 6280 a0 .cfa: sp 0 + .ra: x30
STACK CFI 6284 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6290 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 6310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 6314 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT b100 154 .cfa: sp 0 + .ra: x30
STACK CFI b104 .cfa: sp 128 + x19: .cfa -128 + ^ x20: .cfa -120 + ^
STACK CFI b108 v8: .cfa -64 + ^ v9: .cfa -56 + ^
STACK CFI b118 v10: .cfa -48 + ^ v11: .cfa -40 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI b128 .ra: .cfa -72 + ^ v12: .cfa -32 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^
STACK CFI b228 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI b22c .cfa: sp 128 + .ra: .cfa -72 + ^ v10: .cfa -48 + ^ v11: .cfa -40 + ^ v12: .cfa -32 + ^ v8: .cfa -64 + ^ v9: .cfa -56 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^
STACK CFI INIT b258 3cc .cfa: sp 0 + .ra: x30
STACK CFI b25c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b26c .ra: .cfa -16 + ^
STACK CFI b5a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI b5a8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT b628 3c4 .cfa: sp 0 + .ra: x30
STACK CFI b62c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI b63c .ra: .cfa -16 + ^
STACK CFI b97c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI b980 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT b9f0 3cc .cfa: sp 0 + .ra: x30
STACK CFI b9f4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ba04 .ra: .cfa -16 + ^
STACK CFI bd3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI bd40 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI bdb8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT bdc0 95c .cfa: sp 0 + .ra: x30
STACK CFI bdc4 .cfa: sp 672 +
STACK CFI bdc8 x19: .cfa -672 + ^ x20: .cfa -664 + ^
STACK CFI bdec .ra: .cfa -592 + ^ v8: .cfa -576 + ^ v9: .cfa -568 + ^ x21: .cfa -656 + ^ x22: .cfa -648 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^ x25: .cfa -624 + ^ x26: .cfa -616 + ^ x27: .cfa -608 + ^ x28: .cfa -600 + ^
STACK CFI c400 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI c404 .cfa: sp 672 + .ra: .cfa -592 + ^ v8: .cfa -576 + ^ v9: .cfa -568 + ^ x19: .cfa -672 + ^ x20: .cfa -664 + ^ x21: .cfa -656 + ^ x22: .cfa -648 + ^ x23: .cfa -640 + ^ x24: .cfa -632 + ^ x25: .cfa -624 + ^ x26: .cfa -616 + ^ x27: .cfa -608 + ^ x28: .cfa -600 + ^
STACK CFI INIT c720 70 .cfa: sp 0 + .ra: x30
STACK CFI c734 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c744 .ra: .cfa -48 + ^
STACK CFI INIT c790 70 .cfa: sp 0 + .ra: x30
STACK CFI c7a4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c7b4 .ra: .cfa -48 + ^
STACK CFI INIT c800 84 .cfa: sp 0 + .ra: x30
STACK CFI c804 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c808 .ra: .cfa -48 + ^
STACK CFI c828 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI c82c .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT c890 5a8 .cfa: sp 0 + .ra: x30
STACK CFI c898 .cfa: sp 768 +
STACK CFI c8a0 x19: .cfa -768 + ^ x20: .cfa -760 + ^
STACK CFI c910 .ra: .cfa -736 + ^ x21: .cfa -752 + ^ x22: .cfa -744 + ^
STACK CFI cd40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI cd48 .cfa: sp 768 + .ra: .cfa -736 + ^ x19: .cfa -768 + ^ x20: .cfa -760 + ^ x21: .cfa -752 + ^ x22: .cfa -744 + ^
STACK CFI INIT ce50 47c .cfa: sp 0 + .ra: x30
STACK CFI ce54 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI ce60 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI ce74 .ra: .cfa -48 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI d0e8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI d0f0 .cfa: sp 96 + .ra: .cfa -48 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI INIT 6480 30 .cfa: sp 0 + .ra: x30
STACK CFI 6484 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 64a0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT d2e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d2e8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d2f0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT d310 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT d318 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d320 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d328 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d330 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT d338 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT d350 28 .cfa: sp 0 + .ra: x30
STACK CFI d358 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI d374 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT d378 50 .cfa: sp 0 + .ra: x30
STACK CFI d37c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d388 .ra: .cfa -16 + ^
STACK CFI d3c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT d3c8 30 .cfa: sp 0 + .ra: x30
STACK CFI d3cc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d3d8 .ra: .cfa -16 + ^
STACK CFI d3f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT d3f8 34c .cfa: sp 0 + .ra: x30
STACK CFI d3fc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d40c .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI d6d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI d6d8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI d740 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT d748 344 .cfa: sp 0 + .ra: x30
STACK CFI d74c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d75c .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI da2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI da30 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT da90 34c .cfa: sp 0 + .ra: x30
STACK CFI da94 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI daa4 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI dd6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI dd70 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI ddd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT dde0 848 .cfa: sp 0 + .ra: x30
STACK CFI dde4 .cfa: sp 384 + x19: .cfa -384 + ^ x20: .cfa -376 + ^
STACK CFI ddf4 x25: .cfa -336 + ^ x26: .cfa -328 + ^
STACK CFI de08 .ra: .cfa -304 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI e3b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI e3b8 .cfa: sp 384 + .ra: .cfa -304 + ^ x19: .cfa -384 + ^ x20: .cfa -376 + ^ x21: .cfa -368 + ^ x22: .cfa -360 + ^ x23: .cfa -352 + ^ x24: .cfa -344 + ^ x25: .cfa -336 + ^ x26: .cfa -328 + ^ x27: .cfa -320 + ^ x28: .cfa -312 + ^
STACK CFI INIT e650 400 .cfa: sp 0 + .ra: x30
STACK CFI e654 .cfa: sp 640 +
STACK CFI e65c x19: .cfa -640 + ^ x20: .cfa -632 + ^
STACK CFI e664 x21: .cfa -624 + ^ x22: .cfa -616 + ^
STACK CFI e678 .ra: .cfa -608 + ^
STACK CFI e9dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI e9e0 .cfa: sp 640 + .ra: .cfa -608 + ^ x19: .cfa -640 + ^ x20: .cfa -632 + ^ x21: .cfa -624 + ^ x22: .cfa -616 + ^
STACK CFI INIT ea60 320 .cfa: sp 0 + .ra: x30
STACK CFI ea64 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ea78 .ra: .cfa -16 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI ec18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI ec20 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 64b0 30 .cfa: sp 0 + .ra: x30
STACK CFI 64b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 64d0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT ed90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ed98 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT eda0 1c .cfa: sp 0 + .ra: x30
STACK CFI INIT edc0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT edc8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT edd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT edd8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ede0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ede8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT ee00 28 .cfa: sp 0 + .ra: x30
STACK CFI ee08 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI ee24 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT ee28 50 .cfa: sp 0 + .ra: x30
STACK CFI ee2c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI ee38 .ra: .cfa -16 + ^
STACK CFI ee74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT ee78 1e0 .cfa: sp 0 + .ra: x30
STACK CFI ee7c .cfa: sp 528 +
STACK CFI ee80 x25: .cfa -480 + ^ x26: .cfa -472 + ^
STACK CFI ee88 x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI eeac .ra: .cfa -448 + ^ v10: .cfa -416 + ^ v11: .cfa -408 + ^ v12: .cfa -400 + ^ v13: .cfa -392 + ^ v14: .cfa -384 + ^ v15: .cfa -376 + ^ v8: .cfa -432 + ^ v9: .cfa -424 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^
STACK CFI efdc .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI efe0 .cfa: sp 528 + .ra: .cfa -448 + ^ v10: .cfa -416 + ^ v11: .cfa -408 + ^ v12: .cfa -400 + ^ v13: .cfa -392 + ^ v14: .cfa -384 + ^ v15: .cfa -376 + ^ v8: .cfa -432 + ^ v9: .cfa -424 + ^ x19: .cfa -528 + ^ x20: .cfa -520 + ^ x21: .cfa -512 + ^ x22: .cfa -504 + ^ x23: .cfa -496 + ^ x24: .cfa -488 + ^ x25: .cfa -480 + ^ x26: .cfa -472 + ^ x27: .cfa -464 + ^ x28: .cfa -456 + ^
STACK CFI INIT 6320 a0 .cfa: sp 0 + .ra: x30
STACK CFI 6324 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6330 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 63b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 63b4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT f070 254 .cfa: sp 0 + .ra: x30
STACK CFI f074 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f084 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI f270 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI f278 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI f2c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT f2c8 24c .cfa: sp 0 + .ra: x30
STACK CFI f2cc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f2dc .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI f4d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI f4d8 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT f518 254 .cfa: sp 0 + .ra: x30
STACK CFI f51c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI f52c .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI f718 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI f720 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI f768 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI INIT f770 76c .cfa: sp 0 + .ra: x30
STACK CFI f774 .cfa: sp 1072 +
STACK CFI f77c x19: .cfa -1072 + ^ x20: .cfa -1064 + ^
STACK CFI f7a0 .ra: .cfa -992 + ^ v10: .cfa -960 + ^ v11: .cfa -952 + ^ v8: .cfa -976 + ^ v9: .cfa -968 + ^ x21: .cfa -1056 + ^ x22: .cfa -1048 + ^ x23: .cfa -1040 + ^ x24: .cfa -1032 + ^ x25: .cfa -1024 + ^ x26: .cfa -1016 + ^ x27: .cfa -1008 + ^ x28: .cfa -1000 + ^
STACK CFI fce4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI fce8 .cfa: sp 1072 + .ra: .cfa -992 + ^ v10: .cfa -960 + ^ v11: .cfa -952 + ^ v8: .cfa -976 + ^ v9: .cfa -968 + ^ x19: .cfa -1072 + ^ x20: .cfa -1064 + ^ x21: .cfa -1056 + ^ x22: .cfa -1048 + ^ x23: .cfa -1040 + ^ x24: .cfa -1032 + ^ x25: .cfa -1024 + ^ x26: .cfa -1016 + ^ x27: .cfa -1008 + ^ x28: .cfa -1000 + ^
STACK CFI INIT ff20 70 .cfa: sp 0 + .ra: x30
STACK CFI ff34 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ff44 .ra: .cfa -48 + ^
STACK CFI INIT ff90 70 .cfa: sp 0 + .ra: x30
STACK CFI ffa4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ffb4 .ra: .cfa -48 + ^
STACK CFI INIT 10000 b8 .cfa: sp 0 + .ra: x30
STACK CFI 10004 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10008 .ra: .cfa -48 + ^
STACK CFI 10024 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 10028 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 100b8 bc .cfa: sp 0 + .ra: x30
STACK CFI 100bc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 100c0 .ra: .cfa -48 + ^
STACK CFI 100e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 100e4 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 10180 c4 .cfa: sp 0 + .ra: x30
STACK CFI 10190 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1019c .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 101d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 101dc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT 10260 5c8 .cfa: sp 0 + .ra: x30
STACK CFI 10264 .cfa: sp 176 + x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 10280 .ra: .cfa -96 + ^ v10: .cfa -88 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI 1070c .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 10710 .cfa: sp 176 + .ra: .cfa -96 + ^ v10: .cfa -88 + ^ v8: .cfa -80 + ^ v9: .cfa -72 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x26: .cfa -120 + ^ x27: .cfa -112 + ^ x28: .cfa -104 + ^
STACK CFI INIT 10830 11c .cfa: sp 0 + .ra: x30
STACK CFI 10834 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10838 .ra: .cfa -48 + ^
STACK CFI 108d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 108d8 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 108f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 108f4 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 10950 350 .cfa: sp 0 + .ra: x30
STACK CFI 10954 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 1095c v8: .cfa -8 + ^
STACK CFI 10964 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10974 .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 10b3c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 10b40 .cfa: sp 64 + .ra: .cfa -16 + ^ v8: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 10cb0 da8 .cfa: sp 0 + .ra: x30
STACK CFI 10cb4 .cfa: sp 848 +
STACK CFI 10cb8 x19: .cfa -848 + ^ x20: .cfa -840 + ^
STACK CFI 10ce8 .ra: .cfa -768 + ^ v10: .cfa -736 + ^ v11: .cfa -728 + ^ v12: .cfa -720 + ^ v13: .cfa -712 + ^ v14: .cfa -704 + ^ v15: .cfa -696 + ^ v8: .cfa -752 + ^ v9: .cfa -744 + ^ x21: .cfa -832 + ^ x22: .cfa -824 + ^ x23: .cfa -816 + ^ x24: .cfa -808 + ^ x25: .cfa -800 + ^ x26: .cfa -792 + ^ x27: .cfa -784 + ^ x28: .cfa -776 + ^
STACK CFI 116d4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 116d8 .cfa: sp 848 + .ra: .cfa -768 + ^ v10: .cfa -736 + ^ v11: .cfa -728 + ^ v12: .cfa -720 + ^ v13: .cfa -712 + ^ v14: .cfa -704 + ^ v15: .cfa -696 + ^ v8: .cfa -752 + ^ v9: .cfa -744 + ^ x19: .cfa -848 + ^ x20: .cfa -840 + ^ x21: .cfa -832 + ^ x22: .cfa -824 + ^ x23: .cfa -816 + ^ x24: .cfa -808 + ^ x25: .cfa -800 + ^ x26: .cfa -792 + ^ x27: .cfa -784 + ^ x28: .cfa -776 + ^
STACK CFI INIT 11a80 318 .cfa: sp 0 + .ra: x30
STACK CFI 11a88 .cfa: sp 464 + x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI 11ab4 .ra: .cfa -448 + ^
STACK CFI 11d44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 11d48 .cfa: sp 464 + .ra: .cfa -448 + ^ x19: .cfa -464 + ^ x20: .cfa -456 + ^
STACK CFI INIT 11db0 370 .cfa: sp 0 + .ra: x30
STACK CFI 11db4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 11dbc .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x21: .cfa -64 + ^
STACK CFI 1201c .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21
STACK CFI 12020 .cfa: sp 80 + .ra: .cfa -56 + ^ v8: .cfa -48 + ^ v9: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^
STACK CFI INIT 64e0 30 .cfa: sp 0 + .ra: x30
STACK CFI 64e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6500 .cfa: sp 0 + .ra: .ra x19: x19
