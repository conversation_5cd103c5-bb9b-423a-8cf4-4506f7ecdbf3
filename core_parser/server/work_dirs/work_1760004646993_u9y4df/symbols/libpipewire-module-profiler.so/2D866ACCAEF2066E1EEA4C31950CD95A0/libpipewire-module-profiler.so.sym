MODULE Linux arm64 2D866ACCAEF2066E1EEA4C31950CD95A0 libpipewire-module-profiler.so
INFO CODE_ID CC6A862DF2AE6E061EEA4C31950CD95A83775A68
PUBLIC 6d34 0 pipewire__module_init
STACK CFI INIT 15b0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15e0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1620 48 .cfa: sp 0 + .ra: x30
STACK CFI 1624 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 162c x19: .cfa -16 + ^
STACK CFI 1664 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1670 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1680 1c .cfa: sp 0 + .ra: x30
STACK CFI 1688 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1694 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 16a0 2c8c .cfa: sp 0 + .ra: x30
STACK CFI 16a8 .cfa: sp 480 +
STACK CFI 16bc .ra: .cfa -296 + ^ x29: .cfa -304 + ^
STACK CFI 16d4 x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^
STACK CFI 16dc x23: .cfa -256 + ^ x24: .cfa -248 + ^
STACK CFI 16f4 v8: .cfa -208 + ^ v9: .cfa -200 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^
STACK CFI 1ea0 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 1ea8 .cfa: sp 480 + .ra: .cfa -296 + ^ v8: .cfa -208 + ^ v9: .cfa -200 + ^ x19: .cfa -288 + ^ x20: .cfa -280 + ^ x21: .cfa -272 + ^ x22: .cfa -264 + ^ x23: .cfa -256 + ^ x24: .cfa -248 + ^ x25: .cfa -240 + ^ x26: .cfa -232 + ^ x27: .cfa -224 + ^ x28: .cfa -216 + ^ x29: .cfa -304 + ^
STACK CFI INIT 4330 1bc .cfa: sp 0 + .ra: x30
STACK CFI 4338 .cfa: sp 64 +
STACK CFI 433c .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4344 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4358 x21: .cfa -16 + ^
STACK CFI 43f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 43fc .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 44f0 204 .cfa: sp 0 + .ra: x30
STACK CFI 44f8 .cfa: sp 144 + .ra: .cfa -136 + ^ x29: .cfa -144 + ^
STACK CFI 4508 x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^
STACK CFI 4510 x23: .cfa -96 + ^ x24: .cfa -88 + ^
STACK CFI 4520 x27: .cfa -64 + ^ x28: .cfa -56 + ^
STACK CFI 452c x25: .cfa -80 + ^ x26: .cfa -72 + ^
STACK CFI 4598 x25: x25 x26: x26
STACK CFI 4608 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 4610 .cfa: sp 144 + .ra: .cfa -136 + ^ x19: .cfa -128 + ^ x20: .cfa -120 + ^ x21: .cfa -112 + ^ x22: .cfa -104 + ^ x23: .cfa -96 + ^ x24: .cfa -88 + ^ x25: .cfa -80 + ^ x26: .cfa -72 + ^ x27: .cfa -64 + ^ x28: .cfa -56 + ^ x29: .cfa -144 + ^
STACK CFI 46ec x25: x25 x26: x26
STACK CFI INIT 46f4 dd0 .cfa: sp 0 + .ra: x30
STACK CFI 46fc .cfa: sp 432 +
STACK CFI 4700 .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 4708 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 4714 x19: .cfa -96 + ^ x20: .cfa -88 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 4768 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 4770 .cfa: sp 432 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI 4780 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 4790 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4b58 v8: .cfa -16 + ^
STACK CFI 4d3c v8: v8
STACK CFI 4e2c x25: x25 x26: x26
STACK CFI 4e30 x27: x27 x28: x28
STACK CFI 4e34 v8: .cfa -16 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 4ff8 v8: v8
STACK CFI 5098 v8: .cfa -16 + ^
STACK CFI 50ac v8: v8
STACK CFI 5130 v8: .cfa -16 + ^
STACK CFI 5138 v8: v8
STACK CFI 52f8 v8: .cfa -16 + ^
STACK CFI 5308 v8: v8
STACK CFI 54b4 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 54b8 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 54bc x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 54c0 v8: .cfa -16 + ^
STACK CFI INIT 54c4 bc .cfa: sp 0 + .ra: x30
STACK CFI 54cc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 54d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5520 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5528 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5550 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5580 98 .cfa: sp 0 + .ra: x30
STACK CFI 5588 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5590 x19: .cfa -16 + ^
STACK CFI 55e0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 55e8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 5610 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5620 134 .cfa: sp 0 + .ra: x30
STACK CFI 5628 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5630 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5678 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5680 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 56c4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5748 x21: x21 x22: x22
STACK CFI 574c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5754 e0 .cfa: sp 0 + .ra: x30
STACK CFI 575c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5764 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 57a4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 57ac .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 57b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5830 x19: x19 x20: x20
STACK CFI INIT 5834 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 583c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 5848 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 5870 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5878 x23: .cfa -16 + ^
STACK CFI 58c4 x19: x19 x20: x20
STACK CFI 58d0 x23: x23
STACK CFI 58d4 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 58dc .cfa: sp 64 + .ra: .cfa -56 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 58f0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 58f8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 59f0 b0 .cfa: sp 0 + .ra: x30
STACK CFI 59f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5a00 x19: .cfa -16 + ^
STACK CFI 5a98 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5aa0 de8 .cfa: sp 0 + .ra: x30
STACK CFI 5aa8 .cfa: sp 352 +
STACK CFI 5abc .ra: .cfa -280 + ^ x29: .cfa -288 + ^
STACK CFI 5ac4 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 5ad4 x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 5ae0 x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^
STACK CFI 5ae8 x27: .cfa -208 + ^ x28: .cfa -200 + ^
STACK CFI 5eb0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 5eb8 .cfa: sp 352 + .ra: .cfa -280 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^ x24: .cfa -232 + ^ x25: .cfa -224 + ^ x26: .cfa -216 + ^ x27: .cfa -208 + ^ x28: .cfa -200 + ^ x29: .cfa -288 + ^
STACK CFI INIT 6890 20 .cfa: sp 0 + .ra: x30
STACK CFI 6898 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 68a4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 68b0 2a4 .cfa: sp 0 + .ra: x30
STACK CFI 68b8 .cfa: sp 96 +
STACK CFI 68c4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 68d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6a5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6a64 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6b54 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 6b5c .cfa: sp 128 +
STACK CFI 6b68 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6b70 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 6d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6d28 .cfa: sp 128 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6d34 2f8 .cfa: sp 0 + .ra: x30
STACK CFI 6d3c .cfa: sp 80 +
STACK CFI 6d48 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6d54 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6f80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6f88 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
