MODULE Linux arm64 4CEC9DACBE42E01E890FB044535B7E640 libopencv_bgsegm.so.4.3
INFO CODE_ID AC9DEC4C42BE1EE0890FB044535B7E64E001850D
PUBLIC 5200 0 _init
PUBLIC 5790 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.35]
PUBLIC 5830 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.161]
PUBLIC 58d0 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.30]
PUBLIC 5970 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.27]
PUBLIC 5a10 0 _GLOBAL__sub_I_bgfg_gsoc.cpp
PUBLIC 5a40 0 call_weak_fn
PUBLIC 5a58 0 deregister_tm_clones
PUBLIC 5a90 0 register_tm_clones
PUBLIC 5ad0 0 __do_global_dtors_aux
PUBLIC 5b18 0 frame_dummy
PUBLIC 5b50 0 cv::Algorithm::clear()
PUBLIC 5b58 0 cv::Algorithm::write(cv::FileStorage&) const
PUBLIC 5b60 0 cv::Algorithm::read(cv::FileNode const&)
PUBLIC 5b68 0 cv::Algorithm::empty() const
PUBLIC 5b70 0 cv::bgsegm::BackgroundSubtractorMOGImpl::getHistory() const
PUBLIC 5b78 0 cv::bgsegm::BackgroundSubtractorMOGImpl::setHistory(int)
PUBLIC 5b80 0 cv::bgsegm::BackgroundSubtractorMOGImpl::getNMixtures() const
PUBLIC 5b88 0 cv::bgsegm::BackgroundSubtractorMOGImpl::setNMixtures(int)
PUBLIC 5b90 0 cv::bgsegm::BackgroundSubtractorMOGImpl::getBackgroundRatio() const
PUBLIC 5b98 0 cv::bgsegm::BackgroundSubtractorMOGImpl::setBackgroundRatio(double)
PUBLIC 5ba0 0 cv::bgsegm::BackgroundSubtractorMOGImpl::getNoiseSigma() const
PUBLIC 5ba8 0 cv::bgsegm::BackgroundSubtractorMOGImpl::setNoiseSigma(double)
PUBLIC 5bb0 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorMOGImpl, std::allocator<cv::bgsegm::BackgroundSubtractorMOGImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 5bb8 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorMOGImpl, std::allocator<cv::bgsegm::BackgroundSubtractorMOGImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 5bc0 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorMOGImpl, std::allocator<cv::bgsegm::BackgroundSubtractorMOGImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 5bc8 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorMOGImpl, std::allocator<cv::bgsegm::BackgroundSubtractorMOGImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 5c18 0 std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >::basic_string(char const*, std::allocator<char> const&) [clone .isra.30]
PUBLIC 5cc8 0 cv::bgsegm::BackgroundSubtractorMOGImpl::initialize(cv::Size_<int>, int)
PUBLIC 5df0 0 cv::bgsegm::BackgroundSubtractorMOGImpl::getBackgroundImage(cv::_OutputArray const&) const
PUBLIC 5e50 0 cv::bgsegm::BackgroundSubtractorMOGImpl::read(cv::FileNode const&)
PUBLIC 5fd8 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorMOGImpl, std::allocator<cv::bgsegm::BackgroundSubtractorMOGImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 60b0 0 cv::bgsegm::BackgroundSubtractorMOGImpl::~BackgroundSubtractorMOGImpl()
PUBLIC 6188 0 cv::bgsegm::BackgroundSubtractorMOGImpl::~BackgroundSubtractorMOGImpl()
PUBLIC 6258 0 cv::bgsegm::BackgroundSubtractorMOGImpl::write(cv::FileStorage&) const
PUBLIC 6608 0 cv::Mat::~Mat()
PUBLIC 6698 0 cv::bgsegm::BackgroundSubtractorMOGImpl::apply(cv::_InputArray const&, cv::_OutputArray const&, double)
PUBLIC 75e0 0 cv::bgsegm::createBackgroundSubtractorMOG(int, int, double, double)
PUBLIC 7790 0 cv::bgsegm::BackgroundSubtractorGMGImpl::getMaxFeatures() const
PUBLIC 7798 0 cv::bgsegm::BackgroundSubtractorGMGImpl::setMaxFeatures(int)
PUBLIC 77a0 0 cv::bgsegm::BackgroundSubtractorGMGImpl::getDefaultLearningRate() const
PUBLIC 77a8 0 cv::bgsegm::BackgroundSubtractorGMGImpl::setDefaultLearningRate(double)
PUBLIC 77b0 0 cv::bgsegm::BackgroundSubtractorGMGImpl::getNumFrames() const
PUBLIC 77b8 0 cv::bgsegm::BackgroundSubtractorGMGImpl::setNumFrames(int)
PUBLIC 77c0 0 cv::bgsegm::BackgroundSubtractorGMGImpl::getQuantizationLevels() const
PUBLIC 77c8 0 cv::bgsegm::BackgroundSubtractorGMGImpl::setQuantizationLevels(int)
PUBLIC 77d0 0 cv::bgsegm::BackgroundSubtractorGMGImpl::getBackgroundPrior() const
PUBLIC 77d8 0 cv::bgsegm::BackgroundSubtractorGMGImpl::setBackgroundPrior(double)
PUBLIC 77e0 0 cv::bgsegm::BackgroundSubtractorGMGImpl::getSmoothingRadius() const
PUBLIC 77e8 0 cv::bgsegm::BackgroundSubtractorGMGImpl::setSmoothingRadius(int)
PUBLIC 77f0 0 cv::bgsegm::BackgroundSubtractorGMGImpl::getDecisionThreshold() const
PUBLIC 77f8 0 cv::bgsegm::BackgroundSubtractorGMGImpl::setDecisionThreshold(double)
PUBLIC 7800 0 cv::bgsegm::BackgroundSubtractorGMGImpl::getUpdateBackgroundModel() const
PUBLIC 7808 0 cv::bgsegm::BackgroundSubtractorGMGImpl::setUpdateBackgroundModel(bool)
PUBLIC 7810 0 cv::bgsegm::BackgroundSubtractorGMGImpl::getMinVal() const
PUBLIC 7818 0 cv::bgsegm::BackgroundSubtractorGMGImpl::setMinVal(double)
PUBLIC 7820 0 cv::bgsegm::BackgroundSubtractorGMGImpl::getMaxVal() const
PUBLIC 7828 0 cv::bgsegm::BackgroundSubtractorGMGImpl::setMaxVal(double)
PUBLIC 7830 0 cv::bgsegm::Quantization<unsigned char>::apply(void const*, int, int, double, double, int)
PUBLIC 7cf0 0 cv::bgsegm::Quantization<signed char>::apply(void const*, int, int, double, double, int)
PUBLIC 81b0 0 cv::bgsegm::Quantization<unsigned short>::apply(void const*, int, int, double, double, int)
PUBLIC 8450 0 cv::bgsegm::Quantization<short>::apply(void const*, int, int, double, double, int)
PUBLIC 86f0 0 cv::bgsegm::Quantization<int>::apply(void const*, int, int, double, double, int)
PUBLIC 8880 0 cv::bgsegm::Quantization<float>::apply(void const*, int, int, double, double, int)
PUBLIC 8a10 0 cv::bgsegm::Quantization<double>::apply(void const*, int, int, double, double, int)
PUBLIC 8bc0 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorGMGImpl, std::allocator<cv::bgsegm::BackgroundSubtractorGMGImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 8bc8 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorGMGImpl, std::allocator<cv::bgsegm::BackgroundSubtractorGMGImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 8bd0 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorGMGImpl, std::allocator<cv::bgsegm::BackgroundSubtractorGMGImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 8bd8 0 cv::bgsegm::insertFeature(int, float, int*, float*, int&, int)
PUBLIC 8d08 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorGMGImpl, std::allocator<cv::bgsegm::BackgroundSubtractorGMGImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 8d58 0 cv::bgsegm::BackgroundSubtractorGMGImpl::getBackgroundImage(cv::_OutputArray const&) const
PUBLIC 8d60 0 cv::bgsegm::GMG_LoopBody::operator()(cv::Range const&) const
PUBLIC 94c8 0 cv::bgsegm::BackgroundSubtractorGMGImpl::read(cv::FileNode const&)
PUBLIC 96e8 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorGMGImpl, std::allocator<cv::bgsegm::BackgroundSubtractorGMGImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 98d0 0 cv::bgsegm::BackgroundSubtractorGMGImpl::~BackgroundSubtractorGMGImpl()
PUBLIC 9ab8 0 cv::bgsegm::GMG_LoopBody::~GMG_LoopBody()
PUBLIC 9d80 0 cv::bgsegm::GMG_LoopBody::~GMG_LoopBody()
PUBLIC a040 0 cv::bgsegm::BackgroundSubtractorGMGImpl::~BackgroundSubtractorGMGImpl()
PUBLIC a220 0 cv::bgsegm::BackgroundSubtractorGMGImpl::write(cv::FileStorage&) const
PUBLIC a9a8 0 cv::bgsegm::BackgroundSubtractorGMGImpl::initialize(cv::Size_<int>, double, double)
PUBLIC ad30 0 cv::bgsegm::BackgroundSubtractorGMGImpl::apply(cv::_InputArray const&, cv::_OutputArray const&, double)
PUBLIC bed8 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC bf90 0 cv::bgsegm::createBackgroundSubtractorGMG(int, double)
PUBLIC c280 0 cv::bgsegm::(anonymous namespace)::ParallelFromLocalSVDValues::operator()(cv::Range const&) const
PUBLIC c368 0 std::_Sp_counted_ptr<cv::bgsegm::BackgroundSubtractorLSBPImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC c370 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorGSOCImpl, std::allocator<cv::bgsegm::BackgroundSubtractorGSOCImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC c378 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::(anonymous namespace)::BackgroundModelLSBP, std::allocator<cv::bgsegm::(anonymous namespace)::BackgroundModelLSBP>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC c380 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::(anonymous namespace)::BackgroundModelGSOC, std::allocator<cv::bgsegm::(anonymous namespace)::BackgroundModelGSOC>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC c388 0 std::_Sp_counted_ptr<cv::bgsegm::BackgroundSubtractorLSBPImpl*, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC c390 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::(anonymous namespace)::BackgroundModelGSOC, std::allocator<cv::bgsegm::(anonymous namespace)::BackgroundModelGSOC>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC c3a8 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::(anonymous namespace)::BackgroundModelLSBP, std::allocator<cv::bgsegm::(anonymous namespace)::BackgroundModelLSBP>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC c3c0 0 std::_Sp_counted_ptr<cv::bgsegm::BackgroundSubtractorLSBPImpl*, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr()
PUBLIC c3c8 0 std::_Sp_counted_ptr<cv::bgsegm::BackgroundSubtractorLSBPImpl*, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC c3d0 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::(anonymous namespace)::BackgroundModelGSOC, std::allocator<cv::bgsegm::(anonymous namespace)::BackgroundModelGSOC>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC c3d8 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::(anonymous namespace)::BackgroundModelGSOC, std::allocator<cv::bgsegm::(anonymous namespace)::BackgroundModelGSOC>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC c3e0 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::(anonymous namespace)::BackgroundModelLSBP, std::allocator<cv::bgsegm::(anonymous namespace)::BackgroundModelLSBP>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC c3e8 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::(anonymous namespace)::BackgroundModelLSBP, std::allocator<cv::bgsegm::(anonymous namespace)::BackgroundModelLSBP>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC c3f0 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorGSOCImpl, std::allocator<cv::bgsegm::BackgroundSubtractorGSOCImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC c3f8 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorGSOCImpl, std::allocator<cv::bgsegm::BackgroundSubtractorGSOCImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC c400 0 cv::bgsegm::(anonymous namespace)::ParallelLocalSVDValues::~ParallelLocalSVDValues()
PUBLIC c418 0 cv::bgsegm::(anonymous namespace)::ParallelLocalSVDValues::~ParallelLocalSVDValues()
PUBLIC c440 0 cv::bgsegm::(anonymous namespace)::ParallelFromLocalSVDValues::~ParallelFromLocalSVDValues()
PUBLIC c458 0 cv::bgsegm::(anonymous namespace)::ParallelFromLocalSVDValues::~ParallelFromLocalSVDValues()
PUBLIC c480 0 cv::bgsegm::ParallelGSOC::~ParallelGSOC()
PUBLIC c490 0 cv::bgsegm::ParallelGSOC::~ParallelGSOC()
PUBLIC c4b8 0 cv::bgsegm::ParallelLSBP::~ParallelLSBP()
PUBLIC c4c8 0 cv::bgsegm::ParallelLSBP::~ParallelLSBP()
PUBLIC c4f0 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::(anonymous namespace)::BackgroundModelGSOC, std::allocator<cv::bgsegm::(anonymous namespace)::BackgroundModelGSOC>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC c540 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::(anonymous namespace)::BackgroundModelLSBP, std::allocator<cv::bgsegm::(anonymous namespace)::BackgroundModelLSBP>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC c590 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorGSOCImpl, std::allocator<cv::bgsegm::BackgroundSubtractorGSOCImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC c5e0 0 cv::bgsegm::ParallelLSBP::operator()(cv::Range const&) const
PUBLIC cac8 0 cv::bgsegm::(anonymous namespace)::ParallelLocalSVDValues::operator()(cv::Range const&) const
PUBLIC ce00 0 cv::bgsegm::ParallelGSOC::operator()(cv::Range const&) const
PUBLIC d3e8 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorGSOCImpl, std::allocator<cv::bgsegm::BackgroundSubtractorGSOCImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC d788 0 cv::bgsegm::BackgroundSubtractorLSBPImpl::~BackgroundSubtractorLSBPImpl()
PUBLIC daa8 0 cv::bgsegm::BackgroundSubtractorGSOCImpl::~BackgroundSubtractorGSOCImpl()
PUBLIC de48 0 std::_Sp_counted_ptr<cv::bgsegm::BackgroundSubtractorLSBPImpl*, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC e178 0 cv::bgsegm::BackgroundSubtractorLSBPImpl::~BackgroundSubtractorLSBPImpl()
PUBLIC e490 0 cv::bgsegm::BackgroundSubtractorGSOCImpl::~BackgroundSubtractorGSOCImpl()
PUBLIC e828 0 cv::bgsegm::BackgroundSubtractorLSBPImpl::getBackgroundImage(cv::_OutputArray const&) const
PUBLIC ebc0 0 cv::bgsegm::BackgroundSubtractorGSOCImpl::getBackgroundImage(cv::_OutputArray const&) const
PUBLIC efa0 0 cv::bgsegm::(anonymous namespace)::removeNoise(cv::Mat&, cv::Mat const&, unsigned long, unsigned char)
PUBLIC f1f0 0 cv::bgsegm::BackgroundSubtractorLSBPDesc::calcLocalSVDValues(cv::_OutputArray const&, cv::Mat const&)
PUBLIC fe18 0 cv::bgsegm::BackgroundSubtractorLSBPDesc::computeFromLocalSVDValues(cv::_OutputArray const&, cv::Mat const&, cv::Point_<int> const*)
PUBLIC 10020 0 cv::bgsegm::BackgroundSubtractorLSBPDesc::compute(cv::_OutputArray const&, cv::Mat const&, cv::Point_<int> const*)
PUBLIC 10130 0 cv::MatExpr::~MatExpr()
PUBLIC 102e0 0 cv::bgsegm::BackgroundSubtractorGSOCImpl::postprocessing(cv::Mat&)
PUBLIC 10850 0 cv::bgsegm::BackgroundSubtractorLSBPImpl::postprocessing(cv::Mat&)
PUBLIC 10dc0 0 cv::bgsegm::BackgroundSubtractorGSOCImpl::BackgroundSubtractorGSOCImpl(int, int, float, float, int, float, float, float, float, float, float)
PUBLIC 11190 0 cv::bgsegm::createBackgroundSubtractorGSOC(int, int, float, float, int, float, float, float, float, float, float)
PUBLIC 11240 0 cv::bgsegm::BackgroundSubtractorLSBPImpl::BackgroundSubtractorLSBPImpl(int, int, int, float, float, float, float, float, float, float, float, int, int)
PUBLIC 11640 0 cv::bgsegm::createBackgroundSubtractorLSBP(int, int, int, float, float, float, float, float, float, float, float, int, int)
PUBLIC 11728 0 std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >::_M_default_append(unsigned long)
PUBLIC 11878 0 void std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >::_M_emplace_back_aux<cv::Point_<float> >(cv::Point_<float>&&)
PUBLIC 11980 0 cv::bgsegm::(anonymous namespace)::FindSparseCorrLK(cv::Mat const&, cv::Mat const&, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >&, std::vector<cv::Point_<float>, std::allocator<cv::Point_<float> > >&)
PUBLIC 11ed0 0 cv::bgsegm::BackgroundSubtractorGSOCImpl::apply(cv::_InputArray const&, cv::_OutputArray const&, double)
PUBLIC 13df0 0 cv::bgsegm::BackgroundSubtractorLSBPImpl::apply(cv::_InputArray const&, cv::_OutputArray const&, double)
PUBLIC 15488 0 cv::bgsegm::BackgroundSubtractorCNTImpl::getMinPixelStability() const
PUBLIC 15490 0 cv::bgsegm::BackgroundSubtractorCNTImpl::getMaxPixelStability() const
PUBLIC 15498 0 cv::bgsegm::BackgroundSubtractorCNTImpl::getUseHistory() const
PUBLIC 154a0 0 cv::bgsegm::BackgroundSubtractorCNTImpl::setUseHistory(bool)
PUBLIC 154a8 0 cv::bgsegm::BackgroundSubtractorCNTImpl::getIsParallel() const
PUBLIC 154b0 0 cv::bgsegm::BackgroundSubtractorCNTImpl::setIsParallel(bool)
PUBLIC 154b8 0 cv::bgsegm::BGSubtractPixel::~BGSubtractPixel()
PUBLIC 154c0 0 cv::bgsegm::BGSubtractPixel::operator()(cv::Vec<int, 4>&, unsigned char, unsigned char, unsigned char&)
PUBLIC 15520 0 cv::bgsegm::BGSubtractPixelWithHistory::~BGSubtractPixelWithHistory()
PUBLIC 15528 0 cv::bgsegm::BGSubtractPixelWithHistory::operator()(cv::Vec<int, 4>&, unsigned char, unsigned char, unsigned char&)
PUBLIC 15600 0 cv::bgsegm::CNTInvoker::operator()(cv::Range const&) const
PUBLIC 15708 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorCNTImpl, std::allocator<cv::bgsegm::BackgroundSubtractorCNTImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 15710 0 cv::bgsegm::BGSubtractPixelWithHistory::~BGSubtractPixelWithHistory()
PUBLIC 15718 0 cv::bgsegm::BGSubtractPixel::~BGSubtractPixel()
PUBLIC 15720 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorCNTImpl, std::allocator<cv::bgsegm::BackgroundSubtractorCNTImpl>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 15728 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorCNTImpl, std::allocator<cv::bgsegm::BackgroundSubtractorCNTImpl>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 15730 0 cv::bgsegm::CNTInvoker::~CNTInvoker()
PUBLIC 15740 0 cv::bgsegm::CNTInvoker::~CNTInvoker()
PUBLIC 15768 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorCNTImpl, std::allocator<cv::bgsegm::BackgroundSubtractorCNTImpl>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 157b8 0 cv::bgsegm::BackgroundSubtractorCNTImpl::setMinPixelStability(int)
PUBLIC 15838 0 cv::bgsegm::BackgroundSubtractorCNTImpl::setMaxPixelStability(int)
PUBLIC 158b0 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::BackgroundSubtractorCNTImpl, std::allocator<cv::bgsegm::BackgroundSubtractorCNTImpl>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 15a80 0 cv::bgsegm::BackgroundSubtractorCNTImpl::~BackgroundSubtractorCNTImpl()
PUBLIC 15c50 0 cv::bgsegm::BackgroundSubtractorCNTImpl::~BackgroundSubtractorCNTImpl()
PUBLIC 15e20 0 cv::bgsegm::BackgroundSubtractorCNTImpl::getBackgroundImage(cv::_OutputArray const&) const
PUBLIC 161b0 0 cv::bgsegm::BackgroundSubtractorCNTImpl::apply(cv::_InputArray const&, cv::_OutputArray const&, double)
PUBLIC 16eb0 0 cv::bgsegm::createBackgroundSubtractorCNT(int, bool, int, bool)
PUBLIC 17040 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::SyntheticSequenceGenerator, std::allocator<cv::bgsegm::SyntheticSequenceGenerator>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 17048 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::SyntheticSequenceGenerator, std::allocator<cv::bgsegm::SyntheticSequenceGenerator>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 17060 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::SyntheticSequenceGenerator, std::allocator<cv::bgsegm::SyntheticSequenceGenerator>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 17068 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::SyntheticSequenceGenerator, std::allocator<cv::bgsegm::SyntheticSequenceGenerator>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 17070 0 std::_Sp_counted_ptr_inplace<cv::bgsegm::SyntheticSequenceGenerator, std::allocator<cv::bgsegm::SyntheticSequenceGenerator>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 170c0 0 cv::bgsegm::SyntheticSequenceGenerator::~SyntheticSequenceGenerator()
PUBLIC 17200 0 cv::bgsegm::SyntheticSequenceGenerator::~SyntheticSequenceGenerator()
PUBLIC 17340 0 cv::bgsegm::SyntheticSequenceGenerator::SyntheticSequenceGenerator(cv::_InputArray const&, cv::_InputArray const&, double, double, double, double)
PUBLIC 179d0 0 cv::bgsegm::SyntheticSequenceGenerator::getNextFrame(cv::_OutputArray const&, cv::_OutputArray const&)
PUBLIC 184a0 0 cv::bgsegm::createSyntheticSequenceGenerator(cv::_InputArray const&, cv::_InputArray const&, double, double, double, double)
PUBLIC 18570 0 _fini
STACK CFI INIT 5b50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b58 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b60 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b68 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b78 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b88 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b90 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b98 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ba0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ba8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5bb0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5bb8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5bc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5bc8 50 .cfa: sp 0 + .ra: x30
STACK CFI 5bcc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5bd8 .ra: .cfa -16 + ^
STACK CFI 5c14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 5c18 b0 .cfa: sp 0 + .ra: x30
STACK CFI 5c1c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5c20 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5c28 .ra: .cfa -32 + ^
STACK CFI 5c78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 5c7c .cfa: sp 64 + .ra: .cfa -32 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 5cc8 124 .cfa: sp 0 + .ra: x30
STACK CFI 5ccc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5cd8 .ra: .cfa -48 + ^
STACK CFI 5d60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 5d68 .cfa: sp 64 + .ra: .cfa -48 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI INIT 5df0 60 .cfa: sp 0 + .ra: x30
STACK CFI 5df4 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5e04 .ra: .cfa -48 + ^
STACK CFI INIT 5e50 188 .cfa: sp 0 + .ra: x30
STACK CFI 5e54 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 5e5c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5e70 .ra: .cfa -72 + ^ x23: .cfa -80 + ^
STACK CFI 5f44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 5f48 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT 5fd8 d4 .cfa: sp 0 + .ra: x30
STACK CFI 5fdc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5fec .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 6098 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 60a0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 60b0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 60b4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 60c4 .ra: .cfa -16 + ^
STACK CFI 6174 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 6178 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 6188 cc .cfa: sp 0 + .ra: x30
STACK CFI 618c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 619c .ra: .cfa -16 + ^
STACK CFI 6244 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 6248 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 6258 3b0 .cfa: sp 0 + .ra: x30
STACK CFI 625c .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6270 .ra: .cfa -40 + ^ x21: .cfa -48 + ^
STACK CFI 6500 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 6504 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^
STACK CFI INIT 6608 90 .cfa: sp 0 + .ra: x30
STACK CFI 660c .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6680 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI 6688 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 6694 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 6698 f2c .cfa: sp 0 + .ra: x30
STACK CFI 669c .cfa: sp 480 + x19: .cfa -480 + ^ x20: .cfa -472 + ^
STACK CFI 66ac v12: .cfa -352 + ^ v13: .cfa -344 + ^
STACK CFI 66b4 x21: .cfa -464 + ^ x22: .cfa -456 + ^
STACK CFI 66d4 .ra: .cfa -400 + ^ v10: .cfa -368 + ^ v11: .cfa -360 + ^ v14: .cfa -336 + ^ v15: .cfa -328 + ^ v8: .cfa -384 + ^ v9: .cfa -376 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI 6c5c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 6c60 .cfa: sp 480 + .ra: .cfa -400 + ^ v10: .cfa -368 + ^ v11: .cfa -360 + ^ v12: .cfa -352 + ^ v13: .cfa -344 + ^ v14: .cfa -336 + ^ v15: .cfa -328 + ^ v8: .cfa -384 + ^ v9: .cfa -376 + ^ x19: .cfa -480 + ^ x20: .cfa -472 + ^ x21: .cfa -464 + ^ x22: .cfa -456 + ^ x23: .cfa -448 + ^ x24: .cfa -440 + ^ x25: .cfa -432 + ^ x26: .cfa -424 + ^ x27: .cfa -416 + ^ x28: .cfa -408 + ^
STACK CFI INIT 75e0 190 .cfa: sp 0 + .ra: x30
STACK CFI 75e4 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 75e8 v8: .cfa -32 + ^ v9: .cfa -24 + ^
STACK CFI 75f4 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7600 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 760c .ra: .cfa -48 + ^
STACK CFI 7718 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 7720 .cfa: sp 96 + .ra: .cfa -48 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 7758 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 775c .cfa: sp 96 + .ra: .cfa -48 + ^ v8: .cfa -32 + ^ v9: .cfa -24 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI INIT 7790 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7798 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77b8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77c0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77c8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77d0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77d8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77e0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77e8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77f0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 77f8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7800 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7808 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7810 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7818 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7820 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7828 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7830 4ac .cfa: sp 0 + .ra: x30
STACK CFI INIT 7cf0 4ac .cfa: sp 0 + .ra: x30
STACK CFI INIT 81b0 28c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8450 28c .cfa: sp 0 + .ra: x30
STACK CFI INIT 86f0 17c .cfa: sp 0 + .ra: x30
STACK CFI INIT 8880 174 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a10 194 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8bc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8bc8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8bd0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8bd8 130 .cfa: sp 0 + .ra: x30
STACK CFI 8bdc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 8be4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8bf4 .ra: .cfa -32 + ^ v8: .cfa -24 + ^
STACK CFI 8c6c .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 8c70 .cfa: sp 64 + .ra: .cfa -32 + ^ v8: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 8cb8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 8cbc .cfa: sp 64 + .ra: .cfa -32 + ^ v8: .cfa -24 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI INIT 8d08 50 .cfa: sp 0 + .ra: x30
STACK CFI 8d0c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8d18 .ra: .cfa -16 + ^
STACK CFI 8d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 8d58 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5790 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5794 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 57a0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 5820 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 5824 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 8d60 764 .cfa: sp 0 + .ra: x30
STACK CFI 8d64 .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI 8d70 x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI 8d88 .ra: .cfa -128 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^
STACK CFI 9248 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9250 .cfa: sp 208 + .ra: .cfa -128 + ^ v8: .cfa -112 + ^ v9: .cfa -104 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^ x26: .cfa -152 + ^ x27: .cfa -144 + ^ x28: .cfa -136 + ^
STACK CFI INIT 94c8 220 .cfa: sp 0 + .ra: x30
STACK CFI 94cc .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 94d4 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 94e8 .ra: .cfa -72 + ^ x23: .cfa -80 + ^
STACK CFI 9650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 9658 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^
STACK CFI INIT 96e8 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 96ec .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 96fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9704 .ra: .cfa -16 + ^
STACK CFI 989c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 98a0 .cfa: sp 48 + .ra: .cfa -16 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI INIT 98d0 1e4 .cfa: sp 0 + .ra: x30
STACK CFI 98d4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 98e4 .ra: .cfa -16 + ^
STACK CFI 9a80 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 9a88 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 9ab8 2c4 .cfa: sp 0 + .ra: x30
STACK CFI 9abc .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9acc .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 9d2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 9d30 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 9d80 2bc .cfa: sp 0 + .ra: x30
STACK CFI 9d84 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9d94 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 9fec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 9ff0 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT a040 1dc .cfa: sp 0 + .ra: x30
STACK CFI a044 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI a054 .ra: .cfa -16 + ^
STACK CFI a1e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI a1f0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT a220 784 .cfa: sp 0 + .ra: x30
STACK CFI a224 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI a260 .ra: .cfa -64 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI a7bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI a7c0 .cfa: sp 96 + .ra: .cfa -64 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI INIT a9a8 380 .cfa: sp 0 + .ra: x30
STACK CFI a9b0 .cfa: sp 112 + x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI a9b8 .ra: .cfa -80 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI ab0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI ab10 .cfa: sp 112 + .ra: .cfa -80 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI INIT ad30 1180 .cfa: sp 0 + .ra: x30
STACK CFI ad34 .cfa: sp 1152 +
STACK CFI ad38 v8: .cfa -1056 + ^ v9: .cfa -1048 + ^
STACK CFI ad40 x19: .cfa -1152 + ^ x20: .cfa -1144 + ^
STACK CFI ad50 x21: .cfa -1136 + ^ x22: .cfa -1128 + ^ x23: .cfa -1120 + ^ x24: .cfa -1112 + ^
STACK CFI ad6c .ra: .cfa -1072 + ^ v10: .cfa -1040 + ^ v11: .cfa -1032 + ^ v12: .cfa -1064 + ^ x25: .cfa -1104 + ^ x26: .cfa -1096 + ^ x27: .cfa -1088 + ^ x28: .cfa -1080 + ^
STACK CFI b6d4 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI b6d8 .cfa: sp 1152 + .ra: .cfa -1072 + ^ v10: .cfa -1040 + ^ v11: .cfa -1032 + ^ v12: .cfa -1064 + ^ v8: .cfa -1056 + ^ v9: .cfa -1048 + ^ x19: .cfa -1152 + ^ x20: .cfa -1144 + ^ x21: .cfa -1136 + ^ x22: .cfa -1128 + ^ x23: .cfa -1120 + ^ x24: .cfa -1112 + ^ x25: .cfa -1104 + ^ x26: .cfa -1096 + ^ x27: .cfa -1088 + ^ x28: .cfa -1080 + ^
STACK CFI INIT bed8 b4 .cfa: sp 0 + .ra: x30
STACK CFI bee0 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI beec .ra: .cfa -16 + ^
STACK CFI bf14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI bf18 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI bf5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI bf68 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT bf90 2d0 .cfa: sp 0 + .ra: x30
STACK CFI bf94 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI bf98 v8: .cfa -16 + ^
STACK CFI bfa0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI bfa8 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI bfb8 .ra: .cfa -24 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI c170 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI c178 .cfa: sp 96 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI c1c8 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI c1d0 .cfa: sp 96 + .ra: .cfa -24 + ^ v8: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^
STACK CFI INIT c280 e0 .cfa: sp 0 + .ra: x30
STACK CFI INIT c368 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c370 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c378 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c380 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c388 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT c390 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT c3a8 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT c3c0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c3c8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c3d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c3d8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c3e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c3e8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c3f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c3f8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT c400 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT c418 28 .cfa: sp 0 + .ra: x30
STACK CFI c424 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI c43c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT c440 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT c458 28 .cfa: sp 0 + .ra: x30
STACK CFI c464 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI c47c .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT c480 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT c490 24 .cfa: sp 0 + .ra: x30
STACK CFI c494 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI c4b0 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT c4b8 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT c4c8 24 .cfa: sp 0 + .ra: x30
STACK CFI c4cc .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI c4e8 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT c4f0 50 .cfa: sp 0 + .ra: x30
STACK CFI c4f4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c500 .ra: .cfa -16 + ^
STACK CFI c53c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT c540 50 .cfa: sp 0 + .ra: x30
STACK CFI c544 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c550 .ra: .cfa -16 + ^
STACK CFI c58c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT c590 50 .cfa: sp 0 + .ra: x30
STACK CFI c594 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI c5a0 .ra: .cfa -16 + ^
STACK CFI c5dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT c5e0 4dc .cfa: sp 0 + .ra: x30
STACK CFI c5fc .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI c628 .ra: .cfa -8 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI c848 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI c850 .cfa: sp 64 + .ra: .cfa -8 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI cab8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI INIT 5830 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5834 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5840 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 58c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 58c4 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT cac8 328 .cfa: sp 0 + .ra: x30
STACK CFI cacc .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI caf0 .ra: .cfa -112 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v12: .cfa -64 + ^ v13: .cfa -56 + ^ v14: .cfa -48 + ^ v15: .cfa -40 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI cd64 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI cd68 .cfa: sp 192 + .ra: .cfa -112 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v12: .cfa -64 + ^ v13: .cfa -56 + ^ v14: .cfa -48 + ^ v15: .cfa -40 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^ x25: .cfa -144 + ^ x26: .cfa -136 + ^ x27: .cfa -128 + ^ x28: .cfa -120 + ^
STACK CFI INIT ce00 5e0 .cfa: sp 0 + .ra: x30
STACK CFI ce04 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI ce0c x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI ce24 .ra: .cfa -16 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI d210 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI d218 .cfa: sp 96 + .ra: .cfa -16 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI INIT d3e8 3a0 .cfa: sp 0 + .ra: x30
STACK CFI d3ec .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d3fc .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI d650 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI d658 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT d788 320 .cfa: sp 0 + .ra: x30
STACK CFI d78c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI d79c .ra: .cfa -16 + ^
STACK CFI d980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI d988 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT daa8 3a0 .cfa: sp 0 + .ra: x30
STACK CFI daac .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI dabc .ra: .cfa -16 + ^
STACK CFI dd14 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI dd18 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT de48 330 .cfa: sp 0 + .ra: x30
STACK CFI de4c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI de50 .ra: .cfa -16 + ^
STACK CFI e044 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI e048 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e080 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI e088 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT e178 318 .cfa: sp 0 + .ra: x30
STACK CFI e17c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e18c .ra: .cfa -16 + ^
STACK CFI e368 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI e370 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT e490 398 .cfa: sp 0 + .ra: x30
STACK CFI e494 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI e4a4 .ra: .cfa -16 + ^
STACK CFI e6f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI e6f8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT e828 394 .cfa: sp 0 + .ra: x30
STACK CFI e82c .cfa: sp 224 + x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI e830 x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI e84c .ra: .cfa -144 + ^ v10: .cfa -136 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI eaa0 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI eaa8 .cfa: sp 224 + .ra: .cfa -144 + ^ v10: .cfa -136 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT ebc0 3d8 .cfa: sp 0 + .ra: x30
STACK CFI ebc4 .cfa: sp 224 + x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI ebc8 x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI ebe4 .ra: .cfa -144 + ^ v10: .cfa -136 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI ee70 .cfa: sp 0 + .ra: .ra v10: v10 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI ee78 .cfa: sp 224 + .ra: .cfa -144 + ^ v10: .cfa -136 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^ x27: .cfa -160 + ^ x28: .cfa -152 + ^
STACK CFI INIT efa0 240 .cfa: sp 0 + .ra: x30
STACK CFI efa4 .cfa: sp 224 + x19: .cfa -224 + ^ x20: .cfa -216 + ^
STACK CFI efb4 x21: .cfa -208 + ^ x22: .cfa -200 + ^
STACK CFI efbc x23: .cfa -192 + ^ x24: .cfa -184 + ^
STACK CFI efcc .ra: .cfa -160 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI f1b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI f1b8 .cfa: sp 224 + .ra: .cfa -160 + ^ x19: .cfa -224 + ^ x20: .cfa -216 + ^ x21: .cfa -208 + ^ x22: .cfa -200 + ^ x23: .cfa -192 + ^ x24: .cfa -184 + ^ x25: .cfa -176 + ^ x26: .cfa -168 + ^
STACK CFI INIT f1f0 c00 .cfa: sp 0 + .ra: x30
STACK CFI f1f4 .cfa: sp 448 + x19: .cfa -448 + ^ x20: .cfa -440 + ^
STACK CFI f204 x21: .cfa -432 + ^ x22: .cfa -424 + ^
STACK CFI f20c x23: .cfa -416 + ^ x24: .cfa -408 + ^
STACK CFI f240 .ra: .cfa -368 + ^ v10: .cfa -336 + ^ v11: .cfa -328 + ^ v12: .cfa -320 + ^ v13: .cfa -312 + ^ v14: .cfa -304 + ^ v15: .cfa -296 + ^ v8: .cfa -352 + ^ v9: .cfa -344 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI fb0c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI fb10 .cfa: sp 448 + .ra: .cfa -368 + ^ v10: .cfa -336 + ^ v11: .cfa -328 + ^ v12: .cfa -320 + ^ v13: .cfa -312 + ^ v14: .cfa -304 + ^ v15: .cfa -296 + ^ v8: .cfa -352 + ^ v9: .cfa -344 + ^ x19: .cfa -448 + ^ x20: .cfa -440 + ^ x21: .cfa -432 + ^ x22: .cfa -424 + ^ x23: .cfa -416 + ^ x24: .cfa -408 + ^ x25: .cfa -400 + ^ x26: .cfa -392 + ^ x27: .cfa -384 + ^ x28: .cfa -376 + ^
STACK CFI INIT fe18 200 .cfa: sp 0 + .ra: x30
STACK CFI fe1c .cfa: sp 208 + x19: .cfa -208 + ^ x20: .cfa -200 + ^
STACK CFI fe2c .ra: .cfa -152 + ^ x25: .cfa -160 + ^
STACK CFI fe3c x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^
STACK CFI ff48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI ff50 .cfa: sp 208 + .ra: .cfa -152 + ^ x19: .cfa -208 + ^ x20: .cfa -200 + ^ x21: .cfa -192 + ^ x22: .cfa -184 + ^ x23: .cfa -176 + ^ x24: .cfa -168 + ^ x25: .cfa -160 + ^
STACK CFI INIT 10020 100 .cfa: sp 0 + .ra: x30
STACK CFI 10028 .cfa: sp 160 + x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 10044 .ra: .cfa -136 + ^ x21: .cfa -144 + ^
STACK CFI 100fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 10100 .cfa: sp 160 + .ra: .cfa -136 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^
STACK CFI INIT 10130 1ac .cfa: sp 0 + .ra: x30
STACK CFI 10134 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 10140 .ra: .cfa -16 + ^
STACK CFI 1029c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 102a0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 102d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 102e0 538 .cfa: sp 0 + .ra: x30
STACK CFI 102e4 .cfa: sp 544 +
STACK CFI 102ec x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI 102f4 x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI 102fc .ra: .cfa -504 + ^ x23: .cfa -512 + ^
STACK CFI 10764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 10768 .cfa: sp 544 + .ra: .cfa -504 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^
STACK CFI INIT 10850 538 .cfa: sp 0 + .ra: x30
STACK CFI 10854 .cfa: sp 544 +
STACK CFI 1085c x19: .cfa -544 + ^ x20: .cfa -536 + ^
STACK CFI 10864 x21: .cfa -528 + ^ x22: .cfa -520 + ^
STACK CFI 1086c .ra: .cfa -504 + ^ x23: .cfa -512 + ^
STACK CFI 10cd4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 10cd8 .cfa: sp 544 + .ra: .cfa -504 + ^ x19: .cfa -544 + ^ x20: .cfa -536 + ^ x21: .cfa -528 + ^ x22: .cfa -520 + ^ x23: .cfa -512 + ^
STACK CFI INIT 10dc0 3c0 .cfa: sp 0 + .ra: x30
STACK CFI 10dc4 .cfa: sp 144 + x19: .cfa -144 + ^ x20: .cfa -136 + ^
STACK CFI 10dd0 v8: .cfa -96 + ^ v9: .cfa -88 + ^
STACK CFI 10ddc v10: .cfa -80 + ^ v11: .cfa -72 + ^
STACK CFI 10de8 v12: .cfa -64 + ^ v13: .cfa -56 + ^
STACK CFI 10df4 v14: .cfa -48 + ^ v15: .cfa -40 + ^
STACK CFI 10e00 x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI 10e0c .ra: .cfa -112 + ^
STACK CFI 10f5c .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 10f60 .cfa: sp 144 + .ra: .cfa -112 + ^ v10: .cfa -80 + ^ v11: .cfa -72 + ^ v12: .cfa -64 + ^ v13: .cfa -56 + ^ v14: .cfa -48 + ^ v15: .cfa -40 + ^ v8: .cfa -96 + ^ v9: .cfa -88 + ^ x19: .cfa -144 + ^ x20: .cfa -136 + ^ x21: .cfa -128 + ^ x22: .cfa -120 + ^
STACK CFI INIT 11190 b0 .cfa: sp 0 + .ra: x30
STACK CFI 11194 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1119c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 111a4 .ra: .cfa -40 + ^ x23: .cfa -48 + ^
STACK CFI 11228 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 1122c .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI INIT 11240 3dc .cfa: sp 0 + .ra: x30
STACK CFI 11248 .cfa: sp 192 + x19: .cfa -192 + ^ x20: .cfa -184 + ^
STACK CFI 11254 v8: .cfa -128 + ^ v9: .cfa -120 + ^
STACK CFI 11260 v10: .cfa -112 + ^ v11: .cfa -104 + ^
STACK CFI 11274 x21: .cfa -176 + ^ x22: .cfa -168 + ^
STACK CFI 11280 x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI 11290 .ra: .cfa -144 + ^
STACK CFI 11470 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 11474 .cfa: sp 192 + .ra: .cfa -144 + ^ v10: .cfa -112 + ^ v11: .cfa -104 + ^ v8: .cfa -128 + ^ v9: .cfa -120 + ^ x19: .cfa -192 + ^ x20: .cfa -184 + ^ x21: .cfa -176 + ^ x22: .cfa -168 + ^ x23: .cfa -160 + ^ x24: .cfa -152 + ^
STACK CFI INIT 11640 e4 .cfa: sp 0 + .ra: x30
STACK CFI 11644 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 1164c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 11658 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 11664 .ra: .cfa -40 + ^ x25: .cfa -48 + ^
STACK CFI 116e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25
STACK CFI 116e8 .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^
STACK CFI INIT 11728 150 .cfa: sp 0 + .ra: x30
STACK CFI 11774 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 11780 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1178c .ra: .cfa -16 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 11850 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 11858 .cfa: sp 64 + .ra: .cfa -16 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 11878 100 .cfa: sp 0 + .ra: x30
STACK CFI 1187c .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 11884 .ra: .cfa -8 + ^ x23: .cfa -16 + ^
STACK CFI 1188c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 11940 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 11948 .cfa: sp 48 + .ra: .cfa -8 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI INIT 11980 520 .cfa: sp 0 + .ra: x30
STACK CFI 11984 .cfa: sp 528 +
STACK CFI 11988 x19: .cfa -496 + ^ x20: .cfa -488 + ^
STACK CFI 11990 x23: .cfa -464 + ^ x24: .cfa -456 + ^
STACK CFI 119ac .ra: .cfa -424 + ^ v8: .cfa -416 + ^ v9: .cfa -408 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^
STACK CFI 11e08 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27
STACK CFI 11e10 .cfa: sp 528 + .ra: .cfa -424 + ^ v8: .cfa -416 + ^ v9: .cfa -408 + ^ x19: .cfa -496 + ^ x20: .cfa -488 + ^ x21: .cfa -480 + ^ x22: .cfa -472 + ^ x23: .cfa -464 + ^ x24: .cfa -456 + ^ x25: .cfa -448 + ^ x26: .cfa -440 + ^ x27: .cfa -432 + ^
STACK CFI INIT 11ed0 1ec8 .cfa: sp 0 + .ra: x30
STACK CFI 11ed4 .cfa: sp 1136 +
STACK CFI 11edc v8: .cfa -1040 + ^ v9: .cfa -1032 + ^
STACK CFI 11ee4 x19: .cfa -1136 + ^ x20: .cfa -1128 + ^
STACK CFI 11ef0 x21: .cfa -1120 + ^ x22: .cfa -1112 + ^
STACK CFI 11f0c .ra: .cfa -1056 + ^ x23: .cfa -1104 + ^ x24: .cfa -1096 + ^ x25: .cfa -1088 + ^ x26: .cfa -1080 + ^ x27: .cfa -1072 + ^ x28: .cfa -1064 + ^
STACK CFI 12f24 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 12f28 .cfa: sp 1136 + .ra: .cfa -1056 + ^ v8: .cfa -1040 + ^ v9: .cfa -1032 + ^ x19: .cfa -1136 + ^ x20: .cfa -1128 + ^ x21: .cfa -1120 + ^ x22: .cfa -1112 + ^ x23: .cfa -1104 + ^ x24: .cfa -1096 + ^ x25: .cfa -1088 + ^ x26: .cfa -1080 + ^ x27: .cfa -1072 + ^ x28: .cfa -1064 + ^
STACK CFI INIT 13df0 164c .cfa: sp 0 + .ra: x30
STACK CFI 13df4 .cfa: sp 656 +
STACK CFI 13dfc v8: .cfa -560 + ^ v9: .cfa -552 + ^
STACK CFI 13e04 x19: .cfa -656 + ^ x20: .cfa -648 + ^
STACK CFI 13e1c .ra: .cfa -576 + ^ x21: .cfa -640 + ^ x22: .cfa -632 + ^ x23: .cfa -624 + ^ x24: .cfa -616 + ^
STACK CFI 13e2c x25: .cfa -608 + ^ x26: .cfa -600 + ^ x27: .cfa -592 + ^ x28: .cfa -584 + ^
STACK CFI 147d8 .cfa: sp 0 + .ra: .ra v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 147dc .cfa: sp 656 + .ra: .cfa -576 + ^ v8: .cfa -560 + ^ v9: .cfa -552 + ^ x19: .cfa -656 + ^ x20: .cfa -648 + ^ x21: .cfa -640 + ^ x22: .cfa -632 + ^ x23: .cfa -624 + ^ x24: .cfa -616 + ^ x25: .cfa -608 + ^ x26: .cfa -600 + ^ x27: .cfa -592 + ^ x28: .cfa -584 + ^
STACK CFI INIT 5a10 30 .cfa: sp 0 + .ra: x30
STACK CFI 5a14 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 5a30 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 15488 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15490 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15498 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 154a0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 154a8 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 154b0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 154b8 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 154c0 5c .cfa: sp 0 + .ra: x30
STACK CFI INIT 15520 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15528 d8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15600 108 .cfa: sp 0 + .ra: x30
STACK CFI 15604 .cfa: sp 96 + x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 15618 .ra: .cfa -16 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 15704 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI INIT 15708 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15710 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15718 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15720 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15728 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15730 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 15740 24 .cfa: sp 0 + .ra: x30
STACK CFI 15744 .cfa: sp 16 + .ra: .cfa -8 + ^ x19: .cfa -16 + ^
STACK CFI 15760 .cfa: sp 0 + .ra: .ra x19: x19
STACK CFI INIT 15768 50 .cfa: sp 0 + .ra: x30
STACK CFI 1576c .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15778 .ra: .cfa -16 + ^
STACK CFI 157b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 58d0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 58d4 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 58e0 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 5960 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 5964 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 157b8 7c .cfa: sp 0 + .ra: x30
STACK CFI 157d8 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 157e8 .ra: .cfa -48 + ^
STACK CFI INIT 15838 74 .cfa: sp 0 + .ra: x30
STACK CFI 15850 .cfa: sp 64 + x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 15860 .ra: .cfa -48 + ^
STACK CFI INIT 158b0 1cc .cfa: sp 0 + .ra: x30
STACK CFI 158b4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 158c4 .ra: .cfa -8 + ^ x21: .cfa -16 + ^
STACK CFI 15a48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 15a50 .cfa: sp 32 + .ra: .cfa -8 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI INIT 15a80 1cc .cfa: sp 0 + .ra: x30
STACK CFI 15a84 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15a94 .ra: .cfa -16 + ^
STACK CFI 15c18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 15c20 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 15c50 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 15c54 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 15c64 .ra: .cfa -16 + ^
STACK CFI 15de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 15de8 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 15e20 36c .cfa: sp 0 + .ra: x30
STACK CFI 15e24 .cfa: sp 272 + x19: .cfa -272 + ^ x20: .cfa -264 + ^
STACK CFI 15e28 x21: .cfa -256 + ^ x22: .cfa -248 + ^
STACK CFI 15e30 .ra: .cfa -232 + ^ x23: .cfa -240 + ^
STACK CFI 16060 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 16068 .cfa: sp 272 + .ra: .cfa -232 + ^ x19: .cfa -272 + ^ x20: .cfa -264 + ^ x21: .cfa -256 + ^ x22: .cfa -248 + ^ x23: .cfa -240 + ^
STACK CFI INIT 161b0 cd8 .cfa: sp 0 + .ra: x30
STACK CFI 161b4 .cfa: sp 800 +
STACK CFI 161b8 v8: .cfa -712 + ^
STACK CFI 161c0 x19: .cfa -800 + ^ x20: .cfa -792 + ^
STACK CFI 161d0 x21: .cfa -784 + ^ x22: .cfa -776 + ^ x23: .cfa -768 + ^ x24: .cfa -760 + ^
STACK CFI 161e8 .ra: .cfa -720 + ^ x25: .cfa -752 + ^ x26: .cfa -744 + ^ x27: .cfa -736 + ^ x28: .cfa -728 + ^
STACK CFI 16778 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1677c .cfa: sp 800 + .ra: .cfa -720 + ^ v8: .cfa -712 + ^ x19: .cfa -800 + ^ x20: .cfa -792 + ^ x21: .cfa -784 + ^ x22: .cfa -776 + ^ x23: .cfa -768 + ^ x24: .cfa -760 + ^ x25: .cfa -752 + ^ x26: .cfa -744 + ^ x27: .cfa -736 + ^ x28: .cfa -728 + ^
STACK CFI INIT 16eb0 180 .cfa: sp 0 + .ra: x30
STACK CFI 16eb4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 16eb8 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 16ec8 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 16edc .ra: .cfa -16 + ^
STACK CFI 16fe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 16fe8 .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 17018 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 1701c .cfa: sp 80 + .ra: .cfa -16 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI INIT 17040 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17048 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17060 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17068 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 17070 50 .cfa: sp 0 + .ra: x30
STACK CFI 17074 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 17080 .ra: .cfa -16 + ^
STACK CFI 170bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI INIT 5970 a0 .cfa: sp 0 + .ra: x30
STACK CFI 5974 .cfa: sp 48 + x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5980 .ra: .cfa -24 + ^ x21: .cfa -32 + ^
STACK CFI 5a00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21
STACK CFI 5a04 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^
STACK CFI INIT 170c0 13c .cfa: sp 0 + .ra: x30
STACK CFI 170c4 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 170d4 .ra: .cfa -16 + ^
STACK CFI 171d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 171e0 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 17200 134 .cfa: sp 0 + .ra: x30
STACK CFI 17204 .cfa: sp 32 + x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 17214 .ra: .cfa -16 + ^
STACK CFI 17310 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20
STACK CFI 17318 .cfa: sp 32 + .ra: .cfa -16 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI INIT 17340 66c .cfa: sp 0 + .ra: x30
STACK CFI 17348 .cfa: sp 256 + x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 17354 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 17360 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 17370 .ra: .cfa -208 + ^
STACK CFI 176c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24
STACK CFI 176c8 .cfa: sp 256 + .ra: .cfa -208 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI INIT 179d0 ab0 .cfa: sp 0 + .ra: x30
STACK CFI 179d4 .cfa: sp 592 +
STACK CFI 179d8 x21: .cfa -576 + ^ x22: .cfa -568 + ^
STACK CFI 179e0 x23: .cfa -560 + ^ x24: .cfa -552 + ^
STACK CFI 17a04 .ra: .cfa -512 + ^ v10: .cfa -480 + ^ v11: .cfa -472 + ^ v12: .cfa -464 + ^ v13: .cfa -456 + ^ v14: .cfa -448 + ^ v15: .cfa -440 + ^ v8: .cfa -496 + ^ v9: .cfa -488 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^ x27: .cfa -528 + ^ x28: .cfa -520 + ^
STACK CFI 182a0 .cfa: sp 0 + .ra: .ra v10: v10 v11: v11 v12: v12 v13: v13 v14: v14 v15: v15 v8: v8 v9: v9 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 182a4 .cfa: sp 592 + .ra: .cfa -512 + ^ v10: .cfa -480 + ^ v11: .cfa -472 + ^ v12: .cfa -464 + ^ v13: .cfa -456 + ^ v14: .cfa -448 + ^ v15: .cfa -440 + ^ v8: .cfa -496 + ^ v9: .cfa -488 + ^ x19: .cfa -592 + ^ x20: .cfa -584 + ^ x21: .cfa -576 + ^ x22: .cfa -568 + ^ x23: .cfa -560 + ^ x24: .cfa -552 + ^ x25: .cfa -544 + ^ x26: .cfa -536 + ^ x27: .cfa -528 + ^ x28: .cfa -520 + ^
STACK CFI INIT 184a0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 184a4 .cfa: sp 80 + x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 184a8 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 184b0 .ra: .cfa -40 + ^ x23: .cfa -48 + ^
STACK CFI 1852c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 18530 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
STACK CFI 18558 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23
STACK CFI 1855c .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^
