MODULE Linux arm64 415980312C8DE3C9A0DC9F460C24E29D0 libcommon-auth-samba4.so.0
INFO CODE_ID 318059418D2CC9E3A0DC9F460C24E29D03B21A6C
PUBLIC 30c0 0 log_escape
PUBLIC 32e0 0 authn_policy_is_enforced
PUBLIC 3300 0 authn_kerberos_client_policy_is_enforced
PUBLIC 3320 0 authn_policy_enforced_tgt_lifetime_raw
PUBLIC 3374 0 authn_audit_info_event_id
PUBLIC 3420 0 authn_audit_info_silo_name
PUBLIC 3444 0 authn_audit_info_policy_name
PUBLIC 3470 0 authn_audit_info_policy_enforced
PUBLIC 34a0 0 authn_audit_info_client_info
PUBLIC 34c0 0 authn_audit_info_event
PUBLIC 3554 0 authn_audit_info_reason
PUBLIC 35f4 0 authn_audit_info_policy_status
PUBLIC 3610 0 authn_audit_info_location
PUBLIC 3630 0 authn_audit_info_policy_tgt_lifetime_mins
PUBLIC 3690 0 audit_get_timestamp
PUBLIC 38b0 0 audit_log_human_text
PUBLIC 3950 0 json_new_object
PUBLIC 39f0 0 json_new_array
PUBLIC 3a90 0 json_free
PUBLIC 3af0 0 json_is_invalid
PUBLIC 3b10 0 json_add_int
PUBLIC 3cd0 0 json_add_bool
PUBLIC 3e20 0 json_add_optional_bool
PUBLIC 3ff0 0 json_add_string
PUBLIC 4230 0 json_assert_is_array
PUBLIC 42e0 0 json_add_object
PUBLIC 4520 0 json_add_stringn
PUBLIC 4820 0 json_add_version
PUBLIC 49d4 0 json_add_time
PUBLIC 4c00 0 json_add_timestamp
PUBLIC 4d64 0 json_add_address
PUBLIC 5024 0 json_add_sid
PUBLIC 5214 0 json_add_guid
PUBLIC 5404 0 json_add_flags32
PUBLIC 5584 0 json_update_object
PUBLIC 57a4 0 json_to_string
PUBLIC 5940 0 audit_log_json
PUBLIC 5ad0 0 audit_message_send
PUBLIC 5e14 0 json_get_array
PUBLIC 6000 0 json_get_object
PUBLIC 61c0 0 json_null_object
PUBLIC 61e4 0 json_from_audit_info
PUBLIC 6460 0 auth_convert_user_info_dc_saminfo6
PUBLIC 6cc0 0 auth_convert_user_info_dc_saminfo2
PUBLIC 6dd0 0 auth_convert_user_info_dc_saminfo3
PUBLIC 6ed4 0 make_user_info_SamBaseInfo
PUBLIC 70f0 0 auth_user_info_copy
PUBLIC 7340 0 make_user_info_dc_netlogon_validation
PUBLIC 78c0 0 make_user_info_dc_pac
PUBLIC 7c50 0 wbcAuthUserInfo_to_netr_SamInfo6
PUBLIC 81f4 0 log_authentication_event
PUBLIC 8a30 0 log_successful_authz_event
PUBLIC 8f20 0 log_authz_event
PUBLIC 9280 0 copy_session_info
STACK CFI INIT 2b90 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2bc0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c00 48 .cfa: sp 0 + .ra: x30
STACK CFI 2c04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2c0c x19: .cfa -16 + ^
STACK CFI 2c44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2c50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2c60 278 .cfa: sp 0 + .ra: x30
STACK CFI 2c68 .cfa: sp 160 +
STACK CFI 2c74 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2c80 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2c8c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2cd4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2cdc x27: .cfa -16 + ^
STACK CFI 2d50 x25: x25 x26: x26
STACK CFI 2d54 x27: x27
STACK CFI 2d88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2d90 .cfa: sp 160 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 2d94 x25: x25 x26: x26
STACK CFI 2d98 x27: x27
STACK CFI 2dc8 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2e0c x25: x25 x26: x26
STACK CFI 2e14 x27: x27
STACK CFI 2e70 x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^
STACK CFI 2ecc x25: x25 x26: x26 x27: x27
STACK CFI 2ed0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2ed4 x27: .cfa -16 + ^
STACK CFI INIT 2ee0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 2ee8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2ef0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2f10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2f18 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 2f20 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 2f54 x21: x21 x22: x22
STACK CFI 2f6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2f74 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 2f7c x21: x21 x22: x22
STACK CFI INIT 2f84 13c .cfa: sp 0 + .ra: x30
STACK CFI 2f8c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2f94 x19: .cfa -16 + ^
STACK CFI 2fa8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2fb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 30c0 21c .cfa: sp 0 + .ra: x30
STACK CFI 30c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 30d0 x21: .cfa -16 + ^
STACK CFI 30d8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 31c8 x19: x19 x20: x20
STACK CFI 31d4 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 31dc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3280 x19: x19 x20: x20
STACK CFI 3288 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 32d8 x19: x19 x20: x20
STACK CFI INIT 32e0 1c .cfa: sp 0 + .ra: x30
STACK CFI 32e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 32f0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3300 18 .cfa: sp 0 + .ra: x30
STACK CFI 3308 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3310 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3320 54 .cfa: sp 0 + .ra: x30
STACK CFI 3330 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3338 x19: .cfa -16 + ^
STACK CFI 3350 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 3358 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3364 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3374 ac .cfa: sp 0 + .ra: x30
STACK CFI 337c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3384 x19: .cfa -16 + ^
STACK CFI 33cc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 33d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 33e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 33f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 3414 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 3420 24 .cfa: sp 0 + .ra: x30
STACK CFI 3428 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 343c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3444 24 .cfa: sp 0 + .ra: x30
STACK CFI 344c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3460 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3470 28 .cfa: sp 0 + .ra: x30
STACK CFI 3478 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3484 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 34a0 1c .cfa: sp 0 + .ra: x30
STACK CFI 34a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34b4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 34c0 94 .cfa: sp 0 + .ra: x30
STACK CFI 34c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 34fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3508 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3520 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 352c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3530 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3540 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3544 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3554 a0 .cfa: sp 0 + .ra: x30
STACK CFI 355c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3588 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3594 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35ac .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 35b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35bc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 35cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 35e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 35e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 35f4 1c .cfa: sp 0 + .ra: x30
STACK CFI 35fc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3604 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3610 1c .cfa: sp 0 + .ra: x30
STACK CFI 3618 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3624 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3630 60 .cfa: sp 0 + .ra: x30
STACK CFI 3638 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3668 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 367c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3680 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3690 21c .cfa: sp 0 + .ra: x30
STACK CFI 3698 .cfa: sp 128 +
STACK CFI 36a4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 36ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 36b4 x21: .cfa -16 + ^
STACK CFI 3774 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 377c .cfa: sp 128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 38b0 a0 .cfa: sp 0 + .ra: x30
STACK CFI 38c8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 38d0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 38dc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3900 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3908 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 3948 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 3950 98 .cfa: sp 0 + .ra: x30
STACK CFI 3958 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3960 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3988 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 39f0 98 .cfa: sp 0 + .ra: x30
STACK CFI 39f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3a00 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3a20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3a28 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3a90 60 .cfa: sp 0 + .ra: x30
STACK CFI 3a98 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 3aa0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 3ac8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3ad0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 3af0 20 .cfa: sp 0 + .ra: x30
STACK CFI 3af8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3b04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 3b10 1c0 .cfa: sp 0 + .ra: x30
STACK CFI 3b18 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3b20 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3b28 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3b70 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3b78 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3cd0 14c .cfa: sp 0 + .ra: x30
STACK CFI 3cd8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3ce0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3cec x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3d2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3d34 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3e20 1d0 .cfa: sp 0 + .ra: x30
STACK CFI 3e28 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3e30 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 3e3c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 3e88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 3e90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3ff0 238 .cfa: sp 0 + .ra: x30
STACK CFI 3ff8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4000 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 400c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4058 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4060 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4230 b0 .cfa: sp 0 + .ra: x30
STACK CFI 4238 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4240 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4268 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4270 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 4288 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4290 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 42e0 23c .cfa: sp 0 + .ra: x30
STACK CFI 42e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 42f0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 42f8 x21: .cfa -16 + ^
STACK CFI 4364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 436c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4520 2fc .cfa: sp 0 + .ra: x30
STACK CFI 4528 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4534 .cfa: x29 64 +
STACK CFI 4538 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 4544 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4554 x23: .cfa -16 + ^
STACK CFI 4634 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 463c .cfa: x29 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4820 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 4828 .cfa: sp 80 +
STACK CFI 4834 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 483c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4844 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4908 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4910 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 49d4 224 .cfa: sp 0 + .ra: x30
STACK CFI 49dc .cfa: sp 224 +
STACK CFI 49e8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 49f8 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4a00 x23: .cfa -16 + ^
STACK CFI 4ad8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 4ae0 .cfa: sp 224 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4c00 164 .cfa: sp 0 + .ra: x30
STACK CFI 4c08 .cfa: sp 64 +
STACK CFI 4c14 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4c1c x19: .cfa -16 + ^
STACK CFI 4c80 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 4c88 .cfa: sp 64 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 4d64 2c0 .cfa: sp 0 + .ra: x30
STACK CFI 4d6c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4d74 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4d7c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4dfc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5024 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 502c .cfa: sp 256 +
STACK CFI 5038 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5040 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 504c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 50c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 50cc .cfa: sp 256 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5214 1f0 .cfa: sp 0 + .ra: x30
STACK CFI 521c .cfa: sp 96 +
STACK CFI 5228 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5230 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 523c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 52b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 52bc .cfa: sp 96 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5404 180 .cfa: sp 0 + .ra: x30
STACK CFI 540c .cfa: sp 80 +
STACK CFI 5418 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5420 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 542c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 54b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 54b8 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5584 220 .cfa: sp 0 + .ra: x30
STACK CFI 558c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5594 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 55a0 x21: .cfa -16 + ^
STACK CFI 55fc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5604 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 57a4 198 .cfa: sp 0 + .ra: x30
STACK CFI 57ac .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 57b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 5808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5810 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 582c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5834 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5940 190 .cfa: sp 0 + .ra: x30
STACK CFI 5948 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5950 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5958 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 59ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 59b4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 59cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 59d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 5a78 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5a80 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5ad0 2bc .cfa: sp 0 + .ra: x30
STACK CFI 5ad8 .cfa: sp 160 +
STACK CFI 5ae4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 5af4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 5b00 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 5b0c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 5b78 x25: .cfa -16 + ^
STACK CFI 5bdc x25: x25
STACK CFI 5c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5c14 .cfa: sp 160 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x29: .cfa -80 + ^
STACK CFI 5c80 x25: .cfa -16 + ^
STACK CFI 5cbc x25: x25
STACK CFI 5d6c x25: .cfa -16 + ^
STACK CFI 5d80 x25: x25
STACK CFI 5d88 x25: .cfa -16 + ^
STACK CFI INIT 5d90 84 .cfa: sp 0 + .ra: x30
STACK CFI 5d98 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5da0 x21: .cfa -16 + ^
STACK CFI 5dac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5dd8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5de0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5e08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5e14 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 5e1c .cfa: sp 80 +
STACK CFI 5e28 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5e30 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5e38 x21: .cfa -16 + ^
STACK CFI 5ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5ed0 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6000 1b8 .cfa: sp 0 + .ra: x30
STACK CFI 6008 .cfa: sp 80 +
STACK CFI 6014 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 601c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6028 x21: .cfa -16 + ^
STACK CFI 60b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 60b8 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 61c0 24 .cfa: sp 0 + .ra: x30
STACK CFI 61c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 61d8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 61e4 274 .cfa: sp 0 + .ra: x30
STACK CFI 61ec .cfa: sp 80 +
STACK CFI 61f8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6200 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 626c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6274 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 63b0 x21: .cfa -16 + ^
STACK CFI 6424 x21: x21
STACK CFI 6428 x21: .cfa -16 + ^
STACK CFI 642c x21: x21
STACK CFI 6434 x21: .cfa -16 + ^
STACK CFI 6450 x21: x21
STACK CFI INIT 6460 858 .cfa: sp 0 + .ra: x30
STACK CFI 6468 .cfa: sp 176 +
STACK CFI 646c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 6474 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 6488 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 6494 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 64a0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 684c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 6854 .cfa: sp 176 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 6cc0 110 .cfa: sp 0 + .ra: x30
STACK CFI 6cc8 .cfa: sp 64 +
STACK CFI 6cd4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6cdc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6ce8 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6da0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6da8 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6dd0 104 .cfa: sp 0 + .ra: x30
STACK CFI 6dd8 .cfa: sp 80 +
STACK CFI 6de4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6dec x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6df4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6e00 x23: .cfa -16 + ^
STACK CFI 6ea4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6eac .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 6ed4 214 .cfa: sp 0 + .ra: x30
STACK CFI 6edc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6ee4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6ef0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6f04 x23: .cfa -16 + ^
STACK CFI 701c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7024 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7054 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 705c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 70f0 24c .cfa: sp 0 + .ra: x30
STACK CFI 70f8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7108 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7244 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 724c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7340 57c .cfa: sp 0 + .ra: x30
STACK CFI 7348 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7354 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7360 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7370 x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 7404 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 740c .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 74fc x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 7580 x25: x25 x26: x26
STACK CFI 75a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 75ac .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 75d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x27: x27 x28: x28 x29: x29
STACK CFI 75dc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 7618 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 761c x25: x25 x26: x26
STACK CFI INIT 78c0 390 .cfa: sp 0 + .ra: x30
STACK CFI 78c8 .cfa: sp 112 +
STACK CFI 78d4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 78dc x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 78ec x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 79f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 79fc .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 7a00 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7a6c x25: .cfa -16 + ^
STACK CFI 7af4 x23: x23 x24: x24
STACK CFI 7afc x25: x25
STACK CFI 7b00 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7b04 x23: x23 x24: x24
STACK CFI 7b0c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7b30 x23: x23 x24: x24
STACK CFI 7b70 x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^
STACK CFI 7b74 x23: x23 x24: x24
STACK CFI 7b78 x25: x25
STACK CFI 7b80 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7b98 x23: x23 x24: x24
STACK CFI 7bf0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7c08 x23: x23 x24: x24
STACK CFI 7c10 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7c44 x23: x23 x24: x24
STACK CFI 7c48 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7c4c x25: .cfa -16 + ^
STACK CFI INIT 7c50 5a4 .cfa: sp 0 + .ra: x30
STACK CFI 7c58 .cfa: sp 320 +
STACK CFI 7c64 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 7c6c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 7c74 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7c80 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 7e34 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7f0c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7f5c x27: x27 x28: x28
STACK CFI 7fd0 x25: x25 x26: x26
STACK CFI 8008 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8010 .cfa: sp 320 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x29: .cfa -96 + ^
STACK CFI 8024 x25: x25 x26: x26
STACK CFI 802c x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8040 x27: x27 x28: x28
STACK CFI 8058 x25: x25 x26: x26
STACK CFI 815c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8174 x25: x25 x26: x26
STACK CFI 8178 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8190 x25: x25 x26: x26
STACK CFI 81cc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 81e4 x25: x25 x26: x26
STACK CFI 81ec x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 81f0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 81f4 834 .cfa: sp 0 + .ra: x30
STACK CFI 81fc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 820c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8214 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8220 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 822c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 823c .cfa: sp 544 + x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 82cc .cfa: sp 96 +
STACK CFI 82e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 82ec .cfa: sp 544 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8a30 4e8 .cfa: sp 0 + .ra: x30
STACK CFI 8a38 .cfa: sp 448 +
STACK CFI 8a3c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8a44 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8a58 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8a60 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8a6c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8a78 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8b00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8b08 .cfa: sp 448 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 8f20 360 .cfa: sp 0 + .ra: x30
STACK CFI 8f28 .cfa: sp 192 +
STACK CFI 8f34 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8f40 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8f58 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 8f68 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8f74 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8f7c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8fe8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 8ff0 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 9280 278 .cfa: sp 0 + .ra: x30
STACK CFI 9288 .cfa: sp 80 +
STACK CFI 9294 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 929c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 92a4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 9364 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 936c .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 9500 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 9530 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2b50 24 .cfa: sp 0 + .ra: x30
STACK CFI 2b54 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2b6c .cfa: sp 0 + .ra: .ra x29: x29
