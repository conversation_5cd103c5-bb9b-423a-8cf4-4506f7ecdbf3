MODULE Linux arm64 F9FFCBE80B954616141737B2B42D805B0 libpopt.so.0
INFO CODE_ID E8CBFFF9950B1646141737B2B42D805B79E1B964
PUBLIC 57f0 0 poptSetExecPath
PUBLIC 5890 0 poptGetContext
PUBLIC 59e0 0 poptResetContext
PUBLIC 5b60 0 poptBitsAdd
PUBLIC 5c60 0 poptBitsChk
PUBLIC 5d84 0 poptBitsClr
PUBLIC 5dd4 0 poptBitsDel
PUBLIC 5ed4 0 poptBitsIntersect
PUBLIC 5f80 0 poptBitsUnion
PUBLIC 6024 0 poptBitsArgs
PUBLIC 60d0 0 poptSaveBits
PUBLIC 6230 0 poptSaveString
PUBLIC 6360 0 poptSaveLongLong
PUBLIC 6470 0 poptSaveLong
PUBLIC 6580 0 poptSaveInt
PUBLIC 6694 0 poptSaveShort
PUBLIC 67a4 0 poptGetOptArg
PUBLIC 67e0 0 poptGetArg
PUBLIC 6834 0 poptPeekArg
PUBLIC 6880 0 poptGetArgs
PUBLIC 68d0 0 poptFreeContext
PUBLIC 69d0 0 poptAddItem
PUBLIC 6b80 0 poptAddAlias
PUBLIC 6c20 0 poptBadOption
PUBLIC 6ca0 0 poptStrerror
PUBLIC 7020 0 poptGetInvocationName
PUBLIC 7060 0 poptStrippedArgv
PUBLIC 7140 0 poptDupArgv
PUBLIC 7510 0 poptGetNextOpt
PUBLIC 8420 0 poptStuffArgs
PUBLIC 84b4 0 poptParseArgvString
PUBLIC 86f0 0 poptConfigFileToString
PUBLIC 8ab4 0 poptSaneFile
PUBLIC 8b80 0 poptReadFile
PUBLIC 8d44 0 poptReadConfigFile
PUBLIC 9364 0 poptReadConfigFiles
PUBLIC 9544 0 poptReadDefaultConfig
PUBLIC 9724 0 poptFini
PUBLIC 9740 0 poptInit
PUBLIC 9810 0 poptPrintHelp
PUBLIC 9940 0 poptPrintUsage
PUBLIC 9ba4 0 poptSetOtherOptionHelp
STACK CFI INIT 2380 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 23b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 23f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 23f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 23fc x19: .cfa -16 + ^
STACK CFI 2434 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 2440 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 2450 e8 .cfa: sp 0 + .ra: x30
STACK CFI 245c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2464 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 246c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2478 x23: .cfa -16 + ^
STACK CFI 24e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 24f0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2534 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2540 e4 .cfa: sp 0 + .ra: x30
STACK CFI 254c .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2554 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 255c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 2568 x23: .cfa -16 + ^
STACK CFI 25f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 25fc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 2620 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 2624 1c .cfa: sp 0 + .ra: x30
STACK CFI 262c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2638 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2640 57c .cfa: sp 0 + .ra: x30
STACK CFI 2648 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2718 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2724 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2990 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 299c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2bc0 94 .cfa: sp 0 + .ra: x30
STACK CFI 2bcc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2bf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 2bf8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 2c54 90 .cfa: sp 0 + .ra: x30
STACK CFI 2c5c .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 2c64 x21: .cfa -16 + ^
STACK CFI 2c78 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 2cd0 x19: x19 x20: x20
STACK CFI 2cdc .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI INIT 2ce4 178 .cfa: sp 0 + .ra: x30
STACK CFI 2cf0 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 2cf8 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2d04 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2d14 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2d20 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2d28 x27: .cfa -16 + ^
STACK CFI 2e2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI 2e34 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x29: .cfa -96 + ^
STACK CFI 2e58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x29: x29
STACK CFI INIT 2e60 7c .cfa: sp 0 + .ra: x30
STACK CFI 2e68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2e74 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2ea0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 2ea8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 2ed4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2ee0 e4 .cfa: sp 0 + .ra: x30
STACK CFI 2eec .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2ef4 x19: .cfa -16 + ^
STACK CFI 2f0c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2f14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2f84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2f8c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 2fac .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 2fb4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 2fc4 1b4 .cfa: sp 0 + .ra: x30
STACK CFI 2fcc .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2fd4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 2fe0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 2ffc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 30b0 x19: x19 x20: x20
STACK CFI 30b8 x21: x21 x22: x22
STACK CFI 30bc x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 30d0 x19: x19 x20: x20
STACK CFI 30d8 x21: x21 x22: x22
STACK CFI 30dc x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 30f0 x21: x21 x22: x22
STACK CFI 30f4 x19: x19 x20: x20
STACK CFI 30fc .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 3104 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI 3124 x19: x19 x20: x20 x21: x21 x22: x22
STACK CFI 3130 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 3138 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 3180 2ec .cfa: sp 0 + .ra: x30
STACK CFI 3188 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 3190 x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 3198 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 31a4 x25: .cfa -64 + ^ x26: .cfa -56 + ^
STACK CFI 31bc x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 31d8 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3284 x27: x27 x28: x28
STACK CFI 3298 x19: x19 x20: x20
STACK CFI 32ac .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 32b4 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x26: .cfa -56 + ^ x29: .cfa -128 + ^
STACK CFI 32d0 x19: x19 x20: x20
STACK CFI 32d4 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3438 x27: x27 x28: x28
STACK CFI 3440 x27: .cfa -48 + ^ x28: .cfa -40 + ^
STACK CFI 3454 x19: x19 x20: x20
STACK CFI 3458 x27: x27 x28: x28
STACK CFI 3460 x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI INIT 3470 108 .cfa: sp 0 + .ra: x30
STACK CFI 3478 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3480 x21: .cfa -16 + ^
STACK CFI 3498 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 34d8 x19: x19 x20: x20
STACK CFI 34e0 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 34e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3500 x19: x19 x20: x20
STACK CFI 350c .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 3514 .cfa: sp 48 + .ra: .cfa -40 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 3520 .cfa: sp 0 + .ra: .ra x21: x21 x29: x29
STACK CFI 3528 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3580 228 .cfa: sp 0 + .ra: x30
STACK CFI 3588 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 3594 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 35a0 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 35ac x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 35b4 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 3718 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 3720 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI INIT 37b0 88 .cfa: sp 0 + .ra: x30
STACK CFI 37b8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 37c0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 37c8 x21: .cfa -16 + ^
STACK CFI 3824 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 382c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 3840 354 .cfa: sp 0 + .ra: x30
STACK CFI 3850 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 3858 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 38d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 38e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 38f4 x21: .cfa -16 + ^
STACK CFI 3930 x21: x21
STACK CFI 393c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 3944 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 3978 x21: .cfa -16 + ^
STACK CFI 39a4 x21: x21
STACK CFI 39b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 39bc .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 39c0 x21: .cfa -16 + ^
STACK CFI 3a4c x21: x21
STACK CFI 3a60 x21: .cfa -16 + ^
STACK CFI 3ac8 x21: x21
STACK CFI 3adc x21: .cfa -16 + ^
STACK CFI 3b44 x21: x21
STACK CFI 3b58 x21: .cfa -16 + ^
STACK CFI 3b84 x21: x21
STACK CFI 3b90 x21: .cfa -16 + ^
STACK CFI INIT 3b94 208 .cfa: sp 0 + .ra: x30
STACK CFI 3b9c .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 3ba4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 3bac x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 3bb4 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 3bcc x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 3bd4 x27: .cfa -16 + ^
STACK CFI 3cdc x25: x25 x26: x26
STACK CFI 3ce0 x27: x27
STACK CFI 3d0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3d14 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 3d54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3d5c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 3d74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 3d7c .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI INIT 3da0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 3da8 .cfa: sp 32 +
STACK CFI 3db4 .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 3e28 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 3e30 .cfa: sp 32 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 3e44 258 .cfa: sp 0 + .ra: x30
STACK CFI 3e4c .cfa: sp 112 +
STACK CFI 3e58 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 3e60 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 3e68 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 3e74 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 3e80 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 4004 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 400c .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 40a0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 40a8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 40b0 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 40c4 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 40d0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 4118 x19: x19 x20: x20
STACK CFI 411c x21: x21 x22: x22
STACK CFI 4128 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 4130 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 4164 218 .cfa: sp 0 + .ra: x30
STACK CFI 416c .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 4174 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 4180 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 418c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4198 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 4340 x19: x19 x20: x20
STACK CFI 4344 x21: x21 x22: x22
STACK CFI 4348 x23: x23 x24: x24
STACK CFI 4354 .cfa: sp 0 + .ra: .ra x25: x25 x26: x26 x29: x29
STACK CFI 435c .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 4380 198 .cfa: sp 0 + .ra: x30
STACK CFI 4388 .cfa: sp 112 +
STACK CFI 4394 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 439c x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 43a4 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 43c0 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 43c8 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 44ac x21: x21 x22: x22
STACK CFI 44b0 x25: x25 x26: x26
STACK CFI 44e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 44e8 .cfa: sp 112 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 450c x21: x21 x22: x22 x25: x25 x26: x26
STACK CFI 4510 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 4514 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI INIT 4520 360 .cfa: sp 0 + .ra: x30
STACK CFI 4528 .cfa: sp 416 +
STACK CFI 4538 .ra: .cfa -264 + ^ x29: .cfa -272 + ^
STACK CFI 4548 x19: .cfa -256 + ^ x20: .cfa -248 + ^
STACK CFI 45d0 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 45ec x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 4604 x23: x23 x24: x24
STACK CFI 4660 x21: x21 x22: x22
STACK CFI 468c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4694 .cfa: sp 416 + .ra: .cfa -264 + ^ x19: .cfa -256 + ^ x20: .cfa -248 + ^ x29: .cfa -272 + ^
STACK CFI 469c x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 46f8 x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 4700 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 4784 x23: x23 x24: x24
STACK CFI 4788 x25: x25 x26: x26
STACK CFI 478c x27: x27 x28: x28
STACK CFI 4794 x21: x21 x22: x22
STACK CFI 479c x21: .cfa -240 + ^ x22: .cfa -232 + ^ x23: .cfa -224 + ^ x24: .cfa -216 + ^ x25: .cfa -208 + ^ x26: .cfa -200 + ^ x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 47f0 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 47f4 x21: .cfa -240 + ^ x22: .cfa -232 + ^
STACK CFI 47f8 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 47fc x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 4800 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 4804 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 4808 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI 480c x25: .cfa -208 + ^ x26: .cfa -200 + ^
STACK CFI 4810 x27: .cfa -192 + ^ x28: .cfa -184 + ^
STACK CFI 484c x25: x25 x26: x26
STACK CFI 4850 x27: x27 x28: x28
STACK CFI 4870 x23: x23 x24: x24
STACK CFI 4874 x23: .cfa -224 + ^ x24: .cfa -216 + ^
STACK CFI INIT 4880 108 .cfa: sp 0 + .ra: x30
STACK CFI 4888 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 4890 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 489c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 48b0 x23: .cfa -16 + ^
STACK CFI 4980 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI INIT 4990 168 .cfa: sp 0 + .ra: x30
STACK CFI 4998 .cfa: sp 144 +
STACK CFI 49a4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 49ac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 49b4 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 49d4 x23: .cfa -16 + ^
STACK CFI 4a24 x23: x23
STACK CFI 4a58 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4a60 .cfa: sp 144 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 4ac0 x23: .cfa -16 + ^
STACK CFI 4ac4 x23: x23
STACK CFI 4acc x23: .cfa -16 + ^
STACK CFI INIT 4b00 a18 .cfa: sp 0 + .ra: x30
STACK CFI 4b08 .cfa: sp 192 +
STACK CFI 4b0c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 4b14 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 4b34 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 4b3c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 4b48 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 4f3c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 4f44 .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 51f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 51fc .cfa: sp 192 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 5520 2c8 .cfa: sp 0 + .ra: x30
STACK CFI 5528 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 5530 x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 5540 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 5548 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 5558 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 5570 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 56fc x25: x25 x26: x26
STACK CFI 5700 x27: x27 x28: x28
STACK CFI 5710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5718 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x29: .cfa -112 + ^
STACK CFI INIT 57f0 9c .cfa: sp 0 + .ra: x30
STACK CFI 57f8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5800 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 580c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 585c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5864 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5890 150 .cfa: sp 0 + .ra: x30
STACK CFI 5898 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 58a0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 58ac x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 58bc x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 5990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 5998 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 59e0 180 .cfa: sp 0 + .ra: x30
STACK CFI 59f0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 59f8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5a04 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5b54 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 5b60 fc .cfa: sp 0 + .ra: x30
STACK CFI 5b68 .cfa: sp 64 +
STACK CFI 5b74 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5b7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5b84 x21: .cfa -16 + ^
STACK CFI 5c50 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5c58 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5c60 124 .cfa: sp 0 + .ra: x30
STACK CFI 5c68 .cfa: sp 64 +
STACK CFI 5c74 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5c7c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5c84 x21: .cfa -16 + ^
STACK CFI 5d30 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5d38 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5d84 50 .cfa: sp 0 + .ra: x30
STACK CFI 5da4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 5dc0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 5dd4 100 .cfa: sp 0 + .ra: x30
STACK CFI 5ddc .cfa: sp 64 +
STACK CFI 5de8 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5df0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5df8 x21: .cfa -16 + ^
STACK CFI 5ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5ed0 .cfa: sp 64 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 5ed4 a8 .cfa: sp 0 + .ra: x30
STACK CFI 5efc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5f04 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5f10 x21: .cfa -16 + ^
STACK CFI 5f64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 5f6c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 5f74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 5f80 a4 .cfa: sp 0 + .ra: x30
STACK CFI 5fa8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5fb0 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5fbc x21: .cfa -16 + ^
STACK CFI 600c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6014 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 601c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI INIT 6024 a8 .cfa: sp 0 + .ra: x30
STACK CFI 603c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6044 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 60a0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 60a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 60b4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 60bc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 60c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 60d0 160 .cfa: sp 0 + .ra: x30
STACK CFI 60d8 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 60e8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 60f0 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6110 x23: .cfa -16 + ^
STACK CFI 61a0 x19: x19 x20: x20
STACK CFI 61a4 x23: x23
STACK CFI 61b0 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 61b8 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 61fc x23: x23
STACK CFI 6200 x19: x19 x20: x20
STACK CFI 6208 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x23: .cfa -16 + ^
STACK CFI INIT 6230 128 .cfa: sp 0 + .ra: x30
STACK CFI 6248 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6250 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6260 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 62ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 62f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6300 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6308 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 6360 110 .cfa: sp 0 + .ra: x30
STACK CFI 6370 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6378 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6388 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 63f0 x19: x19 x20: x20
STACK CFI 63f4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6410 x19: x19 x20: x20
STACK CFI 641c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6424 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6428 x19: x19 x20: x20
STACK CFI 6430 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6460 x19: x19 x20: x20
STACK CFI 6468 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 6470 110 .cfa: sp 0 + .ra: x30
STACK CFI 6480 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6488 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6498 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6500 x19: x19 x20: x20
STACK CFI 6504 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6520 x19: x19 x20: x20
STACK CFI 652c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6534 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 6538 x19: x19 x20: x20
STACK CFI 6540 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6570 x19: x19 x20: x20
STACK CFI 6578 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 6580 114 .cfa: sp 0 + .ra: x30
STACK CFI 6590 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6598 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 65a8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6614 x19: x19 x20: x20
STACK CFI 6618 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6634 x19: x19 x20: x20
STACK CFI 6640 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6648 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 664c x19: x19 x20: x20
STACK CFI 6654 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6684 x19: x19 x20: x20
STACK CFI 668c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 6694 110 .cfa: sp 0 + .ra: x30
STACK CFI 66a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 66ac x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 66b8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6724 x19: x19 x20: x20
STACK CFI 6728 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6744 x19: x19 x20: x20
STACK CFI 6750 .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI 6758 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 675c x19: x19 x20: x20
STACK CFI 6764 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6794 x19: x19 x20: x20
STACK CFI 679c .cfa: sp 0 + .ra: .ra x21: x21 x22: x22 x29: x29
STACK CFI INIT 67a4 38 .cfa: sp 0 + .ra: x30
STACK CFI 67ac .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 67c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 67cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 67d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 67e0 54 .cfa: sp 0 + .ra: x30
STACK CFI 67e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 681c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6824 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6828 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6834 4c .cfa: sp 0 + .ra: x30
STACK CFI 683c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6868 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6870 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6874 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6880 50 .cfa: sp 0 + .ra: x30
STACK CFI 6888 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 68b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 68c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 68c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 68d0 100 .cfa: sp 0 + .ra: x30
STACK CFI 68e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 68e8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 6938 x21: .cfa -16 + ^
STACK CFI 6964 x21: x21
STACK CFI 69c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 69d0 1ac .cfa: sp 0 + .ra: x30
STACK CFI 69d8 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 69e0 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 69e8 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 6a1c x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6a3c x25: .cfa -16 + ^
STACK CFI 6a64 x25: x25
STACK CFI 6b04 x23: x23 x24: x24
STACK CFI 6b2c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6b34 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI 6b50 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 6b54 x25: .cfa -16 + ^
STACK CFI INIT 6b80 9c .cfa: sp 0 + .ra: x30
STACK CFI 6b88 .cfa: sp 96 +
STACK CFI 6b9c .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6c10 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6c18 .cfa: sp 96 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6c20 78 .cfa: sp 0 + .ra: x30
STACK CFI 6c28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6c40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6c48 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6c64 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6c6c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6c8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6ca0 380 .cfa: sp 0 + .ra: x30
STACK CFI 6ca8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 6cb8 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^
STACK CFI 6d20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6d28 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI 6f18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 6f20 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7020 3c .cfa: sp 0 + .ra: x30
STACK CFI 7028 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7040 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7048 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 704c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7060 d8 .cfa: sp 0 + .ra: x30
STACK CFI 7068 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 70f8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7100 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7110 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7118 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7140 11c .cfa: sp 0 + .ra: x30
STACK CFI 7158 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 7160 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 7170 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 717c x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 7188 x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 71c8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 71d0 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 7238 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7240 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI 724c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 7254 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 7260 2b0 .cfa: sp 0 + .ra: x30
STACK CFI 7268 .cfa: sp 112 + .ra: .cfa -104 + ^ x29: .cfa -112 + ^
STACK CFI 7270 x27: .cfa -32 + ^ x28: .cfa -24 + ^
STACK CFI 727c x21: .cfa -80 + ^ x22: .cfa -72 + ^
STACK CFI 7284 x19: .cfa -96 + ^ x20: .cfa -88 + ^
STACK CFI 7290 x25: .cfa -48 + ^ x26: .cfa -40 + ^
STACK CFI 72c0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 73a8 x23: x23 x24: x24
STACK CFI 73c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 73cc .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 73e0 x23: .cfa -64 + ^ x24: .cfa -56 + ^
STACK CFI 73fc x23: x23 x24: x24
STACK CFI 7408 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7410 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 7474 x23: x23 x24: x24
STACK CFI 7480 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 7488 .cfa: sp 112 + .ra: .cfa -104 + ^ x19: .cfa -96 + ^ x20: .cfa -88 + ^ x21: .cfa -80 + ^ x22: .cfa -72 + ^ x23: .cfa -64 + ^ x24: .cfa -56 + ^ x25: .cfa -48 + ^ x26: .cfa -40 + ^ x27: .cfa -32 + ^ x28: .cfa -24 + ^ x29: .cfa -112 + ^
STACK CFI 7508 x23: x23 x24: x24
STACK CFI INIT 7510 f0c .cfa: sp 0 + .ra: x30
STACK CFI 7518 .cfa: sp 144 +
STACK CFI 7524 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 752c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 7540 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 754c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 755c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 7564 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7a74 x23: x23 x24: x24
STACK CFI 7a78 x25: x25 x26: x26
STACK CFI 7a7c x27: x27 x28: x28
STACK CFI 7a80 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7aa8 x23: x23 x24: x24
STACK CFI 7aac x25: x25 x26: x26
STACK CFI 7ab4 x27: x27 x28: x28
STACK CFI 7ae4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7aec .cfa: sp 144 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 7cb0 x23: x23 x24: x24
STACK CFI 7cb8 x25: x25 x26: x26
STACK CFI 7cbc x27: x27 x28: x28
STACK CFI 7cc0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7d5c x23: x23 x24: x24
STACK CFI 7d64 x25: x25 x26: x26
STACK CFI 7d68 x27: x27 x28: x28
STACK CFI 7d6c x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7de4 x23: x23 x24: x24
STACK CFI 7dec x25: x25 x26: x26
STACK CFI 7df0 x27: x27 x28: x28
STACK CFI 7df4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7df8 x23: x23 x24: x24
STACK CFI 7e00 x25: x25 x26: x26
STACK CFI 7e04 x27: x27 x28: x28
STACK CFI 7e08 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7ee0 x23: x23 x24: x24
STACK CFI 7ee4 x25: x25 x26: x26
STACK CFI 7ee8 x27: x27 x28: x28
STACK CFI 7ef0 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7f40 x23: x23 x24: x24
STACK CFI 7f48 x25: x25 x26: x26
STACK CFI 7f4c x27: x27 x28: x28
STACK CFI 7f50 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 7fc4 x23: x23 x24: x24
STACK CFI 7fcc x25: x25 x26: x26
STACK CFI 7fd0 x27: x27 x28: x28
STACK CFI 7fd4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8064 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8070 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8074 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 8078 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 80bc x23: x23 x24: x24
STACK CFI 80c4 x25: x25 x26: x26
STACK CFI 80c8 x27: x27 x28: x28
STACK CFI 80cc x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8284 x23: x23 x24: x24
STACK CFI 8288 x25: x25 x26: x26
STACK CFI 828c x27: x27 x28: x28
STACK CFI 8290 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 82f8 x23: x23 x24: x24
STACK CFI 8300 x25: x25 x26: x26
STACK CFI 8304 x27: x27 x28: x28
STACK CFI 8308 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 8420 94 .cfa: sp 0 + .ra: x30
STACK CFI 8428 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8434 x19: .cfa -16 + ^
STACK CFI 849c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 84a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 84b4 238 .cfa: sp 0 + .ra: x30
STACK CFI 84bc .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 84c4 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 84d4 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 84fc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 850c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 85b8 x23: x23 x24: x24
STACK CFI 85bc x27: x27 x28: x28
STACK CFI 85d0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 85d8 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 86ac x23: x23 x24: x24
STACK CFI 86b4 x27: x27 x28: x28
STACK CFI 86b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x29: x29
STACK CFI 86c0 .cfa: sp 96 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 86d0 x27: x27 x28: x28
STACK CFI 86dc x23: x23 x24: x24
STACK CFI 86e4 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 86f0 3c4 .cfa: sp 0 + .ra: x30
STACK CFI 86f8 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 870c .cfa: sp 1104 + x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 872c x25: .cfa -32 + ^
STACK CFI 8730 x26: .cfa -24 + ^
STACK CFI 8734 x27: .cfa -16 + ^
STACK CFI 8740 x28: .cfa -8 + ^
STACK CFI 8758 x21: .cfa -64 + ^
STACK CFI 8764 x19: .cfa -80 + ^
STACK CFI 8768 x20: .cfa -72 + ^
STACK CFI 876c x22: .cfa -56 + ^
STACK CFI 8930 x19: x19
STACK CFI 8934 x20: x20
STACK CFI 8938 x21: x21
STACK CFI 893c x22: x22
STACK CFI 8940 x25: x25
STACK CFI 8944 x26: x26
STACK CFI 8948 x27: x27
STACK CFI 894c x28: x28
STACK CFI 896c .cfa: sp 96 +
STACK CFI 8974 .cfa: sp 0 + .ra: .ra x23: x23 x24: x24 x29: x29
STACK CFI 897c .cfa: sp 1104 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8a58 x19: x19 x20: x20 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 8a60 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8a6c x19: x19
STACK CFI 8a70 x20: x20
STACK CFI 8a74 x21: x21
STACK CFI 8a78 x22: x22
STACK CFI 8a80 x25: x25
STACK CFI 8a84 x26: x26
STACK CFI 8a88 x27: x27
STACK CFI 8a8c x28: x28
STACK CFI 8a94 x19: .cfa -80 + ^
STACK CFI 8a98 x20: .cfa -72 + ^
STACK CFI 8a9c x21: .cfa -64 + ^
STACK CFI 8aa0 x22: .cfa -56 + ^
STACK CFI 8aa4 x25: .cfa -32 + ^
STACK CFI 8aa8 x26: .cfa -24 + ^
STACK CFI 8aac x27: .cfa -16 + ^
STACK CFI 8ab0 x28: .cfa -8 + ^
STACK CFI INIT 8ab4 c8 .cfa: sp 0 + .ra: x30
STACK CFI 8abc .cfa: sp 176 +
STACK CFI 8ac8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8ae8 x19: .cfa -16 + ^
STACK CFI 8af8 x19: x19
STACK CFI 8b20 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8b28 .cfa: sp 176 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 8b6c x19: x19
STACK CFI 8b78 x19: .cfa -16 + ^
STACK CFI INIT 8b80 1c4 .cfa: sp 0 + .ra: x30
STACK CFI 8b88 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8b94 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8ba4 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 8cc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 8ccc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8d44 620 .cfa: sp 0 + .ra: x30
STACK CFI 8d4c .cfa: sp 256 +
STACK CFI 8d5c .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 8d68 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 8d98 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 8db8 x27: x27 x28: x28
STACK CFI 8de8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8df0 .cfa: sp 256 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 8e08 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 8e10 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9168 x23: x23 x24: x24
STACK CFI 916c x25: x25 x26: x26
STACK CFI 9174 x27: x27 x28: x28
STACK CFI 9178 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9180 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9194 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 91bc x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 91c0 x27: x27 x28: x28
STACK CFI 91c8 x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 92b4 x23: x23 x24: x24
STACK CFI 92b8 x25: x25 x26: x26
STACK CFI 92bc x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9354 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 9358 x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 935c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9360 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 9364 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 936c .cfa: sp 128 +
STACK CFI 9378 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 9384 x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 93c4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 93e0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 93f0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 94b0 x21: x21 x22: x22
STACK CFI 94b4 x25: x25 x26: x26
STACK CFI 94c0 x27: x27 x28: x28
STACK CFI 94f0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI 94f8 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x29: .cfa -96 + ^
STACK CFI 9504 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9508 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 950c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 9510 x21: x21 x22: x22 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 952c x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 9534 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 9538 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 9544 1e0 .cfa: sp 0 + .ra: x30
STACK CFI 954c .cfa: sp 224 +
STACK CFI 9558 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9570 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 95b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 95c0 .cfa: sp 224 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x29: .cfa -64 + ^
STACK CFI 95fc x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9658 x21: x21 x22: x22
STACK CFI 968c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9698 x23: .cfa -16 + ^
STACK CFI 96cc x21: x21 x22: x22
STACK CFI 96d0 x23: x23
STACK CFI 96d4 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 9704 x21: x21 x22: x22
STACK CFI 9708 x23: x23
STACK CFI 9710 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9714 x23: .cfa -16 + ^
STACK CFI 9718 x23: x23
STACK CFI 971c x21: x21 x22: x22
STACK CFI INIT 9724 18 .cfa: sp 0 + .ra: x30
STACK CFI 972c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 9734 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 9740 d0 .cfa: sp 0 + .ra: x30
STACK CFI 9748 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9750 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9758 x23: .cfa -16 + ^
STACK CFI 9778 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 97bc x21: x21 x22: x22
STACK CFI 97c8 x23: x23
STACK CFI 97cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 97d4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 97d8 x21: x21 x22: x22
STACK CFI 97dc x23: x23
STACK CFI 97ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 97f4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 97f8 x21: x21 x22: x22
STACK CFI 9804 x23: x23
STACK CFI 9808 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 9810 128 .cfa: sp 0 + .ra: x30
STACK CFI 9818 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 9820 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 982c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 98ac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 98b4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 98c8 x23: .cfa -16 + ^
STACK CFI 9920 x23: x23
STACK CFI 9930 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 9940 214 .cfa: sp 0 + .ra: x30
STACK CFI 9948 .cfa: sp 96 +
STACK CFI 9954 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 995c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 9964 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 9998 x23: .cfa -16 + ^
STACK CFI 9aec x23: x23
STACK CFI 9af0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9af8 .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 9b18 x23: x23
STACK CFI 9b44 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 9b4c .cfa: sp 96 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 9b50 x23: .cfa -16 + ^
STACK CFI INIT 9b54 50 .cfa: sp 0 + .ra: x30
STACK CFI 9b68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 9b74 x19: .cfa -16 + ^
STACK CFI INIT 9ba4 94 .cfa: sp 0 + .ra: x30
STACK CFI 9bac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 9bb4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 9bc0 x21: .cfa -16 + ^
STACK CFI 9c08 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 9c10 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
