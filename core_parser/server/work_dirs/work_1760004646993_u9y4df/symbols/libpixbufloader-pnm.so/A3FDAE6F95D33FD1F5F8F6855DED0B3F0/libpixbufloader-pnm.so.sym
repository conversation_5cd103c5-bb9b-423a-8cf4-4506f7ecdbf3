MODULE Linux arm64 A3FDAE6F95D33FD1F5F8F6855DED0B3F0 libpixbufloader-pnm.so
INFO CODE_ID 6FAEFDA3D395D13FF5F8F6855DED0B3F0A8DBFE6
PUBLIC 2354 0 fill_vtable
PUBLIC 23a0 0 fill_info
STACK CFI INIT d00 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT d30 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT d70 48 .cfa: sp 0 + .ra: x30
STACK CFI d74 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI d7c x19: .cfa -16 + ^
STACK CFI db4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT dc0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT dd0 e0 .cfa: sp 0 + .ra: x30
STACK CFI e80 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI ea4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT eb0 234 .cfa: sp 0 + .ra: x30
STACK CFI eb8 .cfa: sp 240 +
STACK CFI ec4 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI edc x25: .cfa -16 + ^
STACK CFI ee8 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI efc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI f18 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI ff0 x19: x19 x20: x20
STACK CFI ff4 x21: x21 x22: x22
STACK CFI ff8 x23: x23 x24: x24
STACK CFI ffc x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1000 x21: x21 x22: x22
STACK CFI 1004 x23: x23 x24: x24
STACK CFI 1030 .cfa: sp 0 + .ra: .ra x25: x25 x29: x29
STACK CFI 1038 .cfa: sp 240 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 1048 x19: x19 x20: x20
STACK CFI 1050 x21: x21 x22: x22
STACK CFI 1054 x23: x23 x24: x24
STACK CFI 1058 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 10a0 x19: x19 x20: x20
STACK CFI 10a4 x21: x21 x22: x22
STACK CFI 10a8 x23: x23 x24: x24
STACK CFI 10d8 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 10dc x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 10e0 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI INIT 10e4 2f4 .cfa: sp 0 + .ra: x30
STACK CFI 10ec .cfa: sp 48 +
STACK CFI 10f8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1100 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 11e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 11f0 .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 13e0 6b0 .cfa: sp 0 + .ra: x30
STACK CFI 13e8 .cfa: sp 80 +
STACK CFI 13f4 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 13fc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1420 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 144c x23: .cfa -16 + ^
STACK CFI 14e8 x21: x21 x22: x22
STACK CFI 14ec x23: x23
STACK CFI 1514 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 151c .cfa: sp 80 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x29: .cfa -64 + ^
STACK CFI 1520 x23: .cfa -16 + ^
STACK CFI 1618 x21: x21 x22: x22
STACK CFI 161c x23: x23
STACK CFI 1620 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1638 x23: x23
STACK CFI 163c x21: x21 x22: x22
STACK CFI 1678 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 16c8 x21: x21 x22: x22
STACK CFI 16d0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1714 x23: .cfa -16 + ^
STACK CFI 1804 x21: x21 x22: x22
STACK CFI 1808 x23: x23
STACK CFI 180c x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1810 x23: x23
STACK CFI 1818 x21: x21 x22: x22
STACK CFI 181c x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1838 x23: .cfa -16 + ^
STACK CFI 1a30 x21: x21 x22: x22 x23: x23
STACK CFI 1a58 x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1a84 x21: x21 x22: x22 x23: x23
STACK CFI 1a88 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1a8c x23: .cfa -16 + ^
STACK CFI INIT 1a90 32c .cfa: sp 0 + .ra: x30
STACK CFI 1a98 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1ab4 .cfa: sp 4304 + x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^
STACK CFI 1c74 .cfa: sp 80 +
STACK CFI 1c88 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 1c90 .cfa: sp 4304 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x26: .cfa -8 + ^ x29: .cfa -80 + ^
STACK CFI INIT 1dc0 3a8 .cfa: sp 0 + .ra: x30
STACK CFI 1dc8 .cfa: sp 128 +
STACK CFI 1dd4 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1df0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 1dfc x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1e08 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1e14 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1e1c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1f1c x19: x19 x20: x20
STACK CFI 1f24 x21: x21 x22: x22
STACK CFI 1f28 x23: x23 x24: x24
STACK CFI 1f2c x25: x25 x26: x26
STACK CFI 1f30 x27: x27 x28: x28
STACK CFI 1f54 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1f5c .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 2074 x19: x19 x20: x20
STACK CFI 207c x21: x21 x22: x22
STACK CFI 2080 x23: x23 x24: x24
STACK CFI 2084 x25: x25 x26: x26
STACK CFI 2088 x27: x27 x28: x28
STACK CFI 208c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 20c8 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 20f0 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 2114 x21: x21 x22: x22
STACK CFI 211c x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2150 x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 2154 x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 2158 x21: .cfa -64 + ^ x22: .cfa -56 + ^
STACK CFI 215c x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 2160 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 2164 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI INIT 2170 134 .cfa: sp 0 + .ra: x30
STACK CFI 2178 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 2188 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 21e4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 21ec .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x24: .cfa -8 + ^ x29: .cfa -64 + ^
STACK CFI INIT 22a4 b0 .cfa: sp 0 + .ra: x30
STACK CFI 22ac .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 22b4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 22e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 22e8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 22f0 x21: .cfa -16 + ^
STACK CFI 2328 x21: x21
STACK CFI INIT 2354 44 .cfa: sp 0 + .ra: x30
STACK CFI 235c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2370 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 23a0 5c .cfa: sp 0 + .ra: x30
STACK CFI 23a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 23bc .cfa: sp 0 + .ra: .ra x29: x29
