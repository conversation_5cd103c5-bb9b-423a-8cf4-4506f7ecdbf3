MODULE Linux arm64 1540CA39C6031C7757C3449AB92142280 libprintbackend-lpr.so
INFO CODE_ID 39CA401503C6771C57C3449AB9214228FDC5028D
PUBLIC 22f0 0 pb_module_init
PUBLIC 23b4 0 pb_module_exit
PUBLIC 23d0 0 gtk_print_backend_lpr_get_type
PUBLIC 23f0 0 gtk_print_backend_lpr_new
PUBLIC 2410 0 pb_module_create
STACK CFI INIT 1990 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 19c0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a00 48 .cfa: sp 0 + .ra: x30
STACK CFI 1a04 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a0c x19: .cfa -16 + ^
STACK CFI 1a44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1a50 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1a60 5c .cfa: sp 0 + .ra: x30
STACK CFI 1a68 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1a70 x19: .cfa -16 + ^
STACK CFI 1ab4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1ac0 90 .cfa: sp 0 + .ra: x30
STACK CFI 1ac8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1ad8 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1b48 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1b50 6c .cfa: sp 0 + .ra: x30
STACK CFI 1b58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1b64 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1b74 v8: .cfa -16 + ^
STACK CFI 1bb4 .cfa: sp 0 + .ra: .ra v8: v8 x19: x19 x20: x20 x29: x29
STACK CFI INIT 1bc0 18c .cfa: sp 0 + .ra: x30
STACK CFI 1bc8 .cfa: sp 112 +
STACK CFI 1bdc .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1be8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1bf0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1d40 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d48 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1d50 ac .cfa: sp 0 + .ra: x30
STACK CFI 1d58 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1d64 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1de0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1dec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 1df4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 1e00 e4 .cfa: sp 0 + .ra: x30
STACK CFI 1e08 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1e14 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1ec8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1ed0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1ee4 e4 .cfa: sp 0 + .ra: x30
STACK CFI 1eec .cfa: sp 96 +
STACK CFI 1ef8 .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1f14 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1f20 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1f2c x23: .cfa -16 + ^
STACK CFI 1f6c x19: x19 x20: x20
STACK CFI 1f74 x21: x21 x22: x22
STACK CFI 1f78 x23: x23
STACK CFI 1f7c x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^
STACK CFI 1f80 x19: x19 x20: x20
STACK CFI 1f84 x21: x21 x22: x22
STACK CFI 1f88 x23: x23
STACK CFI 1fb0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1fb8 .cfa: sp 96 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1fbc x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1fc0 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1fc4 x23: .cfa -16 + ^
STACK CFI INIT 1fd0 7c .cfa: sp 0 + .ra: x30
STACK CFI 1fd8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1fe0 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 2044 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 2050 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 2058 .cfa: sp 144 +
STACK CFI 2068 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 2074 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 2080 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 208c x25: .cfa -16 + ^
STACK CFI 2164 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 216c .cfa: sp 144 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI INIT 2200 ec .cfa: sp 0 + .ra: x30
STACK CFI 2208 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 221c .cfa: sp 8272 + x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 228c .cfa: sp 48 +
STACK CFI 2298 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 22a0 .cfa: sp 8272 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 22f0 c4 .cfa: sp 0 + .ra: x30
STACK CFI 22f8 .cfa: sp 112 +
STACK CFI 2308 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 2318 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 23a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 23b0 .cfa: sp 112 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 23b4 18 .cfa: sp 0 + .ra: x30
STACK CFI 23bc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 23c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 23d0 20 .cfa: sp 0 + .ra: x30
STACK CFI 23d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 23e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 23f0 20 .cfa: sp 0 + .ra: x30
STACK CFI 23f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2404 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 2410 18 .cfa: sp 0 + .ra: x30
STACK CFI 2418 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 2420 .cfa: sp 0 + .ra: .ra x29: x29
