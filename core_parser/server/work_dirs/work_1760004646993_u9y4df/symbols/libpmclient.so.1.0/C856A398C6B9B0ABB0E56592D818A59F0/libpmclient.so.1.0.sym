MODULE Linux arm64 C856A398C6B9B0ABB0E56592D818A59F0 libpmclient.so.1
INFO CODE_ID 98A356C8B9C6ABB0B0E56592D818A59F
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 4750 24 0 init_have_lse_atomics
4750 4 45 0
4754 4 46 0
4758 4 45 0
475c 4 46 0
4760 4 47 0
4764 4 47 0
4768 4 48 0
476c 4 47 0
4770 4 48 0
PUBLIC 3e68 0 _init
PUBLIC 4240 0 _GLOBAL__sub_I_client_manager.cpp
PUBLIC 43f0 0 _GLOBAL__sub_I_lipm.cpp
PUBLIC 45a0 0 _GLOBAL__sub_I_pm_client.cc
PUBLIC 4774 0 call_weak_fn
PUBLIC 4790 0 deregister_tm_clones
PUBLIC 47c0 0 register_tm_clones
PUBLIC 4800 0 __do_global_dtors_aux
PUBLIC 4850 0 frame_dummy
PUBLIC 4860 0 liware::lipm::ClientManager::ClientManager()
PUBLIC 4fa0 0 liware::lipm::ClientManager::GetInstance()
PUBLIC 5030 0 liware::lipm::ClientManager::GetName[abi:cxx11]()
PUBLIC 5100 0 liware::lipm::ClientManager::GetPid()
PUBLIC 5110 0 liware::lipm::ClientManager::SendMessage(int)
PUBLIC 5a60 0 liware::lipm::ClientManager::Release()
PUBLIC 5a70 0 liware::lipm::ClientManager::Acquire()
PUBLIC 5a80 0 liware::lipm::ClientManager::Reboot()
PUBLIC 5a90 0 lifmt::v7::system_error::~system_error()
PUBLIC 5ab0 0 lifmt::v7::system_error::~system_error()
PUBLIC 5af0 0 lifmt::v7::format_error::~format_error()
PUBLIC 5b10 0 lifmt::v7::format_error::~format_error()
PUBLIC 5b50 0 liware::lipm::ClientManager::~ClientManager()
PUBLIC 5b80 0 std::_Function_handler<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&), liware::lipm::Init(std::function<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)>, bool)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_manager(std::_Any_data&, std::_Function_handler<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&), liware::lipm::Init(std::function<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)>, bool)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}> const&, std::_Manager_operation)
PUBLIC 5c90 0 std::_Function_handler<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&), liware::lipm::Init(std::function<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)>, bool)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_invoke(std::_Any_data const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 5e00 0 liware::lipm::Init(std::function<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)>, bool)
PUBLIC 6090 0 liware::lipm::Acquire()
PUBLIC 60b0 0 liware::lipm::Release()
PUBLIC 60d0 0 liware::lipm::Reboot()
PUBLIC 60f0 0 std::_Function_handler<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&), lipm_init::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_manager(std::_Any_data&, std::_Function_handler<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&), lipm_init::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}> const&, std::_Manager_operation)
PUBLIC 6130 0 std::_Function_handler<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&), lipm_init::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_invoke(std::_Any_data const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 62f0 0 lipm_init
PUBLIC 63d0 0 lipm_acquire
PUBLIC 63e0 0 lipm_release
PUBLIC 63f0 0 lipm_reboot
PUBLIC 6400 0 liware::libs::pmclient::PmClient::send_pmclient_state(liware::libs::pmclient::PmEvent, liware::libs::pmclient::PmEventState)
PUBLIC 6450 0 std::_Function_handler<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&), liware::libs::pmclient::PmClient::register_pmclient(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, liware::libs::wakelocks::WakeLockPri, std::function<bool (liware::libs::pmclient::PmEvent)>)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_manager(std::_Any_data&, std::_Function_handler<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&), liware::libs::pmclient::PmClient::register_pmclient(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, liware::libs::wakelocks::WakeLockPri, std::function<bool (liware::libs::pmclient::PmEvent)>)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}> const&, std::_Manager_operation)
PUBLIC 6560 0 liware::libs::pmclient::PmClient::register_pmclient(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, liware::libs::wakelocks::WakeLockPri, std::function<bool (liware::libs::pmclient::PmEvent)>)
PUBLIC 67a0 0 std::_Function_handler<void (std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&), liware::libs::pmclient::PmClient::register_pmclient(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, liware::libs::wakelocks::WakeLockPri, std::function<bool (liware::libs::pmclient::PmEvent)>)::{lambda(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)#1}>::_M_invoke(std::_Any_data const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 6990 0 liware::libs::pmclient::PmClient::PmClient()
PUBLIC 6ab0 0 liware::libs::pmclient::PmClient::~PmClient()
PUBLIC 6af0 0 liware::libs::pmclient::PmClient::~PmClient()
PUBLIC 6b40 0 liware::libs::pmclient::PmClientInterface::send_pmclient_state(liware::libs::pmclient::PmEvent, liware::libs::pmclient::PmEventState)
PUBLIC 6b60 0 liware::libs::pmclient::PmClientInterface::PmClientInterface(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, liware::libs::wakelocks::WakeLockPri, std::function<bool (liware::libs::pmclient::PmEvent)>)
PUBLIC 6d90 0 std::_Sp_counted_ptr_inplace<liware::libs::pmclient::PmClient, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 6da0 0 std::_Sp_counted_ptr_inplace<liware::libs::pmclient::PmClient, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 6db0 0 std::_Sp_counted_ptr_inplace<liware::libs::pmclient::PmClient, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 6e20 0 std::_Sp_counted_ptr_inplace<liware::libs::pmclient::PmClient, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 6e30 0 std::_Sp_counted_ptr_inplace<liware::libs::pmclient::PmClient, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 6ea0 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release_last_use_cold()
PUBLIC 6f20 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 6fc0 0 __aarch64_ldadd4_acq_rel
PUBLIC 6ff0 0 _fini
STACK CFI INIT 4790 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 47c0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 4800 48 .cfa: sp 0 + .ra: x30
STACK CFI 4804 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 480c x19: .cfa -16 + ^
STACK CFI 4844 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 4850 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5a90 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5ab0 38 .cfa: sp 0 + .ra: x30
STACK CFI 5ab4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5ac4 x19: .cfa -16 + ^
STACK CFI 5ae4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5af0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5b10 38 .cfa: sp 0 + .ra: x30
STACK CFI 5b14 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 5b24 x19: .cfa -16 + ^
STACK CFI 5b44 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 5b50 24 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4860 740 .cfa: sp 0 + .ra: x30
STACK CFI 4864 .cfa: sp 1008 +
STACK CFI 4874 .ra: .cfa -1000 + ^ x29: .cfa -1008 + ^
STACK CFI 4880 x19: .cfa -992 + ^ x20: .cfa -984 + ^ x21: .cfa -976 + ^ x22: .cfa -968 + ^
STACK CFI 4890 x23: .cfa -960 + ^ x24: .cfa -952 + ^ x25: .cfa -944 + ^ x26: .cfa -936 + ^
STACK CFI 4c6c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x29: x29
STACK CFI 4c70 .cfa: sp 1008 + .ra: .cfa -1000 + ^ x19: .cfa -992 + ^ x20: .cfa -984 + ^ x21: .cfa -976 + ^ x22: .cfa -968 + ^ x23: .cfa -960 + ^ x24: .cfa -952 + ^ x25: .cfa -944 + ^ x26: .cfa -936 + ^ x29: .cfa -1008 + ^
STACK CFI INIT 4fa0 90 .cfa: sp 0 + .ra: x30
STACK CFI 4fa4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 4fac x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 4fcc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 4fd0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 5014 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5018 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 5030 d0 .cfa: sp 0 + .ra: x30
STACK CFI 5034 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 504c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 5058 x21: .cfa -32 + ^
STACK CFI 50bc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 50c0 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x29: .cfa -64 + ^
STACK CFI INIT 5100 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5110 944 .cfa: sp 0 + .ra: x30
STACK CFI 5114 .cfa: sp 784 +
STACK CFI 5124 .ra: .cfa -776 + ^ x29: .cfa -784 + ^
STACK CFI 512c x19: .cfa -768 + ^ x20: .cfa -760 + ^
STACK CFI 513c x21: .cfa -752 + ^ x22: .cfa -744 + ^
STACK CFI 515c x27: .cfa -704 + ^ x28: .cfa -696 + ^
STACK CFI 5208 x23: .cfa -736 + ^ x24: .cfa -728 + ^
STACK CFI 5220 x25: .cfa -720 + ^ x26: .cfa -712 + ^
STACK CFI 5644 x23: x23 x24: x24
STACK CFI 5648 x25: x25 x26: x26
STACK CFI 568c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x27: x27 x28: x28 x29: x29
STACK CFI 5690 .cfa: sp 784 + .ra: .cfa -776 + ^ x19: .cfa -768 + ^ x20: .cfa -760 + ^ x21: .cfa -752 + ^ x22: .cfa -744 + ^ x27: .cfa -704 + ^ x28: .cfa -696 + ^ x29: .cfa -784 + ^
STACK CFI 56f0 x23: .cfa -736 + ^ x24: .cfa -728 + ^ x25: .cfa -720 + ^ x26: .cfa -712 + ^
STACK CFI 5814 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 58b8 x23: .cfa -736 + ^ x24: .cfa -728 + ^ x25: .cfa -720 + ^ x26: .cfa -712 + ^
STACK CFI 5944 x23: x23 x24: x24
STACK CFI 5948 x25: x25 x26: x26
STACK CFI 594c x23: .cfa -736 + ^ x24: .cfa -728 + ^ x25: .cfa -720 + ^ x26: .cfa -712 + ^
STACK CFI 59c4 x23: x23 x24: x24 x25: x25 x26: x26
STACK CFI 59c8 x23: .cfa -736 + ^ x24: .cfa -728 + ^
STACK CFI 59cc x25: .cfa -720 + ^ x26: .cfa -712 + ^
STACK CFI 59e8 x23: x23 x24: x24
STACK CFI 59ec x25: x25 x26: x26
STACK CFI 5a3c x23: .cfa -736 + ^ x24: .cfa -728 + ^
STACK CFI 5a40 x25: .cfa -720 + ^ x26: .cfa -712 + ^
STACK CFI INIT 5a60 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5a70 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 5a80 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4240 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 4244 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 424c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4280 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 43c8 x21: x21 x22: x22
STACK CFI 43ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 5b80 104 .cfa: sp 0 + .ra: x30
STACK CFI 5b84 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 5b8c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 5bf0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5bf4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5c0c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 5c10 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 5c14 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 5c50 x21: x21 x22: x22
STACK CFI 5c54 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 5c90 164 .cfa: sp 0 + .ra: x30
STACK CFI 5c94 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 5cb8 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 5d74 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5d78 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT 5e00 290 .cfa: sp 0 + .ra: x30
STACK CFI 5e04 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 5e14 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 5e1c x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 5e38 x23: .cfa -144 + ^
STACK CFI 5f6c x23: x23
STACK CFI 5f98 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 5f9c .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x29: .cfa -192 + ^
STACK CFI 5fb4 x23: x23
STACK CFI 5fd8 x23: .cfa -144 + ^
STACK CFI 5fdc x23: x23
STACK CFI 5fe4 x23: .cfa -144 + ^
STACK CFI INIT 6090 14 .cfa: sp 0 + .ra: x30
STACK CFI 6094 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 60a0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 60b0 14 .cfa: sp 0 + .ra: x30
STACK CFI 60b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 60c0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 60d0 14 .cfa: sp 0 + .ra: x30
STACK CFI 60d4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 60e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 43f0 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 43f4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 43fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4430 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4578 x21: x21 x22: x22
STACK CFI 459c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 60f0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6130 1bc .cfa: sp 0 + .ra: x30
STACK CFI 6134 .cfa: sp 192 + .ra: .cfa -184 + ^ x29: .cfa -192 + ^
STACK CFI 6148 x19: .cfa -176 + ^ x20: .cfa -168 + ^
STACK CFI 6154 x21: .cfa -160 + ^ x22: .cfa -152 + ^
STACK CFI 6164 x23: .cfa -144 + ^ x24: .cfa -136 + ^
STACK CFI 616c x25: .cfa -128 + ^
STACK CFI 6284 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6288 .cfa: sp 192 + .ra: .cfa -184 + ^ x19: .cfa -176 + ^ x20: .cfa -168 + ^ x21: .cfa -160 + ^ x22: .cfa -152 + ^ x23: .cfa -144 + ^ x24: .cfa -136 + ^ x25: .cfa -128 + ^ x29: .cfa -192 + ^
STACK CFI INIT 62f0 d4 .cfa: sp 0 + .ra: x30
STACK CFI 62f4 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 6310 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 637c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 6380 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x29: .cfa -80 + ^
STACK CFI INIT 63d0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 63e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 63f0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ab0 3c .cfa: sp 0 + .ra: x30
STACK CFI 6ad0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6ae4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6af0 4c .cfa: sp 0 + .ra: x30
STACK CFI 6af4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6b00 x19: .cfa -16 + ^
STACK CFI 6b38 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6400 4c .cfa: sp 0 + .ra: x30
STACK CFI 6404 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6424 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6428 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 643c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 6440 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 6450 104 .cfa: sp 0 + .ra: x30
STACK CFI 6454 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 645c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 64c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 64c4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 64dc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 64e0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 64e4 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 6520 x21: x21 x22: x22
STACK CFI 6524 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI INIT 6560 23c .cfa: sp 0 + .ra: x30
STACK CFI 6564 .cfa: sp 176 + .ra: .cfa -168 + ^ x29: .cfa -176 + ^
STACK CFI 6578 x19: .cfa -160 + ^ x20: .cfa -152 + ^
STACK CFI 6584 x21: .cfa -144 + ^ x22: .cfa -136 + ^
STACK CFI 66e8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 66ec .cfa: sp 176 + .ra: .cfa -168 + ^ x19: .cfa -160 + ^ x20: .cfa -152 + ^ x21: .cfa -144 + ^ x22: .cfa -136 + ^ x29: .cfa -176 + ^
STACK CFI INIT 67a0 1e8 .cfa: sp 0 + .ra: x30
STACK CFI 67a4 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 67c8 x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 689c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 68a0 .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6990 114 .cfa: sp 0 + .ra: x30
STACK CFI 6994 .cfa: sp 80 + .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 69a4 x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 69b4 x19: .cfa -64 + ^ x20: .cfa -56 + ^
STACK CFI 6a5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 6a60 .cfa: sp 80 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x29: .cfa -80 + ^
STACK CFI INIT 45a0 1b0 .cfa: sp 0 + .ra: x30
STACK CFI 45a4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 45ac x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 45e0 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4728 x21: x21 x22: x22
STACK CFI 474c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 6d90 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6da0 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6db0 70 .cfa: sp 0 + .ra: x30
STACK CFI 6db4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6dc4 x19: .cfa -16 + ^
STACK CFI 6e08 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6e0c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6e1c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6e20 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6e30 64 .cfa: sp 0 + .ra: x30
STACK CFI 6e68 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6e7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 6b40 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6ea0 78 .cfa: sp 0 + .ra: x30
STACK CFI 6ea4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6eb4 x19: .cfa -16 + ^
STACK CFI 6ee8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6eec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6efc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6f08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 6f20 9c .cfa: sp 0 + .ra: x30
STACK CFI 6f24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f30 x19: .cfa -16 + ^
STACK CFI 6f70 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6f74 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6fa0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 6fac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 6fb8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6b60 224 .cfa: sp 0 + .ra: x30
STACK CFI 6b64 .cfa: sp 128 + .ra: .cfa -120 + ^ x29: .cfa -128 + ^
STACK CFI 6b6c x19: .cfa -112 + ^ x20: .cfa -104 + ^
STACK CFI 6b7c x21: .cfa -96 + ^ x22: .cfa -88 + ^
STACK CFI 6b84 x23: .cfa -80 + ^ x24: .cfa -72 + ^
STACK CFI 6b90 x25: .cfa -64 + ^
STACK CFI 6ca8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x29: x29
STACK CFI 6cac .cfa: sp 128 + .ra: .cfa -120 + ^ x19: .cfa -112 + ^ x20: .cfa -104 + ^ x21: .cfa -96 + ^ x22: .cfa -88 + ^ x23: .cfa -80 + ^ x24: .cfa -72 + ^ x25: .cfa -64 + ^ x29: .cfa -128 + ^
STACK CFI INIT 6fc0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 4750 24 .cfa: sp 0 + .ra: x30
STACK CFI 4754 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 476c .cfa: sp 0 + .ra: .ra x29: x29
