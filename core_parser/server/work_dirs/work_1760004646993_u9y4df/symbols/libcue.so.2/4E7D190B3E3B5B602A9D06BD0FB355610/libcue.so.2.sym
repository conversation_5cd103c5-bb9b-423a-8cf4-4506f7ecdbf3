MODULE Linux arm64 4E7D190B3E3B5B602A9D06BD0FB355610 libcue.so.2
INFO CODE_ID 0B197D4E3B3E605B2A9D06BD0FB35561E4C88486
PUBLIC 18c4 0 cd_get_mode
PUBLIC 18e0 0 cd_get_cdtextfile
PUBLIC 1900 0 cd_get_cdtext
PUBLIC 1930 0 cd_get_rem
PUBLIC 1960 0 cd_get_ntrack
PUBLIC 1980 0 cd_get_track
PUBLIC 19d0 0 track_get_filename
PUBLIC 19f0 0 track_get_start
PUBLIC 1a10 0 track_get_length
PUBLIC 1a30 0 track_get_mode
PUBLIC 1a50 0 track_get_sub_mode
PUBLIC 1a70 0 track_is_set_flag
PUBLIC 1a90 0 track_get_zero_pre
PUBLIC 1ab0 0 track_get_zero_post
PUBLIC 1ad0 0 track_get_isrc
PUBLIC 1af0 0 track_get_cdtext
PUBLIC 1b20 0 track_get_rem
PUBLIC 1b50 0 cd_delete
PUBLIC 1cd0 0 track_get_index
PUBLIC 49b0 0 cdtext_get
PUBLIC 4a00 0 rem_get
PUBLIC 4a50 0 cue_parse_file
PUBLIC 4ad0 0 cue_parse_string
STACK CFI INIT 10d0 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1100 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 1140 48 .cfa: sp 0 + .ra: x30
STACK CFI 1144 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 114c x19: .cfa -16 + ^
STACK CFI 1184 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1190 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 11a0 110 .cfa: sp 0 + .ra: x30
STACK CFI 11a8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 129c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 12a4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 12b0 78 .cfa: sp 0 + .ra: x30
STACK CFI 12bc .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 12c4 x19: .cfa -16 + ^
STACK CFI 1300 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1308 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 131c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1330 88 .cfa: sp 0 + .ra: x30
STACK CFI 1338 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1344 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1350 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 13b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI INIT 13c0 18 .cfa: sp 0 + .ra: x30
STACK CFI 13c8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 13d0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 13e0 c0 .cfa: sp 0 + .ra: x30
STACK CFI 13e8 .cfa: sp 320 +
STACK CFI 13f8 .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1408 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1470 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1478 .cfa: sp 320 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 14a0 d8 .cfa: sp 0 + .ra: x30
STACK CFI 14a8 .cfa: sp 144 +
STACK CFI 14bc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 14cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1548 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1550 .cfa: sp 144 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1580 11c .cfa: sp 0 + .ra: x30
STACK CFI 1588 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1590 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 159c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 164c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1654 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 16a0 34 .cfa: sp 0 + .ra: x30
STACK CFI 16b0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 16d4 d8 .cfa: sp 0 + .ra: x30
STACK CFI 16dc .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 16e4 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1710 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 1718 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1720 x21: .cfa -16 + ^
STACK CFI 1758 x21: x21
STACK CFI 1764 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 176c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 1794 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 179c .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x29: .cfa -48 + ^
STACK CFI 17a0 x21: .cfa -16 + ^
STACK CFI INIT 17b0 a4 .cfa: sp 0 + .ra: x30
STACK CFI 17b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 17c0 x19: .cfa -16 + ^
STACK CFI 183c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 1844 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1854 70 .cfa: sp 0 + .ra: x30
STACK CFI 185c .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1864 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 18b0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 18b8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 18c4 1c .cfa: sp 0 + .ra: x30
STACK CFI 18cc .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 18d4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 18e0 1c .cfa: sp 0 + .ra: x30
STACK CFI 18e8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 18f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1900 30 .cfa: sp 0 + .ra: x30
STACK CFI 1908 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1918 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1920 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1924 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1930 30 .cfa: sp 0 + .ra: x30
STACK CFI 1938 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1948 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1950 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1954 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1960 1c .cfa: sp 0 + .ra: x30
STACK CFI 1968 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1970 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1980 50 .cfa: sp 0 + .ra: x30
STACK CFI 1988 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 19b8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 19c0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 19c4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 19d0 1c .cfa: sp 0 + .ra: x30
STACK CFI 19d8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 19e4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 19f0 1c .cfa: sp 0 + .ra: x30
STACK CFI 19f8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a04 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a10 1c .cfa: sp 0 + .ra: x30
STACK CFI 1a18 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a24 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a30 1c .cfa: sp 0 + .ra: x30
STACK CFI 1a38 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a50 1c .cfa: sp 0 + .ra: x30
STACK CFI 1a58 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a60 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a70 20 .cfa: sp 0 + .ra: x30
STACK CFI 1a78 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1a84 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1a90 1c .cfa: sp 0 + .ra: x30
STACK CFI 1a98 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1aa4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ab0 1c .cfa: sp 0 + .ra: x30
STACK CFI 1ab8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ac4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1ad0 1c .cfa: sp 0 + .ra: x30
STACK CFI 1ad8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1ae4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1af0 30 .cfa: sp 0 + .ra: x30
STACK CFI 1af8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b08 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1b10 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b14 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b20 30 .cfa: sp 0 + .ra: x30
STACK CFI 1b28 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b38 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1b40 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1b44 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 1b50 180 .cfa: sp 0 + .ra: x30
STACK CFI 1b60 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 1b68 x23: .cfa -16 + ^ x24: .cfa -8 + ^
STACK CFI 1b74 x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 1b94 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 1c48 x21: x21 x22: x22
STACK CFI 1cc4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x23: x23 x24: x24 x29: x29
STACK CFI INIT 1cd0 30 .cfa: sp 0 + .ra: x30
STACK CFI 1cd8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 1cf0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 1cf8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 1d00 2cb0 .cfa: sp 0 + .ra: x30
STACK CFI 1d08 .cfa: sp 96 + .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 1d24 .cfa: sp 2048 + x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 2320 .cfa: sp 96 +
STACK CFI 233c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x25: x25 x26: x26 x27: x27 x28: x28 x29: x29
STACK CFI 2344 .cfa: sp 2048 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI INIT 49b0 50 .cfa: sp 0 + .ra: x30
STACK CFI 49b8 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 49e8 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 49f0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 49f4 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a00 4c .cfa: sp 0 + .ra: x30
STACK CFI 4a08 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a34 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 4a3c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 4a40 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 4a50 80 .cfa: sp 0 + .ra: x30
STACK CFI 4a58 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4a60 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 4a68 x21: .cfa -16 + ^
STACK CFI 4ac0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 4ac8 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 4ad0 128 .cfa: sp 0 + .ra: x30
STACK CFI 4ad8 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 4ae4 x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 4bac .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 4bb4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
