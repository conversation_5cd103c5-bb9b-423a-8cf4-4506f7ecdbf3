MODULE Linux arm64 2D1832982B48144BB71E4C7E166725990 libnvstream_core_block.so
INFO CODE_ID 9832182D482B4B14B71E4C7E16672599
FILE 0 /home/<USER>/buildroot/output/build/host-gcc-final-13.2.0/build/aarch64-buildroot-linux-gnu/libgcc/../../../libgcc/config/aarch64/lse-init.c
FUNC 6d20 24 0 init_have_lse_atomics
6d20 4 45 0
6d24 4 46 0
6d28 4 45 0
6d2c 4 46 0
6d30 4 47 0
6d34 4 47 0
6d38 4 48 0
6d3c 4 47 0
6d40 4 48 0
PUBLIC 67d8 0 _init
PUBLIC 6d44 0 call_weak_fn
PUBLIC 6d60 0 deregister_tm_clones
PUBLIC 6d90 0 register_tm_clones
PUBLIC 6dd0 0 __do_global_dtors_aux
PUBLIC 6e20 0 frame_dummy
PUBLIC 6e30 0 linvs::block::C2cDst::C2cDst(linvs::sync::SyncModule const&, linvs::buf::BufModule const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, linvs::block::IBlock const&)
PUBLIC 6f40 0 linvs::block::C2cDst::~C2cDst()
PUBLIC 6f60 0 linvs::block::C2cDst::~C2cDst()
PUBLIC 6fa0 0 linvs::block::C2cSrc::C2cSrc(linvs::sync::SyncModule const&, linvs::buf::BufModule const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&, linvs::block::IBlock const&)
PUBLIC 70b0 0 linvs::block::C2cSrc::~C2cSrc()
PUBLIC 70d0 0 linvs::block::C2cSrc::~C2cSrc()
PUBLIC 7110 0 linvs::block::FifoQueue::FifoQueue()
PUBLIC 71a0 0 linvs::block::StreamQueue::~StreamQueue()
PUBLIC 71c0 0 linvs::block::StreamQueue::~StreamQueue()
PUBLIC 7200 0 linvs::block::FifoQueue::~FifoQueue()
PUBLIC 7220 0 linvs::block::FifoQueue::~FifoQueue()
PUBLIC 7260 0 linvs::block::IBlock::operator*()
PUBLIC 7270 0 linvs::block::IBlock::operator*() const
PUBLIC 7280 0 linvs::block::IBlock::operator bool()
PUBLIC 7290 0 linvs::block::IBlock::Reset()
PUBLIC 7300 0 linvs::block::IBlock::~IBlock()
PUBLIC 7330 0 linvs::block::IBlock::~IBlock()
PUBLIC 7360 0 linvs::block::IBlock::ConnectUp(linvs::block::IBlock const&)
PUBLIC 73c0 0 linvs::block::IBlock::ConnectDown(linvs::block::IBlock const&)
PUBLIC 7410 0 linvs::block::IBlock::Disconnect()
PUBLIC 7460 0 linvs::block::IBlock::QueryEvent(NvSciStreamEventType&, long)
PUBLIC 74c0 0 linvs::block::IBlock::GetSetupStatus(NvSciError&)
PUBLIC 7510 0 linvs::block::IBlock::SetSetupStatus(NvSciStreamSetup, bool)
PUBLIC 7560 0 linvs::block::IBlock::QueryConsumerCount(unsigned int&)
PUBLIC 75b0 0 linvs::block::IBlock::QueryUserInfo(NvSciStreamBlockType, unsigned int, unsigned int, unsigned int&, void*)
PUBLIC 7600 0 linvs::block::IBlock::SetElementAttr(unsigned int, linvs::buf::BufAttrList const&)
PUBLIC 7680 0 linvs::block::IBlock::GetElementCount(NvSciStreamBlockType, unsigned int&)
PUBLIC 76d0 0 linvs::block::IBlock::GetElementAttr(NvSciStreamBlockType, unsigned int, unsigned int&, linvs::buf::BufAttrList&)
PUBLIC 7760 0 linvs::block::IBlock::SetElementWaiterAttr(unsigned int, linvs::sync::SyncAttrList const&)
PUBLIC 77e0 0 linvs::block::IBlock::GetPacketNewHandle(unsigned long&)
PUBLIC 7830 0 linvs::block::IBlock::SetPacketStatus(unsigned long, unsigned long, NvSciError)
PUBLIC 7880 0 linvs::block::IBlock::GetPacketBuf(unsigned long, unsigned int, linvs::buf::BufObj&)
PUBLIC 7900 0 linvs::block::IBlock::GetPacketOldCookie(unsigned long&)
PUBLIC 7950 0 linvs::block::IBlock::GetElementWaiterAttr(unsigned int, linvs::sync::SyncAttrList&)
PUBLIC 79c0 0 linvs::block::IBlock::SetElementSignalObj(unsigned int, linvs::sync::SyncObj const&)
PUBLIC 7a40 0 linvs::block::IBlock::GetElementSignalObj(unsigned int, unsigned int, linvs::sync::SyncObj&)
PUBLIC 7ac0 0 linvs::block::IBlock::GetProducerPacket(unsigned long&)
PUBLIC 7b10 0 linvs::block::IBlock::GetPacketFence(unsigned long, unsigned int, unsigned int, linvs::sync::SyncFence&)
PUBLIC 7ba0 0 linvs::block::IBlock::SetPacketFence(unsigned long, unsigned int, linvs::sync::SyncFence const&)
PUBLIC 7c20 0 linvs::block::IBlock::PresentProducerPacket(unsigned long)
PUBLIC 7c70 0 linvs::block::IBlock::SetElementUsage(unsigned int, bool)
PUBLIC 7cc0 0 linvs::block::IBlock::AcquireConsumerPacket(unsigned long&)
PUBLIC 7d10 0 linvs::block::IBlock::ReleaseConsumerPacket(unsigned long)
PUBLIC 7d60 0 linvs::block::IBlock::CreatePacket(unsigned long, unsigned long&)
PUBLIC 7db0 0 linvs::block::IBlock::InsertBuffer(unsigned long, unsigned int, linvs::buf::BufObj&)
PUBLIC 7e30 0 linvs::block::IBlock::CompletePacket(unsigned long)
PUBLIC 7e80 0 linvs::block::IBlock::GetAcceptPacketStatus(unsigned long, bool&)
PUBLIC 7ed0 0 linvs::block::IBlock::GetPacketStatusValue(unsigned long, NvSciStreamBlockType, unsigned int, NvSciError&)
PUBLIC 7f20 0 linvs::block::Ipc::~Ipc()
PUBLIC 7f60 0 linvs::block::Ipc::~Ipc()
PUBLIC 7f90 0 linvs::block::Ipc::Ipc(std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 7ff0 0 linvs::block::Ipc::operator bool()
PUBLIC 8000 0 linvs::block::IpcDst::IpcDst(linvs::sync::SyncModule const&, linvs::buf::BufModule const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 80f0 0 linvs::block::IpcDst::~IpcDst()
PUBLIC 8110 0 linvs::block::IpcDst::~IpcDst()
PUBLIC 8150 0 linvs::block::IpcSrc::IpcSrc(linvs::sync::SyncModule const&, linvs::buf::BufModule const&, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > const&)
PUBLIC 8240 0 linvs::block::IpcSrc::~IpcSrc()
PUBLIC 8260 0 linvs::block::IpcSrc::~IpcSrc()
PUBLIC 82a0 0 linvs::block::BlockLimit::BlockLimit(int)
PUBLIC 8330 0 linvs::block::BlockLimit::~BlockLimit()
PUBLIC 8350 0 linvs::block::BlockLimit::~BlockLimit()
PUBLIC 8390 0 linvs::block::MailboxQueue::MailboxQueue()
PUBLIC 8420 0 linvs::block::MailboxQueue::~MailboxQueue()
PUBLIC 8440 0 linvs::block::MailboxQueue::~MailboxQueue()
PUBLIC 8480 0 linvs::block::BlockMulticast::BlockMulticast(unsigned int)
PUBLIC 8510 0 linvs::block::BlockMulticast::~BlockMulticast()
PUBLIC 8530 0 linvs::block::BlockMulticast::~BlockMulticast()
PUBLIC 8570 0 linvs::block::PacketPool::~PacketPool()
PUBLIC 8590 0 linvs::block::PacketPool::~PacketPool()
PUBLIC 85c0 0 linvs::block::PacketPool::Init(unsigned int)
PUBLIC 8620 0 linvs::block::PacketPool::PacketPool(unsigned int)
PUBLIC 8660 0 linvs::block::PacketPool::CreateProducer(bool)
PUBLIC 87e0 0 std::_Sp_counted_ptr_inplace<linvs::block::IBlock, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 87f0 0 std::_Sp_counted_ptr_inplace<linvs::block::IBlock, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_dispose()
PUBLIC 8810 0 std::_Sp_counted_ptr_inplace<linvs::block::IBlock, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::~_Sp_counted_ptr_inplace()
PUBLIC 8820 0 std::_Sp_counted_ptr_inplace<linvs::block::IBlock, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_destroy()
PUBLIC 8830 0 std::_Sp_counted_ptr_inplace<linvs::block::IBlock, std::allocator<void>, (__gnu_cxx::_Lock_policy)2>::_M_get_deleter(std::type_info const&)
PUBLIC 88a0 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release_last_use_cold()
PUBLIC 8920 0 std::_Sp_counted_base<(__gnu_cxx::_Lock_policy)2>::_M_release()
PUBLIC 89c0 0 linvs::block::PresentSync::PresentSync(linvs::sync::SyncModule const&)
PUBLIC 8a60 0 linvs::block::PresentSync::~PresentSync()
PUBLIC 8a80 0 linvs::block::PresentSync::~PresentSync()
PUBLIC 8ac0 0 linvs::block::ReturnSync::ReturnSync(linvs::sync::SyncModule const&)
PUBLIC 8b60 0 linvs::block::ReturnSync::~ReturnSync()
PUBLIC 8b80 0 linvs::block::ReturnSync::~ReturnSync()
PUBLIC 8bc0 0 linvs::block::StreamQueue::CreateConsumer(bool)
PUBLIC 8d40 0 __aarch64_ldadd4_acq_rel
PUBLIC 8d70 0 _fini
STACK CFI INIT 6d60 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d90 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 6dd0 48 .cfa: sp 0 + .ra: x30
STACK CFI 6dd4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6ddc x19: .cfa -16 + ^
STACK CFI 6e14 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6e20 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f40 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6f60 38 .cfa: sp 0 + .ra: x30
STACK CFI 6f64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 6f74 x19: .cfa -16 + ^
STACK CFI 6f94 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6e30 10c .cfa: sp 0 + .ra: x30
STACK CFI 6e34 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6e3c x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6e48 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6e58 x23: .cfa -16 + ^
STACK CFI 6ee0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6ee4 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 6f20 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 6f24 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 70b0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 70d0 38 .cfa: sp 0 + .ra: x30
STACK CFI 70d4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 70e4 x19: .cfa -16 + ^
STACK CFI 7104 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 6fa0 10c .cfa: sp 0 + .ra: x30
STACK CFI 6fa4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 6fac x19: .cfa -48 + ^ x20: .cfa -40 + ^
STACK CFI 6fb8 x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 6fc8 x23: .cfa -16 + ^
STACK CFI 7050 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7054 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 7090 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 7094 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 71a0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 71c0 38 .cfa: sp 0 + .ra: x30
STACK CFI 71c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 71d4 x19: .cfa -16 + ^
STACK CFI 71f4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7200 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7220 38 .cfa: sp 0 + .ra: x30
STACK CFI 7224 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7234 x19: .cfa -16 + ^
STACK CFI 7254 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7110 88 .cfa: sp 0 + .ra: x30
STACK CFI 7114 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7124 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7144 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7148 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 7158 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7174 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7260 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7270 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7280 10 .cfa: sp 0 + .ra: x30
STACK CFI INIT 7290 68 .cfa: sp 0 + .ra: x30
STACK CFI 7294 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 729c x19: .cfa -16 + ^
STACK CFI 72b0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 72b4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 72c8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 72cc .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7300 24 .cfa: sp 0 + .ra: x30
STACK CFI 730c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7320 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 7330 28 .cfa: sp 0 + .ra: x30
STACK CFI 7334 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 733c x19: .cfa -16 + ^
STACK CFI 7354 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7360 54 .cfa: sp 0 + .ra: x30
STACK CFI 7368 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7384 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7388 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 73c0 50 .cfa: sp 0 + .ra: x30
STACK CFI 73c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 73e0 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 73e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7410 4c .cfa: sp 0 + .ra: x30
STACK CFI 7414 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 742c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7430 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7460 58 .cfa: sp 0 + .ra: x30
STACK CFI 7464 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7488 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 748c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 74c0 4c .cfa: sp 0 + .ra: x30
STACK CFI 74c4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 74dc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 74e0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7510 4c .cfa: sp 0 + .ra: x30
STACK CFI 7514 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 752c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7530 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7560 4c .cfa: sp 0 + .ra: x30
STACK CFI 7564 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 757c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7580 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 75b0 4c .cfa: sp 0 + .ra: x30
STACK CFI 75b4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 75cc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 75d0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7600 74 .cfa: sp 0 + .ra: x30
STACK CFI 7604 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7614 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7644 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7648 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7680 4c .cfa: sp 0 + .ra: x30
STACK CFI 7684 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 769c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 76a0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 76d0 84 .cfa: sp 0 + .ra: x30
STACK CFI 76d4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 76dc x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 76ec x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7724 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7728 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7760 74 .cfa: sp 0 + .ra: x30
STACK CFI 7764 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7774 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 77a4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 77a8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 77e0 4c .cfa: sp 0 + .ra: x30
STACK CFI 77e4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 77fc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7800 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7830 4c .cfa: sp 0 + .ra: x30
STACK CFI 7834 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 784c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7850 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7880 7c .cfa: sp 0 + .ra: x30
STACK CFI 7884 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 788c x21: .cfa -16 + ^
STACK CFI 7898 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 78cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 78d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7900 4c .cfa: sp 0 + .ra: x30
STACK CFI 7904 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 791c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7920 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7950 70 .cfa: sp 0 + .ra: x30
STACK CFI 7954 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7964 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7990 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7994 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 79c0 74 .cfa: sp 0 + .ra: x30
STACK CFI 79c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 79d4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7a04 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 7a08 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 7a40 7c .cfa: sp 0 + .ra: x30
STACK CFI 7a44 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7a4c x21: .cfa -16 + ^
STACK CFI 7a58 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7a8c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7a90 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7ac0 4c .cfa: sp 0 + .ra: x30
STACK CFI 7ac4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7adc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7ae0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7b10 84 .cfa: sp 0 + .ra: x30
STACK CFI 7b14 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7b1c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 7b2c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7b64 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 7b68 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7ba0 7c .cfa: sp 0 + .ra: x30
STACK CFI 7ba4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7bac x21: .cfa -16 + ^
STACK CFI 7bb8 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7bec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7bf0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7c20 4c .cfa: sp 0 + .ra: x30
STACK CFI 7c24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7c3c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7c40 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7c70 4c .cfa: sp 0 + .ra: x30
STACK CFI 7c74 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7c8c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7c90 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7cc0 4c .cfa: sp 0 + .ra: x30
STACK CFI 7cc4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7cdc .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7ce0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7d10 4c .cfa: sp 0 + .ra: x30
STACK CFI 7d14 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7d2c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7d30 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7d60 4c .cfa: sp 0 + .ra: x30
STACK CFI 7d64 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7d7c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7d80 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7db0 80 .cfa: sp 0 + .ra: x30
STACK CFI 7db4 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 7dbc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 7dc8 x21: .cfa -16 + ^
STACK CFI 7e00 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 7e04 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 7e30 4c .cfa: sp 0 + .ra: x30
STACK CFI 7e34 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7e4c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7e50 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7e80 4c .cfa: sp 0 + .ra: x30
STACK CFI 7e84 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7e9c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7ea0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7ed0 4c .cfa: sp 0 + .ra: x30
STACK CFI 7ed4 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 7eec .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 7ef0 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI INIT 7f20 40 .cfa: sp 0 + .ra: x30
STACK CFI 7f24 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7f34 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7f5c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7f60 28 .cfa: sp 0 + .ra: x30
STACK CFI 7f64 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7f6c x19: .cfa -16 + ^
STACK CFI 7f84 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 7f90 54 .cfa: sp 0 + .ra: x30
STACK CFI 7f94 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 7fa4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 7fe0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI INIT 7ff0 c .cfa: sp 0 + .ra: x30
STACK CFI INIT 80f0 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8110 38 .cfa: sp 0 + .ra: x30
STACK CFI 8114 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8124 x19: .cfa -16 + ^
STACK CFI 8144 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8000 e8 .cfa: sp 0 + .ra: x30
STACK CFI 8004 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 800c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8018 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 8090 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8094 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 80cc .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 80d0 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8240 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8260 38 .cfa: sp 0 + .ra: x30
STACK CFI 8264 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8274 x19: .cfa -16 + ^
STACK CFI 8294 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8150 e8 .cfa: sp 0 + .ra: x30
STACK CFI 8154 .cfa: sp 48 + .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 815c x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 8168 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 81e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 81e4 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI 821c .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 8220 .cfa: sp 48 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 8330 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8350 38 .cfa: sp 0 + .ra: x30
STACK CFI 8354 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8364 x19: .cfa -16 + ^
STACK CFI 8384 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 82a0 88 .cfa: sp 0 + .ra: x30
STACK CFI 82a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 82b4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 82e0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 82e4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 82f4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8310 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8420 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8440 38 .cfa: sp 0 + .ra: x30
STACK CFI 8444 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8454 x19: .cfa -16 + ^
STACK CFI 8474 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8390 88 .cfa: sp 0 + .ra: x30
STACK CFI 8394 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 83a4 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 83c4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 83c8 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 83d8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 83f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8510 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8530 38 .cfa: sp 0 + .ra: x30
STACK CFI 8534 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8544 x19: .cfa -16 + ^
STACK CFI 8564 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8480 88 .cfa: sp 0 + .ra: x30
STACK CFI 8484 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8494 x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 84c0 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 84c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 84d4 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 84f0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 87e0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 87f0 18 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8570 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8590 28 .cfa: sp 0 + .ra: x30
STACK CFI 8594 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 859c x19: .cfa -16 + ^
STACK CFI 85b4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8810 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8820 8 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8830 70 .cfa: sp 0 + .ra: x30
STACK CFI 8834 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8844 x19: .cfa -16 + ^
STACK CFI 8888 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 888c .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 889c .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 85c0 60 .cfa: sp 0 + .ra: x30
STACK CFI 85c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 85cc x19: .cfa -16 + ^
STACK CFI 85f0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 85f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8620 40 .cfa: sp 0 + .ra: x30
STACK CFI 862c .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 8644 .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI 8648 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 864c .cfa: sp 0 + .ra: .ra x29: x29
STACK CFI INIT 88a0 78 .cfa: sp 0 + .ra: x30
STACK CFI 88a4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 88b4 x19: .cfa -16 + ^
STACK CFI 88e8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 88ec .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 88fc .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8908 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8920 9c .cfa: sp 0 + .ra: x30
STACK CFI 8924 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8930 x19: .cfa -16 + ^
STACK CFI 8970 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 8974 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 89a0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI 89ac .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI 89b8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8660 174 .cfa: sp 0 + .ra: x30
STACK CFI 8664 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8670 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 867c x23: .cfa -16 + ^
STACK CFI 8700 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8704 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 87b8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 87bc .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8a60 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8a80 38 .cfa: sp 0 + .ra: x30
STACK CFI 8a84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8a94 x19: .cfa -16 + ^
STACK CFI 8ab4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 89c0 98 .cfa: sp 0 + .ra: x30
STACK CFI 89c4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 89cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8a10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8a14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8a24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8a40 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8b60 14 .cfa: sp 0 + .ra: x30
STACK CFI INIT 8b80 38 .cfa: sp 0 + .ra: x30
STACK CFI 8b84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8b94 x19: .cfa -16 + ^
STACK CFI 8bb4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 8ac0 98 .cfa: sp 0 + .ra: x30
STACK CFI 8ac4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 8acc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 8b10 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8b14 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI 8b24 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 8b40 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 8bc0 174 .cfa: sp 0 + .ra: x30
STACK CFI 8bc4 .cfa: sp 64 + .ra: .cfa -56 + ^ x29: .cfa -64 + ^
STACK CFI 8bd0 x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^
STACK CFI 8bdc x23: .cfa -16 + ^
STACK CFI 8c60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8c64 .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI 8d18 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x29: x29
STACK CFI 8d1c .cfa: sp 64 + .ra: .cfa -56 + ^ x19: .cfa -48 + ^ x20: .cfa -40 + ^ x21: .cfa -32 + ^ x22: .cfa -24 + ^ x23: .cfa -16 + ^ x29: .cfa -64 + ^
STACK CFI INIT 8d40 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 6d20 24 .cfa: sp 0 + .ra: x30
STACK CFI 6d24 .cfa: sp 16 + .ra: .cfa -8 + ^ x29: .cfa -16 + ^
STACK CFI 6d3c .cfa: sp 0 + .ra: .ra x29: x29
