MODULE Linux arm64 C44EAFE9B5BC4585F45E677CE38579320 libpipewire-module-jackdbus-detect.so
INFO CODE_ID E9AF4EC4BCB58545F45E677CE38579324F8E747E
PUBLIC 1f60 0 pipewire__module_init
STACK CFI INIT 1380 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT 13b0 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT 13f0 48 .cfa: sp 0 + .ra: x30
STACK CFI 13f4 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 13fc x19: .cfa -16 + ^
STACK CFI 1434 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1440 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT 1450 50 .cfa: sp 0 + .ra: x30
STACK CFI 1458 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1460 x19: .cfa -16 + ^
STACK CFI 1498 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 14a0 d0 .cfa: sp 0 + .ra: x30
STACK CFI 14a8 .cfa: sp 48 +
STACK CFI 14bc .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 14cc x19: .cfa -16 + ^ x20: .cfa -8 + ^
STACK CFI 1564 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x29: x29
STACK CFI 156c .cfa: sp 48 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x20: .cfa -8 + ^ x29: .cfa -32 + ^
STACK CFI INIT 1570 2b8 .cfa: sp 0 + .ra: x30
STACK CFI 1578 .cfa: sp 80 +
STACK CFI 1584 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 158c x21: .cfa -16 + ^
STACK CFI 1598 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1608 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x29: x29
STACK CFI 1610 .cfa: sp 80 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1830 60 .cfa: sp 0 + .ra: x30
STACK CFI 1838 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 1840 x19: .cfa -16 + ^
STACK CFI 1888 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1890 50 .cfa: sp 0 + .ra: x30
STACK CFI 1898 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI 18a0 x19: .cfa -16 + ^
STACK CFI 18d8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 18e0 354 .cfa: sp 0 + .ra: x30
STACK CFI 18e8 .cfa: sp 112 +
STACK CFI 18f4 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 18fc x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1904 x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 19a8 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 19b0 .cfa: sp 112 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1c34 32c .cfa: sp 0 + .ra: x30
STACK CFI 1c3c .cfa: sp 128 +
STACK CFI 1c48 .ra: .cfa -40 + ^ x29: .cfa -48 + ^
STACK CFI 1c50 x19: .cfa -32 + ^ x20: .cfa -24 + ^
STACK CFI 1c5c x21: .cfa -16 + ^ x22: .cfa -8 + ^
STACK CFI 1d60 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x29: x29
STACK CFI 1d68 .cfa: sp 128 + .ra: .cfa -40 + ^ x19: .cfa -32 + ^ x20: .cfa -24 + ^ x21: .cfa -16 + ^ x22: .cfa -8 + ^ x29: .cfa -48 + ^
STACK CFI INIT 1f60 3c8 .cfa: sp 0 + .ra: x30
STACK CFI 1f68 .cfa: sp 128 +
STACK CFI 1f74 .ra: .cfa -72 + ^ x29: .cfa -80 + ^
STACK CFI 1f80 x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^
STACK CFI 1f88 x23: .cfa -32 + ^ x24: .cfa -24 + ^
STACK CFI 1fd8 x25: .cfa -16 + ^
STACK CFI 2144 x25: x25
STACK CFI 2178 .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 2180 .cfa: sp 128 + .ra: .cfa -72 + ^ x19: .cfa -64 + ^ x20: .cfa -56 + ^ x21: .cfa -48 + ^ x22: .cfa -40 + ^ x23: .cfa -32 + ^ x24: .cfa -24 + ^ x25: .cfa -16 + ^ x29: .cfa -80 + ^
STACK CFI 2198 x25: x25
STACK CFI 21b4 x25: .cfa -16 + ^
STACK CFI 21b8 x25: x25
STACK CFI 21c0 x25: .cfa -16 + ^
STACK CFI 21f8 x25: x25
STACK CFI 21fc x25: .cfa -16 + ^
STACK CFI 22a0 x25: x25
STACK CFI 22ac x25: .cfa -16 + ^
STACK CFI 2314 x25: x25
STACK CFI 2318 x25: .cfa -16 + ^
STACK CFI 2320 x25: x25
