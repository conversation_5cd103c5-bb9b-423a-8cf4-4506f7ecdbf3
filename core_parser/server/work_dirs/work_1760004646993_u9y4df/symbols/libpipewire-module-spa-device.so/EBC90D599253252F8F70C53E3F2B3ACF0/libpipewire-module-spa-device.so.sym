MODULE Linux arm64 EBC90D599253252F8F70C53E3F2B3ACF0 libpipewire-module-spa-device.so
INFO CODE_ID 590DC9EB53922F258F70C53E3F2B3ACF4148F958
PUBLIC 1000 0 pipewire__module_init
STACK CFI INIT e10 30 .cfa: sp 0 + .ra: x30
STACK CFI INIT e40 3c .cfa: sp 0 + .ra: x30
STACK CFI INIT e80 48 .cfa: sp 0 + .ra: x30
STACK CFI e84 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI e8c x19: .cfa -16 + ^
STACK CFI ec4 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT ed0 4 .cfa: sp 0 + .ra: x30
STACK CFI INIT ee0 50 .cfa: sp 0 + .ra: x30
STACK CFI ee8 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI ef0 x19: .cfa -16 + ^
STACK CFI f28 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT f30 c8 .cfa: sp 0 + .ra: x30
STACK CFI f38 .cfa: sp 32 + .ra: .cfa -24 + ^ x29: .cfa -32 + ^
STACK CFI f48 x19: .cfa -16 + ^
STACK CFI fa8 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI fb0 .cfa: sp 32 + .ra: .cfa -24 + ^ x19: .cfa -16 + ^ x29: .cfa -32 + ^
STACK CFI ff0 .cfa: sp 0 + .ra: .ra x19: x19 x29: x29
STACK CFI INIT 1000 4e8 .cfa: sp 0 + .ra: x30
STACK CFI 1008 .cfa: sp 128 +
STACK CFI 1014 .ra: .cfa -88 + ^ x29: .cfa -96 + ^
STACK CFI 101c x19: .cfa -80 + ^ x20: .cfa -72 + ^
STACK CFI 1028 x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^
STACK CFI 1088 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 10e0 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 11a8 x25: x25 x26: x26
STACK CFI 11ac x27: x27 x28: x28
STACK CFI 11ec .cfa: sp 0 + .ra: .ra x19: x19 x20: x20 x21: x21 x22: x22 x23: x23 x24: x24 x29: x29
STACK CFI 11f4 .cfa: sp 128 + .ra: .cfa -88 + ^ x19: .cfa -80 + ^ x20: .cfa -72 + ^ x21: .cfa -64 + ^ x22: .cfa -56 + ^ x23: .cfa -48 + ^ x24: .cfa -40 + ^ x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^ x29: .cfa -96 + ^
STACK CFI 120c x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 1228 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1244 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1270 x27: x27 x28: x28
STACK CFI 12bc x25: x25 x26: x26
STACK CFI 12c4 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 1318 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 134c x27: x27 x28: x28
STACK CFI 13c4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 13f8 x27: x27 x28: x28
STACK CFI 1404 x25: x25 x26: x26
STACK CFI 140c x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 141c x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 1434 x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 14ac x25: .cfa -32 + ^ x26: .cfa -24 + ^ x27: .cfa -16 + ^ x28: .cfa -8 + ^
STACK CFI 14dc x25: x25 x26: x26 x27: x27 x28: x28
STACK CFI 14e0 x25: .cfa -32 + ^ x26: .cfa -24 + ^
STACK CFI 14e4 x27: .cfa -16 + ^ x28: .cfa -8 + ^
